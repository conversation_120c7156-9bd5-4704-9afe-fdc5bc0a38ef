# OneDay 项目架构文档

本文档描述 OneDay Flutter 应用的整体架构设计、核心模块和设计原则。

## 📋 目录

- [项目概述](#项目概述)
- [架构设计](#架构设计)
- [核心模块](#核心模块)
- [数据流管理](#数据流管理)
- [坐标系统设计](#坐标系统设计)
- [设计原则](#设计原则)

---

## 🎯 项目概述

### 应用定位
OneDay 是一个综合性学习效率提升应用，集成了时间管理、记忆训练、学习统计等功能。

### 技术选型
- **开发框架**: Flutter 3.x + Dart
- **状态管理**: Riverpod
- **本地存储**: SharedPreferences + Hive
- **UI设计**: Notion风格 + Material Design
- **架构模式**: MVVM + Provider

---

## 🏗️ 架构设计

### 整体架构图
```
┌─────────────────────────────────────────┐
│                UI Layer                 │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │ Screens │ │ Widgets │ │ Dialogs │   │
│  └─────────┘ └─────────┘ └─────────┘   │
├─────────────────────────────────────────┤
│              Business Layer             │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │Providers│ │Services │ │ Models  │   │
│  └─────────┘ └─────────┘ └─────────┘   │
├─────────────────────────────────────────┤
│               Data Layer                │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐   │
│  │  Local  │ │ Remote  │ │  Cache  │   │
│  │ Storage │ │   API   │ │ Manager │   │
│  └─────────┘ └─────────┘ └─────────┘   │
└─────────────────────────────────────────┘
```

### 目录结构
```
lib/
├── core/                 # 核心基础设施
│   ├── constants/        # 常量定义
│   ├── extensions/       # 扩展方法
│   └── utils/           # 工具类
├── features/            # 功能模块
│   ├── timebox/         # 时间盒子功能
│   ├── memory_palace/   # 记忆宫殿功能
│   ├── community/       # 社区功能
│   └── profile/         # 个人中心
├── shared/              # 共享组件
│   ├── widgets/         # 通用UI组件
│   ├── models/          # 数据模型
│   └── services/        # 共享服务
└── router/              # 路由管理
```

---

## 🧩 核心模块

### 1. TimeBox 模块
**职责**: 番茄钟计时、任务管理、学习统计

**核心组件**:
- `TimeBoxProvider`: 状态管理
- `TimerService`: 计时器服务
- `TaskManager`: 任务管理
- `FloatingTimer`: 浮动计时器

**数据流**:
```
User Action → Provider → Service → Local Storage
     ↓
UI Update ← State Change ← Business Logic
```

### 2. Memory Palace 模块
**职责**: 空间记忆、图片标注、知识点管理

**核心组件**:
- `SceneDetailPage`: 场景详情页面
- `CoordinateSystem`: 坐标系统
- `DragInteraction`: 拖拽交互
- `BubbleManager`: 气泡管理

**坐标转换链**:
```
Screen Coordinates → Transform Matrix → Image Coordinates → Storage
```

### 3. Community 模块
**职责**: 文章管理、词汇学习、内容分享

**核心组件**:
- `ArticleManager`: 文章管理
- `VocabularyService`: 词汇服务
- `ProfanityFilter`: 内容过滤
- `ShareService`: 分享功能

---

## 🔄 数据流管理

### 状态管理架构
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Widget    │───▶│  Provider   │───▶│   Service   │
│             │    │             │    │             │
│  (UI Layer) │◀───│ (Business)  │◀───│ (Data Layer)│
└─────────────┘    └─────────────┘    └─────────────┘
```

### Riverpod Provider 设计
```dart
// 状态提供者
final timeBoxProvider = StateNotifierProvider<TimeBoxNotifier, TimeBoxState>(
  (ref) => TimeBoxNotifier(ref.read(timerServiceProvider)),
);

// 服务提供者
final timerServiceProvider = Provider<TimerService>(
  (ref) => TimerService(),
);

// 数据提供者
final taskDataProvider = FutureProvider<List<Task>>(
  (ref) => ref.read(taskRepositoryProvider).getAllTasks(),
);
```

### 数据持久化策略
- **即时保存**: 用户操作后立即保存关键数据
- **批量同步**: 定期批量同步非关键数据
- **缓存机制**: 内存缓存提高访问性能
- **版本管理**: 数据结构版本控制和迁移

---

## 📐 坐标系统设计

### 多坐标系架构
OneDay 应用中存在多个坐标系统，需要精确的转换机制：

#### 1. 图片坐标系 (Image Coordinates)
- **范围**: (0.0, 0.0) 到 (1.0, 1.0)
- **用途**: 存储知识点的相对位置
- **优势**: 与图片尺寸无关，适配性强

#### 2. 屏幕坐标系 (Screen Coordinates)  
- **范围**: 设备屏幕的像素坐标
- **用途**: UI交互和手势处理
- **特点**: 绝对像素位置

#### 3. 变换坐标系 (Transform Coordinates)
- **Matrix4**: 处理缩放、平移、旋转
- **用途**: 图片查看器的变换操作
- **计算**: 实时计算变换矩阵

### 坐标转换算法
```dart
// 屏幕坐标 → 图片坐标
Offset convertScreenToImage(Offset screenPoint, Matrix4 transform, Size imageSize) {
  final inverse = Matrix4.inverted(transform);
  final imagePoint = inverse.transform3(Vector3(screenPoint.dx, screenPoint.dy, 0.0));
  
  return Offset(
    imagePoint.x / imageSize.width,
    imagePoint.y / imageSize.height,
  );
}

// 图片坐标 → 屏幕坐标  
Offset convertImageToScreen(Offset imagePoint, Matrix4 transform, Size imageSize) {
  final absolutePoint = Offset(
    imagePoint.dx * imageSize.width,
    imagePoint.dy * imageSize.height,
  );
  
  final screenPoint = transform.transform3(Vector3(absolutePoint.dx, absolutePoint.dy, 0.0));
  return Offset(screenPoint.x, screenPoint.y);
}
```

---

## 🎨 设计原则

### 1. 单一职责原则 (SRP)
- 每个类和模块只负责一个功能
- UI组件与业务逻辑分离
- 服务类职责明确

### 2. 依赖注入原则 (DIP)
- 使用Riverpod进行依赖注入
- 面向接口编程
- 降低模块间耦合

### 3. 开闭原则 (OCP)
- 对扩展开放，对修改关闭
- 使用抽象类和接口
- 插件化架构设计

### 4. 响应式设计
- 支持多种屏幕尺寸
- 横屏/竖屏自适应
- 动态布局调整

### 5. 性能优化
- 懒加载和按需加载
- 内存管理和资源释放
- UI渲染优化

---

## 🔧 技术实现细节

### Widget 生命周期管理
```dart
class StatefulWidgetExample extends StatefulWidget {
  @override
  _StatefulWidgetExampleState createState() => _StatefulWidgetExampleState();
}

class _StatefulWidgetExampleState extends State<StatefulWidgetExample> {
  @override
  void initState() {
    super.initState();
    // 初始化资源
  }

  @override
  void dispose() {
    // 释放资源，防止内存泄漏
    super.dispose();
  }
}
```

### 错误处理机制
- **全局错误捕获**: FlutterError.onError
- **异步错误处理**: runZonedGuarded
- **用户友好提示**: 错误信息本地化
- **日志记录**: 开发和生产环境的日志策略

### 测试策略
- **单元测试**: 业务逻辑测试
- **Widget测试**: UI组件测试  
- **集成测试**: 端到端功能测试
- **性能测试**: 内存和渲染性能测试

---

**架构演进**: 本架构文档会随着项目发展持续更新，确保架构设计与实际实现保持一致。
