import 'dart:io';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'photo_selection_validator.dart';

/// 简化的图片选择测试页面
/// 用于快速诊断照片选择问题
class SimpleImagePickerTest extends StatefulWidget {
  const SimpleImagePickerTest({super.key});

  @override
  State<SimpleImagePickerTest> createState() => _SimpleImagePickerTestState();
}

class _SimpleImagePickerTestState extends State<SimpleImagePickerTest> {
  File? _selectedImage;
  String _status = '准备就绪';
  final ImagePicker _picker = ImagePicker();
  final List<ValidationResult> _validationResults = [];

  /// 测试单张图片选择
  Future<void> _testSingleImagePicker() async {
    setState(() {
      _status = '正在选择单张图片...';
    });

    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _status = '单张图片选择成功: ${image.path}';
        });
      } else {
        setState(() {
          _status = '用户取消了单张图片选择';
        });
      }
    } catch (e) {
      setState(() {
        _status = '单张图片选择失败: $e';
      });
    }
  }

  /// 测试多张图片选择（知忆相册使用的方法）
  Future<void> _testMultiImagePicker() async {
    setState(() {
      _status = '正在选择多张图片...';
    });

    try {
      final List<XFile> images = await _picker.pickMultiImage(imageQuality: 80);

      if (images.isNotEmpty) {
        setState(() {
          _selectedImage = File(images.first.path);
          _status = '多张图片选择成功: 选择了${images.length}张图片';
        });
      } else {
        setState(() {
          _status = '用户取消了多张图片选择';
        });
      }
    } catch (e) {
      setState(() {
        _status = '多张图片选择失败: $e';
      });
    }
  }

  /// 测试相机拍照
  Future<void> _testCamera() async {
    setState(() {
      _status = '正在打开相机...';
    });

    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _status = '相机拍照成功: ${image.path}';
        });
      } else {
        setState(() {
          _status = '用户取消了相机拍照';
        });
      }
    } catch (e) {
      setState(() {
        _status = '相机拍照失败: $e';
      });
    }
  }

  /// 运行完整验证
  Future<void> _runValidation() async {
    setState(() {
      _status = '正在运行验证测试...';
      _validationResults.clear();
    });

    try {
      final results = await PhotoSelectionValidator.runFullValidation();
      setState(() {
        _validationResults.addAll(results);
        _status = '验证完成，共${results.length}项测试';
      });
    } catch (e) {
      setState(() {
        _status = '验证失败: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('图片选择测试'),
        backgroundColor: const Color(0xFF2F76DA),
        foregroundColor: Colors.white,
        actions: [
          // iOS模拟器专用测试入口
          if (Platform.isIOS)
            IconButton(
              onPressed: () => context.push('/ios-simulator-test'),
              icon: const Icon(Icons.phone_iphone),
              tooltip: 'iOS模拟器专用测试',
            ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 状态显示
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text('状态: $_status', style: const TextStyle(fontSize: 14)),
            ),

            const SizedBox(height: 20),

            // 测试按钮
            ElevatedButton(
              onPressed: _testSingleImagePicker,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2F76DA),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('测试单张图片选择'),
            ),

            const SizedBox(height: 12),

            ElevatedButton(
              onPressed: _testMultiImagePicker,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('测试多张图片选择（相册功能）'),
            ),

            const SizedBox(height: 12),

            ElevatedButton(
              onPressed: _testCamera,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('测试相机拍照'),
            ),

            const SizedBox(height: 12),

            ElevatedButton(
              onPressed: _runValidation,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('运行完整验证'),
            ),

            const SizedBox(height: 20),

            // 验证结果显示
            if (_validationResults.isNotEmpty) ...[
              const Text(
                '验证结果:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ..._validationResults.map(
                (result) => ValidationResultWidget(result: result),
              ),
              const SizedBox(height: 20),
            ],

            // 选中的图片显示
            if (_selectedImage != null)
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(_selectedImage!, fit: BoxFit.contain),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
