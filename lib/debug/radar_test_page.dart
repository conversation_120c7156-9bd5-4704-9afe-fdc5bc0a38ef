import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// 雷达图测试页面
/// 用于快速测试雷达图的视觉修复效果
class RadarTestPage extends StatelessWidget {
  const RadarTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('雷达图测试'),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF37352F),
        elevation: 0,
        centerTitle: true,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              '雷达图视觉修复测试',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 32),
            
            const Text(
              '修复内容：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 16),
            
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 32),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('• 轴线长度精确裁剪到最外层圆周'),
                  Text('• 100分数据点重新校准到圆周边界'),
                  Text('• 五边形顶点与轴线端点完美对齐'),
                ],
              ),
            ),
            
            const SizedBox(height: 48),
            
            ElevatedButton(
              onPressed: () => context.push('/ability-radar'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2E7EED),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                '查看修复后的雷达图',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
