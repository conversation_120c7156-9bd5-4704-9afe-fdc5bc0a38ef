import 'package:flutter/material.dart';
import '../features/community/profanity_filter_service.dart';

/// 脏话过滤测试页面
class ProfanityFilterTestPage extends StatefulWidget {
  const ProfanityFilterTestPage({super.key});

  @override
  State<ProfanityFilterTestPage> createState() => _ProfanityFilterTestPageState();
}

class _ProfanityFilterTestPageState extends State<ProfanityFilterTestPage> {
  final TextEditingController _testController = TextEditingController();
  final ProfanityFilterService _filterService = ProfanityFilterService();
  
  String _result = '';
  bool _isLoading = false;

  @override
  void dispose() {
    _testController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('脏话过滤测试'),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF37352F),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '测试文本:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _testController,
              decoration: const InputDecoration(
                hintText: '输入包含违禁词的文本进行测试，如：傻逼、操、去死等',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton(
                  onPressed: _isLoading ? null : _testFilter,
                  child: _isLoading 
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('测试过滤'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _testInitialization,
                  child: const Text('测试初始化'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _loadTestWords,
                  child: const Text('加载测试词汇'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              '测试结果:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _result.isEmpty ? '暂无测试结果' : _result,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testFilter() async {
    setState(() {
      _isLoading = true;
      _result = '';
    });

    try {
      final text = _testController.text.trim();
      if (text.isEmpty) {
        setState(() {
          _result = '请输入测试文本';
        });
        return;
      }

      debugPrint('🧪 开始测试脏话过滤: "$text"');
      
      final result = await _filterService.filterText(text);
      
      setState(() {
        _result = '''测试文本: "$text"

原始文本: ${result.originalText}
过滤后文本: ${result.filteredText}
是否有违禁词: ${result.hasViolations}
检测到的违禁词数量: ${result.detectedWords.length}

检测到的违禁词详情:
${result.detectedWords.map((word) => '- ${word.word} (${word.startIndex}-${word.endIndex}) [${word.level.name}]').join('\n')}

测试完成时间: ${DateTime.now()}''';
      });
    } catch (e) {
      setState(() {
        _result = '测试失败: $e';
      });
      debugPrint('🧪 测试失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testInitialization() async {
    setState(() {
      _result = '正在测试初始化...';
    });

    try {
      debugPrint('🧪 开始测试初始化');
      
      await _filterService.initialize();
      final settings = _filterService.getSettings();
      
      setState(() {
        _result = '''初始化测试结果:

过滤功能启用: ${settings.enabled}
过滤级别: ${settings.filterLevel.name}
显示替换文本: ${settings.showReplacementText}
替换文本: "${settings.replacementText}"
启用用户举报: ${settings.enableUserReporting}
启用自定义词库: ${settings.enableCustomWords}

初始化完成时间: ${DateTime.now()}''';
      });
    } catch (e) {
      setState(() {
        _result = '初始化测试失败: $e';
      });
      debugPrint('🧪 初始化测试失败: $e');
    }
  }

  Future<void> _loadTestWords() async {
    setState(() {
      _result = '正在加载测试词汇...';
    });

    try {
      debugPrint('🧪 开始加载测试词汇');
      
      // 测试一些常见的违禁词
      final testWords = ['傻逼', '操', '去死', '白痴', 'fuck', 'shit'];
      final results = <String>[];
      
      for (final word in testWords) {
        final result = await _filterService.filterText(word);
        results.add('$word -> ${result.hasViolations ? "检测到" : "未检测到"} (过滤后: "${result.filteredText}")');
      }
      
      setState(() {
        _result = '''测试词汇检测结果:

${results.join('\n')}

测试完成时间: ${DateTime.now()}''';
      });
    } catch (e) {
      setState(() {
        _result = '加载测试词汇失败: $e';
      });
      debugPrint('🧪 加载测试词汇失败: $e');
    }
  }
}
