import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/ios_simulator_detector.dart';

/// iOS照片选择深度调试器
/// 专门用于诊断iOS模拟器中照片选择功能的问题
class IOSPhotoSelectionDebugger extends StatefulWidget {
  const IOSPhotoSelectionDebugger({super.key});

  @override
  State<IOSPhotoSelectionDebugger> createState() =>
      _IOSPhotoSelectionDebuggerState();
}

class _IOSPhotoSelectionDebuggerState extends State<IOSPhotoSelectionDebugger> {
  final ImagePicker _imagePicker = ImagePicker();
  final List<String> _debugLogs = [];
  final List<String> _selectedImagePaths = [];

  bool _isLoading = false;
  bool? _isSimulator;
  Map<String, dynamic>? _deviceInfo;
  Map<String, dynamic>? _permissionStrategy;

  @override
  void initState() {
    super.initState();
    _initializeDebugger();
  }

  void _addLog(String message) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    setState(() {
      _debugLogs.add('[$timestamp] $message');
    });
    print(message);
  }

  /// 初始化调试器
  Future<void> _initializeDebugger() async {
    _addLog('🔍 开始初始化iOS照片选择调试器...');

    try {
      // 检测设备类型
      _isSimulator = await IOSSimulatorDetector.isSimulator();
      _addLog('📱 设备类型: ${_isSimulator! ? "iOS模拟器" : "真机"}');

      if (_isSimulator!) {
        // 获取模拟器详细信息
        _deviceInfo = await IOSSimulatorDetector.getDebugInfo();
        _addLog('📋 模拟器信息: ${_deviceInfo.toString()}');

        // 获取权限策略
        _permissionStrategy =
            await IOSSimulatorDetector.getSimulatorPermissionStrategy();
        _addLog('🔐 权限策略: ${_permissionStrategy.toString()}');

        // 检查模拟器照片状态
        final hasPhotos = await IOSSimulatorDetector.hasPhotosInSimulator();
        _addLog('📷 模拟器照片状态: ${hasPhotos ? "可能有照片" : "可能无照片"}');
      }

      // 检查当前权限状态
      await _checkCurrentPermissions();

      _addLog('✅ 调试器初始化完成');
    } catch (e) {
      _addLog('❌ 初始化失败: $e');
    }
  }

  /// 检查当前权限状态
  Future<void> _checkCurrentPermissions() async {
    _addLog('🔐 检查当前权限状态...');

    try {
      final photosStatus = await Permission.photos.status;
      _addLog('📸 Photos权限: $photosStatus');

      if (Platform.isIOS) {
        // iOS特有的权限检查
        final photosAddOnlyStatus = await Permission.photosAddOnly.status;
        _addLog('📸 PhotosAddOnly权限: $photosAddOnlyStatus');
      }

      if (Platform.isAndroid) {
        final storageStatus = await Permission.storage.status;
        _addLog('💾 Storage权限: $storageStatus');
      }
    } catch (e) {
      _addLog('❌ 权限检查失败: $e');
    }
  }

  /// 测试基础照片选择功能
  Future<void> _testBasicPhotoSelection() async {
    _addLog('🧪 开始测试基础照片选择功能...');

    setState(() {
      _isLoading = true;
    });

    try {
      // 直接调用image_picker，不做任何权限检查
      _addLog('📸 直接调用pickMultiImage...');

      final List<XFile> images = await _imagePicker.pickMultiImage(
        imageQuality: 80,
      );

      _addLog('📸 pickMultiImage返回结果: ${images.length} 张图片');

      if (images.isNotEmpty) {
        _addLog('✅ 成功选择照片！');

        setState(() {
          _selectedImagePaths.clear();
          _selectedImagePaths.addAll(images.map((img) => img.path));
        });

        // 详细分析每张照片
        for (int i = 0; i < images.length; i++) {
          final image = images[i];
          await _analyzeSelectedImage(image, i + 1);
        }

        _addLog('🎉 照片选择测试成功完成！');
      } else {
        _addLog('⚠️ 没有选择任何照片');

        if (_isSimulator!) {
          _addLog('💡 模拟器提示: 这可能是因为相册为空或用户取消了选择');
          await _diagnoseSimulatorIssues();
        }
      }
    } catch (e) {
      _addLog('❌ 照片选择失败: $e');
      _addLog('❌ 错误类型: ${e.runtimeType}');
      _addLog('❌ 错误详情: ${e.toString()}');

      if (_isSimulator!) {
        await _diagnoseSimulatorError(e);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 分析选中的照片
  Future<void> _analyzeSelectedImage(XFile image, int index) async {
    try {
      final file = File(image.path);
      final exists = await file.exists();
      final size = exists ? await file.length() : 0;

      _addLog('📷 照片 $index:');
      _addLog('  - 路径: ${image.path}');
      _addLog('  - 文件名: ${image.name}');
      _addLog('  - 文件存在: $exists');
      _addLog('  - 文件大小: ${(size / 1024).toStringAsFixed(1)} KB');

      if (exists) {
        // 尝试读取图片信息
        try {
          final bytes = await file.readAsBytes();
          _addLog('  - 字节数: ${bytes.length}');

          // 尝试解码图片
          final image = await decodeImageFromList(bytes);
          _addLog('  - 图片尺寸: ${image.width}x${image.height}');
        } catch (e) {
          _addLog('  - ⚠️ 图片解码失败: $e');
        }
      }
    } catch (e) {
      _addLog('❌ 分析照片 $index 失败: $e');
    }
  }

  /// 诊断模拟器问题
  Future<void> _diagnoseSimulatorIssues() async {
    _addLog('🔍 开始诊断模拟器问题...');

    try {
      // 检查模拟器照片库状态
      final instructions = IOSSimulatorDetector.getSimulatorPhotoInstructions();
      _addLog('📋 模拟器照片添加指导:');
      for (final instruction in instructions) {
        _addLog('  • $instruction');
      }

      // 检查权限状态
      await _checkCurrentPermissions();

      // 提供解决建议
      _addLog('💡 解决建议:');
      _addLog('  1. 确保模拟器相册中有照片');
      _addLog('  2. 尝试重新启动模拟器');
      _addLog('  3. 检查模拟器设置中的隐私权限');
      _addLog('  4. 尝试使用不同的模拟器版本');
    } catch (e) {
      _addLog('❌ 诊断失败: $e');
    }
  }

  /// 诊断模拟器错误
  Future<void> _diagnoseSimulatorError(dynamic error) async {
    _addLog('🔍 开始诊断模拟器错误...');

    try {
      final errorMessage = IOSSimulatorDetector.handleSimulatorError(error);
      _addLog('🔧 模拟器错误处理结果: $errorMessage');

      // 根据错误类型提供具体建议
      final errorString = error.toString().toLowerCase();

      if (errorString.contains('permission') ||
          errorString.contains('denied')) {
        _addLog('🔐 权限相关错误，建议:');
        _addLog('  1. 重置模拟器隐私设置');
        _addLog('  2. 手动授予照片权限');
        _addLog('  3. 重新安装应用');
      } else if (errorString.contains('no such file') ||
          errorString.contains('not found')) {
        _addLog('📁 文件相关错误，建议:');
        _addLog('  1. 确保模拟器相册中有照片');
        _addLog('  2. 重新添加照片到模拟器');
      } else {
        _addLog('❓ 未知错误类型，建议:');
        _addLog('  1. 重启模拟器');
        _addLog('  2. 清理应用数据');
        _addLog('  3. 更新Xcode和模拟器');
      }
    } catch (e) {
      _addLog('❌ 错误诊断失败: $e');
    }
  }

  /// 测试权限请求流程
  Future<void> _testPermissionFlow() async {
    _addLog('🔐 开始测试权限请求流程...');

    try {
      // 检查当前状态
      final currentStatus = await Permission.photos.status;
      _addLog('📸 当前权限状态: $currentStatus');

      if (currentStatus.isDenied) {
        _addLog('📸 权限被拒绝，尝试请求权限...');
        final result = await Permission.photos.request();
        _addLog('📸 权限请求结果: $result');
      } else if (currentStatus.isPermanentlyDenied) {
        _addLog('📸 权限被永久拒绝，需要手动设置');
        _showPermissionDialog();
      } else if (currentStatus.isGranted) {
        _addLog('✅ 权限已授予');
      }
    } catch (e) {
      _addLog('❌ 权限测试失败: $e');
    }
  }

  /// 显示权限对话框
  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('权限设置'),
        content: const Text('照片权限被永久拒绝，请前往设置手动开启'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            child: const Text('去设置'),
          ),
        ],
      ),
    );
  }

  /// 清空日志
  void _clearLogs() {
    setState(() {
      _debugLogs.clear();
      _selectedImagePaths.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('iOS照片选择调试器'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _clearLogs,
            icon: const Icon(Icons.clear_all),
            tooltip: '清空日志',
          ),
        ],
      ),
      body: Column(
        children: [
          // 控制按钮区域
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _isLoading ? null : _testBasicPhotoSelection,
                        icon: _isLoading
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : const Icon(Icons.photo_library),
                        label: Text(_isLoading ? '测试中...' : '测试照片选择'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _testPermissionFlow,
                        icon: const Icon(Icons.security),
                        label: const Text('测试权限'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                if (_selectedImagePaths.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Text(
                    '已选择 ${_selectedImagePaths.length} 张照片',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // 日志显示区域
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey),
              ),
              child: ListView.builder(
                itemCount: _debugLogs.length,
                itemBuilder: (context, index) {
                  final log = _debugLogs[index];
                  Color textColor = Colors.white;

                  if (log.contains('❌')) {
                    textColor = Colors.red;
                  } else if (log.contains('⚠️')) {
                    textColor = Colors.orange;
                  } else if (log.contains('✅')) {
                    textColor = Colors.green;
                  } else if (log.contains('🔍') || log.contains('🧪')) {
                    textColor = Colors.blue;
                  } else if (log.contains('💡')) {
                    textColor = Colors.yellow;
                  }

                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      log,
                      style: TextStyle(
                        color: textColor,
                        fontSize: 12,
                        fontFamily: 'monospace',
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
