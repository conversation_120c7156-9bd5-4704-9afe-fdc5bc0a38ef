import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/ios_simulator_detector.dart';

/// iOS模拟器重置和测试助手
/// 提供模拟器重置、权限重置、照片添加等功能
class SimulatorResetHelper extends StatefulWidget {
  const SimulatorResetHelper({super.key});

  @override
  State<SimulatorResetHelper> createState() => _SimulatorResetHelperState();
}

class _SimulatorResetHelperState extends State<SimulatorResetHelper> {
  final List<String> _logs = [];
  bool _isLoading = false;
  bool? _isSimulator;

  @override
  void initState() {
    super.initState();
    _initializeHelper();
  }

  void _addLog(String message) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    setState(() {
      _logs.add('[$timestamp] $message');
    });
    print(message);
  }

  /// 初始化助手
  Future<void> _initializeHelper() async {
    _addLog('🔧 模拟器重置助手已启动');

    try {
      _isSimulator = await IOSSimulatorDetector.isSimulator();
      _addLog('📱 设备检测: ${_isSimulator! ? "iOS模拟器" : "真机"}');

      if (_isSimulator!) {
        await IOSSimulatorDetector.getDebugInfo();
        _addLog('📋 模拟器信息获取完成');
        await _checkCurrentStatus();
      } else {
        _addLog('⚠️ 当前不是iOS模拟器环境');
      }
    } catch (e) {
      _addLog('❌ 初始化失败: $e');
    }
  }

  /// 检查当前状态
  Future<void> _checkCurrentStatus() async {
    _addLog('🔍 检查当前模拟器状态...');

    try {
      // 检查权限状态
      final photosStatus = await Permission.photos.status;
      _addLog('📸 照片权限: $photosStatus');

      // 检查是否有照片
      final hasPhotos = await IOSSimulatorDetector.hasPhotosInSimulator();
      _addLog('📷 模拟器照片: ${hasPhotos ? "可能有照片" : "可能无照片"}');

      // 检查应用权限
      await _checkAppPermissions();
    } catch (e) {
      _addLog('❌ 状态检查失败: $e');
    }
  }

  /// 检查应用权限
  Future<void> _checkAppPermissions() async {
    try {
      _addLog('🔐 检查应用权限状态...');

      // 尝试获取权限状态
      final permissions = [Permission.photos, Permission.camera];

      for (final permission in permissions) {
        try {
          final status = await permission.status;
          final name = permission.toString().split('.').last;
          _addLog('  - $name: $status');
        } catch (e) {
          _addLog('  - 权限检查异常: $e');
        }
      }
    } catch (e) {
      _addLog('❌ 应用权限检查失败: $e');
    }
  }

  /// 重置模拟器权限
  Future<void> _resetSimulatorPermissions() async {
    if (!_isSimulator!) {
      _addLog('⚠️ 只能在iOS模拟器中重置权限');
      return;
    }

    _addLog('🔄 开始重置模拟器权限...');
    setState(() {
      _isLoading = true;
    });

    try {
      // 显示重置指导
      await _showPermissionResetDialog();
    } catch (e) {
      _addLog('❌ 权限重置失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 显示权限重置对话框
  Future<void> _showPermissionResetDialog() async {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('模拟器权限重置指导'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('请按以下步骤重置模拟器权限：'),
              SizedBox(height: 12),
              Text('方法一：重置隐私设置'),
              Text('1. 打开模拟器设置应用'),
              Text('2. 通用 → 传输或还原iPhone'),
              Text('3. 抹掉所有内容和设置'),
              Text('4. 重新启动模拟器'),
              SizedBox(height: 12),
              Text('方法二：重置特定权限'),
              Text('1. 设置 → 隐私与安全性'),
              Text('2. 照片 → OneDay'),
              Text('3. 选择"所有照片"'),
              SizedBox(height: 12),
              Text('方法三：命令行重置'),
              Text('xcrun simctl privacy booted reset photos'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _executeCommandLineReset();
            },
            child: const Text('执行命令行重置'),
          ),
        ],
      ),
    );
  }

  /// 执行命令行权限重置
  Future<void> _executeCommandLineReset() async {
    _addLog('⚡ 执行命令行权限重置...');

    try {
      // 重置照片权限
      final result = await Process.run('xcrun', [
        'simctl',
        'privacy',
        'booted',
        'reset',
        'photos',
      ]);

      if (result.exitCode == 0) {
        _addLog('✅ 照片权限重置成功');
      } else {
        _addLog('❌ 权限重置失败: ${result.stderr}');
      }

      // 等待一下再检查状态
      await Future.delayed(const Duration(seconds: 2));
      await _checkCurrentStatus();
    } catch (e) {
      _addLog('❌ 命令行重置失败: $e');
    }
  }

  /// 添加测试照片到模拟器
  Future<void> _addTestPhotos() async {
    _addLog('📷 开始添加测试照片指导...');

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加测试照片'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('请按以下方法添加照片到模拟器：'),
              SizedBox(height: 12),
              Text('方法一：拖拽添加'),
              Text('1. 从Mac的访达中选择照片'),
              Text('2. 直接拖拽到模拟器屏幕'),
              Text('3. 照片会自动保存到相册'),
              SizedBox(height: 12),
              Text('方法二：Safari下载'),
              Text('1. 在模拟器中打开Safari'),
              Text('2. 搜索并找到图片'),
              Text('3. 长按图片选择"存储到照片"'),
              SizedBox(height: 12),
              Text('方法三：使用样本照片'),
              Text('1. 打开模拟器照片应用'),
              Text('2. 如果为空，系统会提示添加样本照片'),
              Text('3. 点击"添加样本照片"'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _openPhotosApp();
            },
            child: const Text('打开照片应用'),
          ),
        ],
      ),
    );
  }

  /// 打开照片应用
  Future<void> _openPhotosApp() async {
    _addLog('📱 尝试打开照片应用...');

    try {
      // 尝试通过URL Scheme打开照片应用
      const url = 'photos-redirect://';
      final result = await Process.run('xcrun', [
        'simctl',
        'openurl',
        'booted',
        url,
      ]);

      if (result.exitCode == 0) {
        _addLog('✅ 照片应用已打开');
      } else {
        _addLog('⚠️ 请手动打开模拟器中的照片应用');
      }
    } catch (e) {
      _addLog('❌ 打开照片应用失败: $e');
      _addLog('💡 请手动在模拟器中打开照片应用');
    }
  }

  /// 测试照片选择功能
  Future<void> _testPhotoSelection() async {
    _addLog('🧪 开始测试照片选择功能...');

    setState(() {
      _isLoading = true;
    });

    try {
      final picker = ImagePicker();

      _addLog('📸 调用image_picker...');
      final images = await picker.pickMultiImage(imageQuality: 80);

      if (images.isNotEmpty) {
        _addLog('✅ 成功选择 ${images.length} 张照片');
        for (int i = 0; i < images.length; i++) {
          final image = images[i];
          final file = File(image.path);
          final exists = await file.exists();
          final size = exists ? await file.length() : 0;
          _addLog(
            '📷 照片 ${i + 1}: ${image.name} (${(size / 1024).toStringAsFixed(1)} KB)',
          );
        }

        // 显示成功消息
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('✅ 成功选择 ${images.length} 张照片'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        _addLog('⚠️ 没有选择任何照片');
        _addLog('💡 可能的原因：');
        _addLog('  - 用户取消了选择');
        _addLog('  - 相册为空');
        _addLog('  - 权限被拒绝');
      }
    } catch (e) {
      _addLog('❌ 照片选择失败: $e');
      _addLog('🔧 建议的解决步骤：');
      _addLog('  1. 重置权限');
      _addLog('  2. 添加测试照片');
      _addLog('  3. 重启模拟器');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 清空日志
  void _clearLogs() {
    setState(() {
      _logs.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('模拟器重置助手'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _clearLogs,
            icon: const Icon(Icons.clear_all),
            tooltip: '清空日志',
          ),
        ],
      ),
      body: Column(
        children: [
          // 控制按钮区域
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // 第一行按钮
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _isLoading
                            ? null
                            : _resetSimulatorPermissions,
                        icon: const Icon(Icons.refresh),
                        label: const Text('重置权限'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _addTestPhotos,
                        icon: const Icon(Icons.add_photo_alternate),
                        label: const Text('添加照片'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // 第二行按钮
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _isLoading ? null : _testPhotoSelection,
                        icon: _isLoading
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : const Icon(Icons.photo_library),
                        label: Text(_isLoading ? '测试中...' : '测试选择'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _checkCurrentStatus,
                        icon: const Icon(Icons.info),
                        label: const Text('检查状态'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // 日志显示区域
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey),
              ),
              child: ListView.builder(
                itemCount: _logs.length,
                itemBuilder: (context, index) {
                  final log = _logs[index];
                  Color textColor = Colors.white;

                  if (log.contains('❌')) {
                    textColor = Colors.red;
                  } else if (log.contains('⚠️')) {
                    textColor = Colors.orange;
                  } else if (log.contains('✅')) {
                    textColor = Colors.green;
                  } else if (log.contains('🔍') || log.contains('🧪')) {
                    textColor = Colors.blue;
                  } else if (log.contains('💡')) {
                    textColor = Colors.yellow;
                  }

                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Text(
                      log,
                      style: TextStyle(
                        color: textColor,
                        fontSize: 12,
                        fontFamily: 'monospace',
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
