import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../utils/safe_area_helper.dart';

/// 照片渲染问题调试器
/// 专门用于诊断照片显示重影、渲染异常等问题
class PhotoRenderingDebugger extends StatefulWidget {
  const PhotoRenderingDebugger({super.key});

  @override
  State<PhotoRenderingDebugger> createState() => _PhotoRenderingDebuggerState();
}

class _PhotoRenderingDebuggerState extends State<PhotoRenderingDebugger> {
  final ImagePicker _imagePicker = ImagePicker();
  final List<String> _debugLogs = [];

  String? _selectedImagePath;
  bool _isLoading = false;
  bool _showDebugInfo = true;
  bool _enableRepaintBoundary = false;
  bool _enableClipRect = false;
  double _imageOpacity = 1.0;
  BoxFit _imageFit = BoxFit.cover;

  // 渲染测试选项
  final List<BoxFit> _fitOptions = [
    BoxFit.cover,
    BoxFit.contain,
    BoxFit.fill,
    BoxFit.fitWidth,
    BoxFit.fitHeight,
    BoxFit.scaleDown,
  ];

  @override
  void initState() {
    super.initState();
    _addLog('🔍 照片渲染调试器已启动');
    _analyzeRenderingEnvironment();
  }

  void _addLog(String message) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    setState(() {
      _debugLogs.add('[$timestamp] $message');
    });
    print(message);
  }

  /// 分析渲染环境
  void _analyzeRenderingEnvironment() {
    _addLog('🖥️ 开始分析渲染环境...');

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final context = this.context;
      if (context.mounted) {
        final deviceInfo = SafeAreaHelper.getDeviceInfo(context);
        _addLog('📱 设备类型: ${deviceInfo['deviceType']}');
        _addLog('📐 屏幕尺寸: ${deviceInfo['screenSize']}');
        _addLog('🔢 像素比: ${deviceInfo['devicePixelRatio']}');
        _addLog('🛡️ 安全区域: ${deviceInfo['safeAreaInsets']}');

        // 检查渲染性能
        final renderViews = WidgetsBinding.instance.renderViews;
        if (renderViews.isNotEmpty) {
          _addLog('🎨 渲染视图大小: ${renderViews.first.size}');
        }

        // 检查是否启用了调试模式
        bool isDebugMode = false;
        assert(() {
          isDebugMode = true;
          return true;
        }());
        _addLog('🐛 调试模式: ${isDebugMode ? "已启用" : "未启用"}');

        // 检查平台特定的渲染信息
        if (Platform.isIOS) {
          _addLog('🍎 iOS渲染引擎: Metal/Skia');
        } else if (Platform.isAndroid) {
          _addLog('🤖 Android渲染引擎: Vulkan/OpenGL');
        }
      }
    });
  }

  /// 选择测试照片
  Future<void> _selectTestPhoto() async {
    _addLog('📸 开始选择测试照片...');

    setState(() {
      _isLoading = true;
    });

    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 100, // 使用最高质量避免压缩影响
      );

      if (image != null) {
        _addLog('✅ 照片选择成功: ${image.path}');

        setState(() {
          _selectedImagePath = image.path;
        });

        await _analyzeSelectedPhoto(image);
      } else {
        _addLog('⚠️ 用户取消了照片选择');
      }
    } catch (e) {
      _addLog('❌ 照片选择失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 分析选中的照片
  Future<void> _analyzeSelectedPhoto(XFile image) async {
    _addLog('🔍 开始分析照片属性...');

    try {
      final file = File(image.path);
      final bytes = await file.readAsBytes();
      final decodedImage = await decodeImageFromList(bytes);

      _addLog('📷 照片信息:');
      _addLog('  - 文件大小: ${(bytes.length / 1024).toStringAsFixed(1)} KB');
      _addLog('  - 图片尺寸: ${decodedImage.width}x${decodedImage.height}');
      _addLog(
        '  - 宽高比: ${(decodedImage.width / decodedImage.height).toStringAsFixed(2)}',
      );

      // 检查是否可能导致重影的因素
      final aspectRatio = decodedImage.width / decodedImage.height;
      if (aspectRatio > 3 || aspectRatio < 0.3) {
        _addLog('⚠️ 极端宽高比可能导致显示问题');
      }

      if (decodedImage.width > 4000 || decodedImage.height > 4000) {
        _addLog('⚠️ 高分辨率图片可能影响渲染性能');
      }

      // 检查内存使用
      final estimatedMemory =
          (decodedImage.width * decodedImage.height * 4) / (1024 * 1024);
      _addLog('💾 预估内存使用: ${estimatedMemory.toStringAsFixed(1)} MB');

      if (estimatedMemory > 50) {
        _addLog('⚠️ 内存使用较高，可能导致渲染问题');
      }
    } catch (e) {
      _addLog('❌ 照片分析失败: $e');
    }
  }

  /// 测试不同的渲染配置
  void _testRenderingConfiguration(BoxFit fit) {
    _addLog('🎨 测试渲染配置: $fit');
    setState(() {
      _imageFit = fit;
    });
  }

  /// 切换调试边界
  void _toggleRepaintBoundary() {
    setState(() {
      _enableRepaintBoundary = !_enableRepaintBoundary;
    });
    _addLog('🔄 重绘边界: ${_enableRepaintBoundary ? "已启用" : "已禁用"}');
  }

  /// 切换裁剪
  void _toggleClipRect() {
    setState(() {
      _enableClipRect = !_enableClipRect;
    });
    _addLog('✂️ 裁剪模式: ${_enableClipRect ? "已启用" : "已禁用"}');
  }

  /// 清空日志
  void _clearLogs() {
    setState(() {
      _debugLogs.clear();
      _selectedImagePath = null;
    });
  }

  /// 构建照片显示区域
  Widget _buildPhotoDisplay() {
    if (_selectedImagePath == null) {
      return Container(
        height: 200,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey),
        ),
        child: const Center(
          child: Text('请选择测试照片', style: TextStyle(color: Colors.grey)),
        ),
      );
    }

    Widget imageWidget = Image.file(
      File(_selectedImagePath!),
      fit: _imageFit,
      opacity: AlwaysStoppedAnimation(_imageOpacity),
      errorBuilder: (context, error, stackTrace) {
        _addLog('❌ 图片加载错误: $error');
        return Container(
          color: Colors.red[100],
          child: const Center(
            child: Text('图片加载失败', style: TextStyle(color: Colors.red)),
          ),
        );
      },
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (frame == null) {
          return const Center(child: CircularProgressIndicator());
        }
        return child;
      },
    );

    // 应用调试选项
    if (_enableRepaintBoundary) {
      imageWidget = RepaintBoundary(child: imageWidget);
    }

    if (_enableClipRect) {
      imageWidget = ClipRect(child: imageWidget);
    }

    return Container(
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: imageWidget,
      ),
    );
  }

  /// 构建控制面板
  Widget _buildControlPanel() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '渲染控制',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            // BoxFit选择
            const Text(
              '图片适配模式:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: _fitOptions.map((fit) {
                return ChoiceChip(
                  label: Text(fit.toString().split('.').last),
                  selected: _imageFit == fit,
                  onSelected: (selected) {
                    if (selected) _testRenderingConfiguration(fit);
                  },
                );
              }).toList(),
            ),

            const SizedBox(height: 16),

            // 透明度控制
            Text('透明度: ${_imageOpacity.toStringAsFixed(2)}'),
            Slider(
              value: _imageOpacity,
              min: 0.1,
              max: 1.0,
              divisions: 9,
              onChanged: (value) {
                setState(() {
                  _imageOpacity = value;
                });
              },
            ),

            const SizedBox(height: 16),

            // 调试选项
            const Text('调试选项:', style: TextStyle(fontWeight: FontWeight.w500)),
            CheckboxListTile(
              title: const Text('启用重绘边界'),
              value: _enableRepaintBoundary,
              onChanged: (_) => _toggleRepaintBoundary(),
              dense: true,
            ),
            CheckboxListTile(
              title: const Text('启用裁剪'),
              value: _enableClipRect,
              onChanged: (_) => _toggleClipRect(),
              dense: true,
            ),
            CheckboxListTile(
              title: const Text('显示调试信息'),
              value: _showDebugInfo,
              onChanged: (value) {
                setState(() {
                  _showDebugInfo = value ?? true;
                });
              },
              dense: true,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeAreaScaffold(
      title: '照片渲染调试器',
      appBarBackgroundColor: Colors.purple,
      appBarForegroundColor: Colors.white,
      actions: [
        IconButton(
          onPressed: _clearLogs,
          icon: const Icon(Icons.clear_all),
          tooltip: '清空日志',
        ),
      ],
      body: Column(
        children: [
          // 控制按钮
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _selectTestPhoto,
                    icon: _isLoading
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.photo_library),
                    label: Text(_isLoading ? '选择中...' : '选择测试照片'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 照片显示区域
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: _buildPhotoDisplay(),
          ),

          // 控制面板
          Padding(
            padding: const EdgeInsets.all(16),
            child: _buildControlPanel(),
          ),

          // 设备信息
          if (_showDebugInfo)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: SafeAreaHelper.createDebugInfoWidget(context),
            ),

          // 日志区域
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey),
              ),
              child: ListView.builder(
                itemCount: _debugLogs.length,
                itemBuilder: (context, index) {
                  final log = _debugLogs[index];
                  Color textColor = Colors.white;

                  if (log.contains('❌')) {
                    textColor = Colors.red;
                  } else if (log.contains('⚠️')) {
                    textColor = Colors.orange;
                  } else if (log.contains('✅')) {
                    textColor = Colors.green;
                  } else if (log.contains('🔍') || log.contains('🎨')) {
                    textColor = Colors.blue;
                  }

                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 1),
                    child: Text(
                      log,
                      style: TextStyle(
                        color: textColor,
                        fontSize: 11,
                        fontFamily: 'monospace',
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
