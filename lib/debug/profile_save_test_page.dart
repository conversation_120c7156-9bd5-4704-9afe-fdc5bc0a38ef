import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../features/profile/providers/user_profile_provider.dart';

/// 个人资料保存测试页面
class ProfileSaveTestPage extends ConsumerStatefulWidget {
  const ProfileSaveTestPage({super.key});

  @override
  ConsumerState<ProfileSaveTestPage> createState() =>
      _ProfileSaveTestPageState();
}

class _ProfileSaveTestPageState extends ConsumerState<ProfileSaveTestPage> {
  final _nicknameController = TextEditingController();
  final _bioController = TextEditingController();
  String _debugLog = '';

  @override
  void initState() {
    super.initState();
    _loadCurrentProfile();
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    _bioController.dispose();
    super.dispose();
  }

  void _addLog(String message) {
    setState(() {
      _debugLog += '${DateTime.now().toString().substring(11, 19)}: $message\n';
    });
    debugPrint('ProfileSaveTest: $message');
  }

  /// 加载当前资料
  void _loadCurrentProfile() {
    final profile = ref.read(currentUserProfileProvider);
    if (profile != null) {
      _nicknameController.text = profile.nickname;
      _bioController.text = profile.bio ?? '';
      _addLog('当前资料加载完成');
      _addLog('昵称: ${profile.nickname}');
      _addLog('简介: ${profile.bio ?? '(空)'}');
    } else {
      _addLog('当前资料为空');
    }
  }

  /// 测试昵称保存
  Future<void> _testNicknameSave() async {
    final nickname = _nicknameController.text.trim();
    _addLog('测试昵称保存: $nickname');

    if (nickname.isEmpty) {
      _addLog('❌ 昵称不能为空');
      return;
    }

    try {
      final success = await ref
          .read(userProfileProvider.notifier)
          .updateNickname(nickname);

      if (success) {
        _addLog('✅ 昵称保存成功');

        // 验证保存结果
        final updatedProfile = ref.read(currentUserProfileProvider);
        if (updatedProfile?.nickname == nickname) {
          _addLog('✅ 昵称验证成功: ${updatedProfile!.nickname}');
        } else {
          _addLog('❌ 昵称验证失败: 期望 $nickname, 实际 ${updatedProfile?.nickname}');
        }
      } else {
        _addLog('❌ 昵称保存失败');

        // 检查错误信息
        final error = ref.read(userProfileProvider).error;
        if (error != null) {
          _addLog('错误信息: $error');
        }
      }
    } catch (e) {
      _addLog('❌ 昵称保存异常: $e');
    }
  }

  /// 测试个人简介保存
  Future<void> _testBioSave() async {
    final bio = _bioController.text.trim();
    final finalBio = bio.isEmpty ? null : bio;
    _addLog('测试个人简介保存: ${finalBio ?? '(空)'}');

    try {
      final success = await ref
          .read(userProfileProvider.notifier)
          .updateBio(finalBio);

      if (success) {
        _addLog('✅ 个人简介保存成功');

        // 验证保存结果
        final updatedProfile = ref.read(currentUserProfileProvider);
        if (updatedProfile?.bio == finalBio) {
          _addLog('✅ 个人简介验证成功: ${updatedProfile!.bio ?? '(空)'}');
        } else {
          _addLog(
            '❌ 个人简介验证失败: 期望 ${finalBio ?? '(空)'}, 实际 ${updatedProfile?.bio ?? '(空)'}',
          );
        }
      } else {
        _addLog('❌ 个人简介保存失败');

        // 检查错误信息
        final error = ref.read(userProfileProvider).error;
        if (error != null) {
          _addLog('错误信息: $error');
        }
      }
    } catch (e) {
      _addLog('❌ 个人简介保存异常: $e');
    }
  }

  /// 测试同时保存
  Future<void> _testBothSave() async {
    final nickname = _nicknameController.text.trim();
    final bio = _bioController.text.trim();
    final finalBio = bio.isEmpty ? null : bio;

    _addLog('测试同时保存昵称和个人简介');
    _addLog('昵称: $nickname');
    _addLog('简介: ${finalBio ?? '(空)'}');

    if (nickname.isEmpty) {
      _addLog('❌ 昵称不能为空');
      return;
    }

    try {
      // 先保存昵称
      final nicknameSuccess = await ref
          .read(userProfileProvider.notifier)
          .updateNickname(nickname);

      // 再保存个人简介
      final bioSuccess = await ref
          .read(userProfileProvider.notifier)
          .updateBio(finalBio);

      if (nicknameSuccess && bioSuccess) {
        _addLog('✅ 同时保存成功');

        // 验证保存结果
        final updatedProfile = ref.read(currentUserProfileProvider);
        if (updatedProfile?.nickname == nickname &&
            updatedProfile?.bio == finalBio) {
          _addLog('✅ 验证成功');
          _addLog('昵称: ${updatedProfile!.nickname}');
          _addLog('简介: ${updatedProfile.bio ?? '(空)'}');
        } else {
          _addLog('❌ 验证失败');
          _addLog('期望昵称: $nickname, 实际: ${updatedProfile?.nickname}');
          _addLog(
            '期望简介: ${finalBio ?? '(空)'}, 实际: ${updatedProfile?.bio ?? '(空)'}',
          );
        }
      } else {
        _addLog('❌ 保存失败 - 昵称: $nicknameSuccess, 简介: $bioSuccess');
      }
    } catch (e) {
      _addLog('❌ 同时保存异常: $e');
    }
  }

  /// 检查当前状态
  void _checkCurrentState() async {
    _addLog('检查当前状态...');

    // 检查Provider状态
    final profileState = ref.read(userProfileProvider);
    _addLog('Provider状态:');
    _addLog('- 是否加载中: ${profileState.isLoading}');
    _addLog('- 是否更新中: ${profileState.isUpdating}');
    _addLog('- 错误信息: ${profileState.error ?? '无'}');

    // 检查当前资料
    final profile = ref.read(currentUserProfileProvider);
    if (profile != null) {
      _addLog('当前资料:');
      _addLog('- 用户ID: ${profile.userId}');
      _addLog('- 昵称: ${profile.nickname}');
      _addLog('- 简介: ${profile.bio ?? '(空)'}');
      _addLog('- 头像路径: ${profile.avatarPath ?? '(空)'}');
      _addLog('- 创建时间: ${profile.createdAt}');
      _addLog('- 更新时间: ${profile.updatedAt}');
    } else {
      _addLog('当前资料为空');
    }

    // 检查SharedPreferences
    try {
      final service = ref.read(userProfileServiceProvider);
      final savedProfile = await service.getUserProfile();
      _addLog('SharedPreferences中的资料:');
      _addLog('- 昵称: ${savedProfile.nickname}');
      _addLog('- 简介: ${savedProfile.bio ?? '(空)'}');
    } catch (e) {
      _addLog('读取SharedPreferences失败: $e');
    }
  }

  /// 清除日志
  void _clearLog() {
    setState(() {
      _debugLog = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('个人资料保存测试'),
        backgroundColor: const Color(0xFF2F76DA),
        foregroundColor: Colors.white,
        actions: [
          IconButton(onPressed: _clearLog, icon: const Icon(Icons.clear_all)),
        ],
      ),
      body: Column(
        children: [
          // 输入区域
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '昵称:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: _nicknameController,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: '请输入昵称',
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  '个人简介:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: _bioController,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: '请输入个人简介',
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),

          // 测试按钮
          Padding(
            padding: const EdgeInsets.all(16),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _loadCurrentProfile,
                  child: const Text('加载当前资料'),
                ),
                ElevatedButton(
                  onPressed: _testNicknameSave,
                  child: const Text('测试昵称保存'),
                ),
                ElevatedButton(
                  onPressed: _testBioSave,
                  child: const Text('测试简介保存'),
                ),
                ElevatedButton(
                  onPressed: _testBothSave,
                  child: const Text('测试同时保存'),
                ),
                ElevatedButton(
                  onPressed: _checkCurrentState,
                  child: const Text('检查当前状态'),
                ),
              ],
            ),
          ),

          // 调试日志
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
              ),
              child: SingleChildScrollView(
                child: Text(
                  _debugLog.isEmpty ? '点击按钮开始测试...' : _debugLog,
                  style: const TextStyle(
                    color: Colors.green,
                    fontFamily: 'monospace',
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
