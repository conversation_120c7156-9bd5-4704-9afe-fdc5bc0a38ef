import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';

/// 专门的图片裁剪测试页面
class ImageCropperTestPage extends StatefulWidget {
  const ImageCropperTestPage({super.key});

  @override
  State<ImageCropperTestPage> createState() => _ImageCropperTestPageState();
}

class _ImageCropperTestPageState extends State<ImageCropperTestPage> {
  File? _selectedImage;
  String _debugLog = '';

  void _addLog(String message) {
    setState(() {
      _debugLog += '${DateTime.now().toString().substring(11, 19)}: $message\n';
    });
    debugPrint('ImageCropperTest: $message');
  }

  /// 测试插件是否正确注册
  Future<void> _testPluginRegistration() async {
    _addLog('测试image_cropper插件注册状态...');
    
    try {
      // 尝试创建ImageCropper实例
      final cropper = ImageCropper();
      _addLog('ImageCropper实例创建成功: ${cropper.runtimeType}');
      
      // 测试是否可以调用方法（使用无效路径，但应该能到达原生代码）
      try {
        await cropper.cropImage(
          sourcePath: '/invalid/path/test.jpg',
          uiSettings: [
            AndroidUiSettings(
              toolbarTitle: '测试',
              lockAspectRatio: true,
            ),
            IOSUiSettings(
              title: '测试',
            ),
          ],
        );
      } catch (e) {
        if (e.toString().contains('MissingPluginException')) {
          _addLog('❌ 插件未正确注册: $e');
        } else {
          _addLog('✅ 插件已注册（收到预期的文件错误）: ${e.runtimeType}');
        }
      }
    } catch (e) {
      _addLog('❌ 插件创建失败: $e');
    }
  }

  /// 选择图片进行测试
  Future<void> _pickImageForTest() async {
    _addLog('选择图片进行裁剪测试...');
    
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
      );
      
      if (pickedFile != null) {
        _addLog('图片选择成功: ${pickedFile.path}');
        setState(() {
          _selectedImage = File(pickedFile.path);
        });
        
        // 自动开始裁剪测试
        await _testImageCropping();
      } else {
        _addLog('用户取消了图片选择');
      }
    } catch (e) {
      _addLog('图片选择失败: $e');
    }
  }

  /// 测试图片裁剪功能
  Future<void> _testImageCropping() async {
    if (_selectedImage == null) {
      _addLog('请先选择一张图片');
      return;
    }
    
    _addLog('开始测试图片裁剪...');
    _addLog('源文件: ${_selectedImage!.path}');
    _addLog('文件大小: ${await _selectedImage!.length()} bytes');
    
    try {
      final croppedFile = await ImageCropper().cropImage(
        sourcePath: _selectedImage!.path,
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 85,
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: '测试裁剪',
            toolbarColor: const Color(0xFF2F76DA),
            toolbarWidgetColor: Colors.white,
            backgroundColor: Colors.white,
            activeControlsWidgetColor: const Color(0xFF2F76DA),
            initAspectRatio: CropAspectRatioPreset.square,
            lockAspectRatio: true,
            aspectRatioPresets: [CropAspectRatioPreset.square],
            hideBottomControls: false,
          ),
          IOSUiSettings(
            title: '测试裁剪',
            doneButtonTitle: '完成',
            cancelButtonTitle: '取消',
            aspectRatioLockEnabled: true,
            resetAspectRatioEnabled: false,
            aspectRatioPickerButtonHidden: true,
            resetButtonHidden: true,
            rotateButtonsHidden: false,
            aspectRatioPresets: [CropAspectRatioPreset.square],
          ),
        ],
      );
      
      if (croppedFile != null) {
        _addLog('✅ 裁剪成功!');
        _addLog('裁剪后文件: ${croppedFile.path}');
        
        final croppedImageFile = File(croppedFile.path);
        if (await croppedImageFile.exists()) {
          _addLog('裁剪后文件大小: ${await croppedImageFile.length()} bytes');
          setState(() {
            _selectedImage = croppedImageFile;
          });
        } else {
          _addLog('❌ 裁剪后文件不存在');
        }
      } else {
        _addLog('用户取消了裁剪');
      }
    } catch (e) {
      _addLog('❌ 裁剪失败: $e');
      _addLog('错误类型: ${e.runtimeType}');
      
      if (e.toString().contains('MissingPluginException')) {
        _addLog('🔧 这是插件注册问题，需要重新构建应用');
      }
    }
  }

  /// 清除日志和图片
  void _clearAll() {
    setState(() {
      _debugLog = '';
      _selectedImage = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('图片裁剪测试'),
        backgroundColor: const Color(0xFF2F76DA),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _clearAll,
            icon: const Icon(Icons.clear_all),
          ),
        ],
      ),
      body: Column(
        children: [
          // 选中的图片显示
          if (_selectedImage != null)
            Container(
              height: 200,
              width: double.infinity,
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Image.file(
                _selectedImage!,
                fit: BoxFit.cover,
              ),
            ),
          
          // 测试按钮
          Padding(
            padding: const EdgeInsets.all(16),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _testPluginRegistration,
                  icon: const Icon(Icons.check_circle),
                  label: const Text('测试插件注册'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _pickImageForTest,
                  icon: const Icon(Icons.photo_library),
                  label: const Text('选择图片测试'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
                if (_selectedImage != null)
                  ElevatedButton.icon(
                    onPressed: _testImageCropping,
                    icon: const Icon(Icons.crop),
                    label: const Text('测试裁剪'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
              ],
            ),
          ),
          
          // 调试日志
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
              ),
              child: SingleChildScrollView(
                child: Text(
                  _debugLog.isEmpty ? '点击按钮开始测试...' : _debugLog,
                  style: const TextStyle(
                    color: Colors.green,
                    fontFamily: 'monospace',
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
