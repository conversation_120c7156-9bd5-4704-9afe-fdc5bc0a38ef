import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

/// 照片选择功能验证器
/// 用于自动化测试照片选择功能的各种场景
class PhotoSelectionValidator {
  static final ImagePicker _picker = ImagePicker();

  /// 验证基础图片选择功能
  static Future<ValidationResult> validateBasicImagePicker() async {
    try {
      print('🧪 开始验证基础图片选择功能...');

      // 测试单张图片选择
      final XFile? singleImage = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (singleImage != null) {
        print('✅ 单张图片选择成功: ${singleImage.path}');
        return ValidationResult.success('基础图片选择功能正常');
      } else {
        print('⚠️ 用户取消了图片选择');
        return ValidationResult.warning('用户取消了图片选择');
      }
    } catch (e) {
      print('❌ 基础图片选择失败: $e');
      return ValidationResult.error('基础图片选择失败: $e');
    }
  }

  /// 验证多图片选择功能
  static Future<ValidationResult> validateMultiImagePicker() async {
    try {
      print('🧪 开始验证多图片选择功能...');

      final List<XFile> images = await _picker.pickMultiImage(imageQuality: 80);

      if (images.isNotEmpty) {
        print('✅ 多图片选择成功: ${images.length} 张图片');
        return ValidationResult.success('多图片选择功能正常，选择了${images.length}张图片');
      } else {
        print('⚠️ 用户取消了多图片选择');
        return ValidationResult.warning('用户取消了多图片选择');
      }
    } catch (e) {
      print('❌ 多图片选择失败: $e');
      return ValidationResult.error('多图片选择失败: $e');
    }
  }

  /// 验证权限状态
  static Future<ValidationResult> validatePermissions() async {
    try {
      print('🧪 开始验证权限状态...');

      // 检查photos权限
      final photosStatus = await Permission.photos.status;
      print('📸 photos权限状态: $photosStatus');

      // 检查storage权限（Android）
      if (Platform.isAndroid) {
        final storageStatus = await Permission.storage.status;
        print('📸 storage权限状态: $storageStatus');
      }

      // 检查相机权限
      final cameraStatus = await Permission.camera.status;
      print('📸 camera权限状态: $cameraStatus');

      if (photosStatus.isGranted ||
          (Platform.isAndroid && await Permission.storage.status.isGranted)) {
        return ValidationResult.success('相册权限已授予');
      } else if (photosStatus.isDenied) {
        return ValidationResult.warning('相册权限被拒绝，需要用户授权');
      } else if (photosStatus.isPermanentlyDenied) {
        return ValidationResult.error('相册权限被永久拒绝，需要在设置中开启');
      } else {
        return ValidationResult.warning('权限状态未确定: $photosStatus');
      }
    } catch (e) {
      print('❌ 权限检查失败: $e');
      return ValidationResult.error('权限检查失败: $e');
    }
  }

  /// 验证相机功能
  static Future<ValidationResult> validateCamera() async {
    try {
      print('🧪 开始验证相机功能...');

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (image != null) {
        print('✅ 相机拍照成功: ${image.path}');
        return ValidationResult.success('相机功能正常');
      } else {
        print('⚠️ 用户取消了相机拍照');
        return ValidationResult.warning('用户取消了相机拍照');
      }
    } catch (e) {
      print('❌ 相机功能失败: $e');
      return ValidationResult.error('相机功能失败: $e');
    }
  }

  /// 运行完整的验证套件
  static Future<List<ValidationResult>> runFullValidation() async {
    print('🧪 开始运行完整的照片选择功能验证...');

    final results = <ValidationResult>[];

    // 1. 验证权限
    results.add(await validatePermissions());

    // 2. 验证基础图片选择
    results.add(await validateBasicImagePicker());

    // 3. 验证多图片选择
    results.add(await validateMultiImagePicker());

    // 4. 验证相机功能
    results.add(await validateCamera());

    print('🧪 验证完成，共${results.length}项测试');
    return results;
  }
}

/// 验证结果
class ValidationResult {
  final ValidationStatus status;
  final String message;
  final DateTime timestamp;

  ValidationResult._(this.status, this.message) : timestamp = DateTime.now();

  factory ValidationResult.success(String message) =>
      ValidationResult._(ValidationStatus.success, message);

  factory ValidationResult.warning(String message) =>
      ValidationResult._(ValidationStatus.warning, message);

  factory ValidationResult.error(String message) =>
      ValidationResult._(ValidationStatus.error, message);

  @override
  String toString() {
    final statusIcon = switch (status) {
      ValidationStatus.success => '✅',
      ValidationStatus.warning => '⚠️',
      ValidationStatus.error => '❌',
    };
    return '$statusIcon $message';
  }
}

/// 验证状态
enum ValidationStatus { success, warning, error }

/// 验证结果显示组件
class ValidationResultWidget extends StatelessWidget {
  final ValidationResult result;

  const ValidationResultWidget({super.key, required this.result});

  @override
  Widget build(BuildContext context) {
    final color = switch (result.status) {
      ValidationStatus.success => Colors.green,
      ValidationStatus.warning => Colors.orange,
      ValidationStatus.error => Colors.red,
    };

    final icon = switch (result.status) {
      ValidationStatus.success => Icons.check_circle,
      ValidationStatus.warning => Icons.warning,
      ValidationStatus.error => Icons.error,
    };

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              result.message,
              style: TextStyle(
                color: color.withValues(alpha: 0.8),
                fontSize: 14,
              ),
            ),
          ),
          Text(
            '${result.timestamp.hour.toString().padLeft(2, '0')}:${result.timestamp.minute.toString().padLeft(2, '0')}',
            style: TextStyle(color: color.withValues(alpha: 0.6), fontSize: 12),
          ),
        ],
      ),
    );
  }
}
