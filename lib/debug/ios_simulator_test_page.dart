import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

import '../utils/ios_simulator_detector.dart';
import '../utils/image_rendering_fix.dart';
import 'ios_photo_selection_debugger.dart';
import 'photo_rendering_debugger.dart';
import 'simulator_reset_helper.dart';
import 'photo_selection_advanced_diagnostics.dart';

/// iOS模拟器照片选择测试页面
class IOSSimulatorTestPage extends StatefulWidget {
  const IOSSimulatorTestPage({super.key});

  @override
  State<IOSSimulatorTestPage> createState() => _IOSSimulatorTestPageState();
}

class _IOSSimulatorTestPageState extends State<IOSSimulatorTestPage> {
  final ImagePicker _imagePicker = ImagePicker();
  final List<String> _testLogs = [];
  final List<String> _selectedImages = [];

  bool _isLoading = false;
  Map<String, dynamic>? _deviceInfo;
  bool? _isSimulator;
  Map<String, dynamic>? _permissionStrategy;

  @override
  void initState() {
    super.initState();
    _initializeTest();
  }

  /// 初始化测试环境
  Future<void> _initializeTest() async {
    _addLog('🔍 开始初始化iOS模拟器测试环境...');

    try {
      // 检测设备类型
      _isSimulator = await IOSSimulatorDetector.isSimulator();
      _addLog('📱 设备类型检测: ${_isSimulator! ? "iOS模拟器" : "真机/其他"}');

      // 获取设备信息
      _deviceInfo = await IOSSimulatorDetector.getDebugInfo();
      _addLog('📋 设备信息获取完成');

      // 获取权限策略
      _permissionStrategy =
          await IOSSimulatorDetector.getSimulatorPermissionStrategy();
      _addLog('🔐 权限策略: ${_permissionStrategy.toString()}');

      // 检查照片权限状态
      await _checkPermissionStatus();

      setState(() {});
      _addLog('✅ 测试环境初始化完成');
    } catch (e) {
      _addLog('❌ 初始化失败: $e');
    }
  }

  /// 检查权限状态
  Future<void> _checkPermissionStatus() async {
    try {
      final status = await Permission.photos.status;
      _addLog('📸 当前照片权限状态: $status');

      if (Platform.isAndroid) {
        final storageStatus = await Permission.storage.status;
        _addLog('💾 Android存储权限状态: $storageStatus');
      }
    } catch (e) {
      _addLog('❌ 权限状态检查失败: $e');
    }
  }

  /// 添加测试日志
  void _addLog(String message) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    setState(() {
      _testLogs.add('[$timestamp] $message');
    });
    print(message);
  }

  /// 测试照片选择（使用优化的模拟器流程）
  Future<void> _testOptimizedPhotoSelection() async {
    _addLog('🧪 开始测试优化的照片选择流程...');

    setState(() {
      _isLoading = true;
    });

    try {
      // 使用模拟器感知的权限检查
      final hasPermission = await _checkPhotoPermissionOptimized();
      _addLog('🔐 权限检查结果: ${hasPermission ? "通过" : "失败"}');

      if (!hasPermission && !_isSimulator!) {
        _addLog('❌ 权限被拒绝，无法继续测试');
        return;
      }

      // 尝试选择照片
      _addLog('📸 开始调用image_picker...');
      final List<XFile> images = await _imagePicker.pickMultiImage(
        imageQuality: 80,
      );

      if (images.isNotEmpty) {
        _addLog('✅ 成功选择 ${images.length} 张照片');
        setState(() {
          _selectedImages.clear();
          _selectedImages.addAll(images.map((img) => img.path));
        });

        // 显示照片信息
        for (int i = 0; i < images.length; i++) {
          final image = images[i];
          final file = File(image.path);
          final size = await file.length();
          _addLog(
            '📷 照片 ${i + 1}: ${image.name} (${(size / 1024).toStringAsFixed(1)} KB)',
          );
        }
      } else {
        _addLog('⚠️ 用户取消了照片选择或相册为空');

        if (_isSimulator!) {
          _addLog('💡 模拟器提示: 请确保相册中有照片');
          _showSimulatorGuide();
        }
      }
    } catch (e) {
      _addLog('❌ 照片选择失败: $e');

      if (_isSimulator!) {
        final errorMessage = IOSSimulatorDetector.handleSimulatorError(e);
        _addLog('🔧 模拟器错误处理: $errorMessage');
        _showSimulatorGuide();
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 优化的权限检查（模拟器感知）
  Future<bool> _checkPhotoPermissionOptimized() async {
    try {
      if (_isSimulator! && _permissionStrategy!['allowDirectPicker'] == true) {
        _addLog('🔧 模拟器模式: 使用宽松的权限策略');

        try {
          final status = await Permission.photos.status;
          _addLog('📸 模拟器权限状态: $status');

          if (status.isDenied) {
            final result = await Permission.photos.request();
            _addLog('📸 权限请求结果: $result');
          }
        } catch (e) {
          _addLog('⚠️ 权限检查异常，继续使用image_picker: $e');
        }

        return true; // 模拟器总是允许尝试
      }

      // 标准权限检查
      Permission permission = Permission.photos;

      if (Platform.isAndroid) {
        // Android优先使用photos，回退到storage
        var status = await permission.status;
        if (!status.isGranted) {
          permission = Permission.storage;
          status = await permission.status;
        }
      }

      final status = await permission.status;
      if (status.isGranted) {
        return true;
      } else if (status.isDenied) {
        final result = await permission.request();
        return result.isGranted;
      }

      return false;
    } catch (e) {
      _addLog('❌ 权限检查异常: $e');
      return true; // 异常时允许尝试
    }
  }

  /// 显示模拟器指导
  void _showSimulatorGuide() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('iOS模拟器照片指导'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(IOSSimulatorDetector.getSimulatorPhotoErrorMessage()),
              const SizedBox(height: 16),
              const Text(
                '详细步骤:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ...IOSSimulatorDetector.getSimulatorPhotoInstructions().map(
                (instruction) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    instruction,
                    style: const TextStyle(fontSize: 13),
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 清空测试结果
  void _clearResults() {
    setState(() {
      _testLogs.clear();
      _selectedImages.clear();
    });
  }

  /// 打开照片选择深度调试器
  void _openPhotoSelectionDebugger() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const IOSPhotoSelectionDebugger(),
      ),
    );
  }

  /// 打开照片渲染调试器
  void _openPhotoRenderingDebugger() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const PhotoRenderingDebugger()),
    );
  }

  /// 打开模拟器重置助手
  void _openSimulatorResetHelper() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const SimulatorResetHelper()),
    );
  }

  /// 打开高级诊断工具
  void _openAdvancedDiagnostics() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const PhotoSelectionAdvancedDiagnostics(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('iOS模拟器照片测试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _clearResults,
            icon: const Icon(Icons.clear_all),
            tooltip: '清空结果',
          ),
        ],
      ),
      body: Column(
        children: [
          // 设备信息卡片
          if (_deviceInfo != null) _buildDeviceInfoCard(),

          // 测试按钮
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _testOptimizedPhotoSelection,
                    icon: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.photo_library),
                    label: Text(_isLoading ? '测试中...' : '测试照片选择'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: 48,
                        child: ElevatedButton.icon(
                          onPressed: _openPhotoSelectionDebugger,
                          icon: const Icon(Icons.bug_report),
                          label: const Text('深度调试器'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: SizedBox(
                        height: 48,
                        child: ElevatedButton.icon(
                          onPressed: _openPhotoRenderingDebugger,
                          icon: const Icon(Icons.image),
                          label: const Text('渲染调试'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.purple,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: 48,
                        child: ElevatedButton.icon(
                          onPressed: _openSimulatorResetHelper,
                          icon: const Icon(Icons.refresh),
                          label: const Text('重置助手'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: SizedBox(
                        height: 48,
                        child: ElevatedButton.icon(
                          onPressed: _openAdvancedDiagnostics,
                          icon: const Icon(Icons.medical_services),
                          label: const Text('高级诊断'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // 选中的照片
          if (_selectedImages.isNotEmpty) _buildSelectedPhotos(),

          // 测试日志
          Expanded(child: _buildTestLogs()),
        ],
      ),
    );
  }

  Widget _buildDeviceInfoCard() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isSimulator! ? Icons.phone_iphone : Icons.smartphone,
                  color: _isSimulator! ? Colors.blue : Colors.green,
                ),
                const SizedBox(width: 8),
                Text(
                  '设备信息',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text('类型: ${_isSimulator! ? "iOS模拟器" : "真机/其他"}'),
            if (_deviceInfo!['simulatorModel'] != null)
              Text('型号: ${_deviceInfo!['simulatorModel']}'),
            Text('平台: ${_deviceInfo!['platform']}'),
            Text('调试模式: ${_deviceInfo!['isDebugMode']}'),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedPhotos() {
    return Container(
      height: 120,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '已选择照片 (${_selectedImages.length})',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _selectedImages.length,
              itemBuilder: (context, index) {
                return Container(
                  width: 80,
                  height: 80,
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: ImageRenderingFix.buildOptimizedImageFile(
                      File(_selectedImages[index]),
                      fit: BoxFit.cover,
                      errorWidget: const Icon(Icons.broken_image),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestLogs() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Text(
              '测试日志',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(8),
              itemCount: _testLogs.length,
              itemBuilder: (context, index) {
                final log = _testLogs[index];
                Color? textColor;
                if (log.contains('❌')) textColor = Colors.red;
                if (log.contains('✅')) textColor = Colors.green;
                if (log.contains('⚠️')) textColor = Colors.orange;
                if (log.contains('🔧')) textColor = Colors.blue;

                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Text(
                    log,
                    style: TextStyle(
                      fontSize: 12,
                      fontFamily: 'monospace',
                      color: textColor,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
