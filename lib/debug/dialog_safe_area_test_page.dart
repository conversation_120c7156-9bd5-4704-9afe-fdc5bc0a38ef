import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../utils/safe_area_helper.dart';

/// 对话框安全区域测试页面
/// 用于验证对话框在Dynamic Island和刘海屏设备上的显示效果
class DialogSafeAreaTestPage extends StatefulWidget {
  const DialogSafeAreaTestPage({super.key});

  @override
  State<DialogSafeAreaTestPage> createState() => _DialogSafeAreaTestPageState();
}

class _DialogSafeAreaTestPageState extends State<DialogSafeAreaTestPage> {
  final ImagePicker _imagePicker = ImagePicker();
  String _lastTestResult = '尚未测试';
  final List<String> _testLogs = [];

  void _addLog(String message) {
    setState(() {
      _testLogs.add('${DateTime.now().toString().substring(11, 19)}: $message');
    });
    print('🧪 对话框测试: $message');
  }

  /// 测试系统照片选择器
  Future<void> _testSystemPhotoDialog() async {
    _addLog('开始测试系统照片选择器...');

    try {
      final result = await SafeAreaHelper.showSystemDialogSafely<List<XFile>>(
        context,
        () async {
          _addLog('调用pickMultiImage...');
          return await _imagePicker.pickMultiImage(imageQuality: 80);
        },
      );

      if (result != null && result.isNotEmpty) {
        _addLog('✅ 系统照片选择器测试成功: ${result.length} 张图片');
        setState(() {
          _lastTestResult = '✅ 成功选择 ${result.length} 张图片';
        });
      } else {
        _addLog('⚠️ 用户取消或未选择图片');
        setState(() {
          _lastTestResult = '⚠️ 用户取消选择';
        });
      }
    } catch (e) {
      _addLog('❌ 系统照片选择器测试失败: $e');
      setState(() {
        _lastTestResult = '❌ 测试失败: $e';
      });
    }
  }

  /// 测试自定义对话框
  Future<void> _testCustomDialog() async {
    _addLog('开始测试自定义对话框...');

    final result = await showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (context) => _TestCustomDialog(),
    );

    if (result != null) {
      _addLog('✅ 自定义对话框测试成功: $result');
      setState(() {
        _lastTestResult = '✅ 自定义对话框: $result';
      });
    } else {
      _addLog('⚠️ 自定义对话框被取消');
      setState(() {
        _lastTestResult = '⚠️ 自定义对话框被取消';
      });
    }
  }

  /// 显示设备信息
  void _showDeviceInfo() {
    final deviceInfo = SafeAreaHelper.getDeviceInfo(context);
    final layoutAdvice = SafeAreaHelper.getPhotoSelectionLayoutAdvice(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('设备信息'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('设备类型: ${deviceInfo['deviceType']}'),
              Text('屏幕尺寸: ${deviceInfo['screenSize']}'),
              Text('顶部安全区: ${deviceInfo['topSafeArea']}pt'),
              Text('底部安全区: ${deviceInfo['bottomSafeArea']}pt'),
              Text('有刘海屏: ${deviceInfo['hasNotch']}'),
              Text('有动态岛: ${deviceInfo['hasDynamicIsland']}'),
              const SizedBox(height: 16),
              const Text(
                '布局建议:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                '推荐顶部边距: ${layoutAdvice['layoutSuggestions']['recommendedTopMargin']}pt',
              ),
              Text(
                '推荐底部边距: ${layoutAdvice['layoutSuggestions']['recommendedBottomMargin']}pt',
              ),
              Text(
                '最大对话框高度: ${layoutAdvice['layoutSuggestions']['maxDialogHeight']}pt',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _clearLogs() {
    setState(() {
      _testLogs.clear();
      _lastTestResult = '尚未测试';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('对话框安全区域测试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _clearLogs,
            icon: const Icon(Icons.clear_all),
            tooltip: '清空日志',
          ),
        ],
      ),
      body: Column(
        children: [
          // 设备信息显示
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '最后测试结果',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                Text(_lastTestResult),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _showDeviceInfo,
                  child: const Text('查看设备信息'),
                ),
              ],
            ),
          ),

          // 测试按钮
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _testSystemPhotoDialog,
                    icon: const Icon(Icons.photo_library),
                    label: const Text('测试系统照片选择器'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _testCustomDialog,
                    icon: const Icon(Icons.chat_bubble_outline),
                    label: const Text('测试自定义对话框'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 测试日志
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '测试日志',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: ListView.builder(
                      itemCount: _testLogs.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Text(
                            _testLogs[index],
                            style: const TextStyle(
                              fontSize: 12,
                              fontFamily: 'monospace',
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 测试用的自定义对话框
class _TestCustomDialog extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // 使用与主应用相同的SafeArea处理
    final safeInsets = SafeAreaHelper.getDialogSafeInsets(context);
    final screenSize = MediaQuery.of(context).size;
    final maxDialogHeight =
        screenSize.height - safeInsets.top - safeInsets.bottom - 40;

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.only(
        top: safeInsets.top,
        bottom: safeInsets.bottom,
        left: safeInsets.left,
        right: safeInsets.right,
      ),
      alignment: Alignment.topCenter,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: 400,
          maxHeight: maxDialogHeight.clamp(300, 500),
          minHeight: 300,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE3E2E0), width: 1),
                ),
              ),
              child: Row(
                children: [
                  const Expanded(
                    child: Text(
                      '自定义对话框测试',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF37352F),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Color(0xFF9B9A97)),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),

            // 内容区域
            Flexible(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      '对话框显示正常！',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '如果您能看到这个对话框且没有被系统UI遮挡，说明SafeArea修复生效了。',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 24),
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.of(context).pop('取消'),
                            child: const Text('取消'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () => Navigator.of(context).pop('确认'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF2F76DA),
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('确认'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
