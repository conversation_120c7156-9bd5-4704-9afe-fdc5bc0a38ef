import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// 路由调试页面
/// 用于测试和验证路由配置是否正确
class RouteDebugPage extends StatelessWidget {
  const RouteDebugPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('路由调试'),
        backgroundColor: const Color(0xFF2E7EED),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 当前路由信息
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFF7F6F3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFFE3E2DE)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '📍 当前路由信息',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '路径: ${GoRouterState.of(context).uri.path}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF787774),
                    ),
                  ),
                  Text(
                    '名称: ${GoRouterState.of(context).name ?? "未命名"}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF787774),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            const Text(
              '🧪 路由测试',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 12),

            Expanded(
              child: ListView(
                children: [
                  _buildRouteTestCard(
                    context,
                    '背包页面',
                    '/inventory',
                    '测试背包页面是否正确隐藏底部导航栏',
                    Icons.inventory,
                    const Color(0xFF2E7EED),
                    () => _testInventoryRoute(context),
                  ),
                  const SizedBox(height: 12),
                  _buildRouteTestCard(
                    context,
                    '编辑文章页面',
                    '/community-post-editor',
                    '测试编辑文章页面是否正确隐藏底部导航栏',
                    Icons.edit,
                    const Color(0xFF0F7B6C),
                    () => _testCommunityEditorRoute(context),
                  ),
                  const SizedBox(height: 12),
                  _buildRouteTestCard(
                    context,
                    '工资钱包页面',
                    '/wage-wallet',
                    '测试工资钱包页面是否正确隐藏底部导航栏',
                    Icons.account_balance_wallet,
                    const Color(0xFFE03E3E),
                    () => _testWageWalletRoute(context),
                  ),
                  const SizedBox(height: 12),
                  _buildRouteTestCard(
                    context,
                    '运动会话页面',
                    '/exercise-session',
                    '测试运动会话页面是否正确隐藏底部导航栏',
                    Icons.fitness_center,
                    const Color(0xFFE03E3E),
                    () => _testExerciseSessionRoute(context),
                  ),
                  const SizedBox(height: 12),
                  _buildRouteTestCard(
                    context,
                    '动觉学习页面',
                    '/kinesthetic-learning',
                    '测试动觉学习页面是否正确隐藏底部导航栏',
                    Icons.psychology,
                    const Color(0xFF0F7B6C),
                    () => _testKinestheticLearningRoute(context),
                  ),
                  const SizedBox(height: 12),
                  _buildRouteTestCard(
                    context,
                    '自定义动作库编辑器',
                    '/custom-library-editor',
                    '测试自定义动作库编辑器是否正确隐藏底部导航栏',
                    Icons.edit,
                    const Color(0xFF7C3AED),
                    () => _testCustomLibraryEditorRoute(context),
                  ),
                  const SizedBox(height: 12),
                  _buildRouteTestCard(
                    context,
                    '商城页面',
                    '/store',
                    '测试商城页面（应该显示底部导航栏）',
                    Icons.store,
                    const Color(0xFF9B9A97),
                    () => context.go('/store'),
                  ),
                  const SizedBox(height: 12),
                  _buildRouteTestCard(
                    context,
                    '对话框安全区域测试',
                    '/dialog-safe-area-test',
                    '测试对话框在Dynamic Island和刘海屏设备上的显示效果',
                    Icons.chat_bubble_outline,
                    const Color(0xFF2E7EED),
                    () => context.go('/dialog-safe-area-test'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRouteTestCard(
    BuildContext context,
    String title,
    String route,
    String description,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2DE)),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: color,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        route,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF9B9A97),
                          fontFamily: 'monospace',
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF787774),
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Color(0xFF9B9A97),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _testInventoryRoute(BuildContext context) {
    // 模拟商城页面的数据
    final mockInventory = {
      'focus_potion': 2,
      'memory_enhancer': 1,
      'answer_accelerator': 1,
    };

    final mockItems = [
      // 这里应该是实际的 ShopItem 对象，但为了简化测试，我们传递空列表
    ];

    context.push(
      '/inventory',
      extra: {'inventory': mockInventory, 'allItems': mockItems},
    );
  }

  void _testCommunityEditorRoute(BuildContext context) {
    context.push('/community-post-editor');
  }

  void _testWageWalletRoute(BuildContext context) {
    context.push('/wage-wallet');
  }

  void _testExerciseSessionRoute(BuildContext context) {
    // 模拟运动会话数据
    context.push(
      '/exercise-session',
      extra: {
        'exercises': [], // 空的运动列表用于测试
        'mode': 'single', // 单个运动模式
        'duration': 30, // 30秒测试时长
      },
    );
  }

  void _testKinestheticLearningRoute(BuildContext context) {
    // 模拟动觉学习数据
    context.push(
      '/kinesthetic-learning',
      extra: {
        'onComplete': () {
          // 测试完成回调
          print('动觉学习测试完成');
        },
        'onSkipRest': () {
          // 测试跳过休息回调
          print('跳过休息测试');
        },
      },
    );
  }

  void _testCustomLibraryEditorRoute(BuildContext context) {
    // 模拟自定义动作库数据
    context.push(
      '/custom-library-editor',
      extra: {
        'library': null, // 空的库用于测试
        'customLibraryService': null, // 空的服务用于测试
      },
    );
  }
}
