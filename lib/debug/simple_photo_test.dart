import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

/// 最简单的照片选择测试
class SimplePhotoTest extends StatefulWidget {
  const SimplePhotoTest({super.key});

  @override
  State<SimplePhotoTest> createState() => _SimplePhotoTestState();
}

class _SimplePhotoTestState extends State<SimplePhotoTest> {
  final ImagePicker _picker = ImagePicker();
  String? _imagePath;
  String _status = '点击按钮选择照片';

  Future<void> _pickImage() async {
    setState(() {
      _status = '正在选择照片...';
    });

    try {
      print('🔍 开始选择照片...');
      print('🔍 平台: ${Platform.operatingSystem}');

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image != null) {
        print('✅ 照片选择成功: ${image.path}');
        setState(() {
          _imagePath = image.path;
          _status = '照片选择成功: ${image.name}';
        });
      } else {
        print('❌ 用户取消选择');
        setState(() {
          _status = '用户取消选择或相册为空';
        });
      }
    } catch (e) {
      print('❌ 照片选择失败: $e');
      setState(() {
        _status = '选择失败: ${e.toString()}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('简单照片测试'),
        backgroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // 状态显示
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _status,
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: 20),

            // 选择按钮
            ElevatedButton(
              onPressed: _pickImage,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
              ),
              child: const Text('选择照片', style: TextStyle(fontSize: 18)),
            ),

            const SizedBox(height: 20),

            // 图片显示
            if (_imagePath != null)
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      File(_imagePath!),
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return const Center(child: Text('图片加载失败'));
                      },
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
