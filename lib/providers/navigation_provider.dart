import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 导航状态管理
class NavigationState {
  final String currentRoute;
  final DateTime lastNavigationTime;
  final bool shouldTriggerHomeRefresh;

  const NavigationState({
    required this.currentRoute,
    required this.lastNavigationTime,
    this.shouldTriggerHomeRefresh = false,
  });

  NavigationState copyWith({
    String? currentRoute,
    DateTime? lastNavigationTime,
    bool? shouldTriggerHomeRefresh,
  }) {
    return NavigationState(
      currentRoute: currentRoute ?? this.currentRoute,
      lastNavigationTime: lastNavigationTime ?? this.lastNavigationTime,
      shouldTriggerHomeRefresh: shouldTriggerHomeRefresh ?? this.shouldTriggerHomeRefresh,
    );
  }
}

/// 导航状态通知器
class NavigationNotifier extends StateNotifier<NavigationState> {
  NavigationNotifier() : super(NavigationState(
    currentRoute: '/home',
    lastNavigationTime: DateTime.now(),
  ));

  /// 更新当前路由
  void updateRoute(String route) {
    final now = DateTime.now();
    final shouldRefresh = route == '/home' && state.currentRoute != '/home';
    
    state = state.copyWith(
      currentRoute: route,
      lastNavigationTime: now,
      shouldTriggerHomeRefresh: shouldRefresh,
    );

    print('🧭 导航状态更新: $route (触发首页刷新: $shouldRefresh)');
  }

  /// 清除刷新标志
  void clearRefreshFlag() {
    if (state.shouldTriggerHomeRefresh) {
      state = state.copyWith(shouldTriggerHomeRefresh: false);
      print('🧭 已清除首页刷新标志');
    }
  }
}

/// 导航状态Provider
final navigationProvider = StateNotifierProvider<NavigationNotifier, NavigationState>((ref) {
  return NavigationNotifier();
});

/// 首页刷新触发器Provider
final homeRefreshTriggerProvider = Provider<bool>((ref) {
  final navigationState = ref.watch(navigationProvider);
  return navigationState.shouldTriggerHomeRefresh;
});
