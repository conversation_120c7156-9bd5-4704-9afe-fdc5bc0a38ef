import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';

/// 系统级悬浮窗权限管理服务
class SystemOverlayPermission {
  static const MethodChannel _channel = MethodChannel(
    'system_overlay_permission',
  );

  // 悬浮窗关闭回调
  static VoidCallback? _onOverlayClosed;

  static void _setupMethodCallHandler() {
    _channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'onOverlayClosed':
          print('🔔 收到Android悬浮窗关闭通知');
          _onOverlayClosed?.call();
          break;
        default:
          print('未处理的方法调用: ${call.method}');
      }
    });
  }

  /// 设置悬浮窗关闭回调
  static void setOnOverlayClosedCallback(VoidCallback? callback) {
    _onOverlayClosed = callback;
    _setupMethodCallHandler();
  }

  /// 检查是否有悬浮窗权限
  static Future<bool> hasPermission() async {
    if (Platform.isAndroid) {
      try {
        final bool hasPermission = await _channel.invokeMethod('hasPermission');
        return hasPermission;
      } catch (e) {
        print('检查悬浮窗权限失败: $e');
        return false;
      }
    } else if (Platform.isIOS) {
      // iOS不需要特殊权限，但功能有限
      return true;
    }
    return false;
  }

  /// 请求悬浮窗权限
  static Future<bool> requestPermission() async {
    if (Platform.isAndroid) {
      try {
        final bool granted = await _channel.invokeMethod('requestPermission');
        return granted;
      } catch (e) {
        print('请求悬浮窗权限失败: $e');
        return false;
      }
    } else if (Platform.isIOS) {
      // iOS不需要特殊权限申请
      return true;
    }
    return false;
  }

  /// 打开系统设置页面
  static Future<void> openSettings() async {
    if (Platform.isAndroid) {
      try {
        await _channel.invokeMethod('openSettings');
      } catch (e) {
        print('打开设置页面失败: $e');
      }
    }
  }

  /// 显示Android系统级悬浮窗
  static Future<bool> showSystemOverlay({
    required String taskTitle,
    required String remainingTime,
  }) async {
    if (Platform.isAndroid) {
      try {
        final bool success = await _channel.invokeMethod('showSystemOverlay', {
          'taskTitle': taskTitle,
          'remainingTime': remainingTime,
        });
        return success;
      } catch (e) {
        print('显示系统级悬浮窗失败: $e');
        return false;
      }
    }
    return false;
  }

  /// 隐藏Android系统级悬浮窗
  static Future<bool> hideSystemOverlay() async {
    if (Platform.isAndroid) {
      try {
        final bool success = await _channel.invokeMethod('hideSystemOverlay');
        return success;
      } catch (e) {
        print('隐藏系统级悬浮窗失败: $e');
        return false;
      }
    }
    return false;
  }

  /// 更新Android系统级悬浮窗
  static Future<bool> updateSystemOverlay({
    required String taskTitle,
    required String remainingTime,
  }) async {
    if (Platform.isAndroid) {
      try {
        final bool success = await _channel.invokeMethod(
          'updateSystemOverlay',
          {'taskTitle': taskTitle, 'remainingTime': remainingTime},
        );
        return success;
      } catch (e) {
        print('更新系统级悬浮窗失败: $e');
        return false;
      }
    }
    return false;
  }

  /// 显示权限说明对话框
  static Future<bool> showPermissionDialog(BuildContext context) async {
    if (Platform.isIOS) {
      // iOS显示功能限制说明
      return await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('浮动窗口功能'),
              content: const Text(
                'iOS系统限制，浮动窗口仅在OneDay应用内有效。\n\n'
                '您可以在应用内的任何页面看到浮动计时器，'
                '但切换到其他应用时窗口会暂时隐藏。',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('取消'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('了解'),
                ),
              ],
            ),
          ) ??
          false;
    } else {
      // Android显示权限申请说明
      return await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('需要悬浮窗权限'),
              content: const Text(
                '为了在您使用其他应用时也能显示计时器，'
                'OneDay需要悬浮窗权限。\n\n'
                '这个权限允许应用在屏幕上显示小窗口，'
                '帮助您随时监控学习进度。',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('取消'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('授权'),
                ),
              ],
            ),
          ) ??
          false;
    }
  }

  /// 检查并请求权限的完整流程
  static Future<bool> checkAndRequestPermission(BuildContext context) async {
    // 首先检查是否已有权限
    bool hasPermission = await SystemOverlayPermission.hasPermission();

    if (hasPermission) {
      return true;
    }

    // 显示权限说明对话框
    if (!context.mounted) return false;
    bool userAgreed = await showPermissionDialog(context);
    if (!userAgreed) {
      return false;
    }

    // 请求权限
    if (Platform.isAndroid) {
      bool granted = await requestPermission();

      if (!granted) {
        // 如果权限被拒绝，提示用户手动开启
        if (!context.mounted) return false;
        bool openSettings =
            await showDialog<bool>(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('权限被拒绝'),
                content: const Text(
                  '请在系统设置中手动开启OneDay的悬浮窗权限。\n\n'
                  '设置路径：设置 → 应用管理 → OneDay → 权限管理 → 悬浮窗',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: const Text('稍后'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    child: const Text('去设置'),
                  ),
                ],
              ),
            ) ??
            false;

        if (openSettings) {
          await SystemOverlayPermission.openSettings();
        }

        return false;
      }

      return true;
    } else {
      // iOS直接返回true
      return true;
    }
  }
}

/// 权限状态枚举
enum PermissionStatus {
  granted, // 已授权
  denied, // 被拒绝
  restricted, // 受限制
  unknown, // 未知状态
}

/// 平台特定的权限处理
class PlatformPermissionHandler {
  /// 获取当前平台的权限状态
  static Future<PermissionStatus> getPermissionStatus() async {
    if (Platform.isAndroid) {
      bool hasPermission = await SystemOverlayPermission.hasPermission();
      return hasPermission ? PermissionStatus.granted : PermissionStatus.denied;
    } else if (Platform.isIOS) {
      // iOS总是返回granted，但功能受限
      return PermissionStatus.restricted;
    }
    return PermissionStatus.unknown;
  }

  /// 获取权限描述文本
  static String getPermissionDescription() {
    if (Platform.isAndroid) {
      return '悬浮窗权限允许OneDay在其他应用之上显示计时器窗口，'
          '让您在使用手机的任何时候都能监控学习进度。';
    } else if (Platform.isIOS) {
      return '由于iOS系统限制，浮动窗口仅在OneDay应用内有效。'
          '您可以在应用内的任何页面看到计时器，但无法在其他应用之上显示。';
    }
    return '当前平台不支持系统级浮动窗口功能。';
  }

  /// 获取平台能力描述
  static Map<String, bool> getPlatformCapabilities() {
    if (Platform.isAndroid) {
      return {
        'systemOverlay': true, // 系统级覆盖
        'crossAppDisplay': true, // 跨应用显示
        'backgroundDisplay': true, // 后台显示
        'permissionRequired': true, // 需要权限
      };
    } else if (Platform.isIOS) {
      return {
        'systemOverlay': false, // 不支持系统级覆盖
        'crossAppDisplay': false, // 不支持跨应用显示
        'backgroundDisplay': false, // 不支持后台显示
        'permissionRequired': false, // 不需要特殊权限
      };
    }
    return {
      'systemOverlay': false,
      'crossAppDisplay': false,
      'backgroundDisplay': false,
      'permissionRequired': false,
    };
  }
}
