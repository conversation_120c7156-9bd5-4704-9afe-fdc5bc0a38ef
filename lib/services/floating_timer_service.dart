import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:io';
import 'system_overlay_permission.dart';

/// 全局浮动计时器服务
/// 提供系统级浮动窗口功能，支持跨页面、跨应用显示
class FloatingTimerService with WidgetsBindingObserver {
  static final FloatingTimerService _instance =
      FloatingTimerService._internal();
  factory FloatingTimerService() => _instance;
  FloatingTimerService._internal() {
    WidgetsBinding.instance.addObserver(this);
  }

  // 全局状态
  OverlayEntry? _overlayEntry;
  bool _isVisible = false;
  Offset _position = const Offset(20, 100);

  // 计时器状态
  String? _taskTitle;
  int _remainingSeconds = 0;
  int _totalDurationSeconds = 0;
  bool _isTimerRunning = false;
  bool _isRestMode = false; // 是否为休息模式
  DateTime? _timerStartTime;
  VoidCallback? _onClose;

  // 独立计时器
  Timer? _independentTimer;

  // 平台特定状态
  bool _isAndroidSystemOverlay = false;

  // 全局Navigator Key
  static GlobalKey<NavigatorState>? _navigatorKey;

  /// 设置全局Navigator Key
  static void setNavigatorKey(GlobalKey<NavigatorState> key) {
    _navigatorKey = key;
  }

  /// 显示浮动窗口
  Future<void> showFloatingTimer({
    BuildContext? context,
    required String taskTitle,
    required int remainingSeconds,
    required int totalDurationSeconds,
    required bool isTimerRunning,
    bool isRestMode = false,
    VoidCallback? onClose,
  }) async {
    if (_isVisible) {
      hideFloatingTimer();
    }

    // 检查权限（仅在Android上需要）
    if (context != null) {
      bool hasPermission =
          await SystemOverlayPermission.checkAndRequestPermission(context);
      if (!hasPermission) {
        print('❌ 没有悬浮窗权限，无法显示系统级浮动窗口');
        return;
      }
    }

    // 设置Android悬浮窗关闭回调
    SystemOverlayPermission.setOnOverlayClosedCallback(() {
      print('🔔 Android悬浮窗被关闭，执行清理操作');
      _isVisible = false;
      _isAndroidSystemOverlay = false;
      _onClose?.call();
    });

    _taskTitle = taskTitle;
    _remainingSeconds = remainingSeconds;
    _totalDurationSeconds = totalDurationSeconds;
    _isTimerRunning = isTimerRunning;
    _isRestMode = isRestMode;
    _onClose = onClose;

    // 计算计时器开始时间
    if (isTimerRunning) {
      final elapsedSeconds = totalDurationSeconds - remainingSeconds;
      _timerStartTime = DateTime.now().subtract(
        Duration(seconds: elapsedSeconds),
      );
      _startIndependentTimer();
    }

    _createOverlayEntry();

    // 尝试获取全局Overlay
    OverlayState? overlay;

    if (_navigatorKey?.currentState?.overlay != null) {
      // 使用全局Navigator的Overlay
      overlay = _navigatorKey!.currentState!.overlay;
      print('🎯 使用全局Navigator的Overlay');
    } else if (context != null && context.mounted) {
      // 回退到使用传入的context
      overlay = Overlay.of(context, rootOverlay: true);
      print('🎯 使用传入context的rootOverlay');
    }

    // 根据平台选择显示方式
    if (Platform.isAndroid && context != null) {
      // Android使用原生系统级悬浮窗
      // 对于休息模式，Android原生悬浮窗需要分别显示标题和时间
      String displayTitle = taskTitle;
      String displayTime = _formatTime(_remainingSeconds);

      if (isRestMode) {
        // 休息模式：标题显示"休息中"，时间单独显示
        displayTitle = '休息中';
        displayTime = _formatTime(_remainingSeconds);
      }

      bool success = await SystemOverlayPermission.showSystemOverlay(
        taskTitle: displayTitle,
        remainingTime: displayTime,
      );
      if (success) {
        _isVisible = true;
        _isAndroidSystemOverlay = true;
        print('✅ Android系统级悬浮窗已显示');
      } else {
        print('❌ Android系统级悬浮窗显示失败，回退到Flutter Overlay');
        _showFlutterOverlay(overlay);
      }
    } else {
      // iOS或Android回退方案使用Flutter Overlay
      _showFlutterOverlay(overlay);
    }

    HapticFeedback.lightImpact();
  }

  /// 显示Flutter Overlay（iOS或Android回退方案）
  void _showFlutterOverlay(OverlayState? overlay) {
    if (overlay != null && _overlayEntry != null) {
      overlay.insert(_overlayEntry!);
      _isVisible = true;
      _isAndroidSystemOverlay = false;
      print('✅ Flutter浮动窗口已显示在全局Overlay');
    } else {
      print('❌ 无法获取Overlay，浮动窗口显示失败');
    }
  }

  /// 启动独立计时器
  void _startIndependentTimer() {
    _stopIndependentTimer();

    print('🚀 启动浮动窗口独立计时器');

    _independentTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isTimerRunning || _timerStartTime == null) {
        return;
      }

      final now = DateTime.now();
      final elapsedSeconds = now.difference(_timerStartTime!).inSeconds;
      final newRemainingSeconds = (_totalDurationSeconds - elapsedSeconds)
          .clamp(0, _totalDurationSeconds);

      if (newRemainingSeconds != _remainingSeconds) {
        _remainingSeconds = newRemainingSeconds;
        print('⏰ 浮动窗口计时器更新: 剩余 $_remainingSeconds 秒');

        // 更新UI
        if (_isVisible) {
          if (_isAndroidSystemOverlay) {
            // 更新Android系统级悬浮窗
            // 对于休息模式，Android原生悬浮窗需要分别显示标题和时间
            String displayTitle = _taskTitle!;
            String displayTime = _formatTime(_remainingSeconds);

            if (_isRestMode) {
              // 休息模式：标题显示"休息中"，时间单独显示
              displayTitle = '休息中';
              displayTime = _formatTime(_remainingSeconds);
            }

            SystemOverlayPermission.updateSystemOverlay(
              taskTitle: displayTitle,
              remainingTime: displayTime,
            );
          } else if (_overlayEntry != null) {
            // 更新Flutter Overlay
            _updateOverlayEntry();
          }
        }

        // 检查是否完成
        if (_remainingSeconds <= 0) {
          print('✅ 浮动窗口计时器完成');
          _completeTimer();
        }
      }
    });
  }

  /// 停止独立计时器
  void _stopIndependentTimer() {
    _independentTimer?.cancel();
    _independentTimer = null;
  }

  /// 暂停计时器
  void pauseTimer() {
    _isTimerRunning = false;
    _stopIndependentTimer();
    if (_isVisible) {
      if (_isAndroidSystemOverlay) {
        // 对于休息模式，Android原生悬浮窗需要分别显示标题和时间
        String displayTitle = _taskTitle!;
        String displayTime = _formatTime(_remainingSeconds);

        if (_isRestMode) {
          // 休息模式：标题显示"休息中"，时间单独显示
          displayTitle = '休息中';
          displayTime = _formatTime(_remainingSeconds);
        }

        SystemOverlayPermission.updateSystemOverlay(
          taskTitle: displayTitle,
          remainingTime: displayTime,
        );
      } else if (_overlayEntry != null) {
        _updateOverlayEntry();
      }
    }
    print('⏸️ 浮动窗口计时器已暂停');
  }

  /// 恢复计时器
  void resumeTimer() {
    if (_remainingSeconds > 0) {
      _isTimerRunning = true;
      // 重新计算开始时间
      final elapsedSeconds = _totalDurationSeconds - _remainingSeconds;
      _timerStartTime = DateTime.now().subtract(
        Duration(seconds: elapsedSeconds),
      );
      _startIndependentTimer();
      if (_isVisible) {
        if (_isAndroidSystemOverlay) {
          // 对于休息模式，Android原生悬浮窗需要分别显示标题和时间
          String displayTitle = _taskTitle!;
          String displayTime = _formatTime(_remainingSeconds);

          if (_isRestMode) {
            // 休息模式：标题显示"休息中"，时间单独显示
            displayTitle = '休息中';
            displayTime = _formatTime(_remainingSeconds);
          }

          SystemOverlayPermission.updateSystemOverlay(
            taskTitle: displayTitle,
            remainingTime: displayTime,
          );
        } else if (_overlayEntry != null) {
          _updateOverlayEntry();
        }
      }
      print('▶️ 浮动窗口计时器已恢复');
    }
  }

  /// 完成计时器
  void _completeTimer() {
    _isTimerRunning = false;
    _stopIndependentTimer();
    // 可以在这里添加完成提示
    HapticFeedback.heavyImpact();
  }

  /// 创建OverlayEntry
  void _createOverlayEntry() {
    _overlayEntry = OverlayEntry(
      builder: (context) => _FloatingTimerWidget(
        position: _position,
        taskTitle: _taskTitle!,
        remainingSeconds: _remainingSeconds,
        isTimerRunning: _isTimerRunning,
        isRestMode: _isRestMode,
        onPositionChanged: _updatePosition,
        onClose: _closeFloatingTimer,
      ),
    );
  }

  /// 更新OverlayEntry
  void _updateOverlayEntry() {
    if (_overlayEntry != null && _isVisible) {
      _overlayEntry!.markNeedsBuild();
    }
  }

  /// 与主计时器同步（消除时间差）
  void syncWithMasterTimer({
    required String taskTitle,
    required int remainingSeconds,
    required int totalDurationSeconds,
    required bool isTimerRunning,
    required DateTime timerStartTime,
    bool isRestMode = false,
  }) {
    if (!_isVisible) return;

    // 停止独立计时器，使用主计时器的时间
    _stopIndependentTimer();

    // 更新状态
    _taskTitle = taskTitle;
    _remainingSeconds = remainingSeconds;
    _totalDurationSeconds = totalDurationSeconds;
    _isTimerRunning = isTimerRunning;
    _isRestMode = isRestMode;
    _timerStartTime = timerStartTime;

    // 更新UI显示
    if (_isAndroidSystemOverlay) {
      // 更新Android系统级悬浮窗
      // 对于休息模式，Android原生悬浮窗需要分别显示标题和时间
      String displayTitle = taskTitle;
      String displayTime = _formatTime(remainingSeconds);

      if (isRestMode) {
        // 休息模式：标题显示"休息中"，时间单独显示
        displayTitle = '休息中';
        displayTime = _formatTime(remainingSeconds);
      }

      SystemOverlayPermission.updateSystemOverlay(
        taskTitle: displayTitle,
        remainingTime: displayTime,
      );
    } else if (_overlayEntry != null) {
      // 更新Flutter Overlay
      // Flutter overlay会根据isRestMode自动处理显示格式
      _updateOverlayEntry();
    }

    print('🔄 浮动窗口已与主计时器同步: 剩余 $remainingSeconds 秒');
  }

  /// 更新计时器状态（主要用于外部同步）
  void updateTimer({
    String? taskTitle,
    int? remainingSeconds,
    int? totalDurationSeconds,
    bool? isTimerRunning,
  }) {
    if (!_isVisible) return;

    bool needsUpdate = false;

    if (taskTitle != null && taskTitle != _taskTitle) {
      _taskTitle = taskTitle;
      needsUpdate = true;
    }

    if (totalDurationSeconds != null &&
        totalDurationSeconds != _totalDurationSeconds) {
      _totalDurationSeconds = totalDurationSeconds;
      needsUpdate = true;
    }

    // 对于计时器状态的变化，需要特殊处理
    if (isTimerRunning != null && isTimerRunning != _isTimerRunning) {
      if (isTimerRunning) {
        // 外部请求恢复计时器
        resumeTimer();
      } else {
        // 外部请求暂停计时器
        pauseTimer();
      }
      needsUpdate = true;
    }

    // 如果外部提供了剩余时间，同步一次（但不影响独立计时器）
    if (remainingSeconds != null && !_isTimerRunning) {
      _remainingSeconds = remainingSeconds;
      needsUpdate = true;
    }

    if (needsUpdate && _overlayEntry != null) {
      _updateOverlayEntry();
    }
  }

  /// 隐藏浮动窗口
  void hideFloatingTimer() {
    _stopIndependentTimer();

    if (_isAndroidSystemOverlay) {
      // 隐藏Android系统级悬浮窗
      SystemOverlayPermission.hideSystemOverlay();
      _isAndroidSystemOverlay = false;
    }

    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }

    _isVisible = false;
    print('🔒 浮动窗口已隐藏');
  }

  /// 关闭浮动窗口并执行回调
  void _closeFloatingTimer() {
    hideFloatingTimer();
    _onClose?.call();
    HapticFeedback.lightImpact();
  }

  /// 更新窗口位置
  void _updatePosition(Offset newPosition) {
    _position = newPosition;
  }

  /// 检查是否正在显示
  bool get isVisible => _isVisible;

  /// 获取当前位置
  Offset get position => _position;

  /// 应用生命周期变化处理
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
        // 应用进入后台
        print('🔄 应用进入后台，浮动窗口保持显示');
        // 在Android上，浮动窗口应该继续显示
        // 在iOS上，由于系统限制，窗口会暂时隐藏
        break;
      case AppLifecycleState.resumed:
        // 应用回到前台
        print('🔄 应用回到前台，检查浮动窗口状态');
        // 如果窗口应该显示但当前不可见，重新显示
        if (_taskTitle != null && !_isVisible) {
          _restoreFloatingWindow();
        }
        break;
      case AppLifecycleState.detached:
        // 应用被销毁
        print('🔄 应用被销毁，清理浮动窗口');
        hideFloatingTimer();
        break;
      default:
        break;
    }
  }

  /// 恢复浮动窗口显示
  Future<void> _restoreFloatingWindow() async {
    if (_taskTitle == null) return;

    await showFloatingTimer(
      taskTitle: _taskTitle!,
      remainingSeconds: _remainingSeconds,
      totalDurationSeconds: _totalDurationSeconds,
      isTimerRunning: _isTimerRunning,
      onClose: _onClose,
    );
  }

  /// 清理资源
  void dispose() {
    _stopIndependentTimer();
    WidgetsBinding.instance.removeObserver(this);
    hideFloatingTimer();
  }

  /// 格式化时间显示
  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final secs = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }
}

/// 浮动计时器窗口组件
class _FloatingTimerWidget extends StatefulWidget {
  final Offset position;
  final String taskTitle;
  final int remainingSeconds;
  final bool isTimerRunning;
  final bool isRestMode;
  final Function(Offset) onPositionChanged;
  final VoidCallback onClose;

  const _FloatingTimerWidget({
    required this.position,
    required this.taskTitle,
    required this.remainingSeconds,
    required this.isTimerRunning,
    this.isRestMode = false,
    required this.onPositionChanged,
    required this.onClose,
  });

  @override
  State<_FloatingTimerWidget> createState() => _FloatingTimerWidgetState();
}

class _FloatingTimerWidgetState extends State<_FloatingTimerWidget> {
  late Offset _currentPosition;

  @override
  void initState() {
    super.initState();
    _currentPosition = widget.position;
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final secs = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _currentPosition.dx,
      top: _currentPosition.dy,
      child: GestureDetector(
        onPanUpdate: (details) {
          final screenSize = MediaQuery.of(context).size;
          final windowWidth = 160.0;
          final windowHeight = 60.0;

          // 计算新位置，确保窗口不超出屏幕边界
          double newX = (_currentPosition.dx + details.delta.dx).clamp(
            0,
            screenSize.width - windowWidth,
          );
          double newY = (_currentPosition.dy + details.delta.dy).clamp(
            0,
            screenSize.height - windowHeight - 100,
          );

          setState(() {
            _currentPosition = Offset(newX, newY);
          });
          widget.onPositionChanged(_currentPosition);
        },
        child: Material(
          elevation: 8, // 提高阴影以确保在其他内容之上
          borderRadius: BorderRadius.circular(8),
          color: Colors.white.withValues(alpha: 0.90), // 稍微降低透明度以提高可见性
          child: Container(
            width: 160,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFF2E7EED).withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(6),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 第一行：任务标题居中和关闭按钮
                  Stack(
                    children: [
                      // 居中的标题
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Text(
                            widget.taskTitle,
                            style: const TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF37352F),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      // 右上角关闭按钮
                      Positioned(
                        right: 0,
                        top: 0,
                        child: GestureDetector(
                          onTap: widget.onClose,
                          child: Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade400,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              size: 8,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),

                  // 第二行：剩余时间（工作模式和休息模式都显示）
                  const SizedBox(height: 4),

                  // 第二行：剩余时间
                  Center(
                    child: Text(
                      _formatTime(widget.remainingSeconds),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        color: Color(0xFF37352F),
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
