import 'package:flutter/foundation.dart';
import '../features/time_box/models/timebox_models.dart';
import '../features/study_time/services/study_time_statistics_service.dart';
import '../features/achievement/services/achievement_trigger_service.dart';
import '../features/daily_plan/notifiers/daily_plan_notifier.dart';
import '../features/daily_plan/models/daily_plan.dart';

/// 学习会话完成处理服务
///
/// 负责在时间盒子学习会话结束时，统一处理所有相关数据的更新，
/// 确保数据的一致性和完整性
class StudySessionCompletionService {
  final StudyTimeStatisticsService _studyTimeService;
  final AchievementTriggerService _achievementService;
  final DailyPlanNotifier _dailyPlanNotifier;

  StudySessionCompletionService({
    required StudyTimeStatisticsService studyTimeService,
    required AchievementTriggerService achievementService,
    required DailyPlanNotifier dailyPlanNotifier,
  }) : _studyTimeService = studyTimeService,
       _achievementService = achievementService,
       _dailyPlanNotifier = dailyPlanNotifier;

  /// 处理学习会话完成
  ///
  /// 这是一个原子性操作，确保所有相关数据同步更新
  Future<StudySessionCompletionResult> handleSessionCompletion(
    TimeBoxTask completedTask,
  ) async {
    debugPrint('🎯 开始处理学习会话完成: ${completedTask.title}');

    final result = StudySessionCompletionResult();

    try {
      // 1. 验证任务状态
      if (!completedTask.isCompleted || completedTask.startTime == null) {
        throw Exception('任务状态无效：任务未完成或缺少开始时间');
      }

      // 2. 更新学习时间统计数据
      await _updateStudyTimeStatistics(completedTask, result);

      // 3. 触发成就系统更新
      await _updateAchievementSystem(completedTask, result);

      // 4. 更新每日计划数据
      await _updateDailyPlanData(completedTask, result);

      // 5. 数据持久化（各服务内部已处理）
      debugPrint('💾 所有数据已持久化到本地存储');

      result.isSuccess = true;
      result.message = '学习会话完成处理成功';

      debugPrint('✅ 学习会话完成处理成功: ${completedTask.title}');
    } catch (e, stackTrace) {
      result.isSuccess = false;
      result.error = e.toString();
      result.message = '学习会话完成处理失败: $e';

      debugPrint('❌ 学习会话完成处理失败: $e');
      debugPrint('Stack trace: $stackTrace');
    }

    return result;
  }

  /// 更新学习时间统计数据
  Future<void> _updateStudyTimeStatistics(
    TimeBoxTask task,
    StudySessionCompletionResult result,
  ) async {
    try {
      debugPrint('📊 更新学习时间统计数据...');

      // 调用学习时间统计服务的任务完成处理
      await _studyTimeService.onTaskCompleted(task);

      result.studyTimeUpdated = true;
      result.studyMinutes = task.plannedMinutes;

      debugPrint('✅ 学习时间统计数据更新完成');
    } catch (e) {
      debugPrint('❌ 更新学习时间统计失败: $e');
      result.studyTimeError = e.toString();
      rethrow;
    }
  }

  /// 更新成就系统数据
  Future<void> _updateAchievementSystem(
    TimeBoxTask task,
    StudySessionCompletionResult result,
  ) async {
    try {
      debugPrint('🏆 更新成就系统数据...');

      // 触发学习会话结束成就检测
      await _achievementService.onStudySessionEnd(task.plannedMinutes);

      result.achievementsUpdated = true;
      result.experienceGained = task.plannedMinutes; // 基础经验值

      debugPrint('✅ 成就系统数据更新完成');
    } catch (e) {
      debugPrint('❌ 更新成就系统失败: $e');
      result.achievementError = e.toString();
      rethrow;
    }
  }

  /// 更新每日计划数据
  Future<void> _updateDailyPlanData(
    TimeBoxTask task,
    StudySessionCompletionResult result,
  ) async {
    try {
      debugPrint('📋 更新每日计划数据...');

      // 获取今日计划
      final today = DateTime.now();
      final todayPlan = _dailyPlanNotifier.getPlan(
        today,
        DailyPlanType.planning,
      );

      if (todayPlan != null && !todayPlan.isCompleted) {
        // 检查是否应该标记计划为完成
        // 这里可以根据具体业务逻辑来判断
        // 例如：完成一定数量的学习任务后标记计划完成

        final shouldMarkCompleted = await _shouldMarkPlanCompleted(
          task,
          todayPlan,
        );

        if (shouldMarkCompleted) {
          // 标记计划为已完成
          await _dailyPlanNotifier.savePlan(
            date: today,
            type: DailyPlanType.planning,
            content: todayPlan.content,
            priority: todayPlan.priority,
          );

          result.dailyPlanCompleted = true;
          debugPrint('✅ 今日计划已标记为完成');
        }
      }

      result.dailyPlanUpdated = true;
      debugPrint('✅ 每日计划数据更新完成');
    } catch (e) {
      debugPrint('❌ 更新每日计划失败: $e');
      result.dailyPlanError = e.toString();
      rethrow;
    }
  }

  /// 判断是否应该标记计划为完成
  Future<bool> _shouldMarkPlanCompleted(
    TimeBoxTask completedTask,
    DailyPlan todayPlan,
  ) async {
    // 这里可以实现具体的业务逻辑
    // 例如：
    // 1. 检查今日已完成的学习时长是否达到目标
    // 2. 检查是否完成了计划中的特定任务
    // 3. 检查是否达到了某个学习里程碑

    // 简单示例：如果完成的任务时长超过60分钟，则认为计划完成
    return completedTask.plannedMinutes >= 60;
  }

  /// 获取今日学习统计摘要
  Future<Map<String, dynamic>> getTodayStudySummary() async {
    try {
      final aggregation = _studyTimeService.currentAggregation;

      if (aggregation == null) {
        return {
          'totalStudyMinutes': 0,
          'completedTasks': 0,
          'studyStreak': 0,
          'weeklyTotal': 0,
          'learningROI': 0.0,
          'focusRatio': 0.0,
          'focusLevel': '需改进',
        };
      }

      return {
        'totalStudyMinutes': aggregation.todayMinutes,
        'completedTasks': aggregation.todayCompletedTasks,
        'studyStreak': aggregation.streakDays,
        'weeklyTotal': aggregation.thisWeekMinutes,
        'learningROI': aggregation.todayLearningROI,
        'focusRatio': aggregation.todayFocusSignalToNoiseRatio,
        'focusLevel': aggregation.todayFocusRatingLevel,
      };
    } catch (e) {
      debugPrint('❌ 获取今日学习统计失败: $e');
      return {
        'totalStudyMinutes': 0,
        'completedTasks': 0,
        'studyStreak': 0,
        'weeklyTotal': 0,
        'learningROI': 0.0,
        'focusRatio': 0.0,
        'focusLevel': '需改进',
      };
    }
  }
}

/// 学习会话完成处理结果
class StudySessionCompletionResult {
  bool isSuccess = false;
  String message = '';
  String? error;

  // 各模块更新状态
  bool studyTimeUpdated = false;
  bool achievementsUpdated = false;
  bool dailyPlanUpdated = false;
  bool dailyPlanCompleted = false;

  // 具体数据
  int studyMinutes = 0;
  int experienceGained = 0;

  // 错误信息
  String? studyTimeError;
  String? achievementError;
  String? dailyPlanError;

  /// 获取更新摘要
  String get updateSummary {
    final updates = <String>[];

    if (studyTimeUpdated) updates.add('学习时间统计');
    if (achievementsUpdated) updates.add('成就系统');
    if (dailyPlanUpdated) updates.add('每日计划');

    return updates.isEmpty ? '无更新' : '已更新: ${updates.join('、')}';
  }

  /// 是否有任何错误
  bool get hasErrors {
    return studyTimeError != null ||
        achievementError != null ||
        dailyPlanError != null;
  }

  /// 获取所有错误信息
  List<String> get allErrors {
    final errors = <String>[];

    if (studyTimeError != null) errors.add('学习时间统计: $studyTimeError');
    if (achievementError != null) errors.add('成就系统: $achievementError');
    if (dailyPlanError != null) errors.add('每日计划: $dailyPlanError');

    return errors;
  }
}
