import 'dart:ui';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 语言服务类
/// 
/// 管理应用的语言设置，包括：
/// - 语言偏好的本地存储
/// - 语言切换功能
/// - 系统语言检测
class LocaleService {
  static const String _localeKey = 'app_locale';
  
  /// 支持的语言列表
  static const List<Locale> supportedLocales = [
    Locale('zh', 'CN'), // 中文简体
    Locale('en', 'US'), // 英文
  ];
  
  /// 默认语言（中文简体）
  static const Locale defaultLocale = Locale('zh', 'CN');
  
  /// 获取保存的语言设置
  static Future<Locale> getSavedLocale() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final localeCode = prefs.getString(_localeKey);
      
      if (localeCode != null) {
        // 解析保存的语言代码
        if (localeCode == 'zh_CN') {
          return const Locale('zh', 'CN');
        } else if (localeCode == 'en_US') {
          return const Locale('en', 'US');
        }
      }
      
      // 如果没有保存的设置，返回默认语言
      return defaultLocale;
    } catch (e) {
      // 出错时返回默认语言
      return defaultLocale;
    }
  }
  
  /// 保存语言设置
  static Future<void> saveLocale(Locale locale) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final localeCode = '${locale.languageCode}_${locale.countryCode}';
      await prefs.setString(_localeKey, localeCode);
    } catch (e) {
      // 保存失败时静默处理
      print('Failed to save locale: $e');
    }
  }
  
  /// 获取语言显示名称
  static String getLanguageName(Locale locale) {
    switch ('${locale.languageCode}_${locale.countryCode}') {
      case 'zh_CN':
        return '中文简体';
      case 'en_US':
        return 'English';
      default:
        return '中文简体';
    }
  }
  
  /// 从语言代码字符串创建Locale
  static Locale localeFromString(String localeString) {
    switch (localeString) {
      case 'zh_CN':
        return const Locale('zh', 'CN');
      case 'en_US':
        return const Locale('en', 'US');
      default:
        return defaultLocale;
    }
  }
  
  /// 将Locale转换为字符串
  static String localeToString(Locale locale) {
    return '${locale.languageCode}_${locale.countryCode}';
  }
}

/// 语言状态管理器
class LocaleNotifier extends StateNotifier<Locale> {
  LocaleNotifier() : super(LocaleService.defaultLocale) {
    _loadSavedLocale();
  }
  
  /// 加载保存的语言设置
  Future<void> _loadSavedLocale() async {
    final savedLocale = await LocaleService.getSavedLocale();
    state = savedLocale;
  }
  
  /// 切换语言
  Future<void> changeLocale(Locale newLocale) async {
    if (LocaleService.supportedLocales.contains(newLocale)) {
      state = newLocale;
      await LocaleService.saveLocale(newLocale);
    }
  }
  
  /// 重置为默认语言
  Future<void> resetToDefault() async {
    await changeLocale(LocaleService.defaultLocale);
  }
}

/// 语言状态提供者
final localeProvider = StateNotifierProvider<LocaleNotifier, Locale>((ref) {
  return LocaleNotifier();
});

/// 当前语言显示名称提供者
final currentLanguageNameProvider = Provider<String>((ref) {
  final locale = ref.watch(localeProvider);
  return LocaleService.getLanguageName(locale);
});
