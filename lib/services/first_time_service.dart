import 'package:shared_preferences/shared_preferences.dart';

/// 首次使用检测服务
/// 
/// 负责管理用户是否首次使用应用的状态，确保：
/// 1. 引导页只在首次使用时显示
/// 2. 后续启动直接进入主界面
/// 3. 提供重置功能用于测试
class FirstTimeService {
  static const String _keyFirstTime = 'is_first_time';
  static const String _keyOnboardingCompleted = 'onboarding_completed';
  
  static FirstTimeService? _instance;
  static FirstTimeService get instance => _instance ??= FirstTimeService._();
  
  FirstTimeService._();
  
  SharedPreferences? _prefs;
  
  /// 初始化服务
  Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
  }
  
  /// 检查是否为首次使用
  /// 返回 true 表示首次使用，需要显示引导页
  /// 返回 false 表示非首次使用，直接进入主界面
  Future<bool> isFirstTime() async {
    await initialize();
    return _prefs?.getBool(_keyFirstTime) ?? true;
  }
  
  /// 检查引导页是否已完成
  Future<bool> isOnboardingCompleted() async {
    await initialize();
    return _prefs?.getBool(_keyOnboardingCompleted) ?? false;
  }
  
  /// 标记引导页已完成
  /// 在用户完成引导页时调用
  Future<void> markOnboardingCompleted() async {
    await initialize();
    await _prefs?.setBool(_keyFirstTime, false);
    await _prefs?.setBool(_keyOnboardingCompleted, true);
  }
  
  /// 重置首次使用状态（用于测试和开发）
  Future<void> resetFirstTimeStatus() async {
    await initialize();
    await _prefs?.setBool(_keyFirstTime, true);
    await _prefs?.setBool(_keyOnboardingCompleted, false);
  }
  
  /// 获取应用启动后应该导航到的路径
  Future<String> getInitialRoute() async {
    final isFirst = await isFirstTime();
    final onboardingCompleted = await isOnboardingCompleted();
    
    if (isFirst || !onboardingCompleted) {
      return '/onboarding';
    } else {
      return '/home';
    }
  }
  
  /// 清除所有数据（用于完全重置）
  Future<void> clearAll() async {
    await initialize();
    await _prefs?.remove(_keyFirstTime);
    await _prefs?.remove(_keyOnboardingCompleted);
  }
}
