import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../features/study_time/services/study_time_statistics_service.dart';
import '../features/study_time/providers/study_time_providers.dart';
import 'global_timer_service.dart';
import 'providers/study_session_completion_provider.dart';

/// 应用初始化服务
///
/// 负责初始化和连接各种全局服务，确保它们之间的协调工作
class AppInitializationService {
  static final AppInitializationService _instance =
      AppInitializationService._internal();
  factory AppInitializationService() => _instance;
  AppInitializationService._internal();

  bool _isInitialized = false;
  StudyTimeStatisticsService? _studyTimeStatisticsService;
  GlobalTimerService? _globalTimerService;
  // Note: _ref field removed as it was unused

  /// 获取初始化状态
  bool get isInitialized => _isInitialized;

  /// 初始化应用服务
  Future<void> initialize(Ref ref) async {
    if (_isInitialized) return;

    try {
      print('🚀 应用初始化服务：开始初始化');

      // Note: ref reference no longer stored as it was unused

      // 获取学习时间统计服务实例
      _studyTimeStatisticsService = ref.read(
        studyTimeStatisticsServiceProvider,
      );

      // 获取全局计时器服务实例
      _globalTimerService = GlobalTimerService();

      // 连接服务
      await _connectServices(ref);

      _isInitialized = true;
      print('✅ 应用初始化服务：初始化完成');
    } catch (e) {
      print('❌ 应用初始化服务：初始化失败 - $e');
      rethrow;
    }
  }

  /// 连接各种服务
  Future<void> _connectServices(Ref ref) async {
    if (_studyTimeStatisticsService != null && _globalTimerService != null) {
      // 将学习时间统计服务连接到全局计时器服务
      _globalTimerService!.setStudyTimeStatisticsService(
        _studyTimeStatisticsService!,
      );

      // 获取并设置学习会话完成服务
      final studySessionCompletionService = ref.read(
        studySessionCompletionServiceProvider,
      );
      _globalTimerService!.setStudySessionCompletionService(
        studySessionCompletionService,
      );

      print('🔗 应用初始化服务：已连接学习时间统计服务到全局计时器服务');
      print('🔗 应用初始化服务：已连接学习会话完成服务到全局计时器服务');
    }
  }

  /// 获取学习时间统计服务
  StudyTimeStatisticsService? get studyTimeStatisticsService =>
      _studyTimeStatisticsService;

  /// 获取全局计时器服务
  GlobalTimerService? get globalTimerService => _globalTimerService;
}

/// 应用初始化服务Provider
final appInitializationServiceProvider = Provider<AppInitializationService>((
  ref,
) {
  final service = AppInitializationService();

  // 在Provider创建时自动初始化
  Future.microtask(() async {
    await service.initialize(ref);
  });

  return service;
});
