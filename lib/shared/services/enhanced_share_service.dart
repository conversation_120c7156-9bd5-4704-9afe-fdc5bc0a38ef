import 'dart:io';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import '../utils/image_watermark_utils.dart';
import '../utils/simple_image_test.dart';

/// 增强的分享服务
/// 提供图片+文本的综合分享功能
class EnhancedShareService {
  
  /// 分享当前图片（包含标记点和水印）
  /// 
  /// [imagePath] 原始图片路径
  /// [anchors] 标记点列表
  /// [sceneTitle] 场景标题
  /// [context] 用于显示错误信息的上下文
  static Future<void> shareImageWithAnnotations({
    required String imagePath,
    required List<dynamic> anchors,
    required String sceneTitle,
    BuildContext? context,
  }) async {
    try {
      print('📤 开始分享图片: $sceneTitle');
      
      // 1. 先尝试简单的图片测试
      print('🧪 尝试简单图片测试...');
      final testImagePath = await SimpleImageTest.testImageProcessing(imagePath);

      String? shareImagePath;
      if (testImagePath != null) {
        print('✅ 简单图片测试成功，尝试完整处理...');
        // 2. 创建带水印和标记点的合成图片
        shareImagePath = await ImageWatermarkUtils.createShareImage(
          imagePath: imagePath,
          anchors: anchors,
          sceneTitle: sceneTitle,
        );
      } else {
        print('❌ 简单图片测试失败，跳过图片处理');
      }
      
      if (shareImagePath == null) {
        print('❌ 无法创建分享图片，回退到文本分享');
        print('🔍 图片路径: $imagePath');
        print('🔍 标记点数量: ${anchors.length}');
        await _shareTextOnly(anchors, sceneTitle);
        return;
      }
      
      // 2. 构建分享文本
      final shareText = _buildShareText(anchors, sceneTitle);
      
      // 3. 分享图片和文本
      print('📱 调用系统分享面板...');
      await Share.shareXFiles(
        [XFile(shareImagePath)],
        text: shareText,
        subject: '知识分享 - $sceneTitle',
      );

      print('✅ 分享面板已关闭，图片分享操作完成: $sceneTitle');
      
      // 4. 清理临时文件（延迟删除，确保分享完成）
      _cleanupTempFile(shareImagePath);
      
    } catch (e) {
      print('❌ 图片分享失败: $e');
      
      // 回退到纯文本分享
      await _shareTextOnly(anchors, sceneTitle);
    }
  }
  
  /// 构建分享文本内容
  static String _buildShareText(List<dynamic> anchors, String sceneTitle) {
    final buffer = StringBuffer();

    // 标题部分 - 使用更吸引人的格式
    buffer.writeln('🎓 OneDay记忆宫殿 | 知识分享');
    buffer.writeln('');
    buffer.writeln('🏛️ 学习场景：$sceneTitle');
    buffer.writeln('');

    // 知识点部分
    if (anchors.isNotEmpty) {
      buffer.writeln('📚 核心知识点 (共${anchors.length}个)');
      buffer.writeln('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      buffer.writeln('');

      for (int i = 0; i < anchors.length; i++) {
        final anchor = anchors[i];
        final content = anchor.content ?? '';

        if (content.isNotEmpty) {
          // 使用更清晰的编号格式
          buffer.writeln('📌 ${i + 1}. $content');

          // 在知识点之间添加适当的间距
          if (i < anchors.length - 1) {
            buffer.writeln('');
          }
        }
      }

      buffer.writeln('');
      buffer.writeln('💡 提示：图片中的蓝色标记点对应上述知识要点');
    } else {
      buffer.writeln('📝 此场景暂无标记的知识点');
      buffer.writeln('💭 您可以在OneDay中为重要内容添加记忆标记');
    }

    buffer.writeln('');
    buffer.writeln('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    buffer.writeln('🚀 通过OneDay记忆宫殿，让学习更高效！');
    buffer.writeln('📱 立即下载OneDay，开启您的高效学习之旅');

    return buffer.toString();
  }
  
  /// 纯文本分享（回退方案）
  static Future<void> _shareTextOnly(List<dynamic> anchors, String sceneTitle) async {
    final shareText = _buildShareText(anchors, sceneTitle);

    print('📱 调用系统分享面板（文本模式）...');
    await Share.share(
      shareText,
      subject: '知识分享 - $sceneTitle',
    );

    print('📝 分享面板已关闭，文本分享操作完成');
  }
  
  /// 清理临时文件
  static void _cleanupTempFile(String filePath) {
    // 延迟5秒后删除临时文件，确保分享操作完成
    Future.delayed(const Duration(seconds: 5), () async {
      try {
        final file = File(filePath);
        if (await file.exists()) {
          await file.delete();
          print('🗑️ 已清理临时分享文件: $filePath');
        }
      } catch (e) {
        print('⚠️ 清理临时文件失败: $e');
      }
    });
  }
  
  /// 分享多张图片（为未来的多选分享功能预留）
  static Future<void> shareMultipleImages({
    required List<String> imagePaths,
    required Map<String, List<dynamic>> imageAnchors,
    required String albumTitle,
    BuildContext? context,
  }) async {
    try {
      print('📤 开始分享多张图片: $albumTitle');
      
      final shareFiles = <XFile>[];
      final sceneDescriptions = <String>[];
      
      // 为每张图片创建带水印的版本
      for (int i = 0; i < imagePaths.length; i++) {
        final imagePath = imagePaths[i];
        final anchors = imageAnchors[imagePath] ?? [];
        final sceneTitle = '$albumTitle ${i + 1}';
        
        final shareImagePath = await ImageWatermarkUtils.createShareImage(
          imagePath: imagePath,
          anchors: anchors,
          sceneTitle: sceneTitle,
        );
        
        if (shareImagePath != null) {
          shareFiles.add(XFile(shareImagePath));
          
          if (anchors.isNotEmpty) {
            sceneDescriptions.add('图片${i + 1}: ${anchors.length}个知识点');
          } else {
            sceneDescriptions.add('图片${i + 1}: 无标记点');
          }
        }
      }
      
      if (shareFiles.isEmpty) {
        print('❌ 无法创建任何分享图片');
        return;
      }
      
      // 构建多图分享文本
      final buffer = StringBuffer();
      buffer.writeln('📸 OneDay记忆宫殿 - 相册分享');
      buffer.writeln('');
      buffer.writeln('📚 相册：$albumTitle');
      buffer.writeln('🖼️ 图片数量：${shareFiles.length}张');
      buffer.writeln('');
      buffer.writeln('📋 内容概览：');
      for (final desc in sceneDescriptions) {
        buffer.writeln('• $desc');
      }
      buffer.writeln('');
      buffer.writeln('💡 通过OneDay记忆宫殿，让学习更高效！');
      
      // 分享多张图片
      await Share.shareXFiles(
        shareFiles,
        text: buffer.toString(),
        subject: '相册分享 - $albumTitle',
      );
      
      print('✅ 多图分享完成: $albumTitle');
      
      // 清理临时文件
      for (final file in shareFiles) {
        _cleanupTempFile(file.path);
      }
      
    } catch (e) {
      print('❌ 多图分享失败: $e');
    }
  }
}
