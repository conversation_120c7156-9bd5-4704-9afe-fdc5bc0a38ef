import 'dart:io';
import 'dart:math' as math;
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import '../config/image_compression_config.dart';

/// 图片压缩工具类
/// 提供图片分辨率标准化和压缩功能，解决高分辨率图片的坐标转换问题
class ImageCompressionUtils {
  /// 默认压缩配置
  static const ImageCompressionConfig _defaultConfig =
      ImageCompressionConfig.defaultConfig;

  /// 压缩单张图片
  ///
  /// [imagePath] 原始图片路径
  /// [config] 压缩配置（可选，默认使用标准配置）
  /// 返回压缩后的图片路径
  static Future<String?> compressImage({
    required String imagePath,
    ImageCompressionConfig? config,
  }) async {
    try {
      final compressionConfig = config ?? _defaultConfig;

      // 检查是否启用压缩
      if (!compressionConfig.enableCompression) {
        print('🔧 压缩已禁用，返回原始图片路径');
        return imagePath;
      }

      print('🔧 开始压缩图片: $imagePath');
      print('🎯 压缩配置: ${compressionConfig.description}');

      // 1. 检查原始图片
      final originalFile = File(imagePath);
      if (!await originalFile.exists()) {
        print('❌ 原始图片不存在: $imagePath');
        return null;
      }

      // 2. 读取图片数据
      final originalBytes = await originalFile.readAsBytes();
      final originalImage = img.decodeImage(originalBytes);

      if (originalImage == null) {
        print('❌ 无法解码图片: $imagePath');
        return null;
      }

      final originalWidth = originalImage.width;
      final originalHeight = originalImage.height;
      final maxSize = compressionConfig.maxDimension;

      print('📐 原始尺寸: ${originalWidth}x$originalHeight');

      // 3. 检查是否需要压缩
      if (originalWidth <= maxSize && originalHeight <= maxSize) {
        print('✅ 图片尺寸已符合要求，无需压缩');
        return imagePath; // 返回原始路径
      }

      // 4. 计算压缩后的尺寸
      final compressionResult = _calculateCompressedSize(
        originalWidth,
        originalHeight,
        maxSize,
      );

      final newWidth = compressionResult['width']! as int;
      final newHeight = compressionResult['height']! as int;
      final compressionRatio = compressionResult['ratio']! as double;

      print(
        '🎯 压缩后尺寸: ${newWidth}x$newHeight (压缩比: ${compressionRatio.toStringAsFixed(2)})',
      );

      // 5. 智能选择压缩算法 - 根据压缩比例优化质量
      img.Interpolation interpolation;
      if (compressionRatio > 0.7) {
        // 轻度压缩：使用最高质量算法
        interpolation = img.Interpolation.cubic;
      } else if (compressionRatio > 0.4) {
        // 中度压缩：平衡质量和性能
        interpolation = img.Interpolation.linear;
      } else {
        // 重度压缩：优先保持清晰度
        interpolation = img.Interpolation.average;
      }

      print(
        '🎨 选择插值算法: ${interpolation.toString().split('.').last} (压缩比: ${compressionRatio.toStringAsFixed(2)})',
      );

      final compressedImage = img.copyResize(
        originalImage,
        width: newWidth,
        height: newHeight,
        interpolation: interpolation,
      );

      // 6. 保存压缩后的图片
      final compressedPath = await _saveCompressedImage(
        compressedImage,
        imagePath,
        compressionConfig,
      );

      if (compressedPath != null) {
        // 7. 验证压缩结果
        final compressedFile = File(compressedPath);
        final originalSize = await originalFile.length();
        final compressedSize = await compressedFile.length();
        final sizeReduction =
            ((originalSize - compressedSize) / originalSize * 100);

        print('✅ 图片压缩完成:');
        print('   原始文件: ${_formatFileSize(originalSize)}');
        print('   压缩文件: ${_formatFileSize(compressedSize)}');
        print('   文件减少: ${sizeReduction.toStringAsFixed(1)}%');
        print('   保存路径: $compressedPath');

        // 验证压缩质量：如果文件过小可能影响显示质量
        if (compressedSize < originalSize * 0.05) {
          print('⚠️ 警告：压缩率过高(${sizeReduction.toStringAsFixed(1)}%)，可能影响显示质量');
        }

        return compressedPath;
      } else {
        print('❌ 保存压缩图片失败');
        return null;
      }
    } catch (e) {
      print('❌ 图片压缩失败: $e');
      return null;
    }
  }

  /// 批量压缩图片
  ///
  /// [imagePaths] 原始图片路径列表
  /// [config] 压缩配置（可选）
  /// [onProgress] 进度回调函数
  /// 返回压缩后的图片路径列表
  static Future<List<String>> compressImages({
    required List<String> imagePaths,
    ImageCompressionConfig? config,
    Function(int current, int total)? onProgress,
  }) async {
    final compressedPaths = <String>[];

    print('🔧 开始批量压缩 ${imagePaths.length} 张图片');

    for (int i = 0; i < imagePaths.length; i++) {
      final imagePath = imagePaths[i];
      onProgress?.call(i + 1, imagePaths.length);

      final compressedPath = await compressImage(
        imagePath: imagePath,
        config: config,
      );
      if (compressedPath != null) {
        compressedPaths.add(compressedPath);
      } else {
        // 如果压缩失败，使用原始路径
        print('⚠️ 图片压缩失败，使用原始路径: $imagePath');
        compressedPaths.add(imagePath);
      }
    }

    print('✅ 批量压缩完成: ${compressedPaths.length}/${imagePaths.length}');
    return compressedPaths;
  }

  /// 计算压缩后的尺寸
  static Map<String, dynamic> _calculateCompressedSize(
    int originalWidth,
    int originalHeight,
    int maxSize,
  ) {
    // 计算缩放比例
    final double scaleX = maxSize / originalWidth;
    final double scaleY = maxSize / originalHeight;
    final double scale = scaleX < scaleY ? scaleX : scaleY;

    // 计算新尺寸
    final int newWidth = (originalWidth * scale).round();
    final int newHeight = (originalHeight * scale).round();

    return {'width': newWidth, 'height': newHeight, 'ratio': scale};
  }

  /// 保存压缩后的图片
  static Future<String?> _saveCompressedImage(
    img.Image compressedImage,
    String originalPath,
    ImageCompressionConfig config,
  ) async {
    try {
      // 获取应用文档目录
      final appDir = await getApplicationDocumentsDirectory();
      final compressedDir = Directory('${appDir.path}/compressed_images');

      // 确保目录存在
      if (!await compressedDir.exists()) {
        await compressedDir.create(recursive: true);
      }

      // 生成压缩图片文件名
      final originalFileName = originalPath.split('/').last;
      final nameWithoutExtension = originalFileName.split('.').first;
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final compressedFileName =
          '$nameWithoutExtension${config.filenameSuffix}_$timestamp.jpg';
      final compressedPath = '${compressedDir.path}/$compressedFileName';

      // 智能编码：根据图片特征选择最佳质量
      int adaptiveQuality = config.jpegQuality;

      // 检测图片是否可能包含文字内容（基于尺寸比例）
      final aspectRatio = compressedImage.width / compressedImage.height;
      final isLikelyTextContent =
          aspectRatio > 1.2 || aspectRatio < 0.8; // 非正方形图片更可能包含文字

      if (isLikelyTextContent) {
        // 对可能包含文字的图片使用更高质量
        adaptiveQuality = math.max(adaptiveQuality, 95);
        print('📝 检测到可能的文字内容，提升JPEG质量至: $adaptiveQuality%');
      }

      final jpegBytes = img.encodeJpg(
        compressedImage,
        quality: adaptiveQuality,
      );

      // 保存文件
      final compressedFile = File(compressedPath);
      await compressedFile.writeAsBytes(jpegBytes);

      return compressedPath;
    } catch (e) {
      print('❌ 保存压缩图片失败: $e');
      return null;
    }
  }

  /// 格式化文件大小显示
  static String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  /// 获取图片信息（不压缩）
  static Future<Map<String, dynamic>?> getImageInfo(String imagePath) async {
    try {
      final file = File(imagePath);
      if (!await file.exists()) return null;

      final bytes = await file.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) return null;

      final fileSize = await file.length();

      return {
        'width': image.width,
        'height': image.height,
        'fileSize': fileSize,
        'fileSizeFormatted': _formatFileSize(fileSize),
        'needsCompression':
            image.width > _defaultConfig.maxDimension ||
            image.height > _defaultConfig.maxDimension,
      };
    } catch (e) {
      print('❌ 获取图片信息失败: $e');
      return null;
    }
  }

  /// 清理压缩图片缓存
  static Future<void> clearCompressedImageCache() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final compressedDir = Directory('${appDir.path}/compressed_images');

      if (await compressedDir.exists()) {
        await compressedDir.delete(recursive: true);
        print('✅ 已清理压缩图片缓存');
      }
    } catch (e) {
      print('❌ 清理压缩图片缓存失败: $e');
    }
  }
}
