import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';

/// 简化的图片测试工具
/// 用于验证基本的图片处理功能
class SimpleImageTest {
  
  /// 测试图片加载和简单处理
  static Future<String?> testImageProcessing(String imagePath) async {
    try {
      print('🧪 开始测试图片处理: $imagePath');
      
      // 1. 测试图片加载
      final image = await _loadTestImage(imagePath);
      if (image == null) {
        print('❌ 图片加载失败');
        return null;
      }
      
      // 2. 创建简单的画布
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);
      
      // 3. 绘制原始图片
      canvas.drawImage(image, Offset.zero, Paint());
      
      // 4. 添加简单的测试标记
      _drawTestMarker(canvas, Size(image.width.toDouble(), image.height.toDouble()));
      
      // 5. 生成最终图片
      final picture = recorder.endRecording();
      final finalImage = await picture.toImage(image.width, image.height);
      
      // 6. 保存测试图片
      final filePath = await _saveTestImage(finalImage);
      
      print('✅ 测试图片处理完成: $filePath');
      return filePath;
      
    } catch (e) {
      print('❌ 测试图片处理失败: $e');
      return null;
    }
  }
  
  /// 加载测试图片
  static Future<ui.Image?> _loadTestImage(String imagePath) async {
    try {
      print('📂 加载测试图片: $imagePath');
      
      final file = File(imagePath);
      if (!await file.exists()) {
        print('❌ 测试图片文件不存在');
        return null;
      }
      
      final bytes = await file.readAsBytes();
      print('📊 图片文件大小: ${bytes.length} bytes');
      
      final codec = await ui.instantiateImageCodec(bytes);
      final frame = await codec.getNextFrame();
      
      print('🖼️ 图片尺寸: ${frame.image.width}x${frame.image.height}');
      return frame.image;
      
    } catch (e) {
      print('❌ 加载测试图片失败: $e');
      return null;
    }
  }
  
  /// 绘制测试标记
  static void _drawTestMarker(Canvas canvas, Size imageSize) {
    print('🎨 绘制测试标记');
    
    // 绘制一个简单的红色圆圈作为测试
    final paint = Paint()
      ..color = Colors.red
      ..style = PaintingStyle.fill;
    
    final center = Offset(imageSize.width * 0.5, imageSize.height * 0.5);
    canvas.drawCircle(center, 30, paint);
    
    // 绘制测试文字
    final textPainter = TextPainter(
      text: const TextSpan(
        text: 'TEST',
        style: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    
    final textOffset = Offset(
      center.dx - textPainter.width / 2,
      center.dy - textPainter.height / 2,
    );
    textPainter.paint(canvas, textOffset);
  }
  
  /// 保存测试图片
  static Future<String> _saveTestImage(ui.Image image) async {
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    final bytes = byteData!.buffer.asUint8List();
    
    final tempDir = await getTemporaryDirectory();
    final fileName = 'test_image_${DateTime.now().millisecondsSinceEpoch}.png';
    final file = File('${tempDir.path}/$fileName');
    
    await file.writeAsBytes(bytes);
    print('💾 测试图片已保存: ${file.path}');
    
    return file.path;
  }
}
