import 'package:flutter/foundation.dart';

/// 静默刷新工具类
/// 确保所有后台刷新操作都是真正静默的，不显示任何用户界面反馈
class SilentRefreshUtils {
  /// 执行静默刷新操作
  /// 
  /// [operation] 要执行的刷新操作
  /// [operationName] 操作名称，用于日志记录
  /// [showLogs] 是否显示日志，默认为true
  static Future<T?> executeSilently<T>({
    required Future<T> Function() operation,
    required String operationName,
    bool showLogs = true,
  }) async {
    try {
      if (showLogs) {
        debugPrint('🔄 静默刷新：开始执行 $operationName');
      }
      
      final result = await operation();
      
      if (showLogs) {
        debugPrint('✅ 静默刷新：$operationName 执行成功');
      }
      
      return result;
    } catch (e) {
      // 静默处理错误，只记录日志，不显示任何用户界面反馈
      if (showLogs) {
        debugPrint('❌ 静默刷新：$operationName 执行失败 - $e');
      }
      return null;
    }
  }

  /// 执行多个静默刷新操作
  /// 
  /// [operations] 要执行的操作列表
  /// [operationName] 整体操作名称
  /// [showLogs] 是否显示日志
  static Future<List<T?>> executeMultipleSilently<T>({
    required List<Future<T> Function()> operations,
    required String operationName,
    bool showLogs = true,
  }) async {
    if (showLogs) {
      debugPrint('🔄 静默刷新：开始执行批量操作 $operationName (${operations.length}个操作)');
    }

    final results = <T?>[];
    
    for (int i = 0; i < operations.length; i++) {
      final result = await executeSilently(
        operation: operations[i],
        operationName: '$operationName-${i + 1}',
        showLogs: showLogs,
      );
      results.add(result);
    }

    if (showLogs) {
      final successCount = results.where((r) => r != null).length;
      debugPrint('✅ 静默刷新：批量操作 $operationName 完成 ($successCount/${operations.length} 成功)');
    }

    return results;
  }

  /// 带延迟的静默刷新
  /// 
  /// [operation] 要执行的刷新操作
  /// [operationName] 操作名称
  /// [delay] 延迟时间（毫秒）
  /// [showLogs] 是否显示日志
  static Future<T?> executeSilentlyWithDelay<T>({
    required Future<T> Function() operation,
    required String operationName,
    int delay = 500,
    bool showLogs = true,
  }) async {
    if (showLogs) {
      debugPrint('🔄 静默刷新：延迟 ${delay}ms 后执行 $operationName');
    }

    await Future.delayed(Duration(milliseconds: delay));
    
    return executeSilently(
      operation: operation,
      operationName: operationName,
      showLogs: showLogs,
    );
  }

  /// 检查是否应该跳过刷新（基于时间间隔）
  /// 
  /// [lastRefreshTime] 上次刷新时间
  /// [minInterval] 最小刷新间隔（毫秒）
  /// [operationName] 操作名称
  static bool shouldSkipRefresh({
    DateTime? lastRefreshTime,
    int minInterval = 3000, // 默认3秒
    String operationName = '刷新操作',
  }) {
    if (lastRefreshTime == null) {
      return false;
    }

    final now = DateTime.now();
    final timeSinceLastRefresh = now.difference(lastRefreshTime).inMilliseconds;
    
    if (timeSinceLastRefresh < minInterval) {
      debugPrint('🔄 静默刷新：距离上次$operationName时间过短，跳过本次刷新');
      return true;
    }

    return false;
  }

  /// 静默刷新包装器，自动处理时间间隔检查
  /// 
  /// [operation] 要执行的刷新操作
  /// [operationName] 操作名称
  /// [lastRefreshTime] 上次刷新时间
  /// [minInterval] 最小刷新间隔（毫秒）
  /// [updateLastRefreshTime] 更新上次刷新时间的回调
  /// [showLogs] 是否显示日志
  static Future<T?> executeSilentlyWithInterval<T>({
    required Future<T> Function() operation,
    required String operationName,
    DateTime? lastRefreshTime,
    int minInterval = 3000,
    void Function(DateTime)? updateLastRefreshTime,
    bool showLogs = true,
  }) async {
    // 检查是否应该跳过刷新
    if (shouldSkipRefresh(
      lastRefreshTime: lastRefreshTime,
      minInterval: minInterval,
      operationName: operationName,
    )) {
      return null;
    }

    // 执行刷新操作
    final result = await executeSilently(
      operation: operation,
      operationName: operationName,
      showLogs: showLogs,
    );

    // 更新上次刷新时间
    if (result != null && updateLastRefreshTime != null) {
      updateLastRefreshTime(DateTime.now());
    }

    return result;
  }
}
