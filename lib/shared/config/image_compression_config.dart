/// 图片压缩配置类
/// 提供可调节的压缩参数，便于后续优化
class ImageCompressionConfig {
  /// 默认配置
  static const ImageCompressionConfig defaultConfig = ImageCompressionConfig();

  /// 高质量配置（较大文件）
  static const ImageCompressionConfig highQuality = ImageCompressionConfig(
    maxDimension: 1200,
    jpegQuality: 95,
  );

  /// 低质量配置（较小文件）
  static const ImageCompressionConfig lowQuality = ImageCompressionConfig(
    maxDimension: 600,
    jpegQuality: 70,
  );

  /// 极速配置（最小文件）
  static const ImageCompressionConfig ultraCompressed = ImageCompressionConfig(
    maxDimension: 400,
    jpegQuality: 60,
  );

  /// 长边最大尺寸（像素）
  final int maxDimension;

  /// JPEG压缩质量（0-100）
  final int jpegQuality;

  /// 是否启用压缩
  final bool enableCompression;

  /// 是否保留原始文件
  final bool keepOriginal;

  /// 压缩后文件名后缀
  final String filenameSuffix;

  const ImageCompressionConfig({
    this.maxDimension = 1000, // 提高默认分辨率，减少文字模糊
    this.jpegQuality = 92, // 提高JPEG质量，特别是对文字内容
    this.enableCompression = true,
    this.keepOriginal = false,
    this.filenameSuffix = '_compressed',
  });

  /// 创建自定义配置
  ImageCompressionConfig copyWith({
    int? maxDimension,
    int? jpegQuality,
    bool? enableCompression,
    bool? keepOriginal,
    String? filenameSuffix,
  }) {
    return ImageCompressionConfig(
      maxDimension: maxDimension ?? this.maxDimension,
      jpegQuality: jpegQuality ?? this.jpegQuality,
      enableCompression: enableCompression ?? this.enableCompression,
      keepOriginal: keepOriginal ?? this.keepOriginal,
      filenameSuffix: filenameSuffix ?? this.filenameSuffix,
    );
  }

  /// 验证配置参数
  bool get isValid {
    return maxDimension > 0 && jpegQuality >= 0 && jpegQuality <= 100;
  }

  /// 获取配置描述
  String get description {
    return 'MaxSize: ${maxDimension}px, Quality: $jpegQuality%, '
        'Enabled: $enableCompression';
  }

  /// 根据用途选择合适的配置
  static ImageCompressionConfig forUseCase(ImageCompressionUseCase useCase) {
    switch (useCase) {
      case ImageCompressionUseCase.memoryPalace:
        return defaultConfig; // 平衡质量和性能
      case ImageCompressionUseCase.sharing:
        return lowQuality; // 优化分享文件大小
      case ImageCompressionUseCase.archiving:
        return highQuality; // 保持高质量存档
      case ImageCompressionUseCase.thumbnail:
        return ultraCompressed; // 缩略图最小化
    }
  }
}

/// 图片压缩使用场景
enum ImageCompressionUseCase {
  /// 记忆宫殿（默认）
  memoryPalace,

  /// 分享
  sharing,

  /// 存档
  archiving,

  /// 缩略图
  thumbnail,
}
