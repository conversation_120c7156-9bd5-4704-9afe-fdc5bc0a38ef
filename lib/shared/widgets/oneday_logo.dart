import 'dart:math' as math;
import 'package:flutter/material.dart';

/// OneDay应用自定义Logo组件
/// 基于品牌设计的矢量图标
class OneDayLogo extends StatelessWidget {
  final double size;
  final Color? color;
  final bool enableAnimation;
  final Duration animationDuration;

  const OneDayLogo({
    super.key,
    this.size = 80,
    this.color,
    this.enableAnimation = false,
    this.animationDuration = const Duration(milliseconds: 1200),
  });

  @override
  Widget build(BuildContext context) {
    final logoColor = color ?? const Color(0xFF2E7EED);
    
    if (enableAnimation) {
      return _AnimatedLogo(
        size: size,
        color: logoColor,
        duration: animationDuration,
      );
    }
    
    return _StaticLogo(
      size: size,
      color: logoColor,
    );
  }
}

/// 静态Logo组件
class _StaticLogo extends StatelessWidget {
  final double size;
  final Color color;

  const _StaticLogo({
    required this.size,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CustomPaint(
        painter: _OneDayLogoPainter(color: color),
      ),
    );
  }
}

/// 动画Logo组件
class _AnimatedLogo extends StatefulWidget {
  final double size;
  final Color color;
  final Duration duration;

  const _AnimatedLogo({
    required this.size,
    required this.color,
    required this.duration,
  });

  @override
  State<_AnimatedLogo> createState() => _AnimatedLogoState();
}

class _AnimatedLogoState extends State<_AnimatedLogo>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: _StaticLogo(
            size: widget.size,
            color: widget.color,
          ),
        );
      },
    );
  }
}

/// OneDay Logo绘制器
class _OneDayLogoPainter extends CustomPainter {
  final Color color;

  _OneDayLogoPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill
      ..strokeWidth = size.width * 0.08
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.45;
    final innerRadius = size.width * 0.25;

    // 绘制外圆环
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = size.width * 0.08;
    canvas.drawCircle(center, radius, paint);

    // 绘制内部网格线条
    paint.strokeWidth = size.width * 0.06;
    
    // 水平线
    canvas.drawLine(
      Offset(center.dx - innerRadius, center.dy),
      Offset(center.dx + innerRadius, center.dy),
      paint,
    );
    
    // 垂直线
    canvas.drawLine(
      Offset(center.dx, center.dy - innerRadius),
      Offset(center.dx, center.dy + innerRadius),
      paint,
    );
    
    // 对角线
    final diagonalOffset = innerRadius * 0.7;
    canvas.drawLine(
      Offset(center.dx - diagonalOffset, center.dy - diagonalOffset),
      Offset(center.dx + diagonalOffset, center.dy + diagonalOffset),
      paint,
    );
    
    canvas.drawLine(
      Offset(center.dx - diagonalOffset, center.dy + diagonalOffset),
      Offset(center.dx + diagonalOffset, center.dy - diagonalOffset),
      paint,
    );

    // 绘制中心的"D"字母
    paint.style = PaintingStyle.fill;
    _drawLetterD(canvas, size, paint);
  }

  void _drawLetterD(Canvas canvas, Size size, Paint paint) {
    final center = Offset(size.width / 2, size.height / 2);
    final letterSize = size.width * 0.2;
    
    // 创建"D"字母的路径
    final path = Path();
    
    // D字母的左侧垂直线
    final leftX = center.dx - letterSize * 0.4;
    final topY = center.dy - letterSize * 0.6;
    final bottomY = center.dy + letterSize * 0.6;
    
    path.moveTo(leftX, topY);
    path.lineTo(leftX, bottomY);
    
    // D字母的顶部水平线
    path.moveTo(leftX, topY);
    path.lineTo(leftX + letterSize * 0.5, topY);
    
    // D字母的底部水平线
    path.moveTo(leftX, bottomY);
    path.lineTo(leftX + letterSize * 0.5, bottomY);
    
    // D字母的右侧弧线
    final rightCenterX = leftX + letterSize * 0.5;
    final arcRect = Rect.fromCenter(
      center: Offset(rightCenterX, center.dy),
      width: letterSize * 0.6,
      height: letterSize * 1.2,
    );
    
    path.addArc(arcRect, -math.pi / 2, math.pi);
    
    paint.strokeWidth = size.width * 0.04;
    paint.style = PaintingStyle.stroke;
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! _OneDayLogoPainter || oldDelegate.color != color;
  }
}

/// 简化版Logo（用于小尺寸显示）
class OneDayLogoSimple extends StatelessWidget {
  final double size;
  final Color? color;

  const OneDayLogoSimple({
    super.key,
    this.size = 32,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final logoColor = color ?? const Color(0xFF2E7EED);
    
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: logoColor,
      ),
      child: Center(
        child: Text(
          'D',
          style: TextStyle(
            fontSize: size * 0.5,
            fontWeight: FontWeight.w600,
            color: Colors.white,
            height: 1,
          ),
        ),
      ),
    );
  }
}
