import 'package:flutter/material.dart';

/// 微信图标组件
class WechatIcon extends StatelessWidget {
  final double size;
  final Color color;

  const WechatIcon({
    super.key,
    this.size = 24,
    this.color = Colors.white,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CustomPaint(
        painter: _WechatIconPainter(color: color),
      ),
    );
  }
}

/// 微信图标绘制器
class _WechatIconPainter extends CustomPainter {
  final Color color;

  _WechatIconPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final width = size.width;
    final height = size.height;

    // 绘制微信图标的简化版本
    // 大圆（左侧）
    final bigCircle = Offset(width * 0.35, height * 0.45);
    canvas.drawCircle(bigCircle, width * 0.18, paint);

    // 小圆（右侧）
    final smallCircle = Offset(width * 0.65, height * 0.55);
    canvas.drawCircle(smallCircle, width * 0.15, paint);

    // 左侧圆的眼睛
    paint.color = const Color(0xFF07C160);
    canvas.drawCircle(
      Offset(width * 0.3, height * 0.4),
      width * 0.025,
      paint,
    );
    canvas.drawCircle(
      Offset(width * 0.4, height * 0.4),
      width * 0.025,
      paint,
    );

    // 右侧圆的眼睛
    canvas.drawCircle(
      Offset(width * 0.6, height * 0.5),
      width * 0.02,
      paint,
    );
    canvas.drawCircle(
      Offset(width * 0.7, height * 0.5),
      width * 0.02,
      paint,
    );

    // 重置颜色为原色
    paint.color = color;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! _WechatIconPainter || oldDelegate.color != color;
  }
}
