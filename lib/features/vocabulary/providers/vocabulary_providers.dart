import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../vocabulary_service.dart';
import '../fsrs_service.dart';
import '../vocabulary_learning_service.dart';
import '../word_model.dart';

/// 词汇服务Provider
final vocabularyServiceProvider = Provider<VocabularyService>((ref) {
  return VocabularyService();
});

/// FSRS服务Provider
final fsrsServiceProvider = Provider<FSRSService>((ref) {
  final vocabularyService = ref.watch(vocabularyServiceProvider);
  return FSRSService(
    userId: 'default_user', // 可以从用户配置中获取
    vocabularyService: vocabularyService,
  );
});

/// 词汇学习服务Provider
final vocabularyLearningServiceProvider = Provider<VocabularyLearningService>((
  ref,
) {
  final vocabularyService = ref.watch(vocabularyServiceProvider);
  return VocabularyLearningService(vocabularyService);
});

/// 词汇进度状态Provider
final vocabularyProgressProvider =
    StateNotifierProvider<VocabularyProgressNotifier, VocabularyProgressState>((
      ref,
    ) {
      final vocabularyService = ref.watch(vocabularyServiceProvider);
      final fsrsService = ref.watch(fsrsServiceProvider);
      return VocabularyProgressNotifier(vocabularyService, fsrsService);
    });

/// 词汇进度状态
class VocabularyProgressState {
  final Map<String, WordLearningProgress> progress;
  final bool isLoading;
  final String? error;
  final List<String> dueWords;
  final Map<String, dynamic> statistics;

  const VocabularyProgressState({
    this.progress = const {},
    this.isLoading = false,
    this.error,
    this.dueWords = const [],
    this.statistics = const {},
  });

  VocabularyProgressState copyWith({
    Map<String, WordLearningProgress>? progress,
    bool? isLoading,
    String? error,
    List<String>? dueWords,
    Map<String, dynamic>? statistics,
  }) {
    return VocabularyProgressState(
      progress: progress ?? this.progress,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      dueWords: dueWords ?? this.dueWords,
      statistics: statistics ?? this.statistics,
    );
  }
}

/// 词汇进度状态管理器
class VocabularyProgressNotifier
    extends StateNotifier<VocabularyProgressState> {
  final VocabularyService _vocabularyService;
  final FSRSService _fsrsService;

  VocabularyProgressNotifier(this._vocabularyService, this._fsrsService)
    : super(const VocabularyProgressState()) {
    _loadInitialData();
  }

  /// 加载初始数据
  Future<void> _loadInitialData() async {
    await loadProgress();
    await loadDueWords();
    await loadStatistics();
  }

  /// 加载学习进度
  Future<void> loadProgress() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final progress = await _vocabularyService.getLearningProgress();
      state = state.copyWith(progress: progress, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// 加载待复习单词
  Future<void> loadDueWords() async {
    try {
      final dueWords = await _fsrsService.getDueWords();
      state = state.copyWith(dueWords: dueWords);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 加载统计数据
  Future<void> loadStatistics() async {
    try {
      final stats = await _fsrsService.generateAnalysisReport();
      state = state.copyWith(statistics: stats);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 处理首次学习
  Future<void> handleFirstLearning({
    required String wordId,
    required int rating,
    required int responseTime,
    bool isKinesthetic = false,
    MotorMemoryData? motorData,
  }) async {
    try {
      final progress = await _fsrsService.handleFirstLearning(
        wordId: wordId,
        rating: rating,
        responseTime: responseTime,
        isKinesthetic: isKinesthetic,
        motorData: motorData,
      );

      // 更新本地状态
      final updatedProgress = Map<String, WordLearningProgress>.from(
        state.progress,
      );
      updatedProgress[wordId] = progress;

      state = state.copyWith(progress: updatedProgress);

      // 刷新相关数据
      await loadDueWords();
      await loadStatistics();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 处理复习
  Future<void> handleReview({
    required String wordId,
    required int rating,
    required int responseTime,
    bool isKinesthetic = false,
    MotorMemoryData? motorData,
  }) async {
    try {
      final currentProgress = state.progress[wordId];
      if (currentProgress == null) {
        throw Exception('单词进度不存在');
      }

      final updatedProgress = await _fsrsService.handleReview(
        currentProgress: currentProgress,
        rating: rating,
        responseTime: responseTime,
        isKinesthetic: isKinesthetic,
        motorData: motorData,
      );

      // 更新本地状态
      final newProgress = Map<String, WordLearningProgress>.from(
        state.progress,
      );
      newProgress[wordId] = updatedProgress;

      state = state.copyWith(progress: newProgress);

      // 刷新相关数据
      await loadDueWords();
      await loadStatistics();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 更新单词学习进度
  Future<void> updateWordProgress(
    String wordId,
    WordLearningProgress progress,
  ) async {
    try {
      await _vocabularyService.updateLearningProgress(wordId, progress);

      final updatedProgress = Map<String, WordLearningProgress>.from(
        state.progress,
      );
      updatedProgress[wordId] = progress;

      state = state.copyWith(progress: updatedProgress);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 预测记忆保持率
  Future<double> predictRetrievability(
    String wordId, {
    DateTime? targetDate,
  }) async {
    try {
      return await _fsrsService.predictRetrievability(
        wordId: wordId,
        targetDate: targetDate,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return 0.0;
    }
  }

  /// 刷新所有数据
  Future<void> refresh() async {
    await _loadInitialData();
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// 词汇数据Provider - 管理词汇库数据
final vocabularyDataProvider = FutureProvider<Vocabulary>((ref) async {
  final service = ref.watch(vocabularyServiceProvider);
  return await service.loadVocabulary();
});

/// 记忆词库Provider
final memoryVocabularyProvider = FutureProvider<Map<String, dynamic>>((
  ref,
) async {
  final service = ref.watch(vocabularyServiceProvider);
  return await service.getMemoryVocabulary();
});

/// 词汇搜索Provider
final vocabularySearchProvider =
    StateNotifierProvider<VocabularySearchNotifier, VocabularySearchState>((
      ref,
    ) {
      final service = ref.watch(vocabularyServiceProvider);
      return VocabularySearchNotifier(service);
    });

/// 词汇搜索状态
class VocabularySearchState {
  final String query;
  final List<MapEntry<String, WordDetails>> results;
  final bool isLoading;
  final String? error;

  const VocabularySearchState({
    this.query = '',
    this.results = const [],
    this.isLoading = false,
    this.error,
  });

  VocabularySearchState copyWith({
    String? query,
    List<MapEntry<String, WordDetails>>? results,
    bool? isLoading,
    String? error,
  }) {
    return VocabularySearchState(
      query: query ?? this.query,
      results: results ?? this.results,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// 词汇搜索状态管理器
class VocabularySearchNotifier extends StateNotifier<VocabularySearchState> {
  final VocabularyService _service;

  VocabularySearchNotifier(this._service)
    : super(const VocabularySearchState());

  /// 搜索单词
  Future<void> searchWords(
    String query, {
    String? categoryId,
    List<String>? difficulties,
    List<String>? partsOfSpeech,
    int limit = 50,
  }) async {
    if (query.trim().isEmpty) {
      state = state.copyWith(query: '', results: []);
      return;
    }

    state = state.copyWith(query: query, isLoading: true, error: null);

    try {
      final results = await _service.searchWords(
        query,
        categoryId: categoryId,
        difficulties: difficulties,
        partsOfSpeech: partsOfSpeech,
        limit: limit,
      );

      state = state.copyWith(results: results, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// 清除搜索结果
  void clearSearch() {
    state = const VocabularySearchState();
  }
}
