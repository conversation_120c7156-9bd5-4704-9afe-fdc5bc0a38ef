import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'vocabulary_service.dart';

/// 单词释义查找服务
/// 负责从考研单词库和用户自定义词库中获取单词的中文释义
class WordMeaningService {
  final VocabularyService _vocabularyService;

  WordMeaningService(this._vocabularyService);

  /// 获取单词的中文释义
  /// 优先从考研单词库查找，如果没有找到则从用户自定义词库查找
  Future<WordMeaningResult?> getWordMeaning(String word) async {
    if (word.isEmpty) return null;

    final normalizedWord = word.toLowerCase().trim();
    
    try {
      // 1. 首先从考研单词库查找
      final graduateExamResult = await _searchInGraduateExamVocabulary(normalizedWord);
      if (graduateExamResult != null) {
        return graduateExamResult;
      }

      // 2. 如果考研词库没有找到，从用户自定义词库查找
      final customResult = await _searchInCustomVocabularies(normalizedWord);
      if (customResult != null) {
        return customResult;
      }

      // 3. 都没有找到，返回null
      debugPrint('未找到单词 "$word" 的释义');
      return null;
    } catch (e) {
      debugPrint('查找单词释义时出错: $e');
      return null;
    }
  }

  /// 从考研单词库中搜索单词释义
  Future<WordMeaningResult?> _searchInGraduateExamVocabulary(String word) async {
    try {
      final vocabulary = await _vocabularyService.loadVocabulary();
      final wordDetails = vocabulary.words[word];
      
      if (wordDetails != null) {
        return WordMeaningResult(
          word: word,
          definition: wordDetails.definition,
          phonetic: wordDetails.phonetic,
          partOfSpeech: wordDetails.partOfSpeech,
          source: WordSource.graduateExam,
          sourceId: 'graduate_exam',
          sourceName: '考研词库',
        );
      }
      
      return null;
    } catch (e) {
      debugPrint('从考研词库搜索单词失败: $e');
      return null;
    }
  }

  /// 从用户自定义词库中搜索单词释义
  Future<WordMeaningResult?> _searchInCustomVocabularies(String word) async {
    try {
      final customVocabularies = await _vocabularyService.getCustomVocabularies();
      
      for (final customVocab in customVocabularies) {
        // 在每个自定义词库中查找
        for (final customWord in customVocab.words) {
          if (customWord.word.toLowerCase() == word) {
            return WordMeaningResult(
              word: word,
              definition: customWord.definition,
              phonetic: customWord.phonetic,
              partOfSpeech: customWord.partOfSpeech,
              source: WordSource.custom,
              sourceId: customVocab.id,
              sourceName: customVocab.name,
            );
          }
        }
      }
      
      return null;
    } catch (e) {
      debugPrint('从自定义词库搜索单词失败: $e');
      return null;
    }
  }

  /// 批量获取多个单词的释义
  Future<Map<String, WordMeaningResult>> getBatchWordMeanings(List<String> words) async {
    final results = <String, WordMeaningResult>{};
    
    for (final word in words) {
      final meaning = await getWordMeaning(word);
      if (meaning != null) {
        results[word] = meaning;
      }
    }
    
    return results;
  }

  /// 搜索包含指定文本的单词释义
  Future<List<WordMeaningResult>> searchWordsByMeaning(
    String meaningQuery, {
    int limit = 20,
  }) async {
    final results = <WordMeaningResult>[];
    final query = meaningQuery.toLowerCase().trim();
    
    if (query.isEmpty) return results;

    try {
      // 从考研词库搜索
      final vocabulary = await _vocabularyService.loadVocabulary();
      for (final entry in vocabulary.words.entries) {
        if (results.length >= limit) break;
        
        final word = entry.key;
        final details = entry.value;
        
        if (details.definition.toLowerCase().contains(query)) {
          results.add(WordMeaningResult(
            word: word,
            definition: details.definition,
            phonetic: details.phonetic,
            partOfSpeech: details.partOfSpeech,
            source: WordSource.graduateExam,
            sourceId: 'graduate_exam',
            sourceName: '考研词库',
          ));
        }
      }

      // 从自定义词库搜索
      if (results.length < limit) {
        final customVocabularies = await _vocabularyService.getCustomVocabularies();
        
        for (final customVocab in customVocabularies) {
          if (results.length >= limit) break;
          
          for (final customWord in customVocab.words) {
            if (results.length >= limit) break;
            
            if (customWord.definition.toLowerCase().contains(query)) {
              results.add(WordMeaningResult(
                word: customWord.word,
                definition: customWord.definition,
                phonetic: customWord.phonetic,
                partOfSpeech: customWord.partOfSpeech,
                source: WordSource.custom,
                sourceId: customVocab.id,
                sourceName: customVocab.name,
              ));
            }
          }
        }
      }
    } catch (e) {
      debugPrint('按释义搜索单词时出错: $e');
    }

    return results;
  }
}

/// 单词释义查找结果
class WordMeaningResult {
  final String word;
  final String definition;
  final String? phonetic;
  final String partOfSpeech;
  final WordSource source;
  final String sourceId;
  final String sourceName;

  WordMeaningResult({
    required this.word,
    required this.definition,
    this.phonetic,
    required this.partOfSpeech,
    required this.source,
    required this.sourceId,
    required this.sourceName,
  });

  @override
  String toString() {
    return 'WordMeaningResult(word: $word, definition: $definition, source: $sourceName)';
  }
}

/// 单词来源枚举
enum WordSource {
  graduateExam, // 考研词库
  custom,       // 用户自定义词库
}

/// 单词释义服务Provider
final wordMeaningServiceProvider = Provider<WordMeaningService>((ref) {
  final vocabularyService = ref.read(vocabularyServiceProvider);
  return WordMeaningService(vocabularyService);
});
