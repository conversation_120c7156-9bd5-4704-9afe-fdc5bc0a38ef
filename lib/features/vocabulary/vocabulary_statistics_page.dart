import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'vocabulary_service.dart';

import 'word_model.dart';

/// 词汇学习统计页面
class VocabularyStatisticsPage extends ConsumerStatefulWidget {
  const VocabularyStatisticsPage({super.key});

  @override
  ConsumerState<VocabularyStatisticsPage> createState() =>
      _VocabularyStatisticsPageState();
}

class _VocabularyStatisticsPageState
    extends ConsumerState<VocabularyStatisticsPage> {
  @override
  Widget build(BuildContext context) {
    final statisticsAsync = ref.watch(learningStatisticsProvider);
    final progressAsync = ref.watch(learningProgressProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          '学习统计',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF37352F)),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 总体统计
              statisticsAsync.when(
                loading: () => const _StatisticsLoadingCard(),
                error: (error, stack) => _ErrorCard(error: error.toString()),
                data: (stats) => _OverallStatisticsCard(statistics: stats),
              ),

              const SizedBox(height: 24),

              // 学习进度图表
              progressAsync.when(
                loading: () => const _ProgressLoadingCard(),
                error: (error, stack) => _ErrorCard(error: error.toString()),
                data: (progress) => _LearningProgressChart(progress: progress),
              ),

              const SizedBox(height: 24),

              // 分类统计
              statisticsAsync.when(
                loading: () => const SizedBox.shrink(),
                error: (error, stack) => const SizedBox.shrink(),
                data: (stats) => _CategoryStatistics(statistics: stats),
              ),

              const SizedBox(height: 24),

              // 学习建议
              progressAsync.when(
                loading: () => const SizedBox.shrink(),
                error: (error, stack) => const SizedBox.shrink(),
                data: (progress) =>
                    _LearningRecommendations(progress: progress),
              ),

              const SizedBox(height: 24),

              // 成就徽章
              progressAsync.when(
                loading: () => const SizedBox.shrink(),
                error: (error, stack) => const SizedBox.shrink(),
                data: (progress) => _AchievementBadges(progress: progress),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理下拉刷新
  Future<void> _handleRefresh() async {
    ref.invalidate(learningStatisticsProvider);
    ref.invalidate(learningProgressProvider);
  }
}

/// 总体统计卡片
class _OverallStatisticsCard extends StatelessWidget {
  final Map<String, dynamic> statistics;

  const _OverallStatisticsCard({required this.statistics});

  @override
  Widget build(BuildContext context) {
    final totalWords = statistics['totalWords'] ?? 0;
    final learnedWords = statistics['learnedWords'] ?? 0;
    final selectedWords = statistics['selectedWords'] ?? 0;
    final learningRate = statistics['learningRate'] ?? 0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '学习概览',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 20),

          // 进度环形图
          Row(
            children: [
              SizedBox(
                width: 100,
                height: 100,
                child: Stack(
                  children: [
                    // 背景圆环
                    SizedBox(
                      width: 100,
                      height: 100,
                      child: CircularProgressIndicator(
                        value: 1.0,
                        strokeWidth: 8,
                        backgroundColor: const Color(0xFFE3E2E0),
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          Color(0xFFE3E2E0),
                        ),
                      ),
                    ),
                    // 进度圆环
                    SizedBox(
                      width: 100,
                      height: 100,
                      child: CircularProgressIndicator(
                        value: totalWords > 0 ? learnedWords / totalWords : 0.0,
                        strokeWidth: 8,
                        backgroundColor: Colors.transparent,
                        valueColor: const AlwaysStoppedAnimation<Color>(
                          Color(0xFF2F76DA),
                        ),
                      ),
                    ),
                    // 中心文字
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            '$learningRate%',
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF2F76DA),
                            ),
                          ),
                          const Text(
                            '完成率',
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF787774),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 32),

              // 统计数据
              Expanded(
                child: Column(
                  children: [
                    _StatisticRow(
                      label: '总词汇',
                      value: totalWords.toString(),
                      color: const Color(0xFF37352F),
                    ),
                    const SizedBox(height: 12),
                    _StatisticRow(
                      label: '已学习',
                      value: learnedWords.toString(),
                      color: const Color(0xFF0F7B6C),
                    ),
                    const SizedBox(height: 12),
                    _StatisticRow(
                      label: '已选择',
                      value: selectedWords.toString(),
                      color: const Color(0xFFD9730D),
                    ),
                    const SizedBox(height: 12),
                    _StatisticRow(
                      label: '待学习',
                      value: (selectedWords - learnedWords).toString(),
                      color: const Color(0xFF7C3AED),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// 统计行
class _StatisticRow extends StatelessWidget {
  final String label;
  final String value;
  final Color color;

  const _StatisticRow({
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(fontSize: 14, color: Color(0xFF787774)),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }
}

/// 学习进度图表
class _LearningProgressChart extends StatelessWidget {
  final Map<String, WordLearningProgress> progress;

  const _LearningProgressChart({required this.progress});

  @override
  Widget build(BuildContext context) {
    // 计算最近7天的学习数据
    final recentProgress = _calculateRecentProgress();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '最近7天学习进度',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 20),

          // 简单的条形图
          SizedBox(
            height: 120,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: recentProgress.map((dayData) {
                final height = (dayData['count'] as int) * 4.0; // 简单的高度计算
                return Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      width: 24,
                      height: height.clamp(4.0, 80.0),
                      decoration: BoxDecoration(
                        color: const Color(0xFF2F76DA),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      dayData['day'] as String,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF787774),
                      ),
                    ),
                    Text(
                      '${dayData['count']}',
                      style: const TextStyle(
                        fontSize: 10,
                        color: Color(0xFF9B9A97),
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _calculateRecentProgress() {
    final now = DateTime.now();
    final recentDays = <Map<String, dynamic>>[];

    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayName = _getDayName(date.weekday);

      // 计算当天学习的单词数
      int count = 0;
      for (final wordProgress in progress.values) {
        if (wordProgress.lastReviewedAt != null) {
          final reviewDate = wordProgress.lastReviewedAt!;
          if (reviewDate.year == date.year &&
              reviewDate.month == date.month &&
              reviewDate.day == date.day) {
            count++;
          }
        }
      }

      recentDays.add({'day': dayName, 'count': count, 'date': date});
    }

    return recentDays;
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return '周一';
      case 2:
        return '周二';
      case 3:
        return '周三';
      case 4:
        return '周四';
      case 5:
        return '周五';
      case 6:
        return '周六';
      case 7:
        return '周日';
      default:
        return '';
    }
  }
}

/// 分类统计
class _CategoryStatistics extends StatelessWidget {
  final Map<String, dynamic> statistics;

  const _CategoryStatistics({required this.statistics});

  @override
  Widget build(BuildContext context) {
    final categoryStats =
        statistics['categoryStats'] as Map<String, int>? ?? {};

    if (categoryStats.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '分类学习统计',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 16),

          ...categoryStats.entries.map((entry) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: _CategoryStatItem(
                categoryName: entry.key,
                learnedCount: entry.value,
              ),
            );
          }),
        ],
      ),
    );
  }
}

/// 分类统计项
class _CategoryStatItem extends StatelessWidget {
  final String categoryName;
  final int learnedCount;

  const _CategoryStatItem({
    required this.categoryName,
    required this.learnedCount,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: _getCategoryColor(categoryName),
            borderRadius: BorderRadius.circular(6),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            categoryName,
            style: const TextStyle(fontSize: 14, color: Color(0xFF37352F)),
          ),
        ),
        Text(
          '$learnedCount 个',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF2F76DA),
          ),
        ),
      ],
    );
  }

  Color _getCategoryColor(String categoryName) {
    switch (categoryName) {
      case '考研词库':
        return const Color(0xFF2F76DA);
      default:
        return const Color(0xFF7C3AED);
    }
  }
}

/// 学习建议
class _LearningRecommendations extends ConsumerWidget {
  final Map<String, WordLearningProgress> progress;

  const _LearningRecommendations({required this.progress});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final recommendations = _generateRecommendations();

    if (recommendations.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '学习建议',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 16),

          ...recommendations.map((recommendation) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: _RecommendationItem(
                icon: recommendation['icon'] as IconData,
                title: recommendation['title'] as String,
                description: recommendation['description'] as String,
                color: recommendation['color'] as Color,
              ),
            );
          }),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _generateRecommendations() {
    final recommendations = <Map<String, dynamic>>[];

    // 分析学习进度生成建议
    final totalWords = progress.length;
    final learnedWords = progress.values.where((p) => p.isLearned).length;
    final reviewNeeded = progress.values
        .where(
          (p) =>
              p.lastReviewedAt != null &&
              DateTime.now().difference(p.lastReviewedAt!).inDays >= 3,
        )
        .length;

    if (totalWords == 0) {
      recommendations.add({
        'icon': Icons.play_arrow,
        'title': '开始学习',
        'description': '选择一些单词开始你的学习之旅吧！',
        'color': const Color(0xFF2F76DA),
      });
    } else if (learnedWords < totalWords * 0.1) {
      recommendations.add({
        'icon': Icons.trending_up,
        'title': '保持学习',
        'description': '继续学习新单词，建议每天学习5-10个单词',
        'color': const Color(0xFF0F7B6C),
      });
    } else if (reviewNeeded > 0) {
      recommendations.add({
        'icon': Icons.refresh,
        'title': '复习单词',
        'description': '有 $reviewNeeded 个单词需要复习，及时复习有助于记忆',
        'color': const Color(0xFFD9730D),
      });
    }

    if (learnedWords >= 50) {
      recommendations.add({
        'icon': Icons.emoji_events,
        'title': '学习成就',
        'description': '恭喜！你已经掌握了 $learnedWords 个单词',
        'color': const Color(0xFF7C3AED),
      });
    }

    return recommendations;
  }
}

/// 建议项
class _RecommendationItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final String description;
  final Color color;

  const _RecommendationItem({
    required this.icon,
    required this.title,
    required this.description,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF37352F),
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: const TextStyle(fontSize: 12, color: Color(0xFF787774)),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// 成就徽章
class _AchievementBadges extends StatelessWidget {
  final Map<String, WordLearningProgress> progress;

  const _AchievementBadges({required this.progress});

  @override
  Widget build(BuildContext context) {
    final achievements = _calculateAchievements();

    if (achievements.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '学习成就',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 16),

          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: achievements.map((achievement) {
              return _AchievementBadge(
                icon: achievement['icon'] as IconData,
                title: achievement['title'] as String,
                description: achievement['description'] as String,
                color: achievement['color'] as Color,
                isUnlocked: achievement['isUnlocked'] as bool,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _calculateAchievements() {
    final learnedWords = progress.values.where((p) => p.isLearned).length;
    final totalReviews = progress.values.fold(
      0,
      (sum, p) => sum + p.reviewCount,
    );
    final consecutiveDays = _calculateConsecutiveDays();

    return [
      {
        'icon': Icons.star,
        'title': '初学者',
        'description': '学会第一个单词',
        'color': const Color(0xFF2F76DA),
        'isUnlocked': learnedWords >= 1,
      },
      {
        'icon': Icons.local_fire_department,
        'title': '学习达人',
        'description': '学会50个单词',
        'color': const Color(0xFFE03E3E),
        'isUnlocked': learnedWords >= 50,
      },
      {
        'icon': Icons.emoji_events,
        'title': '词汇大师',
        'description': '学会100个单词',
        'color': const Color(0xFFD9730D),
        'isUnlocked': learnedWords >= 100,
      },
      {
        'icon': Icons.refresh,
        'title': '复习专家',
        'description': '完成100次复习',
        'color': const Color(0xFF0F7B6C),
        'isUnlocked': totalReviews >= 100,
      },
      {
        'icon': Icons.calendar_today,
        'title': '坚持不懈',
        'description': '连续学习7天',
        'color': const Color(0xFF7C3AED),
        'isUnlocked': consecutiveDays >= 7,
      },
    ];
  }

  int _calculateConsecutiveDays() {
    // 简化的连续天数计算
    final now = DateTime.now();
    int consecutiveDays = 0;

    for (int i = 0; i < 30; i++) {
      final date = now.subtract(Duration(days: i));
      bool hasLearningOnDate = false;

      for (final wordProgress in progress.values) {
        if (wordProgress.lastReviewedAt != null) {
          final reviewDate = wordProgress.lastReviewedAt!;
          if (reviewDate.year == date.year &&
              reviewDate.month == date.month &&
              reviewDate.day == date.day) {
            hasLearningOnDate = true;
            break;
          }
        }
      }

      if (hasLearningOnDate) {
        consecutiveDays++;
      } else if (i > 0) {
        break; // 中断连续记录
      }
    }

    return consecutiveDays;
  }
}

/// 成就徽章
class _AchievementBadge extends StatelessWidget {
  final IconData icon;
  final String title;
  final String description;
  final Color color;
  final bool isUnlocked;

  const _AchievementBadge({
    required this.icon,
    required this.title,
    required this.description,
    required this.color,
    required this.isUnlocked,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 100,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isUnlocked
            ? color.withValues(alpha: 0.1)
            : const Color(0xFFF7F6F3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: isUnlocked ? color : const Color(0xFFE3E2E0)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: isUnlocked ? color : const Color(0xFF9B9A97),
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: isUnlocked ? color : const Color(0xFF9B9A97),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              fontSize: 10,
              color: isUnlocked
                  ? const Color(0xFF787774)
                  : const Color(0xFF9B9A97),
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

/// 统计加载卡片
class _StatisticsLoadingCard extends StatelessWidget {
  const _StatisticsLoadingCard();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 200,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
      ),
      child: const Center(child: CircularProgressIndicator()),
    );
  }
}

/// 进度加载卡片
class _ProgressLoadingCard extends StatelessWidget {
  const _ProgressLoadingCard();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 180,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
      ),
      child: const Center(child: CircularProgressIndicator()),
    );
  }
}

/// 错误卡片
class _ErrorCard extends StatelessWidget {
  final String error;

  const _ErrorCard({required this.error});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
      ),
      child: Column(
        children: [
          const Icon(Icons.error_outline, size: 48, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            '加载失败',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: const TextStyle(fontSize: 14, color: Color(0xFF787774)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
