import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'fsrs_algorithm.dart';
import 'word_model.dart';
import 'vocabulary_service.dart';

/// FSRS服务 - 管理智能复习调度
///
/// 提供基于FSRS算法的个性化复习计划，专门针对动觉记忆优化
class FSRSService {
  static const String _userParametersKey = 'fsrs_user_parameters';
  // static const String _reviewStatsKey = 'fsrs_review_stats'; // 暂未使用

  final VocabularyService _vocabularyService;
  late final FSRSAlgorithm _algorithm;
  final String userId;

  FSRSService({
    required this.userId,
    required VocabularyService vocabularyService,
  }) : _vocabularyService = vocabularyService {
    _initializeAlgorithm();
  }

  /// 初始化FSRS算法
  Future<void> _initializeAlgorithm() async {
    final customParameters = await _loadUserParameters();
    _algorithm = FSRSAlgorithm(
      userId: userId,
      customParameters: customParameters,
    );
  }

  /// 处理新单词的首次学习
  Future<WordLearningProgress> handleFirstLearning({
    required String wordId,
    required int rating, // 1-4: Again, Hard, Good, Easy
    required int responseTime, // 响应时间(毫秒)
    bool isKinesthetic = false,
    MotorMemoryData? motorData,
  }) async {
    final now = DateTime.now();

    // 计算动觉记忆质量
    double kinestheticQuality = 1.0;
    if (isKinesthetic && motorData != null) {
      kinestheticQuality = _calculateKinestheticQuality(motorData);
    }

    // 使用FSRS算法计算初始参数
    final fsrsResult = _algorithm.calculateInitialLearning(
      rating: rating,
      isKinesthetic: isKinesthetic,
      kinestheticQuality: kinestheticQuality,
    );

    // 计算下次复习时间
    final optimalInterval = _algorithm.calculateOptimalInterval(
      stability: fsrsResult.stability,
      targetRetrievability: 0.9,
    );

    // 创建FSRS复习记录
    final reviewRecord = FSRSReviewRecord(
      reviewDate: now,
      rating: rating,
      stability: fsrsResult.stability,
      difficulty: fsrsResult.difficulty,
      retrievability: fsrsResult.retrievability,
      responseTime: responseTime,
      isKinesthetic: isKinesthetic,
    );

    // 计算动觉记忆增强系数
    double kinestheticBonus = 1.0;
    if (isKinesthetic && motorData != null) {
      kinestheticBonus = _calculateKinestheticBonus(motorData);
    }

    // 创建学习进度
    final progress = WordLearningProgress(
      wordId: wordId,
      isSelected: true,
      isLearned: rating >= 3, // Good或Easy认为已学会
      reviewCount: 1,
      lastReviewedAt: now,
      firstLearnedAt: now,
      masteryLevel: _ratingToMasteryLevel(rating),
      reviewHistory: [now],
      // FSRS字段
      stability: fsrsResult.stability,
      difficulty: fsrsResult.difficulty,
      nextReviewDate: now.add(Duration(days: optimalInterval)),
      fsrsHistory: [reviewRecord],
      retrievability: fsrsResult.retrievability,
      // 动觉记忆字段
      kinestheticBonus: kinestheticBonus,
      motorMemoryHistory: motorData != null ? [motorData] : [],
    );

    // 保存进度
    await _vocabularyService.updateLearningProgress(wordId, progress);

    // 更新统计数据 (暂时注释掉)
    // await _updateReviewStats(rating, isKinesthetic, responseTime);

    return progress;
  }

  /// 处理复习
  Future<WordLearningProgress> handleReview({
    required WordLearningProgress currentProgress,
    required int rating,
    required int responseTime,
    bool isKinesthetic = false,
    MotorMemoryData? motorData,
  }) async {
    final now = DateTime.now();
    final daysSinceLastReview = currentProgress.lastReviewedAt != null
        ? now.difference(currentProgress.lastReviewedAt!).inDays
        : 1;

    // 计算当前记忆保持率
    final currentRetrievability = _algorithm.calculateRetrievability(
      stability: currentProgress.stability,
      daysSinceReview: daysSinceLastReview,
    );

    // 计算动觉记忆质量
    double kinestheticQuality = 1.0;
    if (isKinesthetic && motorData != null) {
      kinestheticQuality = _calculateKinestheticQuality(motorData);
    }

    // 使用FSRS算法计算新参数
    final fsrsResult = _algorithm.calculateReview(
      currentStability: currentProgress.stability,
      currentDifficulty: currentProgress.difficulty,
      currentRetrievability: currentRetrievability,
      rating: rating,
      daysSinceLastReview: daysSinceLastReview,
      isKinesthetic: isKinesthetic,
      kinestheticQuality: kinestheticQuality,
      motorData: motorData,
    );

    // 计算下次复习时间
    final optimalInterval = _algorithm.calculateOptimalInterval(
      stability: fsrsResult.stability,
      targetRetrievability: 0.9,
    );

    // 创建新的复习记录
    final reviewRecord = FSRSReviewRecord(
      reviewDate: now,
      rating: rating,
      stability: fsrsResult.stability,
      difficulty: fsrsResult.difficulty,
      retrievability: currentRetrievability,
      responseTime: responseTime,
      isKinesthetic: isKinesthetic,
    );

    // 更新动觉记忆数据
    List<MotorMemoryData> updatedMotorHistory = List.from(
      currentProgress.motorMemoryHistory,
    );
    if (motorData != null) {
      updatedMotorHistory.add(motorData);
      // 保留最近20次记录
      if (updatedMotorHistory.length > 20) {
        updatedMotorHistory = updatedMotorHistory.sublist(
          updatedMotorHistory.length - 20,
        );
      }
    }

    // 计算新的掌握程度
    double newMasteryLevel = _updateMasteryLevel(
      currentProgress.masteryLevel,
      rating,
      isKinesthetic,
      motorData,
    );

    // 更新动觉记忆增强系数
    double newKinestheticBonus = currentProgress.kinestheticBonus;
    if (isKinesthetic && motorData != null) {
      newKinestheticBonus = _updateKinestheticBonus(
        currentProgress.kinestheticBonus,
        motorData,
      );
    }

    // 创建更新后的进度
    final updatedProgress = currentProgress.copyWith(
      isLearned: rating >= 3 && newMasteryLevel >= 0.7,
      reviewCount: currentProgress.reviewCount + 1,
      lastReviewedAt: now,
      masteryLevel: newMasteryLevel,
      reviewHistory: [...currentProgress.reviewHistory, now],
      // FSRS字段
      stability: fsrsResult.stability,
      difficulty: fsrsResult.difficulty,
      nextReviewDate: now.add(Duration(days: optimalInterval)),
      fsrsHistory: [...currentProgress.fsrsHistory, reviewRecord],
      retrievability: fsrsResult.retrievability,
      // 动觉记忆字段
      kinestheticBonus: newKinestheticBonus,
      motorMemoryHistory: updatedMotorHistory,
    );

    // 保存进度
    await _vocabularyService.updateLearningProgress(
      currentProgress.wordId,
      updatedProgress,
    );

    // 更新统计数据 (暂时注释掉)
    // await _updateReviewStats(rating, isKinesthetic, responseTime);

    return updatedProgress;
  }

  /// 获取需要复习的单词列表
  Future<List<String>> getDueWords({int limit = 20}) async {
    final allProgress = await _vocabularyService.getLearningProgress();
    final now = DateTime.now();

    List<MapEntry<String, double>> dueWords = [];

    for (final entry in allProgress.entries) {
      final wordId = entry.key;
      final progress = entry.value;

      // 跳过未学习的单词
      if (progress.reviewCount == 0) continue;

      // 检查是否到期
      if (progress.nextReviewDate != null &&
          progress.nextReviewDate!.isBefore(now)) {
        // 计算当前记忆保持率
        final daysSinceReview = now.difference(progress.lastReviewedAt!).inDays;
        final retrievability = _algorithm.calculateRetrievability(
          stability: progress.stability,
          daysSinceReview: daysSinceReview,
        );

        // 计算优先级（记忆保持率越低，优先级越高）
        double priority = 1.0 - retrievability;

        // 困难单词优先级更高
        priority += (progress.difficulty / 10.0) * 0.3;

        // 动觉记忆单词优先级调整
        if (progress.kinestheticBonus > 1.0) {
          priority *= 0.9; // 动觉记忆单词优先级稍低（因为记忆更牢固）
        }

        dueWords.add(MapEntry(wordId, priority));
      }
    }

    // 按优先级排序
    dueWords.sort((a, b) => b.value.compareTo(a.value));

    return dueWords.take(limit).map((e) => e.key).toList();
  }

  /// 预测记忆保持率
  Future<double> predictRetrievability({
    required String wordId,
    DateTime? targetDate,
  }) async {
    final progress = await _vocabularyService.getLearningProgress();
    final wordProgress = progress[wordId];

    if (wordProgress == null || wordProgress.lastReviewedAt == null) {
      return 0.0;
    }

    final target = targetDate ?? DateTime.now();
    final daysSinceReview = target
        .difference(wordProgress.lastReviewedAt!)
        .inDays;

    return _algorithm.calculateRetrievability(
      stability: wordProgress.stability,
      daysSinceReview: daysSinceReview,
    );
  }

  /// 生成学习分析报告
  Future<Map<String, dynamic>> generateAnalysisReport() async {
    final allProgress = await _vocabularyService.getLearningProgress();
    // final stats = await _loadReviewStats(); // 暂时注释掉
    final stats = <String, dynamic>{}; // 临时空数据

    int totalWords = allProgress.length;
    int learnedWords = allProgress.values.where((p) => p.isLearned).length;
    int kinestheticWords = allProgress.values
        .where((p) => p.kinestheticBonus > 1.0)
        .length;

    // 计算平均记忆保持率
    double avgRetrievability = 0.0;
    int validWords = 0;

    for (final progress in allProgress.values) {
      if (progress.lastReviewedAt != null) {
        final daysSinceReview = DateTime.now()
            .difference(progress.lastReviewedAt!)
            .inDays;
        final retrievability = _algorithm.calculateRetrievability(
          stability: progress.stability,
          daysSinceReview: daysSinceReview,
        );
        avgRetrievability += retrievability;
        validWords++;
      }
    }

    if (validWords > 0) {
      avgRetrievability /= validWords;
    }

    return {
      'totalWords': totalWords,
      'learnedWords': learnedWords,
      'kinestheticWords': kinestheticWords,
      'learningRate': totalWords > 0
          ? (learnedWords / totalWords * 100).round()
          : 0,
      'avgRetrievability': avgRetrievability,
      'kinestheticAdvantage': kinestheticWords > 0
          ? (kinestheticWords / totalWords * 100).round()
          : 0,
      'reviewStats': stats,
    };
  }

  /// 计算动觉记忆质量
  double _calculateKinestheticQuality(MotorMemoryData motorData) {
    double quality = motorData.completionQuality;

    // 协调性加成
    quality += motorData.coordinationScore * 0.2;

    // 全身参与加成
    if (motorData.fullBodyEngagement) {
      quality += 0.1;
    }

    // 动作准确度加成
    double avgAccuracy = motorData.exerciseAccuracy.values.isNotEmpty
        ? motorData.exerciseAccuracy.values.reduce((a, b) => a + b) /
              motorData.exerciseAccuracy.length
        : 0.0;
    quality += avgAccuracy * 0.15;

    return quality.clamp(0.0, 1.0);
  }

  /// 计算动觉记忆增强系数
  double _calculateKinestheticBonus(MotorMemoryData motorData) {
    double bonus = 1.0;

    // 基于完成质量
    bonus += motorData.completionQuality * 0.3;

    // 基于协调性
    bonus += motorData.coordinationScore * 0.2;

    // 全身参与奖励
    if (motorData.fullBodyEngagement) {
      bonus += 0.15;
    }

    return bonus.clamp(1.0, 2.0);
  }

  /// 更新动觉记忆增强系数
  double _updateKinestheticBonus(
    double currentBonus,
    MotorMemoryData motorData,
  ) {
    double newBonus = _calculateKinestheticBonus(motorData);

    // 使用加权平均更新
    return (currentBonus * 0.7 + newBonus * 0.3).clamp(1.0, 2.0);
  }

  /// 评级转换为掌握程度
  double _ratingToMasteryLevel(int rating) {
    switch (rating) {
      case 1:
        return 0.2; // Again
      case 2:
        return 0.5; // Hard
      case 3:
        return 0.8; // Good
      case 4:
        return 1.0; // Easy
      default:
        return 0.0;
    }
  }

  /// 更新掌握程度
  double _updateMasteryLevel(
    double currentLevel,
    int rating,
    bool isKinesthetic,
    MotorMemoryData? motorData,
  ) {
    double sessionScore = _ratingToMasteryLevel(rating);

    // 动觉记忆加成
    if (isKinesthetic && motorData != null) {
      double kinestheticMultiplier =
          1.0 + _calculateKinestheticQuality(motorData) * 0.2;
      sessionScore *= kinestheticMultiplier;
    }

    // 加权平均更新
    double weight = 0.3;
    double newLevel = currentLevel * (1 - weight) + sessionScore * weight;

    return newLevel.clamp(0.0, 1.0);
  }

  /// 加载用户个性化参数
  Future<List<double>?> _loadUserParameters() async {
    final prefs = await SharedPreferences.getInstance();
    final parametersJson = prefs.getString('${_userParametersKey}_$userId');

    if (parametersJson != null) {
      final List<dynamic> parametersList = json.decode(parametersJson);
      return parametersList.map((e) => (e as num).toDouble()).toList();
    }

    return null;
  }

  /// 保存用户个性化参数 (暂未使用)
  /*
  Future<void> _saveUserParameters(List<double> parameters) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(
      '${_userParametersKey}_$userId',
      json.encode(parameters),
    );
  }

  /// 加载复习统计数据
  Future<Map<String, dynamic>> _loadReviewStats() async {
    final prefs = await SharedPreferences.getInstance();
    final statsJson = prefs.getString('${_reviewStatsKey}_$userId');

    if (statsJson != null) {
      return json.decode(statsJson);
    }

    return {
      'totalReviews': 0,
      'kinestheticReviews': 0,
      'avgResponseTime': 0,
      'avgRating': 0.0,
    };
  }

  /// 更新复习统计数据
  Future<void> _updateReviewStats(
    int rating,
    bool isKinesthetic,
    int responseTime,
  ) async {
    final stats = await _loadReviewStats();

    stats['totalReviews'] = (stats['totalReviews'] ?? 0) + 1;
    if (isKinesthetic) {
      stats['kinestheticReviews'] = (stats['kinestheticReviews'] ?? 0) + 1;
    }

    // 更新平均响应时间
    int totalReviews = stats['totalReviews'];
    int currentAvgTime = stats['avgResponseTime'] ?? 0;
    stats['avgResponseTime'] =
        ((currentAvgTime * (totalReviews - 1)) + responseTime) ~/ totalReviews;

    // 更新平均评级
    double currentAvgRating = (stats['avgRating'] ?? 0.0).toDouble();
    stats['avgRating'] =
        ((currentAvgRating * (totalReviews - 1)) + rating) / totalReviews;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('${_reviewStatsKey}_$userId', json.encode(stats));
  }
  */
}
