import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'vocabulary_service.dart';
import 'word_model.dart';
import 'vocabulary_category_page.dart';
import 'create_vocabulary_page.dart';
import 'vocabulary_statistics_page.dart';
import 'word_root_service.dart';

/// 词汇管理主界面
class VocabularyManagerPage extends ConsumerStatefulWidget {
  const VocabularyManagerPage({super.key});

  @override
  ConsumerState<VocabularyManagerPage> createState() =>
      _VocabularyManagerPageState();
}

class _VocabularyManagerPageState extends ConsumerState<VocabularyManagerPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _sortBy = 'name'; // name, totalWords, selectedWords, createdAt
  bool _sortAscending = true;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final categoriesAsync = ref.watch(vocabularyCategoriesProvider);
    final statisticsAsync = ref.watch(learningStatisticsProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          '词汇管理',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF37352F)),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(
              Icons.analytics_outlined,
              color: Color(0xFF787774),
            ),
            onPressed: _navigateToStatistics,
          ),
          IconButton(
            icon: const Icon(Icons.sort, color: Color(0xFF787774)),
            onPressed: _showSortOptions,
          ),
          IconButton(
            icon: const Icon(Icons.account_tree, color: Color(0xFF787774)),
            onPressed: _showRootSortOptions,
          ),
          IconButton(
            icon: const Icon(Icons.add, color: Color(0xFF2F76DA)),
            onPressed: _showCreateOptions,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        child: CustomScrollView(
          slivers: [
            // 学习统计概览
            SliverToBoxAdapter(
              child: statisticsAsync.when(
                loading: () => const _StatisticsLoadingCard(),
                error: (error, stack) => const SizedBox.shrink(),
                data: (stats) => _StatisticsCard(statistics: stats),
              ),
            ),

            // 搜索栏
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: '搜索词库...',
                    hintStyle: const TextStyle(color: Color(0xFF9B9A97)),
                    prefixIcon: const Icon(
                      Icons.search,
                      color: Color(0xFF9B9A97),
                    ),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(
                              Icons.clear,
                              color: Color(0xFF9B9A97),
                            ),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFF2F76DA)),
                    ),
                    filled: true,
                    fillColor: const Color(0xFFF7F6F3),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
            ),

            // 分类列表标题
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(24, 24, 24, 16),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        '词汇分类',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF37352F),
                        ),
                      ),
                    ),
                    Text(
                      '排序: ${_getSortLabel()}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF787774),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            categoriesAsync.when(
              loading: () => const SliverToBoxAdapter(
                child: Center(child: CircularProgressIndicator()),
              ),
              error: (error, stack) => SliverToBoxAdapter(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text('加载失败: $error'),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () =>
                            ref.refresh(vocabularyCategoriesProvider),
                        child: const Text('重试'),
                      ),
                    ],
                  ),
                ),
              ),
              data: (categories) {
                // 过滤和排序分类
                final filteredCategories = _filterAndSortCategories(categories);

                if (filteredCategories.isEmpty) {
                  return const SliverToBoxAdapter(
                    child: Center(
                      child: Padding(
                        padding: EdgeInsets.all(48),
                        child: Column(
                          children: [
                            Icon(
                              Icons.search_off,
                              size: 48,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16),
                            Text(
                              '没有找到匹配的词库',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }

                return SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate((context, index) {
                      final category = filteredCategories[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: _CategoryCard(
                          category: category,
                          onTap: () => _navigateToCategory(category),
                        ),
                      );
                    }, childCount: filteredCategories.length),
                  ),
                );
              },
            ),

            // 底部间距
            const SliverToBoxAdapter(child: SizedBox(height: 100)),
          ],
        ),
      ),
    );
  }

  /// 显示创建选项
  void _showCreateOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                const Expanded(
                  child: Text(
                    '创建词库',
                    style: TextStyle(
                      color: Color(0xFF37352F),
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => context.pop(),
                  icon: const Icon(Icons.close, color: Color(0xFF9B9A97)),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 考研词库管理选项
            _CreateOptionTile(
              icon: Icons.school,
              title: '考研词库管理',
              subtitle: '管理考研词汇的选择状态',
              onTap: () {
                context.pop();
                _navigateToGraduateVocabularyManager();
              },
            ),

            const SizedBox(height: 16),

            // 创建词库选项
            _CreateOptionTile(
              icon: Icons.library_books,
              title: '创建词库',
              subtitle: '创建自定义词汇库',
              onTap: () {
                context.pop();
                _navigateToCreateVocabulary();
              },
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  /// 导航到分类页面
  void _navigateToCategory(VocabularyCategory category) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => VocabularyCategoryPage(category: category),
      ),
    );
  }

  /// 导航到考研词库管理页面
  void _navigateToGraduateVocabularyManager() {
    context.push('/graduate-vocabulary-manager');
  }

  /// 导航到创建词库页面
  void _navigateToCreateVocabulary() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const CreateVocabularyPage()),
    );
  }

  /// 导航到统计页面
  void _navigateToStatistics() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const VocabularyStatisticsPage()),
    );
  }

  /// 显示词根排序选项
  void _showRootSortOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _RootSortBottomSheet(),
    );
  }

  /// 处理下拉刷新（静默模式）
  Future<void> _handleRefresh() async {
    print('🔄 词汇管理页：开始静默刷新');
    ref.invalidate(vocabularyCategoriesProvider);
    ref.invalidate(learningStatisticsProvider);
    print('✅ 词汇管理页：静默刷新完成');
  }

  /// 过滤和排序分类
  List<VocabularyCategory> _filterAndSortCategories(
    List<VocabularyCategory> categories,
  ) {
    // 过滤
    var filtered = categories.where((category) {
      if (_searchQuery.isEmpty) return true;
      final query = _searchQuery.toLowerCase();
      return category.name.toLowerCase().contains(query) ||
          category.description.toLowerCase().contains(query);
    }).toList();

    // 排序
    filtered.sort((a, b) {
      int comparison = 0;

      switch (_sortBy) {
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'totalWords':
          comparison = a.totalWords.compareTo(b.totalWords);
          break;
        case 'selectedWords':
          comparison = a.selectedWords.compareTo(b.selectedWords);
          break;
        case 'createdAt':
          if (a.createdAt != null && b.createdAt != null) {
            comparison = a.createdAt!.compareTo(b.createdAt!);
          } else if (a.createdAt != null) {
            comparison = 1;
          } else if (b.createdAt != null) {
            comparison = -1;
          }
          break;
      }

      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  /// 显示排序选项
  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                const Expanded(
                  child: Text(
                    '排序选项',
                    style: TextStyle(
                      color: Color(0xFF37352F),
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Color(0xFF9B9A97)),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 排序选项
            _SortOptionTile(
              title: '按名称',
              value: 'name',
              currentValue: _sortBy,
              ascending: _sortAscending,
              onChanged: _updateSort,
            ),
            _SortOptionTile(
              title: '按总词汇数',
              value: 'totalWords',
              currentValue: _sortBy,
              ascending: _sortAscending,
              onChanged: _updateSort,
            ),
            _SortOptionTile(
              title: '按已选择数',
              value: 'selectedWords',
              currentValue: _sortBy,
              ascending: _sortAscending,
              onChanged: _updateSort,
            ),
            _SortOptionTile(
              title: '按创建时间',
              value: 'createdAt',
              currentValue: _sortBy,
              ascending: _sortAscending,
              onChanged: _updateSort,
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  /// 更新排序
  void _updateSort(String sortBy, bool ascending) {
    setState(() {
      _sortBy = sortBy;
      _sortAscending = ascending;
    });
    Navigator.of(context).pop();
  }

  /// 获取排序标签
  String _getSortLabel() {
    String label;
    switch (_sortBy) {
      case 'name':
        label = '名称';
        break;
      case 'totalWords':
        label = '总词汇数';
        break;
      case 'selectedWords':
        label = '已选择数';
        break;
      case 'createdAt':
        label = '创建时间';
        break;
      default:
        label = '名称';
    }
    return '$label${_sortAscending ? '↑' : '↓'}';
  }
}

/// 统计卡片
class _StatisticsCard extends StatelessWidget {
  final Map<String, dynamic> statistics;

  const _StatisticsCard({required this.statistics});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(24),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '学习概览',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _StatItem(
                  label: '总词汇',
                  value: statistics['totalWords'].toString(),
                  color: const Color(0xFF2F76DA),
                ),
              ),
              Expanded(
                child: _StatItem(
                  label: '已学习',
                  value: statistics['learnedWords'].toString(),
                  color: const Color(0xFF0F7B6C),
                ),
              ),
              Expanded(
                child: _StatItem(
                  label: '已选择',
                  value: statistics['selectedWords'].toString(),
                  color: const Color(0xFFD9730D),
                ),
              ),
              Expanded(
                child: _StatItem(
                  label: '学习率',
                  value: '${statistics['learningRate']}%',
                  color: const Color(0xFF7C3AED),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// 统计项
class _StatItem extends StatelessWidget {
  final String label;
  final String value;
  final Color color;

  const _StatItem({
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Color(0xFF787774)),
        ),
      ],
    );
  }
}

/// 词根排序底部弹窗
class _RootSortBottomSheet extends ConsumerStatefulWidget {
  @override
  ConsumerState<_RootSortBottomSheet> createState() =>
      _RootSortBottomSheetState();
}

class _RootSortBottomSheetState extends ConsumerState<_RootSortBottomSheet> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Row(
            children: [
              const Expanded(
                child: Text(
                  '按词根浏览',
                  style: TextStyle(
                    color: Color(0xFF37352F),
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close, color: Color(0xFF9B9A97)),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // 说明文字
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFFE8F4FD),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFF2F76DA).withValues(alpha: 0.2),
              ),
            ),
            child: const Text(
              '按词根分组显示单词，帮助您更好地理解单词的构成和记忆规律',
              style: TextStyle(
                color: Color(0xFF37352F),
                fontSize: 14,
                height: 1.4,
              ),
            ),
          ),

          const SizedBox(height: 24),

          // 词根浏览按钮
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _navigateToRootBrowser();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2F76DA),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
              icon: const Icon(Icons.account_tree, size: 20),
              label: const Text(
                '按词根浏览单词',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ),
          ),

          const SizedBox(height: 24),
        ],
      ),
    );
  }

  void _navigateToRootBrowser() {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const WordRootBrowserPage()),
    );
  }
}

/// 词根浏览页面
class WordRootBrowserPage extends ConsumerStatefulWidget {
  const WordRootBrowserPage({super.key});

  @override
  ConsumerState<WordRootBrowserPage> createState() =>
      _WordRootBrowserPageState();
}

class _WordRootBrowserPageState extends ConsumerState<WordRootBrowserPage> {
  final String _selectedCategory = 'graduate_exam';
  Map<String, List<MapEntry<String, WordDetails>>> _wordsByRoot = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadWordsByRoot();
  }

  Future<void> _loadWordsByRoot() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final vocabularyService = ref.read(vocabularyServiceProvider);
      final wordRootService = ref.read(wordRootServiceProvider);

      // 获取指定分类的单词
      final words = await vocabularyService.getWordsByCategory(
        _selectedCategory,
      );

      // 按词根分组
      final groupedWords = await wordRootService.groupWordsByRoot(words);

      setState(() {
        _wordsByRoot = groupedWords;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('加载失败: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          '词根浏览',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF37352F)),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _wordsByRoot.isEmpty
          ? const Center(
              child: Text(
                '暂无数据',
                style: TextStyle(color: Color(0xFF787774), fontSize: 16),
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _wordsByRoot.length,
              itemBuilder: (context, index) {
                final rootEntry = _wordsByRoot.entries.elementAt(index);
                final root = rootEntry.key;
                final words = rootEntry.value;

                return Card(
                  margin: const EdgeInsets.only(bottom: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ExpansionTile(
                    title: Text(
                      '词根: $root',
                      style: const TextStyle(
                        color: Color(0xFF37352F),
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    subtitle: Text(
                      '${words.length} 个单词',
                      style: const TextStyle(
                        color: Color(0xFF787774),
                        fontSize: 14,
                      ),
                    ),
                    children: words.map((wordEntry) {
                      final word = wordEntry.key;
                      final details = wordEntry.value;

                      return ListTile(
                        title: Text(
                          word,
                          style: const TextStyle(
                            color: Color(0xFF37352F),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        subtitle: Text(
                          details.definition,
                          style: const TextStyle(
                            color: Color(0xFF787774),
                            fontSize: 13,
                          ),
                        ),
                        dense: true,
                      );
                    }).toList(),
                  ),
                );
              },
            ),
    );
  }
}

/// 统计加载卡片
class _StatisticsLoadingCard extends StatelessWidget {
  const _StatisticsLoadingCard();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(24),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
      ),
      child: const Center(child: CircularProgressIndicator()),
    );
  }
}

/// 分类卡片
class _CategoryCard extends StatelessWidget {
  final VocabularyCategory category;
  final VoidCallback onTap;

  const _CategoryCard({required this.category, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xFFE3E2E0)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.04),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // 图标
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: category.isCustom
                    ? const Color(0xFF7C3AED).withValues(alpha: 0.1)
                    : const Color(0xFF2F76DA).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                category.isCustom ? Icons.library_books : Icons.school,
                color: category.isCustom
                    ? const Color(0xFF7C3AED)
                    : const Color(0xFF2F76DA),
                size: 24,
              ),
            ),

            const SizedBox(width: 16),

            // 内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          category.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF37352F),
                          ),
                        ),
                      ),
                      Text(
                        '${category.selectedWords}/${category.totalWords}',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF2F76DA),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    category.description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF787774),
                    ),
                  ),
                  if (category.isCustom && category.createdAt != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      '创建于 ${_formatDate(category.createdAt!)}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF9B9A97),
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // 箭头
            const Icon(Icons.chevron_right, color: Color(0xFF9B9A97), size: 20),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}

/// 创建选项瓦片
class _CreateOptionTile extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _CreateOptionTile({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFFF7F6F3),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: const Color(0xFFE3E2E0)),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFF2F76DA).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: const Color(0xFF2F76DA), size: 20),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF787774),
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.chevron_right, color: Color(0xFF9B9A97), size: 20),
          ],
        ),
      ),
    );
  }
}

/// 排序选项瓦片
class _SortOptionTile extends StatelessWidget {
  final String title;
  final String value;
  final String currentValue;
  final bool ascending;
  final Function(String, bool) onChanged;

  const _SortOptionTile({
    required this.title,
    required this.value,
    required this.currentValue,
    required this.ascending,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final isSelected = value == currentValue;

    return GestureDetector(
      onTap: () {
        if (isSelected) {
          // 如果已选中，切换升序/降序
          onChanged(value, !ascending);
        } else {
          // 如果未选中，选中并设为升序
          onChanged(value, true);
        }
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF2F76DA).withValues(alpha: 0.1)
              : const Color(0xFFF7F6F3),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? const Color(0xFF2F76DA)
                : const Color(0xFFE3E2E0),
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  color: isSelected
                      ? const Color(0xFF2F76DA)
                      : const Color(0xFF37352F),
                ),
              ),
            ),
            if (isSelected) ...[
              Icon(
                ascending ? Icons.arrow_upward : Icons.arrow_downward,
                color: const Color(0xFF2F76DA),
                size: 20,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
