import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'word_model.dart';

/// 词汇缓存管理器 - 负责大数据集的性能优化
class VocabularyCacheManager {
  static const String _cacheKeyPrefix = 'vocab_cache_';
  static const String _metadataCacheKey = 'vocab_metadata_cache';
  static const String _indexCacheKey = 'vocab_index_cache';
  static const int _batchSize = 500; // 每批处理的单词数量
  static const int _maxCacheAge = 7 * 24 * 60 * 60 * 1000; // 7天缓存有效期

  /// 缓存词汇数据
  static Future<void> cacheVocabularyData(Vocabulary vocabulary) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // 缓存元数据
      await prefs.setString(
        _metadataCacheKey,
        json.encode({
          'metadata': vocabulary.metadata.toJson(),
          'timestamp': timestamp,
        }),
      );

      // 分批缓存单词数据
      final words = vocabulary.words.entries.toList();
      final batches = _splitIntoBatches(words, _batchSize);

      // 创建索引
      final index = <String, int>{};

      for (int i = 0; i < batches.length; i++) {
        final batch = batches[i];
        final batchKey = '${_cacheKeyPrefix}batch_$i';

        // 构建批次数据
        final batchData = <String, dynamic>{};
        for (final entry in batch) {
          batchData[entry.key] = entry.value.toJson();
          index[entry.key] = i; // 记录单词在哪个批次中
        }

        // 缓存批次数据
        await prefs.setString(
          batchKey,
          json.encode({'data': batchData, 'timestamp': timestamp}),
        );
      }

      // 缓存索引
      await prefs.setString(
        _indexCacheKey,
        json.encode({
          'index': index,
          'batchCount': batches.length,
          'timestamp': timestamp,
        }),
      );

      debugPrint('词汇数据缓存完成: ${words.length} 个单词，${batches.length} 个批次');
    } catch (e) {
      debugPrint('缓存词汇数据失败: $e');
      rethrow;
    }
  }

  /// 从缓存加载词汇数据
  static Future<Vocabulary?> loadCachedVocabulary() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 检查元数据缓存
      final metadataCache = prefs.getString(_metadataCacheKey);
      if (metadataCache == null) return null;

      final metadataJson = json.decode(metadataCache);
      final timestamp = metadataJson['timestamp'] as int;

      // 检查缓存是否过期
      if (_isCacheExpired(timestamp)) {
        await clearCache();
        return null;
      }

      // 加载元数据
      final metadata = VocabularyMetadata.fromJson(metadataJson['metadata']);

      // 加载索引
      final indexCache = prefs.getString(_indexCacheKey);
      if (indexCache == null) return null;

      final indexJson = json.decode(indexCache);
      final batchCount = indexJson['batchCount'] as int;

      // 加载所有批次数据
      final allWords = <String, WordDetails>{};

      for (int i = 0; i < batchCount; i++) {
        final batchKey = '${_cacheKeyPrefix}batch_$i';
        final batchCache = prefs.getString(batchKey);

        if (batchCache != null) {
          final batchJson = json.decode(batchCache);
          final batchData = Map<String, dynamic>.from(batchJson['data']);

          for (final entry in batchData.entries) {
            allWords[entry.key] = WordDetails.fromJson(entry.value);
          }
        }
      }

      debugPrint('从缓存加载词汇数据: ${allWords.length} 个单词');

      return Vocabulary(metadata: metadata, words: allWords);
    } catch (e) {
      debugPrint('加载缓存词汇数据失败: $e');
      return null;
    }
  }

  /// 按需加载单词批次
  static Future<Map<String, WordDetails>?> loadWordBatch(
    List<String> words,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 加载索引
      final indexCache = prefs.getString(_indexCacheKey);
      if (indexCache == null) return null;

      final indexJson = json.decode(indexCache);
      final index = Map<String, int>.from(indexJson['index']);

      // 按批次分组需要加载的单词
      final batchGroups = <int, List<String>>{};
      for (final word in words) {
        final batchIndex = index[word];
        if (batchIndex != null) {
          batchGroups.putIfAbsent(batchIndex, () => []).add(word);
        }
      }

      // 加载相关批次
      final result = <String, WordDetails>{};
      for (final entry in batchGroups.entries) {
        final batchIndex = entry.key;
        final wordsInBatch = entry.value;

        final batchKey = '${_cacheKeyPrefix}batch_$batchIndex';
        final batchCache = prefs.getString(batchKey);

        if (batchCache != null) {
          final batchJson = json.decode(batchCache);
          final batchData = Map<String, dynamic>.from(batchJson['data']);

          for (final word in wordsInBatch) {
            if (batchData.containsKey(word)) {
              result[word] = WordDetails.fromJson(batchData[word]);
            }
          }
        }
      }

      return result;
    } catch (e) {
      debugPrint('按需加载单词批次失败: $e');
      return null;
    }
  }

  /// 搜索缓存中的单词
  static Future<List<MapEntry<String, WordDetails>>> searchCachedWords(
    String query, {
    int limit = 50,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 加载索引
      final indexCache = prefs.getString(_indexCacheKey);
      if (indexCache == null) return [];

      final indexJson = json.decode(indexCache);
      final batchCount = indexJson['batchCount'] as int;

      final results = <MapEntry<String, WordDetails>>[];
      final queryLower = query.toLowerCase();

      // 搜索所有批次
      for (int i = 0; i < batchCount && results.length < limit; i++) {
        final batchKey = '${_cacheKeyPrefix}batch_$i';
        final batchCache = prefs.getString(batchKey);

        if (batchCache != null) {
          final batchJson = json.decode(batchCache);
          final batchData = Map<String, dynamic>.from(batchJson['data']);

          for (final entry in batchData.entries) {
            if (results.length >= limit) break;

            final word = entry.key;
            final wordData = entry.value;

            // 检查是否匹配搜索条件
            if (word.contains(queryLower) ||
                (wordData['definition'] as String).toLowerCase().contains(
                  queryLower,
                )) {
              results.add(MapEntry(word, WordDetails.fromJson(wordData)));
            }
          }
        }
      }

      return results;
    } catch (e) {
      debugPrint('搜索缓存单词失败: $e');
      return [];
    }
  }

  /// 更新单个单词的缓存
  static Future<void> updateWordInCache(
    String word,
    WordDetails details,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 加载索引
      final indexCache = prefs.getString(_indexCacheKey);
      if (indexCache == null) return;

      final indexJson = json.decode(indexCache);
      final index = Map<String, int>.from(indexJson['index']);

      final batchIndex = index[word];
      if (batchIndex == null) return;

      // 加载对应批次
      final batchKey = '${_cacheKeyPrefix}batch_$batchIndex';
      final batchCache = prefs.getString(batchKey);

      if (batchCache != null) {
        final batchJson = json.decode(batchCache);
        final batchData = Map<String, dynamic>.from(batchJson['data']);

        // 更新单词数据
        batchData[word] = details.toJson();

        // 保存更新后的批次
        await prefs.setString(
          batchKey,
          json.encode({
            'data': batchData,
            'timestamp': DateTime.now().millisecondsSinceEpoch,
          }),
        );
      }
    } catch (e) {
      debugPrint('更新单词缓存失败: $e');
    }
  }

  /// 清除所有缓存
  static Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 获取所有缓存键
      final keys = prefs
          .getKeys()
          .where(
            (key) =>
                key.startsWith(_cacheKeyPrefix) ||
                key == _metadataCacheKey ||
                key == _indexCacheKey,
          )
          .toList();

      // 删除所有缓存
      for (final key in keys) {
        await prefs.remove(key);
      }

      debugPrint('词汇缓存已清除');
    } catch (e) {
      debugPrint('清除缓存失败: $e');
    }
  }

  /// 获取缓存统计信息
  static Future<Map<String, dynamic>> getCacheStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final metadataCache = prefs.getString(_metadataCacheKey);
      final indexCache = prefs.getString(_indexCacheKey);

      if (metadataCache == null || indexCache == null) {
        return {'cached': false};
      }

      final metadataJson = json.decode(metadataCache);
      final indexJson = json.decode(indexCache);

      final timestamp = metadataJson['timestamp'] as int;
      final batchCount = indexJson['batchCount'] as int;
      final wordCount = (indexJson['index'] as Map).length;

      return {
        'cached': true,
        'wordCount': wordCount,
        'batchCount': batchCount,
        'cacheAge': DateTime.now().millisecondsSinceEpoch - timestamp,
        'isExpired': _isCacheExpired(timestamp),
      };
    } catch (e) {
      return {'cached': false, 'error': e.toString()};
    }
  }

  /// 检查缓存是否过期
  static bool _isCacheExpired(int timestamp) {
    final age = DateTime.now().millisecondsSinceEpoch - timestamp;
    return age > _maxCacheAge;
  }

  /// 将列表分割为批次
  static List<List<T>> _splitIntoBatches<T>(List<T> items, int batchSize) {
    final batches = <List<T>>[];
    for (int i = 0; i < items.length; i += batchSize) {
      final end = (i + batchSize < items.length) ? i + batchSize : items.length;
      batches.add(items.sublist(i, end));
    }
    return batches;
  }
}
