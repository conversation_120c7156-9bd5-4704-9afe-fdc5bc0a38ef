
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/data/pao_exercises_data.dart';
import '../exercise/exercise_session_page.dart';
import 'vocabulary_service.dart';
import 'word_model.dart';

/// 词汇学习服务 - 负责将词汇学习与PAO动作系统集成
class VocabularyLearningService {
  final VocabularyService _vocabularyService;

  VocabularyLearningService(this._vocabularyService);

  /// 为单词生成PAO动作序列
  List<PAOExercise> generatePAOExercisesForWord(String word) {
    final exercises = <PAOExercise>[];
    final allExercises = PAOExercisesData.getAllExercises();
    
    for (int i = 0; i < word.length; i++) {
      final letter = word[i].toUpperCase();
      final exercise = allExercises[letter];
      if (exercise != null) {
        exercises.add(exercise);
      }
    }
    
    return exercises;
  }

  /// 为多个单词生成学习会话
  VocabularyLearningSession createLearningSession({
    required List<String> words,
    required List<MapEntry<String, WordDetails>> wordDetails,
    String sessionType = 'vocabulary',
    int wordsPerSession = 5,
  }) {
    // 限制每次学习的单词数量
    final sessionWords = words.take(wordsPerSession).toList();
    final sessionWordDetails = wordDetails
        .where((entry) => sessionWords.contains(entry.key))
        .toList();

    return VocabularyLearningSession(
      words: sessionWords,
      wordDetails: sessionWordDetails,
      sessionType: sessionType,
      createdAt: DateTime.now(),
    );
  }

  /// 开始词汇学习会话
  Map<String, dynamic> prepareExerciseSession(VocabularyLearningSession session) {
    if (session.words.isEmpty) {
      throw Exception('学习会话中没有单词');
    }

    // 选择第一个单词开始学习
    final firstWord = session.words.first;
    final wordDetail = session.wordDetails
        .firstWhere((entry) => entry.key == firstWord)
        .value;

    final exercises = generatePAOExercisesForWord(firstWord);
    
    if (exercises.isEmpty) {
      throw Exception('无法为单词 "$firstWord" 生成PAO动作');
    }

    return {
      'exercises': exercises,
      'paoWord': firstWord,
      'wordPhonetic': wordDetail.phonetic ?? '/${firstWord.toLowerCase()}/',
      'wordMeaning': wordDetail.definition,
      'mode': ExerciseMode.pao,
      'duration': _calculateDuration(firstWord, exercises),
      'session': session,
    };
  }

  /// 计算学习时长
  int _calculateDuration(String word, List<PAOExercise> exercises) {
    // 基础时长：每个字母15秒
    int baseDuration = word.length * 15;
    
    // 根据动作复杂度调整
    int complexityBonus = 0;
    for (final exercise in exercises) {
      if (exercise.category == '健身' || exercise.category == '篮球技巧') {
        complexityBonus += 10; // 复杂动作额外时间
      } else if (exercise.category == '眼部保健' || exercise.category == '办公室拉伸') {
        complexityBonus += 5; // 简单动作少量时间
      } else {
        complexityBonus += 8; // 中等复杂度
      }
    }
    
    return baseDuration + complexityBonus;
  }

  /// 处理学习完成后的进度更新
  Future<void> handleLearningCompletion({
    required String word,
    required bool completed,
    required double masteryScore, // 0.0 - 1.0
    String? categoryId,
  }) async {
    final currentProgress = await _vocabularyService.getLearningProgress();
    final existingProgress = currentProgress[word];

    final now = DateTime.now();
    final newProgress = WordLearningProgress(
      wordId: word,
      isSelected: existingProgress?.isSelected ?? true,
      isLearned: completed && masteryScore >= 0.7, // 70%以上认为已学会
      reviewCount: (existingProgress?.reviewCount ?? 0) + 1,
      lastReviewedAt: now,
      firstLearnedAt: existingProgress?.firstLearnedAt ?? 
          (completed ? now : null),
      masteryLevel: _updateMasteryLevel(
        existingProgress?.masteryLevel ?? 0.0,
        masteryScore,
        completed,
      ),
      reviewHistory: [
        ...(existingProgress?.reviewHistory ?? []),
        now,
      ],
    );

    await _vocabularyService.updateLearningProgress(word, newProgress);
  }

  /// 更新掌握程度
  double _updateMasteryLevel(double currentLevel, double sessionScore, bool completed) {
    if (!completed) {
      // 未完成学习，掌握程度略微下降
      return (currentLevel * 0.9).clamp(0.0, 1.0);
    }

    // 使用加权平均更新掌握程度
    final weight = 0.3; // 新学习的权重
    final newLevel = currentLevel * (1 - weight) + sessionScore * weight;
    return newLevel.clamp(0.0, 1.0);
  }

  /// 获取下一个学习单词
  String? getNextWordInSession(VocabularyLearningSession session, String currentWord) {
    final currentIndex = session.words.indexOf(currentWord);
    if (currentIndex == -1 || currentIndex >= session.words.length - 1) {
      return null; // 没有下一个单词
    }
    return session.words[currentIndex + 1];
  }

  /// 生成学习报告
  Future<VocabularyLearningReport> generateLearningReport({
    required List<String> words,
    String? categoryId,
  }) async {
    final progress = await _vocabularyService.getLearningProgress();
    
    int learnedCount = 0;
    int reviewedCount = 0;
    double averageMastery = 0.0;
    List<String> strugglingWords = [];
    List<String> masteredWords = [];

    for (final word in words) {
      final wordProgress = progress[word];
      if (wordProgress != null) {
        if (wordProgress.isLearned) {
          learnedCount++;
          masteredWords.add(word);
        }
        if (wordProgress.reviewCount > 0) {
          reviewedCount++;
        }
        averageMastery += wordProgress.masteryLevel;
        
        // 识别困难单词（复习次数多但掌握程度低）
        if (wordProgress.reviewCount >= 3 && wordProgress.masteryLevel < 0.5) {
          strugglingWords.add(word);
        }
      }
    }

    if (words.isNotEmpty) {
      averageMastery /= words.length;
    }

    return VocabularyLearningReport(
      totalWords: words.length,
      learnedWords: learnedCount,
      reviewedWords: reviewedCount,
      averageMasteryLevel: averageMastery,
      strugglingWords: strugglingWords,
      masteredWords: masteredWords,
      generatedAt: DateTime.now(),
    );
  }

  /// 推荐复习单词
  Future<List<String>> getReviewRecommendations({
    String? categoryId,
    int limit = 10,
  }) async {
    final progress = await _vocabularyService.getLearningProgress();
    final now = DateTime.now();
    
    final reviewCandidates = <String, double>{};
    
    for (final entry in progress.entries) {
      final word = entry.key;
      final wordProgress = entry.value;
      
      // 跳过未学习的单词
      if (wordProgress.reviewCount == 0) continue;
      
      // 计算复习优先级
      double priority = 0.0;
      
      // 基于遗忘曲线的时间因子
      if (wordProgress.lastReviewedAt != null) {
        final daysSinceReview = now.difference(wordProgress.lastReviewedAt!).inDays;
        final expectedInterval = _calculateReviewInterval(wordProgress.reviewCount);
        if (daysSinceReview >= expectedInterval) {
          priority += (daysSinceReview / expectedInterval) * 0.5;
        }
      }
      
      // 基于掌握程度的因子（掌握程度越低，优先级越高）
      priority += (1.0 - wordProgress.masteryLevel) * 0.3;
      
      // 基于复习次数的因子（复习次数少的优先）
      priority += (1.0 / (wordProgress.reviewCount + 1)) * 0.2;
      
      if (priority > 0.1) { // 只考虑有一定优先级的单词
        reviewCandidates[word] = priority;
      }
    }
    
    // 按优先级排序并返回
    final sortedWords = reviewCandidates.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedWords.take(limit).map((e) => e.key).toList();
  }

  /// 计算复习间隔
  int _calculateReviewInterval(int reviewCount) {
    // 基于艾宾浩斯遗忘曲线
    switch (reviewCount) {
      case 1: return 1;   // 1天后
      case 2: return 3;   // 3天后
      case 3: return 7;   // 1周后
      case 4: return 15;  // 2周后
      case 5: return 30;  // 1个月后
      default: return 60; // 2个月后
    }
  }
}

/// 词汇学习会话模型
class VocabularyLearningSession {
  final List<String> words;
  final List<MapEntry<String, WordDetails>> wordDetails;
  final String sessionType;
  final DateTime createdAt;

  VocabularyLearningSession({
    required this.words,
    required this.wordDetails,
    required this.sessionType,
    required this.createdAt,
  });
}

/// 词汇学习报告模型
class VocabularyLearningReport {
  final int totalWords;
  final int learnedWords;
  final int reviewedWords;
  final double averageMasteryLevel;
  final List<String> strugglingWords;
  final List<String> masteredWords;
  final DateTime generatedAt;

  VocabularyLearningReport({
    required this.totalWords,
    required this.learnedWords,
    required this.reviewedWords,
    required this.averageMasteryLevel,
    required this.strugglingWords,
    required this.masteredWords,
    required this.generatedAt,
  });

  double get learningProgress => totalWords > 0 ? learnedWords / totalWords : 0.0;
  double get reviewProgress => totalWords > 0 ? reviewedWords / totalWords : 0.0;
}

// Riverpod Providers
final vocabularyLearningServiceProvider = Provider<VocabularyLearningService>((ref) {
  final vocabularyService = ref.watch(vocabularyServiceProvider);
  return VocabularyLearningService(vocabularyService);
});
