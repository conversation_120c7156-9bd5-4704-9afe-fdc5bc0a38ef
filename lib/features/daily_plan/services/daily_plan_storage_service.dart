import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/daily_plan.dart';

/// 每日计划本地存储服务
class DailyPlanStorageService {
  static const String _storageKey = 'daily_plans_v1';
  static const String _backupKey = 'daily_plans_backup_v1';
  static const String _metadataKey = 'daily_plans_metadata_v1';

  /// 保存所有计划数据
  static Future<bool> saveAllPlans(Map<String, DailyPlan> plans) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 创建备份
      await _createBackup(prefs);
      
      // 准备数据
      final Map<String, dynamic> jsonData = {};
      plans.forEach((key, plan) {
        jsonData[key] = plan.toJson();
      });
      
      // 保存主数据
      final success = await prefs.setString(_storageKey, json.encode(jsonData));
      
      if (success) {
        // 更新元数据
        await _updateMetadata(prefs, plans.length);
        print('💾 已保存 ${plans.length} 个每日计划到本地存储');
        return true;
      }
      
      return false;
    } catch (e) {
      print('❌ 保存每日计划数据失败: $e');
      return false;
    }
  }

  /// 加载所有计划数据
  static Future<Map<String, DailyPlan>> loadAllPlans() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_storageKey);
      
      if (jsonString == null) {
        print('📝 首次使用，返回空的计划数据');
        return {};
      }
      
      final Map<String, dynamic> jsonData = json.decode(jsonString);
      final Map<String, DailyPlan> plans = {};
      
      jsonData.forEach((key, value) {
        try {
          plans[key] = DailyPlan.fromJson(value as Map<String, dynamic>);
        } catch (e) {
          print('❌ 解析计划数据失败: $key, $e');
        }
      });
      
      print('✅ 已加载 ${plans.length} 个每日计划');
      return plans;
    } catch (e) {
      print('❌ 加载每日计划数据失败: $e');
      
      // 尝试从备份恢复
      return await _restoreFromBackup();
    }
  }

  /// 保存单个计划
  static Future<bool> savePlan(DailyPlan plan) async {
    try {
      // 先加载所有数据
      final allPlans = await loadAllPlans();
      
      // 更新或添加计划
      allPlans[plan.storageKey] = plan;
      
      // 保存所有数据
      return await saveAllPlans(allPlans);
    } catch (e) {
      print('❌ 保存单个计划失败: $e');
      return false;
    }
  }

  /// 删除单个计划
  static Future<bool> deletePlan(DateTime date, DailyPlanType type) async {
    try {
      // 先加载所有数据
      final allPlans = await loadAllPlans();
      
      // 生成存储键
      final storageKey = '${_formatDate(date)}_${type.name}';
      
      // 删除计划
      allPlans.remove(storageKey);
      
      // 保存所有数据
      return await saveAllPlans(allPlans);
    } catch (e) {
      print('❌ 删除计划失败: $e');
      return false;
    }
  }

  /// 获取单个计划
  static Future<DailyPlan?> getPlan(DateTime date, DailyPlanType type) async {
    try {
      final allPlans = await loadAllPlans();
      final storageKey = '${_formatDate(date)}_${type.name}';
      return allPlans[storageKey];
    } catch (e) {
      print('❌ 获取计划失败: $e');
      return null;
    }
  }

  /// 获取指定日期的所有计划
  static Future<List<DailyPlan>> getPlansForDate(DateTime date) async {
    try {
      final allPlans = await loadAllPlans();
      final dateStr = _formatDate(date);
      
      return allPlans.values
          .where((plan) => _formatDate(plan.date) == dateStr)
          .toList()
        ..sort((a, b) => a.type.index.compareTo(b.type.index));
    } catch (e) {
      print('❌ 获取日期计划失败: $e');
      return [];
    }
  }

  /// 获取指定日期范围的计划
  static Future<List<DailyPlan>> getPlansInRange(DateTime startDate, DateTime endDate) async {
    try {
      final allPlans = await loadAllPlans();
      
      return allPlans.values
          .where((plan) {
            return plan.date.isAfter(startDate.subtract(const Duration(days: 1))) &&
                   plan.date.isBefore(endDate.add(const Duration(days: 1)));
          })
          .toList()
        ..sort((a, b) {
          final dateCompare = a.date.compareTo(b.date);
          if (dateCompare != 0) return dateCompare;
          return a.type.index.compareTo(b.type.index);
        });
    } catch (e) {
      print('❌ 获取范围计划失败: $e');
      return [];
    }
  }

  /// 清除所有数据
  static Future<bool> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 创建备份
      await _createBackup(prefs);
      
      // 清除主数据
      await prefs.remove(_storageKey);
      await prefs.remove(_metadataKey);
      
      print('🗑️ 已清除所有每日计划数据');
      return true;
    } catch (e) {
      print('❌ 清除数据失败: $e');
      return false;
    }
  }

  /// 获取存储统计信息
  static Future<Map<String, dynamic>> getStorageStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metadataString = prefs.getString(_metadataKey);
      
      if (metadataString != null) {
        return json.decode(metadataString) as Map<String, dynamic>;
      }
      
      // 如果没有元数据，计算当前数据
      final allPlans = await loadAllPlans();
      return {
        'totalPlans': allPlans.length,
        'lastUpdated': DateTime.now().toIso8601String(),
        'planningCount': allPlans.values.where((p) => p.isPlanning).length,
        'optimizationCount': allPlans.values.where((p) => p.isOptimization).length,
      };
    } catch (e) {
      print('❌ 获取存储统计失败: $e');
      return {
        'totalPlans': 0,
        'lastUpdated': DateTime.now().toIso8601String(),
        'planningCount': 0,
        'optimizationCount': 0,
      };
    }
  }

  /// 创建备份
  static Future<void> _createBackup(SharedPreferences prefs) async {
    try {
      final currentData = prefs.getString(_storageKey);
      if (currentData != null) {
        await prefs.setString(_backupKey, currentData);
        print('💾 已创建数据备份');
      }
    } catch (e) {
      print('❌ 创建备份失败: $e');
    }
  }

  /// 从备份恢复
  static Future<Map<String, DailyPlan>> _restoreFromBackup() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final backupData = prefs.getString(_backupKey);
      
      if (backupData != null) {
        final Map<String, dynamic> jsonData = json.decode(backupData);
        final Map<String, DailyPlan> plans = {};
        
        jsonData.forEach((key, value) {
          try {
            plans[key] = DailyPlan.fromJson(value as Map<String, dynamic>);
          } catch (e) {
            print('❌ 解析备份数据失败: $key, $e');
          }
        });
        
        print('🔄 已从备份恢复 ${plans.length} 个每日计划');
        return plans;
      }
    } catch (e) {
      print('❌ 从备份恢复失败: $e');
    }
    
    return {};
  }

  /// 更新元数据
  static Future<void> _updateMetadata(SharedPreferences prefs, int totalPlans) async {
    try {
      final metadata = {
        'totalPlans': totalPlans,
        'lastUpdated': DateTime.now().toIso8601String(),
        'version': '1.0',
      };
      
      await prefs.setString(_metadataKey, json.encode(metadata));
    } catch (e) {
      print('❌ 更新元数据失败: $e');
    }
  }

  /// 格式化日期
  static String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 数据迁移（如果需要）
  static Future<bool> migrateData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 检查是否需要迁移
      final hasOldData = prefs.containsKey('daily_plans'); // 假设的旧版本键
      final hasNewData = prefs.containsKey(_storageKey);
      
      if (hasOldData && !hasNewData) {
        print('🔄 开始数据迁移...');
        
        // 这里可以添加具体的迁移逻辑
        // 例如从旧格式转换到新格式
        
        print('✅ 数据迁移完成');
        return true;
      }
      
      return false;
    } catch (e) {
      print('❌ 数据迁移失败: $e');
      return false;
    }
  }
}
