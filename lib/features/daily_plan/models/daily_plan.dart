import 'package:json_annotation/json_annotation.dart';

part 'daily_plan.g.dart';

/// 每日计划类型枚举
enum DailyPlanType {
  @JsonValue('planning')
  planning, // 每日计划
  
  @JsonValue('optimization')
  optimization, // 每日优化
}

/// 每日计划数据模型
@JsonSerializable()
class DailyPlan {
  /// 唯一标识符
  final String id;
  
  /// 日期（年-月-日）
  final DateTime date;
  
  /// 计划/优化内容
  final String content;
  
  /// 计划类型（计划或优化）
  final DailyPlanType type;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 最后更新时间
  final DateTime? updatedAt;
  
  /// 是否已完成（仅对计划类型有效）
  final bool isCompleted;
  
  /// 优先级（1-5，5最高）
  final int priority;

  const DailyPlan({
    required this.id,
    required this.date,
    required this.content,
    required this.type,
    required this.createdAt,
    this.updatedAt,
    this.isCompleted = false,
    this.priority = 3,
  });

  /// 从JSON创建实例
  factory DailyPlan.fromJson(Map<String, dynamic> json) => _$DailyPlanFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$DailyPlanToJson(this);

  /// 创建副本并更新指定字段
  DailyPlan copyWith({
    String? id,
    DateTime? date,
    String? content,
    DailyPlanType? type,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isCompleted,
    int? priority,
  }) {
    return DailyPlan(
      id: id ?? this.id,
      date: date ?? this.date,
      content: content ?? this.content,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isCompleted: isCompleted ?? this.isCompleted,
      priority: priority ?? this.priority,
    );
  }

  /// 生成唯一的存储键
  String get storageKey => '${_formatDate(date)}_${type.name}';

  /// 格式化日期为字符串（yyyy-MM-dd）
  static String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 从日期字符串解析DateTime
  static DateTime parseDate(String dateStr) {
    final parts = dateStr.split('-');
    return DateTime(
      int.parse(parts[0]),
      int.parse(parts[1]),
      int.parse(parts[2]),
    );
  }

  /// 检查是否为今天的计划
  bool get isToday {
    final now = DateTime.now();
    return date.year == now.year && 
           date.month == now.month && 
           date.day == now.day;
  }

  /// 检查是否为计划类型
  bool get isPlanning => type == DailyPlanType.planning;

  /// 检查是否为优化类型
  bool get isOptimization => type == DailyPlanType.optimization;

  /// 获取显示标题
  String get displayTitle {
    return isPlanning ? '每日计划' : '每日优化';
  }

  /// 获取主题颜色
  int get themeColor {
    return isPlanning ? 0xFF2E7EED : 0xFF0F7B6C;
  }

  /// 获取内容预览（最多显示指定字符数）
  String getContentPreview([int maxLength = 20]) {
    if (content.isEmpty) return '';
    if (content.length <= maxLength) return content;
    return '${content.substring(0, maxLength)}...';
  }

  /// 检查内容是否为空
  bool get isEmpty => content.trim().isEmpty;

  /// 检查内容是否不为空
  bool get isNotEmpty => content.trim().isNotEmpty;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DailyPlan &&
        other.id == id &&
        other.date == date &&
        other.content == content &&
        other.type == type &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.isCompleted == isCompleted &&
        other.priority == priority;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      date,
      content,
      type,
      createdAt,
      updatedAt,
      isCompleted,
      priority,
    );
  }

  @override
  String toString() {
    return 'DailyPlan(id: $id, date: $date, type: $type, content: ${content.length > 50 ? '${content.substring(0, 50)}...' : content})';
  }
}

/// 每日计划工厂类
class DailyPlanFactory {
  /// 创建新的每日计划
  static DailyPlan createPlanning({
    required DateTime date,
    required String content,
    int priority = 3,
  }) {
    return DailyPlan(
      id: _generateId(),
      date: _normalizeDate(date),
      content: content.trim(),
      type: DailyPlanType.planning,
      createdAt: DateTime.now(),
      priority: priority,
    );
  }

  /// 创建新的每日优化
  static DailyPlan createOptimization({
    required DateTime date,
    required String content,
    int priority = 3,
  }) {
    return DailyPlan(
      id: _generateId(),
      date: _normalizeDate(date),
      content: content.trim(),
      type: DailyPlanType.optimization,
      createdAt: DateTime.now(),
      priority: priority,
    );
  }

  /// 生成唯一ID
  static String _generateId() {
    return '${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecond}';
  }

  /// 标准化日期（只保留年月日，去除时分秒）
  static DateTime _normalizeDate(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }
}
