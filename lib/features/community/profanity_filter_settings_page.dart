import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'profanity_filter_service.dart';

/// 脏话过滤设置页面
class ProfanityFilterSettingsPage extends StatefulWidget {
  const ProfanityFilterSettingsPage({super.key});

  @override
  State<ProfanityFilterSettingsPage> createState() => _ProfanityFilterSettingsPageState();
}

class _ProfanityFilterSettingsPageState extends State<ProfanityFilterSettingsPage> {
  final ProfanityFilterService _filterService = ProfanityFilterService();
  late ProfanityFilterSettings _settings;
  bool _isLoading = true;
  final TextEditingController _customWordController = TextEditingController();
  final TextEditingController _replacementController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void dispose() {
    _customWordController.dispose();
    _replacementController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    await _filterService.initialize();
    setState(() {
      _settings = _filterService.getSettings();
      _replacementController.text = _settings.replacementText;
      _isLoading = false;
    });
  }

  Future<void> _saveSettings() async {
    await _filterService.saveSettings(_settings);
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('设置已保存'),
          backgroundColor: Color(0xFF0F7B6C),
          duration: Duration(seconds: 1),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('内容过滤设置'),
          backgroundColor: Colors.white,
          foregroundColor: const Color(0xFF37352F),
          elevation: 0,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: AppBar(
        title: const Text(
          '内容过滤设置',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF37352F),
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBasicSettings(),
            const SizedBox(height: 16),
            _buildFilterLevelSettings(),
            const SizedBox(height: 16),
            _buildDisplaySettings(),
            const SizedBox(height: 16),
            _buildCustomWordsSection(),
            const SizedBox(height: 16),
            _buildTestSection(),
          ],
        ),
      ),
    );
  }

  /// 基础设置
  Widget _buildBasicSettings() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '基础设置',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 16),
          _buildSwitchItem(
            icon: Icons.shield_outlined,
            title: '启用内容过滤',
            subtitle: '自动检测和过滤不当内容',
            iconColor: const Color(0xFF2E7EED),
            value: _settings.enabled,
            onChanged: (value) {
              setState(() {
                _settings = _settings.copyWith(enabled: value);
              });
              _saveSettings();
            },
          ),
          const Divider(height: 24, color: Color(0xFFE3E2E0)),
          _buildSwitchItem(
            icon: Icons.report_outlined,
            title: '用户举报功能',
            subtitle: '允许用户举报不当内容',
            iconColor: const Color(0xFFE03E3E),
            value: _settings.enableUserReporting,
            onChanged: (value) {
              setState(() {
                _settings = _settings.copyWith(enableUserReporting: value);
              });
              _saveSettings();
            },
          ),
          const Divider(height: 24, color: Color(0xFFE3E2E0)),
          _buildSwitchItem(
            icon: Icons.edit_outlined,
            title: '自定义词库',
            subtitle: '允许添加个人屏蔽词汇',
            iconColor: const Color(0xFF0F7B6C),
            value: _settings.enableCustomWords,
            onChanged: (value) {
              setState(() {
                _settings = _settings.copyWith(enableCustomWords: value);
              });
              _saveSettings();
            },
          ),
        ],
      ),
    );
  }

  /// 过滤级别设置
  Widget _buildFilterLevelSettings() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '过滤级别',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '选择要过滤的内容严重程度',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF787774),
            ),
          ),
          const SizedBox(height: 16),
          ...ProfanityLevel.values.map((level) => _buildLevelOption(level)),
        ],
      ),
    );
  }

  /// 级别选项
  Widget _buildLevelOption(ProfanityLevel level) {
    final isSelected = _settings.filterLevel == level;
    final levelInfo = _getLevelInfo(level);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () {
          setState(() {
            _settings = _settings.copyWith(filterLevel: level);
          });
          _saveSettings();
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isSelected ? const Color(0xFF2E7EED).withValues(alpha: 0.1) : null,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? const Color(0xFF2E7EED) : const Color(0xFFE3E2E0),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                color: isSelected ? const Color(0xFF2E7EED) : const Color(0xFF787774),
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      levelInfo['title']!,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isSelected ? const Color(0xFF2E7EED) : const Color(0xFF37352F),
                      ),
                    ),
                    Text(
                      levelInfo['description']!,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF787774),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示设置
  Widget _buildDisplaySettings() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '显示设置',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 16),
          _buildSwitchItem(
            icon: Icons.visibility_outlined,
            title: '显示替换文本',
            subtitle: '用替换文本显示被过滤的内容',
            iconColor: const Color(0xFF787774),
            value: _settings.showReplacementText,
            onChanged: (value) {
              setState(() {
                _settings = _settings.copyWith(showReplacementText: value);
              });
              _saveSettings();
            },
          ),
          if (_settings.showReplacementText) ...[
            const SizedBox(height: 16),
            const Text(
              '替换文本',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _replacementController,
              decoration: InputDecoration(
                hintText: '输入替换文本（如：***）',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF2E7EED)),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              onChanged: (value) {
                setState(() {
                  _settings = _settings.copyWith(replacementText: value);
                });
              },
              onEditingComplete: _saveSettings,
            ),
          ],
        ],
      ),
    );
  }

  /// 自定义词库部分
  Widget _buildCustomWordsSection() {
    if (!_settings.enableCustomWords) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '自定义词库',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF37352F),
                ),
              ),
              TextButton.icon(
                onPressed: _showAddCustomWordDialog,
                icon: const Icon(Icons.add, size: 16),
                label: const Text('添加'),
                style: TextButton.styleFrom(
                  foregroundColor: const Color(0xFF2E7EED),
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            '管理您的个人屏蔽词汇',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF787774),
            ),
          ),
          const SizedBox(height: 16),
          _buildCustomWordsList(),
        ],
      ),
    );
  }

  /// 自定义词汇列表
  Widget _buildCustomWordsList() {
    final customWords = _filterService.getCustomWords();
    
    if (customWords.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: const Color(0xFFF7F6F3),
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Text(
            '暂无自定义词汇',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF787774),
            ),
          ),
        ),
      );
    }

    return Column(
      children: customWords.map((word) => _buildCustomWordItem(word)).toList(),
    );
  }

  /// 自定义词汇项
  Widget _buildCustomWordItem(ProfanityWord word) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFF7F6F3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  word.word,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF37352F),
                  ),
                ),
                if (word.variants.isNotEmpty)
                  Text(
                    '变体: ${word.variants.join(', ')}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF787774),
                    ),
                  ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _removeCustomWord(word.word),
            icon: const Icon(Icons.delete_outline),
            color: const Color(0xFFE03E3E),
            iconSize: 20,
          ),
        ],
      ),
    );
  }

  /// 测试部分
  Widget _buildTestSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '测试过滤效果',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '输入文本测试当前过滤设置的效果',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF787774),
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _showTestDialog,
            icon: const Icon(Icons.play_arrow),
            label: const Text('开始测试'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7EED),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 开关项组件
  Widget _buildSwitchItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color iconColor,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: iconColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: iconColor, size: 20),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF37352F),
                ),
              ),
              Text(
                subtitle,
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF787774),
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: const Color(0xFF2E7EED),
        ),
      ],
    );
  }

  /// 获取级别信息
  Map<String, String> _getLevelInfo(ProfanityLevel level) {
    switch (level) {
      case ProfanityLevel.mild:
        return {
          'title': '宽松',
          'description': '只过滤严重不当内容',
        };
      case ProfanityLevel.moderate:
        return {
          'title': '标准',
          'description': '过滤大部分不当内容（推荐）',
        };
      case ProfanityLevel.severe:
        return {
          'title': '严格',
          'description': '过滤所有可能不当的内容',
        };
      case ProfanityLevel.extreme:
        return {
          'title': '极严格',
          'description': '过滤所有敏感内容',
        };
    }
  }

  /// 显示添加自定义词汇对话框
  void _showAddCustomWordDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text('添加自定义词汇'),
        content: TextField(
          controller: _customWordController,
          decoration: const InputDecoration(
            hintText: '输入要屏蔽的词汇',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: _addCustomWord,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7EED),
              foregroundColor: Colors.white,
            ),
            child: const Text('添加'),
          ),
        ],
      ),
    );
  }

  /// 添加自定义词汇
  void _addCustomWord() async {
    final word = _customWordController.text.trim();
    if (word.isNotEmpty) {
      await _filterService.addCustomWord(
        ProfanityWord(
          word: word,
          level: ProfanityLevel.moderate,
        ),
      );
      _customWordController.clear();
      setState(() {});
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('自定义词汇已添加'),
            backgroundColor: Color(0xFF0F7B6C),
          ),
        );
      }
    }
  }

  /// 移除自定义词汇
  void _removeCustomWord(String word) async {
    await _filterService.removeCustomWord(word);
    setState(() {});
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('自定义词汇已移除'),
          backgroundColor: Color(0xFFE03E3E),
        ),
      );
    }
  }

  /// 显示测试对话框
  void _showTestDialog() {
    final testController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text('测试过滤效果'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: testController,
              decoration: const InputDecoration(
                hintText: '输入测试文本',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () async {
                final result = await _filterService.filterText(testController.text);
                if (context.mounted) {
                  _showTestResult(result);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2E7EED),
                foregroundColor: Colors.white,
              ),
              child: const Text('测试'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 显示测试结果
  void _showTestResult(ProfanityDetectionResult result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text('测试结果'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('原文:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text(result.originalText),
            const SizedBox(height: 8),
            const Text('过滤后:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text(result.filteredText),
            const SizedBox(height: 8),
            Text('检测到 ${result.detectedWords.length} 个不当词汇'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
