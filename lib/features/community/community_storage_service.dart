import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'community_feed_page.dart';

/// 社区帖子存储服务
class CommunityStorageService {
  static const String _postsKey = 'community_posts';
  static const String _nextIdKey = 'community_next_id';
  
  static CommunityStorageService? _instance;
  static CommunityStorageService get instance {
    _instance ??= CommunityStorageService._();
    return _instance!;
  }
  
  CommunityStorageService._();
  
  List<CommunityPost>? _cachedPosts;
  
  /// 初始化服务
  Future<void> initialize() async {
    await _loadPosts();
  }
  
  /// 获取所有帖子
  Future<List<CommunityPost>> getAllPosts() async {
    if (_cachedPosts == null) {
      await _loadPosts();
    }
    return List.from(_cachedPosts ?? []);
  }
  
  /// 添加新帖子
  Future<bool> addPost(CommunityPost post) async {
    try {
      if (_cachedPosts == null) {
        await _loadPosts();
      }
      
      _cachedPosts!.insert(0, post); // 插入到列表开头（最新的在前面）
      await _savePosts();
      
      print('✅ 成功保存新帖子: ${post.id}');
      return true;
    } catch (e) {
      print('❌ 保存帖子失败: $e');
      return false;
    }
  }
  
  /// 更新帖子（点赞、评论等）
  Future<bool> updatePost(CommunityPost updatedPost) async {
    try {
      if (_cachedPosts == null) {
        await _loadPosts();
      }
      
      final index = _cachedPosts!.indexWhere((p) => p.id == updatedPost.id);
      if (index != -1) {
        _cachedPosts![index] = updatedPost;
        await _savePosts();
        return true;
      }
      return false;
    } catch (e) {
      print('❌ 更新帖子失败: $e');
      return false;
    }
  }
  
  /// 删除帖子
  Future<bool> deletePost(int postId) async {
    try {
      if (_cachedPosts == null) {
        await _loadPosts();
      }
      
      _cachedPosts!.removeWhere((p) => p.id == postId);
      await _savePosts();
      return true;
    } catch (e) {
      print('❌ 删除帖子失败: $e');
      return false;
    }
  }
  
  /// 获取下一个可用的帖子ID
  Future<int> getNextPostId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final nextId = prefs.getInt(_nextIdKey) ?? 1000; // 从1000开始，避免与模拟数据冲突
      await prefs.setInt(_nextIdKey, nextId + 1);
      return nextId;
    } catch (e) {
      print('❌ 获取下一个帖子ID失败: $e');
      return DateTime.now().millisecondsSinceEpoch; // 使用时间戳作为备用ID
    }
  }
  
  /// 从本地存储加载帖子
  Future<void> _loadPosts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_postsKey);
      
      if (jsonString != null) {
        final List<dynamic> jsonList = json.decode(jsonString);
        _cachedPosts = jsonList
            .map((json) => CommunityPost.fromJson(json))
            .toList();
        print('📱 从本地存储加载 ${_cachedPosts!.length} 个帖子');
      } else {
        _cachedPosts = [];
        print('📱 本地存储中没有帖子数据，初始化为空列表');
      }
    } catch (e) {
      print('❌ 加载帖子失败: $e');
      _cachedPosts = [];
    }
  }
  
  /// 保存帖子到本地存储
  Future<void> _savePosts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = (_cachedPosts ?? []).map((post) => post.toJson()).toList();
      await prefs.setString(_postsKey, json.encode(jsonList));
      print('💾 已保存 ${_cachedPosts?.length ?? 0} 个帖子到本地存储');
    } catch (e) {
      print('❌ 保存帖子失败: $e');
    }
  }
  
  /// 清空所有帖子（用于测试）
  Future<void> clearAllPosts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_postsKey);
      await prefs.remove(_nextIdKey);
      _cachedPosts = [];
      print('🗑️ 已清空所有帖子数据');
    } catch (e) {
      print('❌ 清空帖子数据失败: $e');
    }
  }
  
  /// 刷新缓存
  void refreshCache() {
    _cachedPosts = null;
  }
}
