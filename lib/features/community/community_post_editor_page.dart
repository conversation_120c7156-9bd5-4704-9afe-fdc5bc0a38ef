import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
// import 'package:file_picker/file_picker.dart';
// import 'package:flutter_html/flutter_html.dart'; // 暂时注释掉
import 'article_import_service.dart';
import 'community_storage_service.dart';
import 'community_feed_page.dart';

/// 社区发帖编辑器页面
class CommunityPostEditorPage extends StatefulWidget {
  const CommunityPostEditorPage({super.key});

  @override
  State<CommunityPostEditorPage> createState() =>
      _CommunityPostEditorPageState();
}

class _CommunityPostEditorPageState extends State<CommunityPostEditorPage> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _contentController = TextEditingController();
  final ArticleImportService _importService = ArticleImportService();
  final CommunityStorageService _storageService =
      CommunityStorageService.instance;

  bool _isImporting = false;
  bool _isPublishing = false;
  bool _showPreview = false;
  ImportResult? _importResult;
  final List<String> _selectedTags = [];

  final List<String> _availableTags = [
    '考研',
    '英语',
    '学习方法',
    '时间管理',
    '经验分享',
    '词汇学习',
    '阅读技巧',
    '写作技巧',
    '听力练习',
    '口语练习',
  ];

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// 构建顶部应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      title: const Text(
        '发布动态',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Color(0xFF37352F),
        ),
      ),
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(Icons.close, color: Color(0xFF787774)),
      ),
      actions: [
        TextButton(
          onPressed: _showPreview ? _togglePreview : null,
          child: Text(
            '编辑',
            style: TextStyle(
              color: _showPreview
                  ? const Color(0xFF2E7EED)
                  : const Color(0xFF787774),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        TextButton(
          onPressed: !_showPreview ? _togglePreview : null,
          child: Text(
            '预览',
            style: TextStyle(
              color: !_showPreview
                  ? const Color(0xFF2E7EED)
                  : const Color(0xFF787774),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Container(
          margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
          child: ElevatedButton(
            onPressed: _isPublishing ? null : _publishPost,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7EED),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
            ),
            child: _isPublishing
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text('发布'),
          ),
        ),
      ],
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitleInput(),
          const SizedBox(height: 16),
          _buildImportSection(),
          const SizedBox(height: 16),
          _buildContentSection(),
          const SizedBox(height: 16),
          _buildTagsSection(),
          const SizedBox(height: 16),
          if (_importResult != null) _buildImportStats(),
        ],
      ),
    );
  }

  /// 构建标题输入
  Widget _buildTitleInput() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE3E2DE)),
      ),
      child: TextField(
        controller: _titleController,
        decoration: const InputDecoration(
          hintText: '请输入标题...',
          hintStyle: TextStyle(color: Color(0xFF787774)),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16),
        ),
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Color(0xFF37352F),
        ),
        maxLines: 2,
      ),
    );
  }

  /// 构建导入区域
  Widget _buildImportSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE3E2DE)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.file_upload_outlined,
            color: const Color(0xFF2E7EED),
            size: 20,
          ),
          const SizedBox(width: 8),
          const Text(
            '导入文章',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFF37352F),
            ),
          ),
          const Spacer(),
          ElevatedButton.icon(
            onPressed: _isImporting ? null : _importFile,
            icon: _isImporting
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.add, size: 16),
            label: Text(_isImporting ? '导入中...' : '导入 TXT'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7EED),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              textStyle: const TextStyle(fontSize: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContentSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE3E2DE)),
      ),
      child: _showPreview ? _buildPreviewContent() : _buildEditContent(),
    );
  }

  /// 构建编辑内容
  Widget _buildEditContent() {
    return TextField(
      controller: _contentController,
      decoration: const InputDecoration(
        hintText: '分享你的学习心得、经验或问题...\n\n支持导入 .txt 和 .md 文件，自动高亮考研词汇',
        hintStyle: TextStyle(color: Color(0xFF787774)),
        border: InputBorder.none,
        contentPadding: EdgeInsets.all(16),
      ),
      style: const TextStyle(
        fontSize: 14,
        color: Color(0xFF37352F),
        height: 1.5,
      ),
      maxLines: 15,
      minLines: 8,
    );
  }

  /// 构建预览内容
  Widget _buildPreviewContent() {
    final content = _importResult?.highlightedText ?? _contentController.text;

    if (content.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Text(
          '暂无内容预览',
          style: TextStyle(color: Color(0xFF787774), fontSize: 14),
        ),
      );
    }

    // 如果有高亮词汇，使用自定义渲染
    if (_importResult != null && _importResult!.highlightedWords.isNotEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: _buildInteractiveContent(),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Text(
        content,
        style: const TextStyle(
          fontSize: 14,
          color: Color(0xFF37352F),
          height: 1.5,
        ),
      ),
    );
  }

  /// 构建可交互的内容（支持词汇点击）
  Widget _buildInteractiveContent() {
    final result = _importResult!;
    final originalText = result.originalText;
    final highlightedWords = result.highlightedWords;

    List<TextSpan> spans = [];
    int currentIndex = 0;

    for (final highlightedWord in highlightedWords) {
      // 添加高亮词汇前的普通文本
      if (currentIndex < highlightedWord.startIndex) {
        spans.add(
          TextSpan(
            text: originalText.substring(
              currentIndex,
              highlightedWord.startIndex,
            ),
            style: const TextStyle(
              color: Color(0xFF37352F),
              fontSize: 14,
              height: 1.5,
            ),
          ),
        );
      }

      // 添加高亮词汇（可点击）
      spans.add(
        TextSpan(
          text: highlightedWord.word,
          style: const TextStyle(
            color: Color(0xFFE03E3E),
            fontSize: 14,
            fontWeight: FontWeight.bold,
            backgroundColor: Color(0xFFFFF2F2),
            height: 1.5,
          ),
          recognizer: TapGestureRecognizer()
            ..onTap = () => _showWordDetails(highlightedWord),
        ),
      );

      currentIndex = highlightedWord.endIndex;
    }

    // 添加最后剩余的普通文本
    if (currentIndex < originalText.length) {
      spans.add(
        TextSpan(
          text: originalText.substring(currentIndex),
          style: const TextStyle(
            color: Color(0xFF37352F),
            fontSize: 14,
            height: 1.5,
          ),
        ),
      );
    }

    return RichText(text: TextSpan(children: spans));
  }

  /// 构建标签区域
  Widget _buildTagsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE3E2DE)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '添加标签',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _availableTags.map((tag) {
              final isSelected = _selectedTags.contains(tag);
              return GestureDetector(
                onTap: () => _toggleTag(tag),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? const Color(0xFF2E7EED)
                        : const Color(0xFFF7F6F3),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFF2E7EED)
                          : const Color(0xFFE3E2DE),
                    ),
                  ),
                  child: Text(
                    tag,
                    style: TextStyle(
                      fontSize: 12,
                      color: isSelected
                          ? Colors.white
                          : const Color(0xFF787774),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// 构建导入统计信息
  Widget _buildImportStats() {
    final result = _importResult!;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF0F9FF),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFB3E5FC)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '导入统计',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '总词数: ${result.totalWords} | 考研词汇: ${result.vocabularyWordsFound}',
            style: const TextStyle(fontSize: 12, color: Color(0xFF787774)),
          ),
        ],
      ),
    );
  }

  /// 导入文件（暂时使用示例文本）
  Future<void> _importFile() async {
    try {
      setState(() {
        _isImporting = true;
      });

      // 暂时使用示例文本代替文件选择
      const sampleText =
          '''The Importance of Vocabulary in Graduate Exam Preparation

Learning vocabulary is essential for success in graduate examinations. Students must develop comprehensive understanding of academic words to achieve their goals.

Effective vocabulary acquisition requires systematic approach. Research shows that students who consistently practice word recognition and comprehension perform better on standardized tests.

Key strategies include:
1. Regular reading of academic texts
2. Creating word associations and memory techniques
3. Using spaced repetition for long-term retention
4. Practicing with context-based exercises

Advanced learners should focus on expert-level vocabulary that appears frequently in graduate-level materials. These words often carry sophisticated meanings and require deeper understanding.

The process of vocabulary development involves multiple cognitive functions including memory, analysis, and synthesis. Students must engage with words through various contexts to achieve mastery.

Consistent practice and dedication are fundamental to vocabulary improvement. Success depends on maintaining regular study habits and applying learned words in academic writing and speaking contexts.''';

      // 处理文章内容，高亮词汇
      final importResult = await _importService.processArticleContent(
        sampleText,
      );

      setState(() {
        _contentController.text = importResult.originalText;
        _importResult = importResult;
      });

      // 显示导入成功提示
      if (mounted) {
        String message = '导入成功！发现 ${importResult.vocabularyWordsFound} 个考研词汇';
        if (importResult.hasProfanityViolations) {
          message += '，过滤了 ${importResult.profanityWordsFiltered} 个不当词汇';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: importResult.hasProfanityViolations
                ? const Color(0xFFE03E3E)
                : const Color(0xFF0F7B6C),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('导入失败: $e'),
            backgroundColor: const Color(0xFFE03E3E),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isImporting = false;
        });
      }
    }
  }

  /// 切换预览模式
  void _togglePreview() {
    setState(() {
      _showPreview = !_showPreview;
    });
  }

  /// 切换标签选择
  void _toggleTag(String tag) {
    setState(() {
      if (_selectedTags.contains(tag)) {
        _selectedTags.remove(tag);
      } else {
        _selectedTags.add(tag);
      }
    });
  }

  /// 发布动态
  Future<void> _publishPost() async {
    // 验证输入
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请输入标题'),
          backgroundColor: Color(0xFFE03E3E),
        ),
      );
      return;
    }

    if (_contentController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请输入内容'),
          backgroundColor: Color(0xFFE03E3E),
        ),
      );
      return;
    }

    try {
      setState(() {
        _isPublishing = true;
      });

      // 第一步：检查内容是否包含违禁词
      debugPrint('🔍 开始检查违禁词...');
      debugPrint('📝 标题内容: "${_titleController.text.trim()}"');
      debugPrint('📝 正文内容: "${_contentController.text.trim()}"');

      final titleResult = await _importService.processArticleContent(
        _titleController.text.trim(),
      );
      final contentResult = await _importService.processArticleContent(
        _contentController.text.trim(),
      );

      debugPrint(
        '🔍 标题检测结果: 违禁=${titleResult.hasProfanityViolations}, 过滤数=${titleResult.profanityWordsFiltered}',
      );
      debugPrint(
        '🔍 内容检测结果: 违禁=${contentResult.hasProfanityViolations}, 过滤数=${contentResult.profanityWordsFiltered}',
      );

      // 获取过滤后的内容（无论是否有违禁词都使用过滤后的版本）
      String filteredTitle = titleResult.highlightedText;
      String filteredContent = contentResult.highlightedText;

      // 移除HTML标签（如果有词汇高亮标签）
      filteredTitle = _removeHtmlTags(filteredTitle);
      filteredContent = _removeHtmlTags(filteredContent);

      // 检查是否有违禁内容
      if (titleResult.hasProfanityViolations ||
          contentResult.hasProfanityViolations) {
        final totalViolations =
            titleResult.profanityWordsFiltered +
            contentResult.profanityWordsFiltered;

        debugPrint('⚠️ 检测到 $totalViolations 个违禁词，显示警告对话框');
        debugPrint('📝 过滤后标题: "$filteredTitle"');
        debugPrint('📝 过滤后内容: "$filteredContent"');

        if (mounted) {
          // 显示违禁词警告
          final shouldContinue = await _showProfanityWarningDialog(
            totalViolations,
          );
          if (!shouldContinue) {
            debugPrint('❌ 用户取消发布');
            return;
          }

          debugPrint('✅ 用户选择继续发布，使用过滤后的内容');
        }
      } else {
        debugPrint('✅ 未检测到违禁词，继续正常发布流程');
      }

      // 第二步：创建并保存帖子
      debugPrint('💾 开始保存帖子数据...');

      // 获取下一个帖子ID
      final postId = await _storageService.getNextPostId();

      // 创建当前用户信息（实际应用中应该从用户状态获取）
      final currentUser = UserInfo(
        id: 1,
        username: '当前用户',
        avatar:
            'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
        isVerified: false,
      );

      // 确定帖子类型（基于选择的标签）
      PostType postType = PostType.study; // 默认为学习心得
      if (_selectedTags.contains('经验分享')) {
        postType = PostType.experience;
      } else if (_selectedTags.contains('提问求助')) {
        postType = PostType.question;
      } else if (_selectedTags.contains('成果展示')) {
        postType = PostType.achievement;
      }

      // 创建新帖子（使用过滤后的内容）
      final newPost = CommunityPost(
        id: postId,
        author: currentUser,
        content: '${filteredTitle.trim()}\n\n${filteredContent.trim()}',
        type: postType,
        tags: List.from(_selectedTags),
        images: [], // 暂时不支持图片
        likeCount: 0,
        commentCount: 0,
        shareCount: 0,
        createdAt: DateTime.now(),
        isLiked: false,
        isPremium: false, // 用户创建的帖子默认不是优质文章
      );

      debugPrint('📝 创建帖子内容: "${newPost.content}"');

      // 保存到本地存储
      final saveSuccess = await _storageService.addPost(newPost);

      if (mounted) {
        if (saveSuccess) {
          // 显示成功提示
          String successMessage = '发布成功！';
          if (titleResult.hasProfanityViolations ||
              contentResult.hasProfanityViolations) {
            final totalFiltered =
                titleResult.profanityWordsFiltered +
                contentResult.profanityWordsFiltered;
            successMessage += ' 已自动过滤 $totalFiltered 个不当词汇';
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(successMessage),
              backgroundColor: const Color(0xFF0F7B6C),
              duration: const Duration(milliseconds: 300),
            ),
          );

          // 返回上一页，并传递刷新标志
          Navigator.pop(context, true);
        } else {
          // 保存失败
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('发布失败，请重试'),
              backgroundColor: Color(0xFFE03E3E),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('发布失败: $e'),
            backgroundColor: const Color(0xFFE03E3E),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isPublishing = false;
        });
      }
    }
  }

  /// 移除HTML标签
  String _removeHtmlTags(String htmlString) {
    if (htmlString.isEmpty) return htmlString;

    // 移除HTML标签，保留文本内容
    final RegExp htmlTagRegExp = RegExp(r'<[^>]*>');
    return htmlString.replaceAll(htmlTagRegExp, '');
  }

  /// 显示违禁词警告对话框
  Future<bool> _showProfanityWarningDialog(int violationCount) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            title: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFE03E3E).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.warning_outlined,
                    color: Color(0xFFE03E3E),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  '内容警告',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '检测到您的内容包含 $violationCount 个不当词汇，系统将自动过滤这些内容。',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF37352F),
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF7F6F3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Color(0xFF787774),
                        size: 16,
                      ),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '为维护社区环境，我们会自动过滤不当内容。请遵守社区规范。',
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(0xFF787774),
                            height: 1.4,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text(
                  '取消发布',
                  style: TextStyle(color: Color(0xFF787774), fontSize: 14),
                ),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF2E7EED),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                ),
                child: const Text('继续发布', style: TextStyle(fontSize: 14)),
              ),
            ],
          ),
        ) ??
        false;
  }

  /// 显示词汇详情
  void _showWordDetails(HighlightedWord highlightedWord) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          children: [
            // 顶部拖拽指示器
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: const Color(0xFFE3E2DE),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // 标题栏
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE3E2DE), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    highlightedWord.word,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                    color: const Color(0xFF787774),
                  ),
                ],
              ),
            ),

            // 词汇信息
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 难度标签
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getDifficultyColor(highlightedWord.difficulty),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        highlightedWord.difficulty.toUpperCase(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // 释义
                    const Text(
                      '释义',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF37352F),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      highlightedWord.definition,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF37352F),
                        height: 1.5,
                      ),
                    ),

                    if (highlightedWord.isVariant) ...[
                      const SizedBox(height: 16),
                      const Text(
                        '原始形式',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF37352F),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        highlightedWord.originalForm,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF2E7EED),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],

                    const Spacer(),

                    // 添加到背诵列表按钮
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () => _addWordToStudyList(highlightedWord),
                        icon: const Icon(Icons.bookmark_add, size: 20),
                        label: const Text('添加到背诵列表'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF2E7EED),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 添加词汇到背诵列表
  Future<void> _addWordToStudyList(HighlightedWord highlightedWord) async {
    try {
      // 使用原始形式的单词
      final wordToAdd = highlightedWord.isVariant
          ? highlightedWord.originalForm
          : highlightedWord.word;

      await _importService.addWordToStudyList(wordToAdd);

      if (mounted) {
        Navigator.pop(context); // 关闭词汇详情弹窗

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已添加 "$wordToAdd" 到背诵列表'),
            backgroundColor: const Color(0xFF0F7B6C),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('添加失败: $e'),
            backgroundColor: const Color(0xFFE03E3E),
          ),
        );
      }
    }
  }

  /// 获取难度颜色
  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return const Color(0xFF0F7B6C);
      case 'intermediate':
        return const Color(0xFFD9730D);
      case 'advanced':
        return const Color(0xFFE03E3E);
      case 'expert':
        return const Color(0xFF7C3AED);
      default:
        return const Color(0xFF787774);
    }
  }
}
