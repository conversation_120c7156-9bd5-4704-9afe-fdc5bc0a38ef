import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// 优化日志页面
///
/// 提供每日反思记录功能，支持Markdown编辑、模板选择、历史回顾
/// 采用Notion风格，简洁清晰的卡片式布局
class ReflectionLogPage extends StatefulWidget {
  const ReflectionLogPage({super.key});

  @override
  State<ReflectionLogPage> createState() => _ReflectionLogPageState();
}

class _ReflectionLogPageState extends State<ReflectionLogPage> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  DateTime _selectedDate = DateTime.now();
  String _searchQuery = '';
  String _selectedTag = '全部';
  bool _isCalendarVisible = false;

  // 模拟日志数据
  final List<ReflectionEntry> _entries = [
    ReflectionEntry(
      id: '1',
      title: '今日学习收获',
      content: '''# 今日学习收获

## 完成的任务
- ✅ 完成数学习题 50 道
- ✅ 背诵英语单词 100 个  
- ✅ 阅读专业文献 1 篇

## 学习感悟
今天的学习效率比较高，特别是在数学方面有了新的理解。发现了一个新的解题方法，能够更快速地解决相关问题。

## 明日计划
- 继续巩固数学知识点
- 准备英语口语练习
- 开始新的专业课程学习

**今日评分：8/10** ⭐''',
      date: DateTime.now(),
      tags: ['学习', '反思'],
      mood: 4,
      isPublic: false,
    ),
    ReflectionEntry(
      id: '2',
      title: '时间管理心得',
      content: '''# 时间管理心得

## 使用番茄工作法的体验
今天尝试使用了番茄工作法来管理学习时间，发现确实能够提高专注度。

### 优点
- 能够更好地控制学习节奏
- 休息时间让大脑得到放松
- 任务完成感更强

### 需要改进
- 有时候 25 分钟不够完成一个完整的思考
- 需要更好地规划任务的粒度

## 明天的调整
尝试将大任务拆分成更小的子任务，更好地适应番茄工作法的时间分割。''',
      date: DateTime.now().subtract(const Duration(days: 1)),
      tags: ['时间管理', '方法'],
      mood: 3,
      isPublic: true,
    ),
    ReflectionEntry(
      id: '3',
      title: '个人成长规划',
      content: '''# 个人成长规划

## 短期目标（本月）
1. 提高学习效率
2. 建立良好的作息习惯
3. 加强英语口语练习

## 中期目标（半年）
1. 掌握核心专业技能
2. 参加相关竞赛或项目
3. 建立个人知识体系

## 长期目标（一年）
1. 完成学业目标
2. 培养综合能力
3. 明确职业方向

**反思**：目标要具体可衡量，需要制定详细的执行计划。''',
      date: DateTime.now().subtract(const Duration(days: 3)),
      tags: ['目标', '规划'],
      mood: 5,
      isPublic: false,
    ),
  ];

  // 标签列表
  final List<String> _tags = [
    '全部',
    '学习',
    '反思',
    '时间管理',
    '方法',
    '目标',
    '规划',
    '情感',
    '健康',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // 搜索和筛选栏
          _buildSearchAndFilter(),

          // 日历视图（可展开）
          if (_isCalendarVisible) Flexible(child: _buildCalendarView()),

          // 日志列表
          Expanded(child: _buildReflectionList()),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// 构建应用栏
  AppBar _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      surfaceTintColor: Colors.transparent,
      centerTitle: false,
      title: const Text(
        '优化日志',
        style: TextStyle(
          color: Color(0xFF37352F),
          fontSize: 22,
          fontWeight: FontWeight.bold,
        ),
      ),
      actions: [
        // 日历切换按钮
        IconButton(
          onPressed: () {
            setState(() {
              _isCalendarVisible = !_isCalendarVisible;
            });
          },
          icon: Icon(
            _isCalendarVisible
                ? Icons.calendar_today
                : Icons.calendar_month_outlined,
            color: const Color(0xFF37352F),
          ),
        ),

        // 更多操作
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          icon: const Icon(Icons.more_vert, color: Color(0xFF37352F)),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'export',
              child: Row(
                children: [
                  Icon(Icons.download_outlined, size: 18),
                  SizedBox(width: 8),
                  Text('导出日志'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'templates',
              child: Row(
                children: [
                  Icon(Icons.description_outlined, size: 18),
                  SizedBox(width: 8),
                  Text('日志模板'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings_outlined, size: 18),
                  SizedBox(width: 8),
                  Text('设置'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建搜索和筛选栏
  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          // 搜索框
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFFF7F6F3),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFF37352F).withValues(alpha: 0.1),
              ),
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: '搜索日志内容...',
                hintStyle: TextStyle(
                  color: const Color(0xFF9B9A97),
                  fontSize: 14,
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: Color(0xFF9B9A97),
                  size: 20,
                ),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                        icon: const Icon(
                          Icons.clear,
                          color: Color(0xFF9B9A97),
                          size: 18,
                        ),
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              style: const TextStyle(color: Color(0xFF37352F), fontSize: 14),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          const SizedBox(height: 12),

          // 标签筛选
          SizedBox(
            height: 32,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _tags.length,
              itemBuilder: (context, index) {
                final tag = _tags[index];
                final isSelected = _selectedTag == tag;

                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(
                      tag,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: isSelected
                            ? Colors.white
                            : const Color(0xFF37352F),
                      ),
                    ),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedTag = tag;
                      });
                    },
                    selectedColor: const Color(0xFF2E7EED),
                    backgroundColor: Colors.white,
                    side: BorderSide(
                      color: isSelected
                          ? const Color(0xFF2E7EED)
                          : const Color(0xFF37352F).withValues(alpha: 0.2),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建日历视图
  Widget _buildCalendarView() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 响应式布局参数
        double horizontalPadding = 16;
        double fontSize = 16;
        double iconSize = 24;
        double spacing = 12;

        // iPad 横屏适配
        if (constraints.maxWidth > 900) {
          horizontalPadding = 32;
          fontSize = 18;
          iconSize = 28;
          spacing = 16;
        } else if (constraints.maxWidth > 600) {
          horizontalPadding = 24;
          fontSize = 17;
          iconSize = 26;
          spacing = 14;
        } else if (constraints.maxWidth < 400) {
          horizontalPadding = 12;
          fontSize = 14;
          iconSize = 20;
          spacing = 8;
        }

        return Container(
          padding: EdgeInsets.symmetric(
            horizontal: horizontalPadding,
            vertical: 16,
          ),
          color: Colors.white,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: Text(
                        DateFormat('yyyy年MM月').format(_selectedDate),
                        style: TextStyle(
                          fontSize: fontSize,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF37352F),
                        ),
                      ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _selectedDate = DateTime(
                                _selectedDate.year,
                                _selectedDate.month - 1,
                              );
                            });
                          },
                          icon: Icon(
                            Icons.chevron_left,
                            color: const Color(0xFF37352F),
                            size: iconSize,
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _selectedDate = DateTime(
                                _selectedDate.year,
                                _selectedDate.month + 1,
                              );
                            });
                          },
                          icon: Icon(
                            Icons.chevron_right,
                            color: const Color(0xFF37352F),
                            size: iconSize,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                SizedBox(height: spacing),

                // 响应式日历网格
                _buildResponsiveCalendar(constraints),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建响应式日历
  Widget _buildResponsiveCalendar(BoxConstraints constraints) {
    // 响应式参数
    double aspectRatio = 1.0;
    double margin = 2;
    double borderRadius = 4;
    double fontSize = 12;

    // iPad 横屏适配
    if (constraints.maxWidth > 900) {
      aspectRatio = 1.2; // 增加高度，避免溢出
      margin = 3;
      borderRadius = 6;
      fontSize = 14;
    } else if (constraints.maxWidth > 600) {
      aspectRatio = 1.1;
      margin = 2.5;
      borderRadius = 5;
      fontSize = 13;
    } else if (constraints.maxWidth < 400) {
      aspectRatio = 0.9; // 窄屏时降低高度
      margin = 1.5;
      borderRadius = 3;
      fontSize = 10;
    }

    return _buildCalendarGrid(
      aspectRatio: aspectRatio,
      margin: margin,
      borderRadius: borderRadius,
      fontSize: fontSize,
    );
  }

  /// 构建日历网格
  Widget _buildCalendarGrid({
    required double aspectRatio,
    required double margin,
    required double borderRadius,
    required double fontSize,
  }) {
    final now = DateTime.now();
    final firstDayOfMonth = DateTime(
      _selectedDate.year,
      _selectedDate.month,
      1,
    );
    final startDate = firstDayOfMonth.subtract(
      Duration(days: firstDayOfMonth.weekday - 1),
    );

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: aspectRatio,
        crossAxisSpacing: margin,
        mainAxisSpacing: margin,
      ),
      itemCount: 42, // 6 weeks * 7 days
      itemBuilder: (context, index) {
        final date = startDate.add(Duration(days: index));
        final isCurrentMonth = date.month == _selectedDate.month;
        final isToday =
            date.year == now.year &&
            date.month == now.month &&
            date.day == now.day;
        final hasEntry = _hasEntryOnDate(date);

        return GestureDetector(
          onTap: isCurrentMonth
              ? () {
                  setState(() {
                    _selectedDate = date;
                    _isCalendarVisible = false;
                  });
                  // 滚动到对应日期的日志
                  _scrollToDate(date);
                }
              : null,
          child: Container(
            decoration: BoxDecoration(
              color: isToday
                  ? const Color(0xFF2E7EED)
                  : hasEntry
                  ? const Color(0xFF2E7EED).withValues(alpha: 0.1)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(borderRadius),
            ),
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '${date.day}',
                    style: TextStyle(
                      fontSize: fontSize,
                      fontWeight: isToday ? FontWeight.w600 : FontWeight.w400,
                      color: isCurrentMonth
                          ? isToday
                                ? Colors.white
                                : const Color(0xFF37352F)
                          : const Color(0xFF9B9A97),
                    ),
                  ),
                  if (hasEntry && !isToday)
                    Container(
                      width: fontSize * 0.3, // 响应式指示器大小
                      height: fontSize * 0.3,
                      margin: EdgeInsets.only(top: fontSize * 0.15),
                      decoration: const BoxDecoration(
                        color: Color(0xFF2E7EED),
                        shape: BoxShape.circle,
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建日志列表
  Widget _buildReflectionList() {
    final filteredEntries = _getFilteredEntries();

    if (filteredEntries.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: filteredEntries.length,
      itemBuilder: (context, index) {
        final entry = filteredEntries[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildReflectionCard(entry),
        );
      },
    );
  }

  /// 构建日志卡片
  Widget _buildReflectionCard(ReflectionEntry entry) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF37352F).withValues(alpha: 0.1)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _editEntry(entry),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 头部信息
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            entry.title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF37352F),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            DateFormat(
                              'MM月dd日 EEEE',
                              'zh_CN',
                            ).format(entry.date),
                            style: TextStyle(
                              fontSize: 12,
                              color: const Color(0xFF9B9A97),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // 心情评分
                    _buildMoodIndicator(entry.mood),

                    const SizedBox(width: 8),

                    // 更多操作
                    PopupMenuButton<String>(
                      onSelected: (value) => _handleEntryAction(value, entry),
                      icon: const Icon(
                        Icons.more_horiz,
                        color: Color(0xFF9B9A97),
                        size: 18,
                      ),
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit_outlined, size: 16),
                              SizedBox(width: 8),
                              Text('编辑'),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'share',
                          child: Row(
                            children: [
                              Icon(
                                entry.isPublic
                                    ? Icons.public_off_outlined
                                    : Icons.public_outlined,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Text(entry.isPublic ? '取消分享' : '分享'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(
                                Icons.delete_outline,
                                size: 16,
                                color: Colors.red,
                              ),
                              SizedBox(width: 8),
                              Text('删除', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // 内容预览
                Text(
                  _getContentPreview(entry.content),
                  style: TextStyle(
                    fontSize: 14,
                    color: const Color(0xFF787774),
                    height: 1.5,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 12),

                // 标签和状态
                Row(
                  children: [
                    // 标签
                    Expanded(
                      child: Wrap(
                        spacing: 4,
                        runSpacing: 4,
                        children: entry.tags.map((tag) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              tag,
                              style: const TextStyle(
                                fontSize: 10,
                                color: Color(0xFF2E7EED),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),

                    // 公开状态
                    if (entry.isPublic)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF0F7B6C).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.public,
                              size: 10,
                              color: Color(0xFF0F7B6C),
                            ),
                            const SizedBox(width: 2),
                            const Text(
                              '已分享',
                              style: TextStyle(
                                fontSize: 10,
                                color: Color(0xFF0F7B6C),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建心情指示器
  Widget _buildMoodIndicator(int mood) {
    final colors = [
      Colors.grey,
      const Color(0xFFE03E3E),
      const Color(0xFFD9730D),
      const Color(0xFFFFD700),
      const Color(0xFF0F7B6C),
      const Color(0xFF2E7EED),
    ];

    final emojis = ['😐', '😢', '😕', '😊', '😄', '🤩'];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: colors[mood].withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(emojis[mood], style: const TextStyle(fontSize: 12)),
          const SizedBox(width: 4),
          Text(
            '$mood',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: colors[mood],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.book_outlined,
              size: 40,
              color: Color(0xFF2E7EED),
            ),
          ),

          const SizedBox(height: 24),

          const Text(
            '还没有日志记录',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),

          const SizedBox(height: 8),

          Text(
            _searchQuery.isNotEmpty || _selectedTag != '全部'
                ? '没有找到匹配的日志'
                : '开始记录您的学习与成长历程',
            style: TextStyle(fontSize: 14, color: const Color(0xFF9B9A97)),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 24),

          if (_searchQuery.isEmpty && _selectedTag == '全部')
            ElevatedButton.icon(
              onPressed: _createNewEntry,
              icon: const Icon(Icons.add),
              label: const Text('写第一篇日志'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2E7EED),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建浮动操作按钮
  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      heroTag: "reflection_fab", // 添加唯一标签解决Hero冲突
      onPressed: _createNewEntry,
      backgroundColor: const Color(0xFF2E7EED),
      foregroundColor: Colors.white,
      icon: const Icon(Icons.add),
      label: const Text('写日志'),
    );
  }

  /// 获取过滤后的日志条目
  List<ReflectionEntry> _getFilteredEntries() {
    return _entries.where((entry) {
      // 搜索过滤
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final matchesTitle = entry.title.toLowerCase().contains(query);
        final matchesContent = entry.content.toLowerCase().contains(query);
        if (!matchesTitle && !matchesContent) return false;
      }

      // 标签过滤
      if (_selectedTag != '全部') {
        if (!entry.tags.contains(_selectedTag)) return false;
      }

      return true;
    }).toList()..sort((a, b) => b.date.compareTo(a.date)); // 按日期倒序
  }

  /// 检查指定日期是否有日志
  bool _hasEntryOnDate(DateTime date) {
    return _entries.any(
      (entry) =>
          entry.date.year == date.year &&
          entry.date.month == date.month &&
          entry.date.day == date.day,
    );
  }

  /// 滚动到指定日期
  void _scrollToDate(DateTime date) {
    // 简化实现，实际应用中可以精确定位
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  /// 获取内容预览
  String _getContentPreview(String content) {
    // 移除Markdown语法，提取纯文本
    String preview = content
        .replaceAll(RegExp(r'#{1,6}\s'), '') // 移除标题标记
        .replaceAll(RegExp(r'\*\*(.*?)\*\*'), r'$1') // 移除粗体标记
        .replaceAll(RegExp(r'\*(.*?)\*'), r'$1') // 移除斜体标记
        .replaceAll(RegExp(r'`(.*?)`'), r'$1') // 移除代码标记
        .replaceAll(RegExp(r'\n+'), ' ') // 替换换行为空格
        .trim();

    if (preview.length > 100) {
      preview = '${preview.substring(0, 100)}...';
    }

    return preview;
  }

  /// 处理菜单操作
  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _showComingSoon('导出功能');
        break;
      case 'templates':
        _showTemplatesDialog();
        break;
      case 'settings':
        _showComingSoon('设置功能');
        break;
    }
  }

  /// 处理日志条目操作
  void _handleEntryAction(String action, ReflectionEntry entry) {
    switch (action) {
      case 'edit':
        _editEntry(entry);
        break;
      case 'share':
        _toggleEntryPublic(entry);
        break;
      case 'delete':
        _deleteEntry(entry);
        break;
    }
  }

  /// 创建新日志
  void _createNewEntry() {
    _showEditorDialog();
  }

  /// 编辑日志
  void _editEntry(ReflectionEntry entry) {
    _showEditorDialog(entry: entry);
  }

  /// 切换日志公开状态
  void _toggleEntryPublic(ReflectionEntry entry) {
    setState(() {
      entry.isPublic = !entry.isPublic;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(entry.isPublic ? '日志已设为公开' : '日志已设为私有'),
        backgroundColor: const Color(0xFF0F7B6C),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// 删除日志
  void _deleteEntry(ReflectionEntry entry) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除日志'),
        content: const Text('确定要删除这篇日志吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _entries.remove(entry);
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('日志已删除'),
                  backgroundColor: Color(0xFFE03E3E),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 显示编辑器对话框
  void _showEditorDialog({ReflectionEntry? entry}) {
    showDialog(
      context: context,
      builder: (context) => _ReflectionEditorDialog(
        entry: entry,
        onSaved: (savedEntry) {
          setState(() {
            if (entry == null) {
              _entries.insert(0, savedEntry);
            } else {
              final index = _entries.indexOf(entry);
              _entries[index] = savedEntry;
            }
          });
        },
      ),
    );
  }

  /// 显示模板对话框
  void _showTemplatesDialog() {
    showDialog(
      context: context,
      builder: (context) => _TemplatesDialog(
        onTemplateSelected: (template) {
          _showEditorDialog(
            entry: ReflectionEntry(
              id: DateTime.now().millisecondsSinceEpoch.toString(),
              title: template.title,
              content: template.content,
              date: DateTime.now(),
              tags: template.tags,
              mood: 3,
              isPublic: false,
            ),
          );
        },
      ),
    );
  }

  /// 显示即将推出提示
  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('该功能即将推出，敬请期待'),
        backgroundColor: const Color(0xFF2E7EED),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}

/// 反思日志条目数据模型
class ReflectionEntry {
  String id;
  String title;
  String content;
  DateTime date;
  List<String> tags;
  int mood; // 心情评分 0-5
  bool isPublic;

  ReflectionEntry({
    required this.id,
    required this.title,
    required this.content,
    required this.date,
    required this.tags,
    required this.mood,
    required this.isPublic,
  });
}

/// 日志模板数据模型
class ReflectionTemplate {
  final String title;
  final String content;
  final List<String> tags;

  const ReflectionTemplate({
    required this.title,
    required this.content,
    required this.tags,
  });
}

/// 反思编辑器对话框
class _ReflectionEditorDialog extends StatefulWidget {
  final ReflectionEntry? entry;
  final Function(ReflectionEntry) onSaved;

  const _ReflectionEditorDialog({this.entry, required this.onSaved});

  @override
  State<_ReflectionEditorDialog> createState() =>
      _ReflectionEditorDialogState();
}

class _ReflectionEditorDialogState extends State<_ReflectionEditorDialog> {
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _tagController = TextEditingController();

  List<String> _tags = [];
  int _mood = 3;
  bool _isPublic = false;

  @override
  void initState() {
    super.initState();

    if (widget.entry != null) {
      _titleController.text = widget.entry!.title;
      _contentController.text = widget.entry!.content;
      _tags = List.from(widget.entry!.tags);
      _mood = widget.entry!.mood;
      _isPublic = widget.entry!.isPublic;
    } else {
      _titleController.text = DateFormat('MM月dd日 学习反思').format(DateTime.now());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头部
            Row(
              children: [
                const Expanded(
                  child: Text(
                    '编写日志',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 标题输入
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: '标题',
                border: OutlineInputBorder(),
              ),
            ),

            const SizedBox(height: 16),

            // 内容输入
            Expanded(
              child: TextField(
                controller: _contentController,
                maxLines: null,
                expands: true,
                decoration: const InputDecoration(
                  labelText: '内容（支持Markdown）',
                  border: OutlineInputBorder(),
                  alignLabelWithHint: true,
                ),
                textAlignVertical: TextAlignVertical.top,
              ),
            ),

            const SizedBox(height: 16),

            // 标签和设置 - 修复溢出问题，改为垂直布局
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 心情评分行
                Row(
                  children: [
                    const Text(
                      '心情: ',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF37352F),
                      ),
                    ),
                    Expanded(
                      child: Wrap(
                        spacing: 4,
                        children: List.generate(6, (index) {
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                _mood = index;
                              });
                            },
                            child: Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: _mood == index
                                    ? const Color(0xFF2E7EED).withValues(alpha: 0.1)
                                    : null,
                                borderRadius: BorderRadius.circular(6),
                                border: _mood == index
                                    ? Border.all(
                                        color: const Color(0xFF2E7EED),
                                        width: 1,
                                      )
                                    : null,
                              ),
                              child: Text(
                                ['😐', '😢', '😕', '😊', '😄', '🤩'][index],
                                style: const TextStyle(fontSize: 18),
                              ),
                            ),
                          );
                        }),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // 公开分享行
                Row(
                  children: [
                    const Text(
                      '公开分享',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF37352F),
                      ),
                    ),
                    const Spacer(),
                    Switch(
                      value: _isPublic,
                      onChanged: (value) {
                        setState(() {
                          _isPublic = value;
                        });
                      },
                      activeColor: const Color(0xFF2E7EED),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 操作按钮
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('取消'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _saveEntry,
                    child: const Text('保存'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _saveEntry() {
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请输入标题')));
      return;
    }

    final entry = ReflectionEntry(
      id: widget.entry?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      title: _titleController.text.trim(),
      content: _contentController.text.trim(),
      date: widget.entry?.date ?? DateTime.now(),
      tags: _tags,
      mood: _mood,
      isPublic: _isPublic,
    );

    widget.onSaved(entry);
    Navigator.pop(context);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _tagController.dispose();
    super.dispose();
  }
}

/// 模板选择对话框
class _TemplatesDialog extends StatelessWidget {
  final Function(ReflectionTemplate) onTemplateSelected;

  const _TemplatesDialog({required this.onTemplateSelected});

  static const _templates = [
    ReflectionTemplate(
      title: '每日学习反思',
      content: '''# 今日学习反思

## 完成的任务
- [ ] 任务1
- [ ] 任务2
- [ ] 任务3

## 学习收获
今天学到了什么新知识？

## 遇到的困难
学习过程中遇到了哪些困难？是如何解决的？

## 明日计划
明天准备学习什么？

**今日评分：/10** ⭐''',
      tags: ['学习', '反思'],
    ),
    ReflectionTemplate(
      title: '目标规划',
      content: '''# 目标规划

## 短期目标（本周/本月）
1. 
2. 
3. 

## 中期目标（半年）
1. 
2. 
3. 

## 长期目标（一年）
1. 
2. 
3. 

## 行动计划
为了实现这些目标，我需要：

## 评估标准
如何判断目标是否达成？''',
      tags: ['目标', '规划'],
    ),
    ReflectionTemplate(
      title: '时间管理总结',
      content: '''# 时间管理总结

## 今日时间分配
- 学习时间：小时
- 娱乐时间：小时
- 其他时间：小时

## 效率最高的时段


## 时间浪费分析


## 改进方案


## 明日时间安排''',
      tags: ['时间管理', '效率'],
    ),
    ReflectionTemplate(
      title: '情感记录',
      content: '''# 情感记录

## 今日心情
总体心情：

## 情感触发事件
什么事情影响了我的心情？

## 情感处理
我是如何处理这些情感的？

## 感悟与成长
通过今天的经历，我学到了什么？

## 感恩记录
今天感谢的三件事：
1. 
2. 
3. ''',
      tags: ['情感', '成长'],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头部
            Row(
              children: [
                const Expanded(
                  child: Text(
                    '选择模板',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 模板列表
            Expanded(
              child: ListView.builder(
                itemCount: _templates.length,
                itemBuilder: (context, index) {
                  final template = _templates[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: ListTile(
                      title: Text(
                        template.title,
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                      subtitle: Wrap(
                        spacing: 4,
                        children: template.tags.map((tag) {
                          return Chip(
                            label: Text(
                              tag,
                              style: const TextStyle(fontSize: 10),
                            ),
                            backgroundColor: const Color(
                              0xFF2E7EED,
                            ).withValues(alpha: 0.1),
                            side: BorderSide.none,
                          );
                        }).toList(),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        onTemplateSelected(template);
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
