import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../l10n/app_localizations.dart';
import '../../providers/navigation_provider.dart';

/// 主容器页面 - 包含底部导航栏
///
/// 管理应用的主要导航和页面切换
/// 采用Notion风格设计，白纸黑字极简美学
class MainContainerPage extends ConsumerStatefulWidget {
  final Widget child;

  const MainContainerPage({super.key, required this.child});

  @override
  ConsumerState<MainContainerPage> createState() => _MainContainerPageState();
}

class _MainContainerPageState extends ConsumerState<MainContainerPage> {
  int _calculateSelectedIndex(BuildContext context) {
    final String location = GoRouterState.of(context).uri.path;
    if (location.startsWith('/home')) {
      return 0;
    }
    if (location.startsWith('/calendar')) {
      return 1;
    }
    if (location.startsWith('/store')) {
      return 2;
    }
    if (location.startsWith('/community')) {
      return 3;
    }
    if (location.startsWith('/profile')) {
      return 4;
    }
    return 0;
  }

  void _onItemTapped(int index, BuildContext context) {
    String route;
    switch (index) {
      case 0:
        route = '/home';
        break;
      case 1:
        route = '/calendar';
        break;
      case 2:
        route = '/store';
        break;
      case 3:
        route = '/community';
        break;
      case 4:
        route = '/profile';
        break;
      default:
        route = '/home';
    }

    // 更新导航状态
    ref.read(navigationProvider.notifier).updateRoute(route);

    // 执行导航
    context.go(route);
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    // SafeArea is properly applied to handle Dynamic Island and notch

    return Scaffold(
      body: SafeArea(child: widget.child),
      bottomNavigationBar: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          border: Border(top: BorderSide(color: Color(0xFFEAE9E7), width: 0.5)),
        ),
        child: BottomNavigationBar(
          currentIndex: _calculateSelectedIndex(context),
          onTap: (index) => _onItemTapped(index, context),
          items: [
            BottomNavigationBarItem(
              icon: const Icon(Icons.home_outlined),
              activeIcon: const Icon(Icons.home),
              label: l10n.home,
            ),
            BottomNavigationBarItem(
              icon: const Icon(Icons.calendar_today_outlined),
              activeIcon: const Icon(Icons.calendar_today),
              label: l10n.calendar,
            ),
            BottomNavigationBarItem(
              icon: const Icon(Icons.store_outlined),
              activeIcon: const Icon(Icons.store),
              label: l10n.store,
            ),
            BottomNavigationBarItem(
              icon: const Icon(Icons.people_outline),
              activeIcon: const Icon(Icons.people),
              label: l10n.community,
            ),
            BottomNavigationBarItem(
              icon: const Icon(Icons.person_outline),
              activeIcon: const Icon(Icons.person),
              label: l10n.profile,
            ),
          ],
          type: BottomNavigationBarType.fixed,
          selectedItemColor: const Color(0xFF2E7EED),
          unselectedItemColor: Colors.grey,
          backgroundColor: Colors.transparent, // 设置为透明，使用Container的背景色
          elevation: 0, // 移除阴影
        ),
      ),
    );
  }
}
