import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/user_profile.dart';

/// 用户个人资料服务
class UserProfileService {
  static const String _userProfileKey = 'oneday_user_profile';
  static const String _avatarDirectoryName = 'avatars';

  static UserProfileService? _instance;
  static UserProfileService get instance =>
      _instance ??= UserProfileService._();

  UserProfileService._();

  SharedPreferences? _prefs;

  /// 初始化服务
  Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// 获取用户资料
  Future<UserProfile> getUserProfile() async {
    try {
      await initialize();

      final jsonString = _prefs?.getString(_userProfileKey);
      if (jsonString != null) {
        final json = jsonDecode(jsonString) as Map<String, dynamic>;
        return UserProfile.fromJson(json);
      }

      // 如果没有保存的资料，返回默认资料
      final defaultProfile = UserProfile.defaultProfile('default_user');
      await saveUserProfile(defaultProfile);
      return defaultProfile;
    } catch (e) {
      debugPrint('获取用户资料失败: $e');
      return UserProfile.defaultProfile('default_user');
    }
  }

  /// 保存用户资料
  Future<bool> saveUserProfile(UserProfile profile) async {
    try {
      await initialize();

      final jsonString = jsonEncode(profile.toJson());
      final success =
          await _prefs?.setString(_userProfileKey, jsonString) ?? false;

      if (success) {
        debugPrint('✅ 用户资料已保存');
      } else {
        debugPrint('❌ 用户资料保存失败');
      }

      return success;
    } catch (e) {
      debugPrint('保存用户资料失败: $e');
      return false;
    }
  }

  /// 更新用户昵称
  Future<bool> updateNickname(String nickname) async {
    try {
      if (!UserProfile.isValidNickname(nickname)) {
        debugPrint('昵称格式无效');
        return false;
      }

      final currentProfile = await getUserProfile();
      final updatedProfile = currentProfile.copyWith(nickname: nickname.trim());
      return await saveUserProfile(updatedProfile);
    } catch (e) {
      debugPrint('更新昵称失败: $e');
      return false;
    }
  }

  /// 更新个人简介
  Future<bool> updateBio(String? bio) async {
    try {
      if (!UserProfile.isValidBio(bio)) {
        debugPrint('个人简介格式无效');
        return false;
      }

      final currentProfile = await getUserProfile();
      // 处理空字符串的情况
      final trimmedBio = bio?.trim();
      final finalBio = (trimmedBio == null || trimmedBio.isEmpty)
          ? null
          : trimmedBio;

      final updatedProfile = UserProfile(
        userId: currentProfile.userId,
        nickname: currentProfile.nickname,
        bio: finalBio,
        avatarPath: currentProfile.avatarPath,
        createdAt: currentProfile.createdAt,
        updatedAt: DateTime.now(),
      );
      return await saveUserProfile(updatedProfile);
    } catch (e) {
      debugPrint('更新个人简介失败: $e');
      return false;
    }
  }

  /// 保存头像文件
  Future<String?> saveAvatarFile(File imageFile) async {
    try {
      // 获取应用文档目录
      final appDir = await getApplicationDocumentsDirectory();
      final avatarDir = Directory(path.join(appDir.path, _avatarDirectoryName));

      // 创建头像目录（如果不存在）
      if (!await avatarDir.exists()) {
        await avatarDir.create(recursive: true);
      }

      // 生成唯一的文件名
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = path.extension(imageFile.path);
      final fileName = 'avatar_$timestamp$extension';
      final targetPath = path.join(avatarDir.path, fileName);

      // 复制文件到目标位置
      final savedFile = await imageFile.copy(targetPath);

      debugPrint('✅ 头像文件已保存: ${savedFile.path}');
      return savedFile.path;
    } catch (e) {
      debugPrint('保存头像文件失败: $e');
      return null;
    }
  }

  /// 更新用户头像
  Future<bool> updateAvatar(File imageFile) async {
    try {
      final avatarPath = await saveAvatarFile(imageFile);
      if (avatarPath == null) {
        return false;
      }

      final currentProfile = await getUserProfile();

      // 删除旧头像文件
      if (currentProfile.avatarPath != null) {
        await _deleteOldAvatar(currentProfile.avatarPath!);
      }

      final updatedProfile = currentProfile.copyWith(avatarPath: avatarPath);
      return await saveUserProfile(updatedProfile);
    } catch (e) {
      debugPrint('更新头像失败: $e');
      return false;
    }
  }

  /// 删除旧头像文件
  Future<void> _deleteOldAvatar(String oldAvatarPath) async {
    try {
      final oldFile = File(oldAvatarPath);
      if (await oldFile.exists()) {
        await oldFile.delete();
        debugPrint('🗑️ 已删除旧头像文件: $oldAvatarPath');
      }
    } catch (e) {
      debugPrint('删除旧头像文件失败: $e');
    }
  }

  /// 删除用户头像
  Future<bool> removeAvatar() async {
    try {
      final currentProfile = await getUserProfile();

      if (currentProfile.avatarPath != null) {
        await _deleteOldAvatar(currentProfile.avatarPath!);
      }

      final updatedProfile = currentProfile.copyWith(avatarPath: null);
      return await saveUserProfile(updatedProfile);
    } catch (e) {
      debugPrint('删除头像失败: $e');
      return false;
    }
  }

  /// 清理所有用户数据
  Future<bool> clearUserData() async {
    try {
      await initialize();

      // 删除用户资料
      await _prefs?.remove(_userProfileKey);

      // 删除头像目录
      final appDir = await getApplicationDocumentsDirectory();
      final avatarDir = Directory(path.join(appDir.path, _avatarDirectoryName));
      if (await avatarDir.exists()) {
        await avatarDir.delete(recursive: true);
      }

      debugPrint('🗑️ 用户数据已清理');
      return true;
    } catch (e) {
      debugPrint('清理用户数据失败: $e');
      return false;
    }
  }

  /// 验证头像文件
  static bool isValidAvatarFile(File file) {
    final extension = path.extension(file.path).toLowerCase();
    return ['.jpg', '.jpeg', '.png', '.webp'].contains(extension);
  }

  /// 获取头像文件大小（MB）
  static Future<double> getAvatarFileSize(File file) async {
    try {
      final bytes = await file.length();
      return bytes / (1024 * 1024); // 转换为MB
    } catch (e) {
      return 0.0;
    }
  }

  /// 重置服务状态（仅用于测试）
  @visibleForTesting
  void resetForTesting() {
    _prefs = null;
  }
}
