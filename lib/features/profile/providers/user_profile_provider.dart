import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_profile.dart';
import '../services/user_profile_service.dart';

/// 用户资料状态
class UserProfileState {
  final UserProfile? profile;
  final bool isLoading;
  final String? error;
  final bool isUpdating;

  const UserProfileState({
    this.profile,
    this.isLoading = false,
    this.error,
    this.isUpdating = false,
  });

  UserProfileState copyWith({
    UserProfile? profile,
    bool? isLoading,
    String? error,
    bool? isUpdating,
  }) {
    return UserProfileState(
      profile: profile ?? this.profile,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isUpdating: isUpdating ?? this.isUpdating,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserProfileState &&
          runtimeType == other.runtimeType &&
          profile == other.profile &&
          isLoading == other.isLoading &&
          error == other.error &&
          isUpdating == other.isUpdating;

  @override
  int get hashCode =>
      profile.hashCode ^
      isLoading.hashCode ^
      error.hashCode ^
      isUpdating.hashCode;
}

/// 用户资料状态管理器
class UserProfileNotifier extends StateNotifier<UserProfileState> {
  final UserProfileService _service;

  UserProfileNotifier(this._service) : super(const UserProfileState()) {
    loadProfile();
  }

  /// 加载用户资料
  Future<void> loadProfile() async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final profile = await _service.getUserProfile();
      state = state.copyWith(profile: profile, isLoading: false);
    } catch (e) {
      debugPrint('加载用户资料失败: $e');
      state = state.copyWith(isLoading: false, error: '加载用户资料失败: $e');
    }
  }

  /// 更新昵称
  Future<bool> updateNickname(String nickname) async {
    if (state.isUpdating) return false;

    // 验证昵称
    if (!UserProfile.isValidNickname(nickname)) {
      state = state.copyWith(error: '昵称长度应在1-20个字符之间');
      return false;
    }

    state = state.copyWith(isUpdating: true, error: null);

    try {
      final success = await _service.updateNickname(nickname);
      if (success) {
        final updatedProfile = state.profile?.copyWith(
          nickname: nickname.trim(),
        );
        state = state.copyWith(profile: updatedProfile, isUpdating: false);
        return true;
      } else {
        state = state.copyWith(isUpdating: false, error: '更新昵称失败');
        return false;
      }
    } catch (e) {
      debugPrint('更新昵称失败: $e');
      state = state.copyWith(isUpdating: false, error: '更新昵称失败: $e');
      return false;
    }
  }

  /// 更新个人简介
  Future<bool> updateBio(String? bio) async {
    if (state.isUpdating) return false;

    // 验证个人简介
    if (!UserProfile.isValidBio(bio)) {
      state = state.copyWith(error: '个人简介长度不能超过100个字符');
      return false;
    }

    state = state.copyWith(isUpdating: true, error: null);

    try {
      final success = await _service.updateBio(bio);
      if (success) {
        final trimmedBio = bio?.trim();
        final finalBio = (trimmedBio == null || trimmedBio.isEmpty)
            ? null
            : trimmedBio;
        final updatedProfile = state.profile?.copyWith(
          bio: finalBio,
          clearBio: finalBio == null,
        );
        state = state.copyWith(profile: updatedProfile, isUpdating: false);
        return true;
      } else {
        state = state.copyWith(isUpdating: false, error: '更新个人简介失败');
        return false;
      }
    } catch (e) {
      debugPrint('更新个人简介失败: $e');
      state = state.copyWith(isUpdating: false, error: '更新个人简介失败: $e');
      return false;
    }
  }

  /// 更新头像
  Future<bool> updateAvatar(File imageFile) async {
    if (state.isUpdating) return false;

    // 验证文件
    if (!UserProfileService.isValidAvatarFile(imageFile)) {
      state = state.copyWith(error: '不支持的图片格式，请选择JPG、PNG或WebP格式的图片');
      return false;
    }

    // 检查文件大小（限制为5MB）
    final fileSize = await UserProfileService.getAvatarFileSize(imageFile);
    if (fileSize > 5.0) {
      state = state.copyWith(error: '图片文件过大，请选择小于5MB的图片');
      return false;
    }

    state = state.copyWith(isUpdating: true, error: null);

    try {
      final success = await _service.updateAvatar(imageFile);
      if (success) {
        // 重新加载资料以获取最新的头像路径
        await loadProfile();
        return true;
      } else {
        state = state.copyWith(isUpdating: false, error: '更新头像失败');
        return false;
      }
    } catch (e) {
      debugPrint('更新头像失败: $e');
      state = state.copyWith(isUpdating: false, error: '更新头像失败: $e');
      return false;
    }
  }

  /// 删除头像
  Future<bool> removeAvatar() async {
    if (state.isUpdating) return false;

    state = state.copyWith(isUpdating: true, error: null);

    try {
      final success = await _service.removeAvatar();
      if (success) {
        final updatedProfile = state.profile?.copyWith(clearAvatarPath: true);
        state = state.copyWith(profile: updatedProfile, isUpdating: false);
        return true;
      } else {
        state = state.copyWith(isUpdating: false, error: '删除头像失败');
        return false;
      }
    } catch (e) {
      debugPrint('删除头像失败: $e');
      state = state.copyWith(isUpdating: false, error: '删除头像失败: $e');
      return false;
    }
  }

  /// 清除错误信息
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// 刷新资料
  Future<void> refresh() async {
    await loadProfile();
  }
}

/// 用户资料服务Provider
final userProfileServiceProvider = Provider<UserProfileService>((ref) {
  return UserProfileService.instance;
});

/// 用户资料状态Provider
final userProfileProvider =
    StateNotifierProvider<UserProfileNotifier, UserProfileState>((ref) {
      final service = ref.watch(userProfileServiceProvider);
      return UserProfileNotifier(service);
    });

/// 当前用户资料Provider（便捷访问）
final currentUserProfileProvider = Provider<UserProfile?>((ref) {
  return ref.watch(userProfileProvider).profile;
});

/// 用户资料加载状态Provider
final userProfileLoadingProvider = Provider<bool>((ref) {
  return ref.watch(userProfileProvider).isLoading;
});

/// 用户资料更新状态Provider
final userProfileUpdatingProvider = Provider<bool>((ref) {
  return ref.watch(userProfileProvider).isUpdating;
});

/// 用户资料错误Provider
final userProfileErrorProvider = Provider<String?>((ref) {
  return ref.watch(userProfileProvider).error;
});
