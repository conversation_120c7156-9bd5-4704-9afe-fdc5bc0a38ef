import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:permission_handler/permission_handler.dart';
import '../providers/user_profile_provider.dart';
import '../models/user_profile.dart';
import '../../../utils/safe_area_helper.dart';

/// 个人资料编辑页面
class ProfileEditPage extends ConsumerStatefulWidget {
  const ProfileEditPage({super.key});

  @override
  ConsumerState<ProfileEditPage> createState() => _ProfileEditPageState();
}

class _ProfileEditPageState extends ConsumerState<ProfileEditPage> {
  final _formKey = GlobalKey<FormState>();
  final _nicknameController = TextEditingController();
  final _bioController = TextEditingController();
  final _nicknameFocusNode = FocusNode();
  final _bioFocusNode = FocusNode();

  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _setupFocusListeners();
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    _bioController.dispose();
    _nicknameFocusNode.dispose();
    _bioFocusNode.dispose();
    super.dispose();
  }

  /// 初始化控制器
  void _initializeControllers() {
    final profile = ref.read(currentUserProfileProvider);
    if (profile != null) {
      _nicknameController.text = profile.nickname;
      _bioController.text = profile.bio ?? '';
    }

    // 监听文本变化
    _nicknameController.addListener(_onTextChanged);
    _bioController.addListener(_onTextChanged);
  }

  /// 设置焦点监听器
  void _setupFocusListeners() {
    _nicknameFocusNode.addListener(() {
      if (!_nicknameFocusNode.hasFocus) {
        _saveNicknameIfChanged();
      }
    });

    _bioFocusNode.addListener(() {
      if (!_bioFocusNode.hasFocus) {
        _saveBioIfChanged();
      }
    });
  }

  /// 文本变化监听
  void _onTextChanged() {
    final profile = ref.read(currentUserProfileProvider);
    if (profile != null) {
      final hasNicknameChanged =
          _nicknameController.text.trim() != profile.nickname;
      final hasBioChanged = _bioController.text.trim() != (profile.bio ?? '');

      if (hasNicknameChanged || hasBioChanged) {
        if (!_hasChanges) {
          setState(() {
            _hasChanges = true;
          });
        }
      } else {
        if (_hasChanges) {
          setState(() {
            _hasChanges = false;
          });
        }
      }
    }
  }

  /// 保存昵称（如果有变化）
  Future<void> _saveNicknameIfChanged() async {
    final profile = ref.read(currentUserProfileProvider);
    if (profile != null &&
        _nicknameController.text.trim() != profile.nickname) {
      debugPrint('自动保存昵称: ${_nicknameController.text.trim()}');
      final success = await ref
          .read(userProfileProvider.notifier)
          .updateNickname(_nicknameController.text);
      debugPrint('昵称自动保存结果: $success');

      if (success) {
        _showSuccessSnackBar('昵称已保存');
      } else {
        _showErrorSnackBar('昵称保存失败');
      }
    }
  }

  /// 保存个人简介（如果有变化）
  Future<void> _saveBioIfChanged() async {
    final profile = ref.read(currentUserProfileProvider);
    final newBio = _bioController.text.trim().isEmpty
        ? null
        : _bioController.text.trim();
    if (profile != null && newBio != profile.bio) {
      debugPrint('自动保存个人简介: $newBio');
      final success = await ref
          .read(userProfileProvider.notifier)
          .updateBio(newBio);
      debugPrint('个人简介自动保存结果: $success');

      if (success) {
        _showSuccessSnackBar('个人简介已保存');
      } else {
        _showErrorSnackBar('个人简介保存失败');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final profileState = ref.watch(userProfileProvider);
    final profile = profileState.profile;

    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF7F6F3),
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Color(0xFF37352F),
            size: 20,
          ),
        ),
        title: const Text(
          '编辑资料',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        actions: [
          if (_hasChanges)
            TextButton(
              onPressed: _saveAllChanges,
              child: Text(
                '保存',
                style: TextStyle(
                  color: const Color(0xFF2F76DA),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
      body: profileState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 头像编辑区域
                    _buildAvatarSection(profile),

                    const SizedBox(height: 24),

                    // 基本信息编辑
                    _buildBasicInfoSection(profile),

                    const SizedBox(height: 24),

                    // 错误提示
                    if (profileState.error != null)
                      _buildErrorMessage(profileState.error!),
                  ],
                ),
              ),
            ),
    );
  }

  /// 构建头像编辑区域
  Widget _buildAvatarSection(UserProfile? profile) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        children: [
          // 头像显示
          GestureDetector(
            onTap: _showAvatarOptions,
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(50),
                border: Border.all(
                  color: const Color(0xFF37352F).withValues(alpha: 0.1),
                  width: 2,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(48),
                child: profile?.avatarPath != null
                    ? Image.file(
                        File(profile!.avatarPath!),
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return _buildDefaultAvatar();
                        },
                      )
                    : _buildDefaultAvatar(),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 头像操作按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextButton.icon(
                onPressed: _showAvatarOptions,
                icon: const Icon(
                  Icons.camera_alt_outlined,
                  size: 18,
                  color: Color(0xFF2F76DA),
                ),
                label: const Text(
                  '更换头像',
                  style: TextStyle(color: Color(0xFF2F76DA), fontSize: 14),
                ),
              ),
              if (profile?.avatarPath != null) ...[
                const SizedBox(width: 16),
                TextButton.icon(
                  onPressed: _removeAvatar,
                  icon: const Icon(
                    Icons.delete_outline,
                    size: 18,
                    color: Color(0xFF9B9A97),
                  ),
                  label: const Text(
                    '删除头像',
                    style: TextStyle(color: Color(0xFF9B9A97), fontSize: 14),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// 构建默认头像
  Widget _buildDefaultAvatar() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(48),
      ),
      child: const Icon(Icons.person, size: 50, color: Color(0xFF2E7EED)),
    );
  }

  /// 构建基本信息编辑区域
  Widget _buildBasicInfoSection(UserProfile? profile) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 昵称输入
          _buildInputField(
            label: '昵称',
            controller: _nicknameController,
            focusNode: _nicknameFocusNode,
            hintText: '请输入昵称',
            maxLength: 20,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '昵称不能为空';
              }
              if (!UserProfile.isValidNickname(value)) {
                return '昵称长度应在1-20个字符之间';
              }
              return null;
            },
          ),

          const SizedBox(height: 20),

          // 个人简介输入
          _buildInputField(
            label: '个人简介',
            controller: _bioController,
            focusNode: _bioFocusNode,
            hintText: '介绍一下自己吧',
            maxLength: 100,
            maxLines: 3,
            validator: (value) {
              if (!UserProfile.isValidBio(value)) {
                return '个人简介长度不能超过100个字符';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  /// 构建输入字段
  Widget _buildInputField({
    required String label,
    required TextEditingController controller,
    required FocusNode focusNode,
    required String hintText,
    int? maxLength,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Color(0xFF37352F),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          focusNode: focusNode,
          maxLength: maxLength,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: TextStyle(
              color: const Color(0xFF37352F).withValues(alpha: 0.5),
              fontSize: 14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: const Color(0xFF37352F).withValues(alpha: 0.2),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: const Color(0xFF37352F).withValues(alpha: 0.2),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF2F76DA), width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFE03E3E), width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 12,
            ),
            counterText: '',
          ),
        ),
      ],
    );
  }

  /// 构建错误消息
  Widget _buildErrorMessage(String error) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFE03E3E).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFFE03E3E).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(Icons.error_outline, color: Color(0xFFE03E3E), size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              error,
              style: const TextStyle(color: Color(0xFFE03E3E), fontSize: 14),
            ),
          ),
          IconButton(
            onPressed: () {
              ref.read(userProfileProvider.notifier).clearError();
            },
            icon: const Icon(Icons.close, color: Color(0xFFE03E3E), size: 18),
          ),
        ],
      ),
    );
  }

  /// 显示头像选项
  void _showAvatarOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '选择头像',
              style: TextStyle(
                color: Color(0xFF37352F),
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildAvatarOption(
                  icon: Icons.camera_alt_outlined,
                  label: '拍照',
                  onTap: () => _pickImage(ImageSource.camera),
                ),
                _buildAvatarOption(
                  icon: Icons.photo_library_outlined,
                  label: '相册',
                  onTap: () => _pickImage(ImageSource.gallery),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  /// 构建头像选项按钮
  Widget _buildAvatarOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: const Color(0xFF2F76DA).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: const Color(0xFF2F76DA), size: 32),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                color: Color(0xFF2F76DA),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 检查权限
  Future<bool> _checkPermissions(ImageSource source) async {
    debugPrint('开始检查权限，源: $source');

    try {
      if (source == ImageSource.camera) {
        debugPrint('检查相机权限...');
        final status = await Permission.camera.status;
        debugPrint('相机权限状态: $status');

        if (status.isGranted) {
          return true;
        } else if (status.isDenied) {
          debugPrint('请求相机权限...');
          final result = await Permission.camera.request();
          debugPrint('相机权限请求结果: $result');
          return result.isGranted;
        } else if (status.isPermanentlyDenied) {
          _showPermissionDeniedDialog(source);
          return false;
        }
      } else {
        // 对于相册，根据平台和Android版本选择合适的权限
        debugPrint('检查相册权限...');

        if (Platform.isAndroid) {
          // 先尝试photos权限（Android 13+）
          var status = await Permission.photos.status;
          debugPrint('photos权限状态: $status');

          if (status.isGranted) {
            return true;
          } else if (status.isDenied) {
            debugPrint('请求photos权限...');
            var result = await Permission.photos.request();
            debugPrint('photos权限请求结果: $result');

            if (result.isGranted) {
              return true;
            }
          }

          // 如果photos权限不可用，尝试storage权限（Android 12及以下）
          debugPrint('尝试storage权限...');
          status = await Permission.storage.status;
          debugPrint('storage权限状态: $status');

          if (status.isGranted) {
            return true;
          } else if (status.isDenied) {
            debugPrint('请求storage权限...');
            final result = await Permission.storage.request();
            debugPrint('storage权限请求结果: $result');
            return result.isGranted;
          } else if (status.isPermanentlyDenied) {
            _showPermissionDeniedDialog(source);
            return false;
          }
        } else {
          // iOS 使用 photos 权限
          final status = await Permission.photos.status;
          debugPrint('iOS photos权限状态: $status');

          if (status.isGranted) {
            return true;
          } else if (status.isDenied) {
            debugPrint('请求iOS photos权限...');
            final result = await Permission.photos.request();
            debugPrint('iOS photos权限请求结果: $result');
            return result.isGranted;
          } else if (status.isPermanentlyDenied) {
            _showPermissionDeniedDialog(source);
            return false;
          }
        }
      }
    } catch (e) {
      debugPrint('权限检查失败: $e');
      // 如果权限检查失败，尝试直接使用image_picker
      return true;
    }

    return false;
  }

  /// 显示权限被拒绝的对话框
  void _showPermissionDeniedDialog(ImageSource source) {
    final String permissionName = source == ImageSource.camera ? '相机' : '相册';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Text(
          '需要$permissionName权限',
          style: const TextStyle(
            color: Color(0xFF37352F),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          '请在设置中允许OneDay访问您的$permissionName，以便选择头像。',
          style: const TextStyle(color: Color(0xFF37352F), fontSize: 14),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消', style: TextStyle(color: Color(0xFF9B9A97))),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings();
            },
            child: const Text(
              '去设置',
              style: TextStyle(color: Color(0xFF2F76DA)),
            ),
          ),
        ],
      ),
    );
  }

  /// 选择图片
  Future<void> _pickImage(ImageSource source) async {
    final sourceName = source == ImageSource.camera ? '相机' : '相册';
    debugPrint('开始选择图片，源: $sourceName');

    Navigator.of(context).pop(); // 关闭底部弹窗

    try {
      // 🔧 修复：使用安全的系统对话框调用方式
      debugPrint('使用SafeAreaHelper调用ImagePicker...');

      final pickedFile = await SafeAreaHelper.showSystemDialogSafely<XFile?>(
        context,
        () async {
          final picker = ImagePicker();
          debugPrint('创建ImagePicker实例成功');

          debugPrint('调用pickImage方法...');
          return await picker.pickImage(
            source: source,
            maxWidth: 1024,
            maxHeight: 1024,
            imageQuality: 85,
          );
        },
      );

      if (pickedFile != null) {
        debugPrint('图片选择成功: ${pickedFile.path}');
        await _cropImage(File(pickedFile.path));
      } else {
        debugPrint('用户取消了图片选择');
      }
    } catch (e) {
      debugPrint('选择图片错误: $e');
      debugPrint('错误类型: ${e.runtimeType}');
      debugPrint('错误堆栈: ${StackTrace.current}');

      // 如果直接调用失败，尝试权限检查
      debugPrint('直接调用失败，尝试权限检查...');
      try {
        final hasPermission = await _checkPermissions(source);
        if (hasPermission && mounted) {
          debugPrint('权限已授予，重试选择图片...');
          final retryPickedFile =
              await SafeAreaHelper.showSystemDialogSafely<XFile?>(
                context,
                () async {
                  final retryPicker = ImagePicker();
                  return await retryPicker.pickImage(
                    source: source,
                    maxWidth: 1024,
                    maxHeight: 1024,
                    imageQuality: 85,
                  );
                },
              );

          if (retryPickedFile != null) {
            debugPrint('重试图片选择成功: ${retryPickedFile.path}');
            await _cropImage(File(retryPickedFile.path));
          }
        } else {
          debugPrint('权限未授予');
          _showErrorSnackBar('需要$sourceName权限才能选择图片');
        }
      } catch (permissionError) {
        debugPrint('权限检查也失败: $permissionError');
        _showErrorSnackBar('选择图片失败: $e');
      }
    }
  }

  /// 裁剪图片
  Future<void> _cropImage(File imageFile) async {
    debugPrint('开始裁剪图片: ${imageFile.path}');

    try {
      // 检查文件是否存在
      if (!await imageFile.exists()) {
        debugPrint('图片文件不存在: ${imageFile.path}');
        _showErrorSnackBar('图片文件不存在');
        return;
      }

      debugPrint('图片文件存在，文件大小: ${await imageFile.length()} bytes');
      debugPrint('开始调用ImageCropper...');

      // 使用更简化的配置
      final croppedFile = await ImageCropper().cropImage(
        sourcePath: imageFile.path,
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 85,
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: '裁剪头像',
            toolbarColor: const Color(0xFF2F76DA),
            toolbarWidgetColor: Colors.white,
            backgroundColor: Colors.white,
            activeControlsWidgetColor: const Color(0xFF2F76DA),
            initAspectRatio: CropAspectRatioPreset.square,
            lockAspectRatio: true,
            aspectRatioPresets: [CropAspectRatioPreset.square],
            hideBottomControls: false,
          ),
          IOSUiSettings(
            title: '裁剪头像',
            doneButtonTitle: '完成',
            cancelButtonTitle: '取消',
            aspectRatioLockEnabled: true,
            resetAspectRatioEnabled: false,
            aspectRatioPickerButtonHidden: true,
            resetButtonHidden: true,
            rotateButtonsHidden: false,
            aspectRatioPresets: [CropAspectRatioPreset.square],
          ),
        ],
      );

      debugPrint('ImageCropper调用完成，结果: ${croppedFile?.path ?? 'null'}');

      if (!mounted) return;

      if (croppedFile != null) {
        debugPrint('裁剪成功，裁剪后文件: ${croppedFile.path}');

        // 检查裁剪后的文件是否存在
        final croppedImageFile = File(croppedFile.path);
        if (await croppedImageFile.exists()) {
          debugPrint('裁剪后文件存在，大小: ${await croppedImageFile.length()} bytes');

          final success = await ref
              .read(userProfileProvider.notifier)
              .updateAvatar(croppedImageFile);
          if (success && mounted) {
            _showSuccessSnackBar('头像更新成功');
          } else if (mounted) {
            _showErrorSnackBar('头像更新失败');
          }
        } else {
          debugPrint('裁剪后文件不存在');
          _showErrorSnackBar('裁剪后文件不存在');
        }
      } else {
        debugPrint('用户取消了裁剪');
        // 用户取消了裁剪，提供备用方案
        _showCropCancelledDialog(imageFile);
      }
    } catch (e) {
      debugPrint('裁剪图片错误: $e');
      debugPrint('错误类型: ${e.runtimeType}');
      debugPrint('错误堆栈: ${StackTrace.current}');

      if (!mounted) return;

      // 如果裁剪失败，提供直接使用原图的选项
      _showCropErrorDialog(imageFile, e.toString());
    }
  }

  /// 显示裁剪取消对话框
  void _showCropCancelledDialog(File imageFile) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text(
          '使用原图',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: const Text(
          '您取消了图片裁剪，是否直接使用原图作为头像？',
          style: TextStyle(color: Color(0xFF37352F), fontSize: 14),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消', style: TextStyle(color: Color(0xFF9B9A97))),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await ref
                  .read(userProfileProvider.notifier)
                  .updateAvatar(imageFile);
              if (success) {
                _showSuccessSnackBar('头像更新成功');
              }
            },
            child: const Text(
              '使用原图',
              style: TextStyle(color: Color(0xFF2F76DA)),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示裁剪错误对话框
  void _showCropErrorDialog(File imageFile, String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text(
          '裁剪失败',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: const Text(
          '图片裁剪失败，是否直接使用原图作为头像？',
          style: TextStyle(color: Color(0xFF37352F), fontSize: 14),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消', style: TextStyle(color: Color(0xFF9B9A97))),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await ref
                  .read(userProfileProvider.notifier)
                  .updateAvatar(imageFile);
              if (success) {
                _showSuccessSnackBar('头像更新成功');
              }
            },
            child: const Text(
              '使用原图',
              style: TextStyle(color: Color(0xFF2F76DA)),
            ),
          ),
        ],
      ),
    );
  }

  /// 删除头像
  Future<void> _removeAvatar() async {
    final success = await ref.read(userProfileProvider.notifier).removeAvatar();
    if (success) {
      _showSuccessSnackBar('头像已删除');
    }
  }

  /// 保存所有更改
  Future<void> _saveAllChanges() async {
    if (_formKey.currentState?.validate() ?? false) {
      debugPrint('开始保存所有更改...');

      bool nicknameSuccess = true;
      bool bioSuccess = true;

      // 保存昵称
      final profile = ref.read(currentUserProfileProvider);
      if (profile != null &&
          _nicknameController.text.trim() != profile.nickname) {
        debugPrint('保存昵称: ${_nicknameController.text.trim()}');
        nicknameSuccess = await ref
            .read(userProfileProvider.notifier)
            .updateNickname(_nicknameController.text);
        debugPrint('昵称保存结果: $nicknameSuccess');
      }

      // 保存个人简介
      final newBio = _bioController.text.trim().isEmpty
          ? null
          : _bioController.text.trim();
      if (profile != null && newBio != profile.bio) {
        debugPrint('保存个人简介: $newBio');
        bioSuccess = await ref
            .read(userProfileProvider.notifier)
            .updateBio(newBio);
        debugPrint('个人简介保存结果: $bioSuccess');
      }

      if (nicknameSuccess && bioSuccess) {
        setState(() {
          _hasChanges = false;
        });
        _showSuccessSnackBar('资料已保存');
        debugPrint('✅ 所有更改保存成功');
      } else {
        _showErrorSnackBar('保存失败，请重试');
        debugPrint('❌ 保存失败 - 昵称: $nicknameSuccess, 简介: $bioSuccess');
      }
    } else {
      _showErrorSnackBar('请检查输入内容');
      debugPrint('❌ 表单验证失败');
    }
  }

  /// 显示成功提示
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF0F7B6C),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFFE03E3E),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
