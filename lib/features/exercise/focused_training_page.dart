import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../core/data/pao_exercises_data.dart';
import '../vocabulary/vocabulary_service.dart';
import '../vocabulary/word_model.dart';
import '../vocabulary/word_root_service.dart';
import 'exercise_session_page.dart';

/// 集中训练页面 - 从考研词汇中选择单词进行30-60分钟的具身记忆训练
class FocusedTrainingPage extends ConsumerStatefulWidget {
  const FocusedTrainingPage({super.key});

  @override
  ConsumerState<FocusedTrainingPage> createState() =>
      _FocusedTrainingPageState();
}

class _FocusedTrainingPageState extends ConsumerState<FocusedTrainingPage> {
  String? _selectedRoot;
  String _selectedDifficulty = 'all';
  int _selectedDuration = 30; // 默认30分钟
  bool _isLoading = false;
  List<WordRoot> _availableRoots = [];
  bool _isLoadingRoots = true;
  @override
  void initState() {
    super.initState();
    _loadAvailableRoots();
  }

  /// 加载可用的词根
  Future<void> _loadAvailableRoots() async {
    try {
      final wordRootService = ref.read(wordRootServiceProvider);
      final allRoots = await wordRootService.getAllRoots();

      setState(() {
        _availableRoots = allRoots.values.toList();
        _isLoadingRoots = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingRoots = false;
      });
      print('加载词根失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          '集中训练',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF37352F)),
          onPressed: () => context.pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 页面说明
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFE8F4FD),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: const Color(0xFF2F76DA).withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: const Color(0xFF2F76DA),
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        '集中训练说明',
                        style: TextStyle(
                          color: Color(0xFF2F76DA),
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '选择词根进行针对性训练，比如选择"act"（行动）词根可以练习相关动作的同时记忆包含该词根的考研单词。训练时长建议30-60分钟。',
                    style: TextStyle(
                      color: Color(0xFF37352F),
                      fontSize: 14,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 32),

            // 词根选择
            _buildSectionTitle('选择词根'),
            const SizedBox(height: 16),
            _buildRootSelection(),

            const SizedBox(height: 32),

            // 难度选择
            _buildSectionTitle('选择难度'),
            const SizedBox(height: 16),
            _buildDifficultySelection(),

            const SizedBox(height: 32),

            // 训练时长
            _buildSectionTitle('训练时长'),
            const SizedBox(height: 16),
            _buildDurationSelection(),

            const SizedBox(height: 48),

            // 开始训练按钮
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: _selectedRoot != null && !_isLoading
                    ? _startTraining
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF2F76DA),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : Text(
                        '开始训练 ($_selectedDuration分钟)',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        color: Color(0xFF37352F),
        fontSize: 18,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildRootSelection() {
    if (_isLoadingRoots) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32.0),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_availableRoots.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32.0),
          child: Text(
            '暂无可用词根',
            style: TextStyle(color: Color(0xFF787774), fontSize: 16),
          ),
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // 根据可用宽度动态计算列数和宽高比
        int columns = 3;
        double aspectRatio = 1.4; // 降低宽高比，增加卡片高度

        // 响应式布局调整
        if (constraints.maxWidth < 600) {
          columns = 2;
          aspectRatio = 1.6; // 窄屏时稍微增加宽高比
        } else if (constraints.maxWidth > 900) {
          columns = 4;
          aspectRatio = 1.2; // 宽屏时进一步降低宽高比
        }

        return GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: columns,
            childAspectRatio: aspectRatio,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: _availableRoots.length,
          itemBuilder: (context, index) {
            final root = _availableRoots[index];
            final isSelected = _selectedRoot == root.root;

            return Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  setState(() {
                    _selectedRoot = root.root;
                  });
                },
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.all(10), // 稍微减少padding
                  decoration: BoxDecoration(
                    color: isSelected ? const Color(0xFF2F76DA) : Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFF2F76DA)
                          : const Color(0xFFE3E2E0),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Flexible(
                        child: Text(
                          '${root.root} - ${root.meaning}',
                          style: TextStyle(
                            color: isSelected
                                ? Colors.white
                                : const Color(0xFF37352F),
                            fontSize: 13, // 稍微减小字体
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(height: 3), // 减少间距
                      Flexible(
                        flex: 2,
                        child: Text(
                          '${root.origin} | 例词: ${root.examples.take(2).join(', ')}',
                          style: TextStyle(
                            color: isSelected
                                ? Colors.white.withValues(alpha: 0.8)
                                : const Color(0xFF787774),
                            fontSize: 11, // 稍微减小字体
                            height: 1.2, // 调整行高
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildDifficultySelection() {
    final difficulties = [
      {'value': 'all', 'label': '全部难度'},
      {'value': 'easy', 'label': '简单'},
      {'value': 'intermediate', 'label': '中等'},
      {'value': 'hard', 'label': '困难'},
    ];

    return Row(
      children: difficulties.map((difficulty) {
        final isSelected = _selectedDifficulty == difficulty['value'];
        return Expanded(
          child: Padding(
            padding: const EdgeInsets.only(right: 8),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  setState(() {
                    _selectedDifficulty = difficulty['value']!;
                  });
                },
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: isSelected ? const Color(0xFF2F76DA) : Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFF2F76DA)
                          : const Color(0xFFE3E2E0),
                    ),
                  ),
                  child: Text(
                    difficulty['label']!,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: isSelected
                          ? Colors.white
                          : const Color(0xFF37352F),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDurationSelection() {
    final durations = [30, 45, 60];

    return Row(
      children: durations.map((duration) {
        final isSelected = _selectedDuration == duration;
        return Expanded(
          child: Padding(
            padding: const EdgeInsets.only(right: 8),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  setState(() {
                    _selectedDuration = duration;
                  });
                },
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: isSelected ? const Color(0xFF2F76DA) : Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFF2F76DA)
                          : const Color(0xFFE3E2E0),
                    ),
                  ),
                  child: Text(
                    '$duration分钟',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: isSelected
                          ? Colors.white
                          : const Color(0xFF37352F),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// 开始训练
  Future<void> _startTraining() async {
    if (_selectedRoot == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // 从考研词汇库中获取以选定字母开头的单词
      final vocabularyService = ref.read(vocabularyServiceProvider);
      final words = await _getWordsByRoot(_selectedRoot!, vocabularyService);

      if (words.isEmpty) {
        _showErrorDialog('未找到包含词根"$_selectedRoot"的单词');
        return;
      }

      // 随机选择一个单词开始训练
      final selectedWord = words.first;
      final exercises = _generatePAOExercisesForWord(selectedWord.key);

      if (mounted) {
        context.push(
          '/exercise-session',
          extra: {
            'exercises': exercises,
            'paoWord': selectedWord.key,
            'wordPhonetic':
                selectedWord.value.phonetic ??
                '/${selectedWord.key.toLowerCase()}/',
            'wordMeaning': selectedWord.value.definition,
            'mode': ExerciseMode.pao,
            'duration': _selectedDuration * 60, // 转换为秒
          },
        );
      }
    } catch (e) {
      _showErrorDialog('启动训练失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 根据词根获取单词
  Future<List<MapEntry<String, WordDetails>>> _getWordsByRoot(
    String root,
    VocabularyService vocabularyService,
  ) async {
    final allWords = await vocabularyService.getWordsByCategory(
      'graduate_exam',
    );
    final wordRootService = ref.read(wordRootServiceProvider);

    // 使用词根服务按词根分组单词
    final wordsByRoot = await wordRootService.groupWordsByRoot(allWords);

    // 获取指定词根的单词
    var filteredWords = wordsByRoot[root] ?? [];

    // 如果指定了难度，进一步筛选
    if (_selectedDifficulty != 'all') {
      filteredWords = filteredWords
          .where((entry) => entry.value.difficulty == _selectedDifficulty)
          .toList();
    }

    // 随机打乱并限制数量
    filteredWords.shuffle();
    return filteredWords.take(20).toList(); // 最多20个单词
  }

  /// 为单词生成PAO动作序列
  List<PAOExercise> _generatePAOExercisesForWord(String word) {
    final exercises = <PAOExercise>[];
    final allExercises = PAOExercisesData.getAllExercises();

    for (int i = 0; i < word.length; i++) {
      final letter = word[i].toUpperCase();
      final exercise = allExercises[letter];
      if (exercise != null) {
        exercises.add(exercise);
      }
    }

    return exercises;
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('提示'),
        content: Text(message),
        actions: [
          TextButton(onPressed: () => context.pop(), child: const Text('确定')),
        ],
      ),
    );
  }
}
