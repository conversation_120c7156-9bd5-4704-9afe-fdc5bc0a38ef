import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'custom_action_library.dart';
import 'custom_action_library_service.dart';
import 'custom_library_editor_page.dart';
import 'action_library_category_manager.dart';
import 'create_action_library_dialog.dart';
import '../community/community_storage_service.dart';
import '../community/community_feed_page.dart';

/// 管理自定义动作库对话框
class ManageCustomLibrariesDialog extends StatefulWidget {
  final CustomActionLibraryService customLibraryService;
  final VoidCallback onLibrariesChanged;

  const ManageCustomLibrariesDialog({
    super.key,
    required this.customLibraryService,
    required this.onLibrariesChanged,
  });

  @override
  State<ManageCustomLibrariesDialog> createState() =>
      _ManageCustomLibrariesDialogState();
}

class _ManageCustomLibrariesDialogState
    extends State<ManageCustomLibrariesDialog> {
  // 分类管理器
  final ActionLibraryCategoryManager _categoryManager =
      ActionLibraryCategoryManager();

  // 分类侧边栏状态
  bool _isSidebarVisible = false;
  String? _selectedCategory;

  // 过滤后的动作库列表
  List<CustomActionLibrary> get _filteredLibraries {
    final libraries = widget.customLibraryService.libraries;
    if (_selectedCategory == null) {
      return libraries;
    }
    return libraries
        .where((library) => library.category == _selectedCategory)
        .toList();
  }

  @override
  void initState() {
    super.initState();
    _initializeCategoryManager();
  }

  /// 初始化分类管理器
  Future<void> _initializeCategoryManager() async {
    await _categoryManager.loadFromStorage();

    // 如果没有分类数据，强制初始化默认分类
    if (_categoryManager.categories.isEmpty) {
      print('⚠️ 分类数据为空，强制初始化默认分类');
      _categoryManager.initializeDefaultCategories();
    }

    print('🔧 分类管理器初始化完成，分类数量: ${_categoryManager.categories.length}');
    for (var category in _categoryManager.categories) {
      print('📂 分类: ${category.title} (子分类: ${category.children.length})');
    }
    if (mounted) {
      setState(() {});
    }
  }

  /// 切换侧边栏显示状态
  void _toggleSidebar() {
    setState(() {
      _isSidebarVisible = !_isSidebarVisible;
    });
  }

  /// 分类节点选择回调
  void _onCategorySelected(ActionLibraryCategoryNode node) {
    setState(() {
      if (node.title.isEmpty) {
        // 空节点表示清除选择
        _selectedCategory = null;
      } else if (_selectedCategory == node.title) {
        _selectedCategory = null; // 取消选择
      } else {
        _selectedCategory = node.title;
      }
      _isSidebarVisible = false; // 关闭侧边栏
    });
  }

  /// 分类变更回调
  void _onCategoriesChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final libraries = _filteredLibraries; // 使用过滤后的列表
    final selectedLibraryId = widget.customLibraryService.selectedLibraryId;

    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxWidth: 500,
          maxHeight: MediaQuery.of(context).size.height * 0.9,
        ),
        child: Stack(
          children: [
            // 主内容
            SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 标题栏
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 16,
                    ),
                    decoration: const BoxDecoration(
                      border: Border(
                        bottom: BorderSide(color: Color(0xFFE3E2E0), width: 1),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: const Color(
                              0xFF7C3AED,
                            ).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.folder_outlined,
                            color: Color(0xFF7C3AED),
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                '管理自定义动作库',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF37352F),
                                ),
                              ),
                              Text(
                                _selectedCategory == null
                                    ? '编辑、删除或选择您的动作库'
                                    : '分类：$_selectedCategory',
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFF9B9A97),
                                ),
                              ),
                            ],
                          ),
                        ),
                        // 左上角三个杠按钮（触发分类抽屉栏）
                        IconButton(
                          onPressed: _toggleSidebar,
                          icon: const Icon(
                            Icons.menu,
                            color: Color(0xFF9B9A97),
                          ),
                          tooltip: '分类管理',
                        ),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(
                            Icons.close,
                            color: Color(0xFF9B9A97),
                          ),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(
                            minWidth: 32,
                            minHeight: 32,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // 动作库列表
                  Flexible(
                    child: Container(
                      constraints: const BoxConstraints(
                        minHeight: 200,
                        maxHeight: 400,
                      ),
                      child: libraries.isEmpty
                          ? _buildEmptyState()
                          : ListView.builder(
                              padding: const EdgeInsets.all(16),
                              itemCount: libraries.length,
                              itemBuilder: (context, index) {
                                final library = libraries[index];
                                final isSelected =
                                    library.id == selectedLibraryId;

                                return Container(
                                  margin: const EdgeInsets.only(bottom: 12),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? const Color(
                                            0xFF7C3AED,
                                          ).withValues(alpha: 0.05)
                                        : const Color(0xFFF7F6F3),
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: isSelected
                                          ? const Color(0xFF7C3AED)
                                          : const Color(0xFFE3E2E0),
                                      width: isSelected ? 2 : 1,
                                    ),
                                  ),
                                  child: ListTile(
                                    contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 8,
                                    ),
                                    leading: Container(
                                      width: 48,
                                      height: 48,
                                      decoration: BoxDecoration(
                                        color: const Color(
                                          0xFF7C3AED,
                                        ).withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            '${library.completedActionsCount}',
                                            style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                              color: Color(0xFF7C3AED),
                                            ),
                                          ),
                                          const Text(
                                            '/26',
                                            style: TextStyle(
                                              fontSize: 10,
                                              color: Color(0xFF9B9A97),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    title: Text(
                                      library.name,
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: Color(0xFF37352F),
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        if (library.description.isNotEmpty) ...[
                                          const SizedBox(height: 4),
                                          Text(
                                            library.description,
                                            style: const TextStyle(
                                              fontSize: 14,
                                              color: Color(0xFF9B9A97),
                                            ),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                        const SizedBox(height: 4),
                                        Wrap(
                                          spacing: 8,
                                          runSpacing: 4,
                                          children: [
                                            // 分类标签
                                            Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                    horizontal: 6,
                                                    vertical: 2,
                                                  ),
                                              decoration: BoxDecoration(
                                                color: const Color(
                                                  0xFF2E7EED,
                                                ).withValues(alpha: 0.1),
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                              child: Text(
                                                library.category,
                                                style: const TextStyle(
                                                  fontSize: 12,
                                                  color: Color(0xFF2E7EED),
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ),
                                            // 完成度标签
                                            Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                    horizontal: 6,
                                                    vertical: 2,
                                                  ),
                                              decoration: BoxDecoration(
                                                color: const Color(
                                                  0xFF0F7B6C,
                                                ).withValues(alpha: 0.1),
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                              child: Text(
                                                '${(library.completionPercentage * 100).toInt()}% 完成',
                                                style: const TextStyle(
                                                  fontSize: 12,
                                                  color: Color(0xFF0F7B6C),
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ),
                                            if (isSelected)
                                              Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                      horizontal: 6,
                                                      vertical: 2,
                                                    ),
                                                decoration: BoxDecoration(
                                                  color: const Color(
                                                    0xFF7C3AED,
                                                  ).withValues(alpha: 0.1),
                                                  borderRadius:
                                                      BorderRadius.circular(4),
                                                ),
                                                child: const Text(
                                                  '当前使用',
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    color: Color(0xFF7C3AED),
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    trailing: PopupMenuButton<String>(
                                      icon: const Icon(
                                        Icons.more_vert,
                                        color: Color(0xFF9B9A97),
                                      ),
                                      onSelected: (action) =>
                                          _handleLibraryAction(action, library),
                                      itemBuilder: (context) => [
                                        if (!isSelected)
                                          const PopupMenuItem(
                                            value: 'select',
                                            child: Row(
                                              children: [
                                                Icon(
                                                  Icons.radio_button_checked,
                                                  size: 16,
                                                ),
                                                SizedBox(width: 8),
                                                Text('选择使用'),
                                              ],
                                            ),
                                          ),
                                        const PopupMenuItem(
                                          value: 'edit',
                                          child: Row(
                                            children: [
                                              Icon(Icons.edit, size: 16),
                                              SizedBox(width: 8),
                                              Text('编辑动作'),
                                            ],
                                          ),
                                        ),
                                        const PopupMenuItem(
                                          value: 'change_category',
                                          child: Row(
                                            children: [
                                              Icon(Icons.category, size: 16),
                                              SizedBox(width: 8),
                                              Text('更改分类'),
                                            ],
                                          ),
                                        ),
                                        const PopupMenuItem(
                                          value: 'duplicate',
                                          child: Row(
                                            children: [
                                              Icon(Icons.copy, size: 16),
                                              SizedBox(width: 8),
                                              Text('复制'),
                                            ],
                                          ),
                                        ),
                                        const PopupMenuItem(
                                          value: 'export',
                                          child: Row(
                                            children: [
                                              Icon(Icons.download, size: 16),
                                              SizedBox(width: 8),
                                              Text('导出'),
                                            ],
                                          ),
                                        ),
                                        const PopupMenuDivider(),
                                        const PopupMenuItem(
                                          value: 'delete',
                                          child: Row(
                                            children: [
                                              Icon(
                                                Icons.delete,
                                                size: 16,
                                                color: Colors.red,
                                              ),
                                              SizedBox(width: 8),
                                              Text(
                                                '删除',
                                                style: TextStyle(
                                                  color: Colors.red,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    onTap: () => _editLibrary(library),
                                  ),
                                );
                              },
                            ),
                    ),
                  ),

                  // 底部操作栏
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: const BoxDecoration(
                      border: Border(
                        top: BorderSide(color: Color(0xFFE3E2E0), width: 1),
                      ),
                    ),
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        // 如果宽度足够，使用Row布局；否则使用Column布局
                        final isWideEnough = constraints.maxWidth > 400;

                        if (isWideEnough) {
                          return Row(
                            children: [
                              Flexible(
                                child: Text(
                                  '共 ${libraries.length} 个动作库',
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Color(0xFF9B9A97),
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              const SizedBox(width: 16),
                              // 按钮区域
                              Wrap(
                                spacing: 8,
                                children: [
                                  ElevatedButton.icon(
                                    onPressed: _showCreateLibraryDialog,
                                    icon: const Icon(Icons.add, size: 16),
                                    label: const Text('创建动作库'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xFF7C3AED),
                                      foregroundColor: Colors.white,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 8,
                                      ),
                                    ),
                                  ),
                                  OutlinedButton.icon(
                                    onPressed: _showImportDialog,
                                    icon: const Icon(Icons.upload, size: 16),
                                    label: const Text('导入'),
                                    style: OutlinedButton.styleFrom(
                                      foregroundColor: const Color(0xFF2E7EED),
                                      side: const BorderSide(
                                        color: Color(0xFF2E7EED),
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 8,
                                      ),
                                    ),
                                  ),
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(),
                                    style: TextButton.styleFrom(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 8,
                                      ),
                                    ),
                                    child: const Text(
                                      '关闭',
                                      style: TextStyle(
                                        color: Color(0xFF9B9A97),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          );
                        } else {
                          return Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // 统计信息
                              Row(
                                children: [
                                  Flexible(
                                    child: Text(
                                      '共 ${libraries.length} 个动作库',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Color(0xFF9B9A97),
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              // 按钮区域 - 使用Wrap来处理溢出
                              Wrap(
                                alignment: WrapAlignment.end,
                                spacing: 6,
                                runSpacing: 6,
                                children: [
                                  ElevatedButton.icon(
                                    onPressed: _showCreateLibraryDialog,
                                    icon: const Icon(Icons.add, size: 16),
                                    label: const Text('创建动作库'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xFF7C3AED),
                                      foregroundColor: Colors.white,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 10,
                                        vertical: 6,
                                      ),
                                    ),
                                  ),
                                  OutlinedButton.icon(
                                    onPressed: _showImportDialog,
                                    icon: const Icon(Icons.upload, size: 16),
                                    label: const Text('导入'),
                                    style: OutlinedButton.styleFrom(
                                      foregroundColor: const Color(0xFF2E7EED),
                                      side: const BorderSide(
                                        color: Color(0xFF2E7EED),
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 10,
                                        vertical: 6,
                                      ),
                                    ),
                                  ),
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(),
                                    style: TextButton.styleFrom(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 10,
                                        vertical: 6,
                                      ),
                                    ),
                                    child: const Text(
                                      '关闭',
                                      style: TextStyle(
                                        color: Color(0xFF9B9A97),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          );
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),

            // 分类侧边栏
            _buildCategorySidebar(),
          ],
        ),
      ),
    );
  }

  /// 构建分类侧边栏
  Widget _buildCategorySidebar() {
    return ActionLibraryCategorySidebar(
      isVisible: _isSidebarVisible,
      onClose: _toggleSidebar,
      onCategorySelected: _onCategorySelected,
      categoryManager: _categoryManager,
      onCategoriesChanged: _onCategoriesChanged,
      selectedCategory: _selectedCategory,
      customLibraryService: widget.customLibraryService,
    );
  }

  /// 显示创建动作库对话框
  Future<void> _showCreateLibraryDialog() async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => CreateActionLibraryDialog(
        customLibraryService: widget.customLibraryService,
        categoryManager: _categoryManager,
        initialCategory: _selectedCategory, // 传递当前选中的分类
        onLibraryCreated: () {
          setState(() {});
          widget.onLibrariesChanged();
        },
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.folder_open_outlined,
            size: 64,
            color: Color(0xFF9B9A97),
          ),
          const SizedBox(height: 16),
          const Text(
            '暂无自定义动作库',
            style: TextStyle(fontSize: 16, color: Color(0xFF9B9A97)),
          ),
          const SizedBox(height: 8),
          Text(
            _selectedCategory == null ? '点击"创建动作库"按钮创建您的第一个动作库' : '当前分类下暂无动作库',
            style: const TextStyle(fontSize: 14, color: Color(0xFF9B9A97)),
          ),
        ],
      ),
    );
  }

  /// 处理动作库操作
  void _handleLibraryAction(String action, CustomActionLibrary library) async {
    switch (action) {
      case 'select':
        await widget.customLibraryService.selectLibrary(library.id);
        setState(() {});
        widget.onLibrariesChanged();
        break;
      case 'edit':
        _editLibrary(library);
        break;
      case 'change_category':
        _changeLibraryCategory(library);
        break;
      case 'duplicate':
        _duplicateLibrary(library);
        break;
      case 'export':
        _exportLibrary(library);
        break;
      case 'delete':
        _deleteLibrary(library);
        break;
    }
  }

  /// 编辑动作库
  void _editLibrary(CustomActionLibrary library) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => CustomLibraryEditorPage(
              library: library,
              customLibraryService: widget.customLibraryService,
            ),
          ),
        )
        .then((_) {
          setState(() {});
          widget.onLibrariesChanged();
        });
  }

  /// 更改动作库分类
  Future<void> _changeLibraryCategory(CustomActionLibrary library) async {
    // 调试信息
    print('🔧 开始更改动作库分类');
    print('📚 动作库: ${library.name}');
    print('📂 当前分类: ${library.category}');
    print('📋 可用分类: ${_categoryManager.getAllCategoryNames()}');

    final String? newCategory = await showDialog<String>(
      context: context,
      builder: (context) => _CategorySelectionDialog(
        categoryManager: _categoryManager,
        currentCategory: library.category,
        libraryTitle: library.name,
      ),
    );

    if (newCategory != null && newCategory != library.category) {
      try {
        final updatedLibrary = library.copyWith(category: newCategory);
        await widget.customLibraryService.updateLibrary(updatedLibrary);
        setState(() {});
        widget.onLibrariesChanged();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('动作库"${library.name}"已移动到"$newCategory"分类'),
              backgroundColor: const Color(0xFF0F7B6C),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('更改分类失败: $e'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    }
  }

  /// 复制动作库
  void _duplicateLibrary(CustomActionLibrary library) async {
    try {
      final newName = '${library.name} (副本)';
      await widget.customLibraryService.duplicateLibrary(library.id, newName);
      setState(() {});
      widget.onLibrariesChanged();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('动作库"$newName"复制成功'),
            backgroundColor: const Color(0xFF0F7B6C),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('复制失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// 导出动作库
  void _exportLibrary(CustomActionLibrary library) async {
    try {
      final file = await widget.customLibraryService.saveLibraryToFile(
        library.id,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('动作库已导出到: ${file.path}'),
            backgroundColor: const Color(0xFF0F7B6C),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('导出失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// 删除动作库
  void _deleteLibrary(CustomActionLibrary library) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除动作库"${library.name}"吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              // 保存context引用
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              Navigator.of(context).pop();
              await widget.customLibraryService.deleteLibrary(library.id);
              setState(() {});
              widget.onLibrariesChanged();

              if (mounted) {
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text('动作库"${library.name}"已删除'),
                    backgroundColor: const Color(0xFF0F7B6C),
                  ),
                );
              }
            },
            child: const Text('删除', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// 显示导入对话框
  void _showImportDialog() {
    final textController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.upload,
                      color: Color(0xFF2E7EED),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '导入动作库',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF37352F),
                          ),
                        ),
                        Text(
                          '粘贴动作库的JSON数据',
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF9B9A97),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Color(0xFF9B9A97)),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // 输入框
              const Text(
                'JSON数据',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF37352F),
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: textController,
                maxLines: 10,
                decoration: InputDecoration(
                  hintText: '粘贴从其他设备导出的动作库JSON数据...',
                  hintStyle: const TextStyle(color: Color(0xFF9B9A97)),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFF2E7EED)),
                  ),
                  filled: true,
                  fillColor: const Color(0xFFF7F6F3),
                  contentPadding: const EdgeInsets.all(12),
                ),
              ),

              const SizedBox(height: 24),

              // 操作按钮
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        '取消',
                        style: TextStyle(
                          color: Color(0xFF9B9A97),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _importLibrary(textController.text),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2E7EED),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child: const Text(
                        '导入',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 导入动作库
  void _importLibrary(String jsonData) async {
    if (jsonData.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入JSON数据'), backgroundColor: Colors.red),
      );
      return;
    }

    try {
      Navigator.of(context).pop(); // 关闭导入对话框

      final library = await widget.customLibraryService.importLibrary(jsonData);
      setState(() {});
      widget.onLibrariesChanged();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('动作库"${library.name}"导入成功'),
            backgroundColor: const Color(0xFF0F7B6C),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('导入失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }
}

/// 动作库分类侧边栏组件（复用知忆相册的CategorySidebar设计）
class ActionLibraryCategorySidebar extends StatefulWidget {
  final bool isVisible;
  final VoidCallback onClose;
  final Function(ActionLibraryCategoryNode) onCategorySelected;
  final ActionLibraryCategoryManager categoryManager;
  final VoidCallback onCategoriesChanged;
  final String? selectedCategory;
  final CustomActionLibraryService customLibraryService;

  const ActionLibraryCategorySidebar({
    super.key,
    required this.isVisible,
    required this.onClose,
    required this.onCategorySelected,
    required this.categoryManager,
    required this.onCategoriesChanged,
    required this.customLibraryService,
    this.selectedCategory,
  });

  @override
  State<ActionLibraryCategorySidebar> createState() =>
      _ActionLibraryCategorySidebarState();
}

class _ActionLibraryCategorySidebarState
    extends State<ActionLibraryCategorySidebar> {
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final sidebarWidth = screenWidth > 600 ? 320.0 : screenWidth * 0.7;

    return Stack(
      children: [
        // 背景遮罩
        if (widget.isVisible)
          GestureDetector(
            onTap: widget.onClose,
            child: Container(color: Colors.black.withValues(alpha: 0.3)),
          ),

        // 侧边栏
        AnimatedPositioned(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeInOut,
          left: widget.isVisible ? 0 : -sidebarWidth,
          top: 0,
          bottom: 0,
          width: sidebarWidth,
          child: Material(
            elevation: 8,
            child: Container(
              color: Colors.white,
              child: SafeArea(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 顶部操作栏
                    _buildTopBar(),
                    // 分类树列表
                    Expanded(
                      child: ListView(
                        padding: EdgeInsets.zero,
                        children: _buildTreeNodes(
                          widget.categoryManager.categories,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建顶部操作栏
  Widget _buildTopBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        border: Border(bottom: BorderSide(color: Color(0xFFE3E2E0), width: 1)),
      ),
      child: Row(
        children: [
          const Icon(Icons.folder_outlined, color: Color(0xFF7C3AED), size: 20),
          const SizedBox(width: 8),
          const Expanded(
            child: Text(
              '分类',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
          ),
          // 添加分类按钮
          IconButton(
            onPressed: _addRootCategory,
            icon: const Icon(Icons.add, color: Color(0xFF7C3AED), size: 20),
            tooltip: '添加分类',
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
          if (widget.selectedCategory != null)
            TextButton(
              onPressed: () {
                widget.onCategorySelected(
                  ActionLibraryCategoryNode(title: ''), // 传递空节点表示清除选择
                );
              },
              child: const Text(
                '清除',
                style: TextStyle(color: Color(0xFF7C3AED), fontSize: 14),
              ),
            ),
          IconButton(
            onPressed: widget.onClose,
            icon: const Icon(Icons.close, color: Color(0xFF9B9A97)),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
        ],
      ),
    );
  }

  /// 构建分类树节点
  List<Widget> _buildTreeNodes(
    List<ActionLibraryCategoryNode> nodes, {
    int level = 0,
  }) {
    List<Widget> widgets = [];

    for (final node in nodes) {
      final isSelected = widget.selectedCategory == node.title;

      widgets.add(
        Container(
          decoration: BoxDecoration(
            color: isSelected
                ? const Color(0xFF7C3AED).withValues(alpha: 0.1)
                : null,
            border: const Border(
              bottom: BorderSide(color: Color(0xFFE3E2E0), width: 0.5),
            ),
          ),
          child: Row(
            children: [
              // 可点击的分类名称区域
              Expanded(
                child: InkWell(
                  onTap: () => widget.onCategorySelected(node),
                  child: Container(
                    padding: EdgeInsets.only(
                      left: 16.0 + (level * 20.0),
                      right: 8.0,
                      top: 12.0,
                      bottom: 12.0,
                    ),
                    child: Row(
                      children: [
                        if (node.children.isNotEmpty) ...[
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                node.isExpanded = !node.isExpanded;
                              });
                            },
                            child: Icon(
                              node.isExpanded
                                  ? Icons.expand_more
                                  : Icons.chevron_right,
                              size: 16,
                              color: const Color(0xFF9B9A97),
                            ),
                          ),
                          const SizedBox(width: 4),
                        ] else ...[
                          const SizedBox(width: 20),
                        ],
                        Expanded(
                          child: Text(
                            node.title,
                            style: TextStyle(
                              fontSize: 14,
                              color: isSelected
                                  ? const Color(0xFF7C3AED)
                                  : const Color(0xFF37352F),
                              fontWeight: isSelected
                                  ? FontWeight.w500
                                  : FontWeight.w400,
                            ),
                          ),
                        ),
                        if (isSelected)
                          const Icon(
                            Icons.check,
                            size: 16,
                            color: Color(0xFF7C3AED),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
              // 更多操作按钮
              PopupMenuButton<String>(
                onSelected: (action) => _handleCategoryAction(action, node),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(
                          Icons.edit_outlined,
                          size: 16,
                          color: Color(0xFF2F76DA),
                        ),
                        SizedBox(width: 8),
                        Text('编辑分类'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'add_child',
                    child: Row(
                      children: [
                        Icon(
                          Icons.create_new_folder_outlined,
                          size: 16,
                          color: Color(0xFF2F76DA),
                        ),
                        SizedBox(width: 8),
                        Text('添加子分类'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'share',
                    child: Row(
                      children: [
                        Icon(
                          Icons.share_outlined,
                          size: 16,
                          color: Color(0xFF2F76DA),
                        ),
                        SizedBox(width: 8),
                        Text('共享到社区'),
                      ],
                    ),
                  ),
                  const PopupMenuDivider(),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete_outline, size: 16, color: Colors.red),
                        SizedBox(width: 8),
                        Text('删除分类', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
                icon: const Icon(
                  Icons.more_horiz,
                  color: Color(0xFF9B9A97),
                  size: 20,
                ),
                tooltip: '更多操作',
                padding: EdgeInsets.zero,
                splashRadius: 20,
              ),
            ],
          ),
        ),
      );

      // 递归添加子节点
      if (node.isExpanded && node.children.isNotEmpty) {
        widgets.addAll(_buildTreeNodes(node.children, level: level + 1));
      }
    }

    return widgets;
  }

  /// 添加根分类
  void _addRootCategory() {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加分类'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: '分类名称',
            border: OutlineInputBorder(),
          ),
          onSubmitted: (value) {
            if (value.trim().isNotEmpty) {
              setState(() {
                widget.categoryManager.categories.add(
                  ActionLibraryCategoryNode(
                    title: value.trim(),
                    children: [],
                    isExpanded: false,
                  ),
                );
              });
              widget.onCategoriesChanged();
              Navigator.of(context).pop();
            }
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final value = controller.text.trim();
              if (value.isNotEmpty) {
                setState(() {
                  widget.categoryManager.categories.add(
                    ActionLibraryCategoryNode(
                      title: value,
                      children: [],
                      isExpanded: false,
                    ),
                  );
                });
                widget.onCategoriesChanged();
              }
              Navigator.of(context).pop();
            },
            child: const Text('添加'),
          ),
        ],
      ),
    );
  }

  /// 处理分类操作
  void _handleCategoryAction(String action, ActionLibraryCategoryNode node) {
    switch (action) {
      case 'edit':
        _editCategory(node);
        break;
      case 'add_child':
        _addChildCategory(node);
        break;
      case 'share':
        _shareCategory(node);
        break;
      case 'delete':
        _deleteCategory(node);
        break;
    }
  }

  /// 编辑分类
  void _editCategory(ActionLibraryCategoryNode node) {
    final controller = TextEditingController(text: node.title);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑分类'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: '分类名称',
            border: OutlineInputBorder(),
          ),
          onSubmitted: (value) => _saveEditedCategory(controller, node),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => _saveEditedCategory(controller, node),
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  /// 保存编辑的分类
  void _saveEditedCategory(
    TextEditingController controller,
    ActionLibraryCategoryNode node,
  ) async {
    final newTitle = controller.text.trim();

    // 验证输入
    if (newTitle.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('分类名称不能为空'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
      return;
    }

    // 检查是否与现有分类重复（排除当前分类）
    final allCategoryNames = widget.categoryManager.getAllCategoryNames();
    if (newTitle != node.title && allCategoryNames.contains(newTitle)) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('分类名称"$newTitle"已存在'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
      return;
    }

    try {
      // 保存原始名称用于提示
      final originalTitle = node.title;

      // 更新节点标题
      setState(() {
        node.title = newTitle;
      });

      // 保存到本地存储
      await widget.categoryManager.saveToStorage();

      // 触发回调刷新UI
      widget.onCategoriesChanged();

      // 关闭对话框
      if (mounted) {
        Navigator.of(context).pop();

        // 显示成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              originalTitle == newTitle
                  ? '分类已保存'
                  : '分类"$originalTitle"已重命名为"$newTitle"',
            ),
            backgroundColor: const Color(0xFF0F7B6C),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(milliseconds: 300),
          ),
        );
      }
    } catch (e) {
      // 保存失败，恢复原始状态
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('保存失败: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// 添加子分类
  void _addChildCategory(ActionLibraryCategoryNode node) {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加子分类'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: '子分类名称',
            border: OutlineInputBorder(),
          ),
          onSubmitted: (value) {
            if (value.trim().isNotEmpty) {
              setState(() {
                node.children.add(
                  ActionLibraryCategoryNode(
                    title: value.trim(),
                    children: [],
                    isExpanded: false,
                  ),
                );
                node.isExpanded = true; // 自动展开父节点
              });
              widget.onCategoriesChanged();
              Navigator.of(context).pop();
            }
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final value = controller.text.trim();
              if (value.isNotEmpty) {
                setState(() {
                  node.children.add(
                    ActionLibraryCategoryNode(
                      title: value,
                      children: [],
                      isExpanded: false,
                    ),
                  );
                  node.isExpanded = true; // 自动展开父节点
                });
                widget.onCategoriesChanged();
              }
              Navigator.of(context).pop();
            },
            child: const Text('添加'),
          ),
        ],
      ),
    );
  }

  /// 共享分类到社区
  void _shareCategory(ActionLibraryCategoryNode node) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text('共享到社区'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('确定要将分类"${node.title}"共享到OneDay社区吗？'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFF7F6F3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFFE3E2E0)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.folder_outlined,
                        color: Color(0xFF7C3AED),
                        size: 16,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        node.title,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF37352F),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '包含 ${_getCategoryChildrenCount(node)} 个子分类',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF9B9A97),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '分享后其他用户可以导入您的分类结构',
              style: TextStyle(fontSize: 12, color: Color(0xFF9B9A97)),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => _performShareCategory(node),
            child: const Text('共享'),
          ),
        ],
      ),
    );
  }

  /// 执行分类共享
  void _performShareCategory(ActionLibraryCategoryNode node) async {
    try {
      // 关闭确认对话框
      Navigator.of(context).pop();

      // 显示加载提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 12),
                Text('正在共享到社区...'),
              ],
            ),
            backgroundColor: Color(0xFF2E7EED),
            duration: Duration(seconds: 2),
          ),
        );
      }

      // 创建社区帖子
      await _createCategorySharePost(node);

      // 等待一小段时间确保数据已保存
      await Future.delayed(const Duration(milliseconds: 100));

      // 显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('分类"${node.title}"已成功共享到社区！'),
            backgroundColor: const Color(0xFF0F7B6C),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(milliseconds: 300),
            action: SnackBarAction(
              label: '查看',
              textColor: Colors.white,
              onPressed: () {
                // 导航到社区页面
                context.go('/community');
              },
            ),
          ),
        );
      }
    } catch (e) {
      // 显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('共享失败: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// 获取分类子节点数量（递归计算）
  int _getCategoryChildrenCount(ActionLibraryCategoryNode node) {
    int count = node.children.length;
    for (var child in node.children) {
      count += _getCategoryChildrenCount(child);
    }
    return count;
  }

  /// 创建分类共享帖子
  Future<void> _createCategorySharePost(ActionLibraryCategoryNode node) async {
    final storageService = CommunityStorageService.instance;

    // 获取下一个帖子ID
    final postId = await storageService.getNextPostId();

    // 创建用户信息（实际应用中应该从用户状态获取）
    final currentUser = UserInfo(
      id: 1,
      username: '我',
      avatar: '',
      isVerified: false,
    );

    // 构建分类结构描述
    final categoryStructure = _buildCategoryStructureText(node);
    final childrenCount = _getCategoryChildrenCount(node);

    // 构建帖子内容
    final content =
        '''🗂️ 分享动作库分类结构：${node.title}

📊 分类统计：
• 总分类数：${childrenCount + 1} 个
• 子分类层级：${_getMaxDepth(node)} 层

📋 分类结构：
$categoryStructure

💡 这个分类结构可以帮助您更好地组织动作库，欢迎大家使用！

#动作库分类 #学习组织 #效率工具''';

    // 创建新帖子
    final newPost = CommunityPost(
      id: postId,
      author: currentUser,
      content: content,
      type: PostType.experience, // 分类共享属于经验分享类型
      tags: ['动作库分类', '学习组织', '效率工具'],
      images: [], // 分类共享暂不包含图片
      likeCount: 0,
      commentCount: 0,
      shareCount: 0,
      createdAt: DateTime.now(),
      isLiked: false,
      isPremium: false,
    );

    // 保存到社区存储
    final success = await storageService.addPost(newPost);
    if (!success) {
      throw Exception('保存到社区失败');
    }

    // 确保数据已经持久化
    await storageService.initialize();
    final allPosts = await storageService.getAllPosts();
    final savedPostExists = allPosts.any((post) => post.id == postId);

    if (!savedPostExists) {
      throw Exception('帖子保存验证失败');
    }

    print('✅ 分类"${node.title}"已成功共享到社区，帖子ID: $postId');
    print('📊 当前社区帖子总数: ${allPosts.length}');
  }

  /// 构建分类结构文本
  String _buildCategoryStructureText(
    ActionLibraryCategoryNode node, {
    int level = 0,
  }) {
    final indent = '  ' * level;
    final prefix = level == 0 ? '📁' : '📂';

    String result = '$indent$prefix ${node.title}\n';

    for (var child in node.children) {
      result += _buildCategoryStructureText(child, level: level + 1);
    }

    return result;
  }

  /// 获取分类树的最大深度
  int _getMaxDepth(ActionLibraryCategoryNode node, {int currentDepth = 0}) {
    if (node.children.isEmpty) {
      return currentDepth;
    }

    int maxChildDepth = currentDepth;
    for (var child in node.children) {
      final childDepth = _getMaxDepth(child, currentDepth: currentDepth + 1);
      if (childDepth > maxChildDepth) {
        maxChildDepth = childDepth;
      }
    }

    return maxChildDepth;
  }

  /// 删除分类
  void _deleteCategory(ActionLibraryCategoryNode node) async {
    // 首先检查是否有动作库使用了这个分类
    final affectedCategories = _getAffectedCategoryNames(node);
    final librariesUsingCategory = <String>[];

    // 检查每个受影响的分类是否被动作库使用
    for (final categoryName in affectedCategories) {
      final libraries = widget.customLibraryService.getLibrariesByCategory(
        categoryName,
      );
      if (libraries.isNotEmpty) {
        librariesUsingCategory.addAll(libraries.map((lib) => lib.name));
      }
    }

    if (librariesUsingCategory.isNotEmpty) {
      // 如果有动作库使用该分类，显示警告对话框
      _showCategoryInUseDialog(node, librariesUsingCategory);
    } else {
      // 如果没有动作库使用该分类，直接显示删除确认对话框
      _showDeleteConfirmDialog(node);
    }
  }

  /// 显示分类正在使用的警告对话框
  void _showCategoryInUseDialog(
    ActionLibraryCategoryNode node,
    List<String> libraryNames,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Row(
          children: [
            const Icon(Icons.warning, color: Colors.orange, size: 24),
            const SizedBox(width: 8),
            const Text('无法删除分类'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '分类"${node.title}"${node.children.isNotEmpty ? '及其子分类' : ''}正在被以下动作库使用：',
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFFFF3CD),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFFFFE69C)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  for (final libraryName in libraryNames.take(5)) // 最多显示5个
                    Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.library_books,
                            size: 16,
                            color: Colors.orange,
                          ),
                          const SizedBox(width: 6),
                          Expanded(
                            child: Text(
                              libraryName,
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (libraryNames.length > 5)
                    Text(
                      '... 还有 ${libraryNames.length - 5} 个动作库',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF9B9A97),
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              '请先将这些动作库移动到其他分类，或删除这些动作库后再删除此分类。',
              style: TextStyle(fontSize: 14, color: Color(0xFF9B9A97)),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('我知道了'),
          ),
        ],
      ),
    );
  }

  /// 显示删除确认对话框
  void _showDeleteConfirmDialog(ActionLibraryCategoryNode node) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text('确认删除'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('确定要删除分类"${node.title}"吗？'),
            if (node.children.isNotEmpty) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFF3CD),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.warning, color: Colors.orange, size: 16),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        '注意：这将同时删除 ${_getCategoryChildrenCount(node)} 个子分类',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => _performDeleteCategory(node),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 获取受影响的分类名称（包括当前分类和所有子分类）
  List<String> _getAffectedCategoryNames(ActionLibraryCategoryNode node) {
    final names = <String>[];
    _collectCategoryNamesRecursive(node, names);
    return names;
  }

  /// 递归收集分类名称
  void _collectCategoryNamesRecursive(
    ActionLibraryCategoryNode node,
    List<String> names,
  ) {
    names.add(node.title);
    for (var child in node.children) {
      _collectCategoryNamesRecursive(child, names);
    }
  }

  /// 执行删除分类操作
  void _performDeleteCategory(ActionLibraryCategoryNode node) async {
    try {
      // 关闭确认对话框
      Navigator.of(context).pop();

      // 保存分类名称用于提示
      final categoryName = node.title;
      final childrenCount = _getCategoryChildrenCount(node);

      // 从分类管理器中删除节点
      final success = widget.categoryManager.deleteNode(node.id);

      if (success) {
        // 保存到本地存储
        await widget.categoryManager.saveToStorage();

        // 触发回调刷新UI
        widget.onCategoriesChanged();

        // 显示成功提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                childrenCount > 0
                    ? '分类"$categoryName"及其 $childrenCount 个子分类已删除'
                    : '分类"$categoryName"已删除',
              ),
              backgroundColor: const Color(0xFF0F7B6C),
              behavior: SnackBarBehavior.floating,
              duration: const Duration(milliseconds: 300),
            ),
          );
        }
      } else {
        // 删除失败
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('删除分类"$categoryName"失败'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      // 显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('删除失败: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}

/// 分类选择对话框（复用知忆相册的分类选择逻辑）
class _CategorySelectionDialog extends StatefulWidget {
  final ActionLibraryCategoryManager categoryManager;
  final String currentCategory;
  final String libraryTitle;

  const _CategorySelectionDialog({
    required this.categoryManager,
    required this.currentCategory,
    required this.libraryTitle,
  });

  @override
  State<_CategorySelectionDialog> createState() =>
      _CategorySelectionDialogState();
}

class _CategorySelectionDialogState extends State<_CategorySelectionDialog> {
  String? _selectedCategory;
  List<String> _availableCategories = [];

  @override
  void initState() {
    super.initState();
    _availableCategories = widget.categoryManager.getAllCategoryNames();

    // 确保当前分类在可用分类列表中，如果不在则设为null
    if (_availableCategories.contains(widget.currentCategory)) {
      _selectedCategory = widget.currentCategory;
    } else {
      _selectedCategory = _availableCategories.isNotEmpty
          ? _availableCategories.first
          : null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      title: Row(
        children: [
          const Icon(Icons.category, color: Color(0xFF7C3AED)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '更改分类',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
          ),
        ],
      ),
      content: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: 400,
          maxHeight: MediaQuery.of(context).size.height * 0.6,
        ),
        child: SizedBox(
          width: MediaQuery.of(context).size.width * 0.8,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '为动作库"${widget.libraryTitle}"选择新的分类：',
                style: const TextStyle(fontSize: 14, color: Color(0xFF9B9A97)),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: const InputDecoration(
                  labelText: '分类',
                  border: OutlineInputBorder(),
                ),
                dropdownColor: Colors.white,
                items: _availableCategories
                    .map(
                      (category) => DropdownMenuItem(
                        value: category,
                        child: Text(category),
                      ),
                    )
                    .toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedCategory = newValue;
                  });
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消', style: TextStyle(color: Color(0xFF9B9A97))),
        ),
        ElevatedButton(
          onPressed: _selectedCategory != null
              ? () => Navigator.of(context).pop(_selectedCategory)
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF7C3AED),
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            elevation: 0,
          ),
          child: const Text('确定'),
        ),
      ],
    );
  }
}
