import 'package:flutter/material.dart';
import 'custom_action_library.dart';
import 'custom_action_library_service.dart';
import 'custom_action_editor_dialog.dart';

/// 自定义动作库编辑页面
class CustomLibraryEditorPage extends StatefulWidget {
  final CustomActionLibrary library;
  final CustomActionLibraryService customLibraryService;

  const CustomLibraryEditorPage({
    super.key,
    required this.library,
    required this.customLibraryService,
  });

  @override
  State<CustomLibraryEditorPage> createState() =>
      _CustomLibraryEditorPageState();
}

class _CustomLibraryEditorPageState extends State<CustomLibraryEditorPage> {
  late CustomActionLibrary _library;
  bool _hasUnsavedChanges = false;
  bool _isSaving = false;
  final List<String> _letters = [
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
  ];

  @override
  void initState() {
    super.initState();
    _library = widget.library;
    _loadDraftState();
  }

  /// 加载草稿状态
  void _loadDraftState() {
    // 检查是否有未保存的更改
    _checkForUnsavedChanges();
  }

  /// 检查是否有未保存的更改
  void _checkForUnsavedChanges() {
    // 这里可以通过比较当前状态与服务中的状态来判断是否有更改
    // 暂时设为false，实际使用时会在编辑动作时设置为true
    setState(() {
      _hasUnsavedChanges = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !_hasUnsavedChanges,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop && _hasUnsavedChanges) {
          await _onBackPressed();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Color(0xFF37352F)),
            onPressed: _onBackPressed,
          ),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _library.name,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF37352F),
                ),
              ),
              Text(
                '${_library.completedActionsCount}/26 动作已设置',
                style: const TextStyle(fontSize: 14, color: Color(0xFF9B9A97)),
              ),
            ],
          ),
          actions: [
            // 保存草稿按钮
            if (_hasUnsavedChanges)
              TextButton.icon(
                onPressed: _isSaving ? null : _saveDraft,
                icon: _isSaving
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Color(0xFF9B9A97),
                          ),
                        ),
                      )
                    : const Icon(Icons.save_outlined, size: 16),
                label: Text(_isSaving ? '保存中...' : '保存草稿'),
                style: TextButton.styleFrom(
                  foregroundColor: const Color(0xFF2F76DA),
                  textStyle: const TextStyle(fontSize: 14),
                ),
              ),

            // 最终保存按钮
            Container(
              margin: const EdgeInsets.only(left: 8),
              child: ElevatedButton.icon(
                onPressed: _isSaving ? null : _saveLibrary,
                icon: _isSaving
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : const Icon(Icons.check, size: 16),
                label: Text(_isSaving ? '保存中...' : '完成'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0F7B6C),
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  textStyle: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),

            // 进度指示器
            Container(
              margin: const EdgeInsets.only(left: 12, right: 16),
              child: Center(
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: const Color(0xFF0F7B6C).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Center(
                    child: Text(
                      '${(_library.completionPercentage * 100).toInt()}%',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF0F7B6C),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        body: Column(
          children: [
            // 描述信息
            if (_library.description.isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFFF7F6F3),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: const Color(0xFFE3E2E0)),
                ),
                child: Text(
                  _library.description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF9B9A97),
                    height: 1.4,
                  ),
                ),
              ),

            // 字母网格
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: MediaQuery.of(context).size.width > 600
                        ? 6
                        : 4, // 响应式列数
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                    childAspectRatio: 1.0,
                  ),
                  itemCount: _letters.length,
                  itemBuilder: (context, index) {
                    final letter = _letters[index];
                    final action = _library.getActionForLetter(letter);
                    final hasAction = action != null;

                    return GestureDetector(
                      onTap: () => _editAction(letter),
                      child: Container(
                        decoration: BoxDecoration(
                          color: hasAction
                              ? const Color(0xFF0F7B6C).withValues(alpha: 0.05)
                              : const Color(0xFFF7F6F3),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: hasAction
                                ? const Color(0xFF0F7B6C)
                                : const Color(0xFFE3E2E0),
                            width: hasAction ? 2 : 1,
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // 字母
                            Container(
                              width: 32,
                              height: 32,
                              decoration: BoxDecoration(
                                color: hasAction
                                    ? const Color(0xFF0F7B6C)
                                    : const Color(0xFF9B9A97),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Center(
                                child: Text(
                                  letter,
                                  style: const TextStyle(
                                    fontSize: 18, // 从16增加到18，提高可读性
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),

                            const SizedBox(height: 8),

                            // 动作名称或占位符
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                              ),
                              child: Text(
                                hasAction ? action.nameCn : '点击设置',
                                style: TextStyle(
                                  fontSize: 14, // 从12增加到14，提高可读性
                                  fontWeight: hasAction
                                      ? FontWeight.w500
                                      : FontWeight.normal,
                                  color: hasAction
                                      ? const Color(0xFF37352F)
                                      : const Color(0xFF9B9A97),
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),

                            // 状态指示器
                            const SizedBox(height: 4),
                            Container(
                              width: 6,
                              height: 6,
                              decoration: BoxDecoration(
                                color: hasAction
                                    ? const Color(0xFF0F7B6C)
                                    : const Color(0xFFE3E2E0),
                                borderRadius: BorderRadius.circular(3),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),

            // 底部操作栏
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.white,
                border: Border(
                  top: BorderSide(color: Color(0xFFE3E2E0), width: 1),
                ),
              ),
              child: Row(
                children: [
                  // 统计信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              '完成进度',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Color(0xFF9B9A97),
                              ),
                            ),
                            if (_hasUnsavedChanges) ...[
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(
                                    0xFFFFD700,
                                  ).withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(4),
                                  border: Border.all(
                                    color: const Color(0xFFFFD700),
                                    width: 1,
                                  ),
                                ),
                                child: const Text(
                                  '有未保存更改',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Color(0xFF8B7000),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Expanded(
                              child: LinearProgressIndicator(
                                value: _library.completionPercentage,
                                backgroundColor: const Color(0xFFE3E2E0),
                                valueColor: const AlwaysStoppedAnimation<Color>(
                                  Color(0xFF0F7B6C),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '${_library.completedActionsCount}/26',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF37352F),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(width: 16),

                  // 批量操作按钮
                  OutlinedButton.icon(
                    onPressed: _showBatchActions,
                    icon: const Icon(Icons.more_horiz, size: 16),
                    label: const Text('批量操作'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: const Color(0xFF37352F),
                      side: const BorderSide(color: Color(0xFFE3E2E0)),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ), // 闭合PopScope
    );
  }

  /// 保存草稿
  Future<void> _saveDraft() async {
    if (_isSaving) return;

    setState(() {
      _isSaving = true;
    });

    try {
      await widget.customLibraryService.updateLibrary(_library);

      if (mounted) {
        setState(() {
          _hasUnsavedChanges = false;
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('草稿已保存'),
            backgroundColor: Color(0xFF0F7B6C),
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('保存失败: $e'),
            backgroundColor: const Color(0xFFE03E3E),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// 最终保存动作库
  Future<void> _saveLibrary() async {
    if (_isSaving) return;

    setState(() {
      _isSaving = true;
    });

    try {
      await widget.customLibraryService.updateLibrary(_library);

      if (mounted) {
        setState(() {
          _hasUnsavedChanges = false;
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('动作库保存成功'),
            backgroundColor: Color(0xFF0F7B6C),
            duration: Duration(seconds: 1),
          ),
        );

        // 延迟一下再返回，让用户看到成功提示
        await Future.delayed(const Duration(milliseconds: 500));
        if (mounted) {
          Navigator.of(context).pop(true); // 返回true表示有更改
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('保存失败: $e'),
            backgroundColor: const Color(0xFFE03E3E),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  /// 自动保存草稿（在编辑动作时调用）
  Future<void> _autoSaveDraft() async {
    try {
      await widget.customLibraryService.updateLibrary(_library);

      // 重要：更新本地引用以保持数据一致性
      final updatedLibrary = widget.customLibraryService.findLibraryById(
        _library.id,
      );
      if (updatedLibrary != null) {
        setState(() {
          _library = updatedLibrary;
        });
      }

      debugPrint('🔄 自动保存草稿成功');
    } catch (e) {
      debugPrint('❌ 自动保存草稿失败: $e');
    }
  }

  /// 处理返回按钮点击
  Future<void> _onBackPressed() async {
    if (_hasUnsavedChanges) {
      final shouldLeave = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: const Text(
            '有未保存的更改',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          content: const Text(
            '您有未保存的动作更改，确定要离开吗？',
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF9B9A97),
              height: 1.4,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text(
                '取消',
                style: TextStyle(color: Color(0xFF9B9A97)),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text(
                '保存并离开',
                style: TextStyle(color: Color(0xFF0F7B6C)),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text(
                '直接离开',
                style: TextStyle(color: Color(0xFFE03E3E)),
              ),
            ),
          ],
        ),
      );

      if (shouldLeave == true && mounted) {
        Navigator.of(context).pop();
      }
    } else {
      Navigator.of(context).pop();
    }
  }

  /// 编辑动作
  void _editAction(String letter) {
    final existingAction = _library.getActionForLetter(letter);

    showDialog(
      context: context,
      builder: (context) => CustomActionEditorDialog(
        letter: letter,
        existingAction: existingAction,
        onActionSaved: (action) async {
          // 更新本地状态
          setState(() {
            _library.setActionForLetter(letter, action);
            _hasUnsavedChanges = true;
          });

          // 自动保存草稿
          await _autoSaveDraft();

          // 确保UI反映最新状态
          if (mounted) {
            setState(() {
              // 触发UI重建以显示最新的动作内容
            });
          }
        },
        onActionDeleted: () async {
          // 保存context引用
          final scaffoldMessenger = ScaffoldMessenger.of(context);

          // 更新本地状态
          setState(() {
            _library.removeActionForLetter(letter);
            _hasUnsavedChanges = true;
          });

          // 自动保存草稿
          await _autoSaveDraft();

          // 确保UI反映最新状态
          if (mounted) {
            setState(() {
              // 触发UI重建以显示删除后的状态
            });

            scaffoldMessenger.showSnackBar(
              SnackBar(
                content: Text('字母 $letter 的动作已删除'),
                backgroundColor: const Color(0xFF0F7B6C),
                duration: const Duration(seconds: 1),
              ),
            );
          }
        },
      ),
    );
  }

  /// 显示批量操作
  void _showBatchActions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: const Color(0xFFE3E2E0),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              '批量操作',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.copy, color: Color(0xFF2E7EED)),
              title: const Text('从预设动作库复制'),
              subtitle: const Text('复制系统预设的A-Z动作'),
              onTap: () {
                Navigator.pop(context);
                _copyFromPresetLibrary();
              },
            ),
            ListTile(
              leading: const Icon(Icons.clear_all, color: Color(0xFFE03E3E)),
              title: const Text('清空所有动作'),
              subtitle: const Text('删除当前动作库中的所有动作'),
              onTap: () {
                Navigator.pop(context);
                _clearAllActions();
              },
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  /// 从预设动作库复制
  void _copyFromPresetLibrary() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('从预设动作库复制'),
        content: const Text('这将覆盖当前已设置的动作，确定要继续吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              // 保存context引用
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              Navigator.pop(context);

              // 批量复制所有预设动作
              setState(() {
                for (final letter in _letters) {
                  final defaultAction =
                      DefaultActionTemplates.getDefaultActionForLetter(letter);
                  if (defaultAction != null) {
                    _library.setActionForLetter(letter, defaultAction);
                  }
                }
                _hasUnsavedChanges = true;
              });

              // 一次性保存所有更改
              await _autoSaveDraft();

              if (mounted) {
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('已从预设动作库复制所有动作'),
                    backgroundColor: Color(0xFF0F7B6C),
                    duration: Duration(seconds: 1),
                  ),
                );
              }
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 清空所有动作
  void _clearAllActions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清空所有动作'),
        content: const Text('确定要删除当前动作库中的所有动作吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              // 保存context引用
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              Navigator.pop(context);

              // 批量删除所有动作
              setState(() {
                for (final letter in _letters) {
                  if (_library.hasActionForLetter(letter)) {
                    _library.removeActionForLetter(letter);
                  }
                }
                _hasUnsavedChanges = true;
              });

              // 一次性保存所有更改
              await _autoSaveDraft();

              if (mounted) {
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('已清空所有动作'),
                    backgroundColor: Color(0xFF0F7B6C),
                    duration: Duration(seconds: 1),
                  ),
                );
              }
            },
            child: const Text('确定', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
