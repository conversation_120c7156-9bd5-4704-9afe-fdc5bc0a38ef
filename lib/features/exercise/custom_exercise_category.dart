import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/data/pao_exercises_data.dart';

/// 自定义动作分类模型
class CustomExerciseCategory {
  final String id;
  final String name;
  final String icon;
  final String? description;
  final DateTime createdAt;
  final DateTime lastModified;
  final Map<String, PAOExercise> exercises;

  // 新增层级结构支持字段
  final String? parentId; // 父分类ID
  final List<String> childrenIds; // 子分类ID列表
  final int level; // 层级深度（0为根级）
  final bool isExpanded; // 是否展开子分类

  CustomExerciseCategory({
    required this.id,
    required this.name,
    required this.icon,
    this.description,
    required this.createdAt,
    required this.lastModified,
    Map<String, PAOExercise>? exercises,
    this.parentId,
    List<String>? childrenIds,
    this.level = 0,
    this.isExpanded = false,
  }) : exercises = exercises ?? {},
       childrenIds = childrenIds ?? [];

  /// 从JSON创建实例
  factory CustomExerciseCategory.fromJson(Map<String, dynamic> json) {
    final exercisesMap = <String, PAOExercise>{};
    if (json['exercises'] != null) {
      final exercisesJson = json['exercises'] as Map<String, dynamic>;
      exercisesJson.forEach((key, value) {
        exercisesMap[key] = PAOExercise(
          letter: value['letter'] ?? key,
          nameEn: value['nameEn'] ?? '',
          nameCn: value['nameCn'] ?? '',
          category: value['category'] ?? json['name'],
          scene: value['scene'] ?? '简单活动',
          description: value['description'] ?? '',
          keywords: List<String>.from(value['keywords'] ?? []),
        );
      });
    }

    return CustomExerciseCategory(
      id: json['id'],
      name: json['name'],
      icon: json['icon'],
      description: json['description'],
      createdAt: DateTime.parse(json['createdAt']),
      lastModified: DateTime.parse(json['lastModified']),
      exercises: exercisesMap,
      parentId: json['parentId'],
      childrenIds: List<String>.from(json['childrenIds'] ?? []),
      level: json['level'] ?? 0,
      isExpanded: json['isExpanded'] ?? false,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final exercisesJson = <String, dynamic>{};
    exercises.forEach((key, exercise) {
      exercisesJson[key] = {
        'letter': exercise.letter,
        'nameEn': exercise.nameEn,
        'nameCn': exercise.nameCn,
        'category': exercise.category,
        'scene': exercise.scene,
        'description': exercise.description,
        'keywords': exercise.keywords,
      };
    });

    return {
      'id': id,
      'name': name,
      'icon': icon,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified.toIso8601String(),
      'exercises': exercisesJson,
      'parentId': parentId,
      'childrenIds': childrenIds,
      'level': level,
      'isExpanded': isExpanded,
    };
  }

  /// 创建副本
  CustomExerciseCategory copyWith({
    String? id,
    String? name,
    String? icon,
    String? description,
    DateTime? createdAt,
    DateTime? lastModified,
    Map<String, PAOExercise>? exercises,
    String? parentId,
    List<String>? childrenIds,
    int? level,
    bool? isExpanded,
  }) {
    return CustomExerciseCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      icon: icon ?? this.icon,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
      exercises: exercises ?? Map.from(this.exercises),
      parentId: parentId ?? this.parentId,
      childrenIds: childrenIds ?? List.from(this.childrenIds),
      level: level ?? this.level,
      isExpanded: isExpanded ?? this.isExpanded,
    );
  }

  /// 是否为根分类
  bool get isRoot => parentId == null;

  /// 是否有子分类
  bool get hasChildren => childrenIds.isNotEmpty;

  /// 添加子分类ID
  CustomExerciseCategory addChild(String childId) {
    final newChildrenIds = List<String>.from(childrenIds);
    if (!newChildrenIds.contains(childId)) {
      newChildrenIds.add(childId);
    }
    return copyWith(childrenIds: newChildrenIds, lastModified: DateTime.now());
  }

  /// 移除子分类ID
  CustomExerciseCategory removeChild(String childId) {
    final newChildrenIds = List<String>.from(childrenIds);
    newChildrenIds.remove(childId);
    return copyWith(childrenIds: newChildrenIds, lastModified: DateTime.now());
  }

  /// 切换展开状态
  CustomExerciseCategory toggleExpanded() {
    return copyWith(isExpanded: !isExpanded);
  }
}

/// 自定义动作分类管理器
class CustomExerciseCategoryManager {
  static const String _storageKey = 'custom_exercise_categories';

  List<CustomExerciseCategory> _categories = [];

  List<CustomExerciseCategory> get categories => List.unmodifiable(_categories);

  /// 从本地存储加载分类
  Future<void> loadFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_storageKey);

      if (jsonString != null) {
        final jsonList = json.decode(jsonString) as List;
        _categories = jsonList
            .map((json) => CustomExerciseCategory.fromJson(json))
            .toList();
        print('📚 成功加载 ${_categories.length} 个自定义动作分类');
      } else {
        _categories = [];
        print('📚 未找到自定义动作分类数据，初始化为空列表');
      }
    } catch (e) {
      print('❌ 加载自定义动作分类失败: $e');
      _categories = [];
    }
  }

  /// 保存分类到本地存储
  Future<void> saveToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = _categories
          .map((category) => category.toJson())
          .toList();
      final jsonString = json.encode(jsonList);

      await prefs.setString(_storageKey, jsonString);
      print('💾 成功保存 ${_categories.length} 个自定义动作分类');
    } catch (e) {
      print('❌ 保存自定义动作分类失败: $e');
    }
  }

  /// 添加新分类
  Future<void> addCategory(CustomExerciseCategory category) async {
    _categories.add(category);
    await saveToStorage();
  }

  /// 更新分类
  Future<void> updateCategory(CustomExerciseCategory updatedCategory) async {
    final index = _categories.indexWhere((cat) => cat.id == updatedCategory.id);
    if (index != -1) {
      _categories[index] = updatedCategory;
      await saveToStorage();
    }
  }

  /// 删除分类
  Future<void> deleteCategory(String categoryId) async {
    _categories.removeWhere((cat) => cat.id == categoryId);
    await saveToStorage();
  }

  /// 根据ID查找分类
  CustomExerciseCategory? findCategoryById(String id) {
    try {
      return _categories.firstWhere((cat) => cat.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 根据名称查找分类
  CustomExerciseCategory? findCategoryByName(String name) {
    try {
      return _categories.firstWhere((cat) => cat.name == name);
    } catch (e) {
      return null;
    }
  }

  /// 检查分类名称是否已存在
  bool isCategoryNameExists(String name, {String? excludeId}) {
    return _categories.any(
      (cat) => cat.name == name && (excludeId == null || cat.id != excludeId),
    );
  }

  /// 获取所有分类名称
  List<String> getAllCategoryNames() {
    return _categories.map((cat) => cat.name).toList();
  }

  /// 获取根分类列表
  List<CustomExerciseCategory> getRootCategories() {
    return _categories.where((cat) => cat.isRoot).toList();
  }

  /// 获取指定分类的子分类列表
  List<CustomExerciseCategory> getChildCategories(String parentId) {
    final parent = findCategoryById(parentId);
    if (parent == null) return [];

    return parent.childrenIds
        .map((childId) => findCategoryById(childId))
        .where((child) => child != null)
        .cast<CustomExerciseCategory>()
        .toList();
  }

  /// 获取分类的层级结构（用于显示）
  List<CustomExerciseCategory> getHierarchicalCategories() {
    final result = <CustomExerciseCategory>[];
    final rootCategories = getRootCategories();

    for (final root in rootCategories) {
      _addCategoryWithChildren(root, result);
    }

    return result;
  }

  /// 递归添加分类及其子分类到结果列表
  void _addCategoryWithChildren(
    CustomExerciseCategory category,
    List<CustomExerciseCategory> result,
  ) {
    result.add(category);

    if (category.isExpanded && category.hasChildren) {
      final children = getChildCategories(category.id);
      for (final child in children) {
        _addCategoryWithChildren(child, result);
      }
    }
  }

  /// 添加子分类
  Future<void> addChildCategory(
    String parentId,
    CustomExerciseCategory childCategory,
  ) async {
    final parent = findCategoryById(parentId);
    if (parent == null) return;

    // 设置子分类的层级信息
    final updatedChild = childCategory.copyWith(
      parentId: parentId,
      level: parent.level + 1,
    );

    // 添加子分类到列表
    _categories.add(updatedChild);

    // 更新父分类的子分类列表
    final updatedParent = parent.addChild(updatedChild.id);
    await updateCategory(updatedParent);

    await saveToStorage();
  }

  /// 切换分类展开状态
  Future<void> toggleCategoryExpanded(String categoryId) async {
    final category = findCategoryById(categoryId);
    if (category == null) return;

    final updatedCategory = category.toggleExpanded();
    await updateCategory(updatedCategory);
  }

  /// 移动分类到新的父分类下
  Future<void> moveCategoryToParent(
    String categoryId,
    String? newParentId,
  ) async {
    final category = findCategoryById(categoryId);
    if (category == null) return;

    // 从原父分类中移除
    if (category.parentId != null) {
      final oldParent = findCategoryById(category.parentId!);
      if (oldParent != null) {
        final updatedOldParent = oldParent.removeChild(categoryId);
        await updateCategory(updatedOldParent);
      }
    }

    // 计算新的层级
    int newLevel = 0;
    if (newParentId != null) {
      final newParent = findCategoryById(newParentId);
      if (newParent != null) {
        newLevel = newParent.level + 1;
        // 添加到新父分类
        final updatedNewParent = newParent.addChild(categoryId);
        await updateCategory(updatedNewParent);
      }
    }

    // 更新分类的父子关系和层级
    final updatedCategory = category.copyWith(
      parentId: newParentId,
      level: newLevel,
    );
    await updateCategory(updatedCategory);

    // 递归更新所有子分类的层级
    await _updateChildrenLevels(updatedCategory);
  }

  /// 递归更新子分类的层级
  Future<void> _updateChildrenLevels(CustomExerciseCategory parent) async {
    final children = getChildCategories(parent.id);
    for (final child in children) {
      final updatedChild = child.copyWith(level: parent.level + 1);
      await updateCategory(updatedChild);
      await _updateChildrenLevels(updatedChild);
    }
  }

  /// 清空所有分类
  Future<void> clearAllCategories() async {
    _categories.clear();
    await saveToStorage();
  }
}
