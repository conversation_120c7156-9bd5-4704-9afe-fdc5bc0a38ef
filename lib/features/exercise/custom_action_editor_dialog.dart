import 'package:flutter/material.dart';
import 'custom_action_library.dart';

/// 自定义动作编辑对话框
class CustomActionEditorDialog extends StatefulWidget {
  final String letter;
  final CustomPAOAction? existingAction;
  final Future<void> Function(CustomPAOAction) onActionSaved;
  final VoidCallback? onActionDeleted;

  const CustomActionEditorDialog({
    super.key,
    required this.letter,
    this.existingAction,
    required this.onActionSaved,
    this.onActionDeleted,
  });

  @override
  State<CustomActionEditorDialog> createState() =>
      _CustomActionEditorDialogState();
}

class _CustomActionEditorDialogState extends State<CustomActionEditorDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameEnController;
  late TextEditingController _nameCnController;
  late TextEditingController _descriptionController;
  late TextEditingController _keywordsController;

  String _selectedCategory = '自定义';
  String _selectedScene = '简单活动';
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();

    final action =
        widget.existingAction ??
        DefaultActionTemplates.createEmptyTemplate(widget.letter);

    _nameEnController = TextEditingController(text: action.nameEn);
    _nameCnController = TextEditingController(text: action.nameCn);
    _descriptionController = TextEditingController(text: action.description);
    _keywordsController = TextEditingController(
      text: action.keywords.join(', '),
    );
    _selectedCategory = action.category;
    _selectedScene = action.scene;
  }

  @override
  void dispose() {
    _nameEnController.dispose();
    _nameCnController.dispose();
    _descriptionController.dispose();
    _keywordsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.95,
        height: MediaQuery.of(context).size.height * 0.8,
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 700),
        child: Column(
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE3E2E0), width: 1),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFF0F7B6C).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        widget.letter,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF0F7B6C),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '编辑字母 ${widget.letter} 的动作',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF37352F),
                          ),
                        ),
                        const Text(
                          '设置动作的详细信息',
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF9B9A97),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (widget.existingAction != null &&
                      widget.onActionDeleted != null)
                    IconButton(
                      onPressed: _deleteAction,
                      icon: const Icon(Icons.delete, color: Colors.red),
                      tooltip: '删除动作',
                    ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Color(0xFF9B9A97)),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                ],
              ),
            ),

            // 表单内容
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 动作中文名称
                      _buildFormField(
                        label: '动作中文名称',
                        controller: _nameCnController,
                        hintText: '例如：俯卧撑',
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '请输入动作中文名称';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // 动作英文名称
                      _buildFormField(
                        label: '动作英文名称',
                        controller: _nameEnController,
                        hintText: '例如：Push Up',
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '请输入动作英文名称';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // 动作分类
                      _buildDropdownField(
                        label: '动作分类',
                        value: _selectedCategory,
                        items: DefaultActionTemplates.getAvailableCategories(),
                        onChanged: (value) {
                          setState(() {
                            _selectedCategory = value!;
                          });
                        },
                      ),

                      const SizedBox(height: 16),

                      // 适用场景
                      _buildDropdownField(
                        label: '适用场景',
                        value: _selectedScene,
                        items: DefaultActionTemplates.getAvailableScenes(),
                        onChanged: (value) {
                          setState(() {
                            _selectedScene = value!;
                          });
                        },
                      ),

                      const SizedBox(height: 16),

                      // 动作描述 (可选)
                      _buildFormField(
                        label: '动作描述 (可选)',
                        controller: _descriptionController,
                        hintText: '详细描述动作的执行方法...',
                        maxLines: 4,
                        validator: null, // 移除必填验证，使描述字段可选
                      ),

                      const SizedBox(height: 16),

                      // 关键词
                      _buildFormField(
                        label: '关键词 (用逗号分隔)',
                        controller: _keywordsController,
                        hintText: '例如：Push, Power, Practice',
                        helperText: '用于PAO记忆法的关键词，建议3-5个',
                      ),

                      const SizedBox(height: 16),

                      // 预设动作参考
                      if (widget.existingAction == null)
                        _buildPresetReference(),
                    ],
                  ),
                ),
              ),
            ),

            // 底部操作栏
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE3E2E0), width: 1),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: _isSaving
                          ? null
                          : () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.white, // 白色背景
                        padding: const EdgeInsets.symmetric(
                          vertical: 14,
                        ), // 增加垂直内边距
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12), // 使用12px圆角
                          side: const BorderSide(
                            color: Color(0xFFE3E2E0),
                          ), // 添加边框
                        ),
                      ),
                      child: const Text(
                        '取消',
                        style: TextStyle(
                          color: Color(0xFF9B9A97),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isSaving ? null : _saveAction,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF0F7B6C),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          vertical: 14,
                        ), // 增加垂直内边距
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12), // 使用12px圆角
                        ),
                        elevation: 0,
                      ),
                      child: _isSaving
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                          : const Text(
                              '保存',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建表单字段
  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    String? hintText,
    String? helperText,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hintText,
            helperText: helperText,
            hintStyle: const TextStyle(color: Color(0xFF9B9A97)),
            helperStyle: const TextStyle(
              color: Color(0xFF9B9A97),
              fontSize: 12,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0F7B6C)),
            ),
            filled: true,
            fillColor: const Color(0xFFF7F6F3),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 12,
            ),
          ),
          validator: validator,
        ),
      ],
    );
  }

  /// 构建下拉选择字段
  Widget _buildDropdownField({
    required String label,
    required String value,
    required List<String> items,
    required Function(String?) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: value,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0F7B6C)),
            ),
            filled: true,
            fillColor: const Color(0xFFF7F6F3),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 12,
            ),
          ),
          items: items.map((item) {
            return DropdownMenuItem(value: item, child: Text(item));
          }).toList(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  /// 构建预设动作参考
  Widget _buildPresetReference() {
    final defaultAction = DefaultActionTemplates.getDefaultActionForLetter(
      widget.letter,
    );
    if (defaultAction == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF2E7EED).withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFF2E7EED).withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.lightbulb_outline,
                color: Color(0xFF2E7EED),
                size: 16,
              ),
              const SizedBox(width: 8),
              const Text(
                '预设动作参考',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF2E7EED),
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: _copyFromPreset,
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  minimumSize: Size.zero,
                ),
                child: const Text(
                  '复制',
                  style: TextStyle(fontSize: 12, color: Color(0xFF2E7EED)),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '${defaultAction.nameCn} (${defaultAction.nameEn})',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            defaultAction.description,
            style: const TextStyle(fontSize: 12, color: Color(0xFF9B9A97)),
          ),
        ],
      ),
    );
  }

  /// 从预设复制
  void _copyFromPreset() {
    final defaultAction = DefaultActionTemplates.getDefaultActionForLetter(
      widget.letter,
    );
    if (defaultAction == null) return;

    setState(() {
      _nameEnController.text = defaultAction.nameEn;
      _nameCnController.text = defaultAction.nameCn;
      _descriptionController.text = defaultAction.description;
      _keywordsController.text = defaultAction.keywords.join(', ');
      _selectedCategory = defaultAction.category;
      _selectedScene = defaultAction.scene;
    });
  }

  /// 保存动作
  void _saveAction() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final keywords = _keywordsController.text
          .split(',')
          .map((k) => k.trim())
          .where((k) => k.isNotEmpty)
          .toList();

      final action = CustomPAOAction(
        letter: widget.letter,
        nameEn: _nameEnController.text.trim(),
        nameCn: _nameCnController.text.trim(),
        description: _descriptionController.text.trim(),
        category: _selectedCategory,
        scene: _selectedScene,
        keywords: keywords,
      );

      // 先调用回调保存数据，再关闭对话框
      await widget.onActionSaved(action);

      if (mounted) {
        // 显示保存成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('字母 ${widget.letter} 的动作已保存'),
            backgroundColor: const Color(0xFF0F7B6C),
            duration: const Duration(seconds: 1),
          ),
        );

        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('保存失败: $e'),
            backgroundColor: const Color(0xFFE03E3E),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  /// 删除动作
  void _deleteAction() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除字母 ${widget.letter} 的动作吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // 关闭确认对话框
              Navigator.of(context).pop(); // 关闭编辑对话框
              widget.onActionDeleted?.call();
            },
            child: const Text('删除', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
