import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../custom_action_library_service.dart';
import '../pao_integration_service.dart';
import '../custom_action_library.dart';
import '../exercise_session_page.dart';
import '../../../core/data/pao_exercises_data.dart';

/// 自定义动作库服务Provider
final customActionLibraryServiceProvider = Provider<CustomActionLibraryService>(
  (ref) {
    return CustomActionLibraryService();
  },
);

/// PAO集成服务Provider
final paoIntegrationServiceProvider = Provider<PAOIntegrationService>((ref) {
  final customLibraryService = ref.watch(customActionLibraryServiceProvider);
  return PAOIntegrationService(customLibraryService);
});

/// 自定义动作库列表Provider
final customLibrariesProvider =
    StateNotifierProvider<CustomLibrariesNotifier, CustomLibrariesState>((ref) {
      final service = ref.watch(customActionLibraryServiceProvider);
      return CustomLibrariesNotifier(service);
    });

/// 运动训练会话Provider
final exerciseSessionProvider =
    StateNotifierProvider<ExerciseSessionNotifier, ExerciseSessionState>((ref) {
      final paoService = ref.watch(paoIntegrationServiceProvider);
      return ExerciseSessionNotifier(paoService);
    });

/// 自定义动作库状态
class CustomLibrariesState {
  final List<CustomActionLibrary> libraries;
  final CustomActionLibrary? selectedLibrary;
  final bool isLoading;
  final String? error;

  const CustomLibrariesState({
    this.libraries = const [],
    this.selectedLibrary,
    this.isLoading = false,
    this.error,
  });

  CustomLibrariesState copyWith({
    List<CustomActionLibrary>? libraries,
    CustomActionLibrary? selectedLibrary,
    bool? isLoading,
    String? error,
  }) {
    return CustomLibrariesState(
      libraries: libraries ?? this.libraries,
      selectedLibrary: selectedLibrary ?? this.selectedLibrary,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// 自定义动作库状态管理器
class CustomLibrariesNotifier extends StateNotifier<CustomLibrariesState> {
  final CustomActionLibraryService _service;

  CustomLibrariesNotifier(this._service) : super(const CustomLibrariesState()) {
    _loadInitialData();
  }

  /// 加载初始数据
  Future<void> _loadInitialData() async {
    await loadLibraries();
  }

  /// 加载动作库列表
  Future<void> loadLibraries() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _service.loadFromStorage();
      final libraries = _service.libraries;
      final selectedLibrary = _service.selectedLibrary;

      state = state.copyWith(
        libraries: libraries,
        selectedLibrary: selectedLibrary,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  /// 创建新动作库
  Future<CustomActionLibrary?> createLibrary({
    required String name,
    required String description,
    String? category,
  }) async {
    try {
      final library = await _service.createLibrary(
        name: name,
        description: description,
        category: category,
      );

      await loadLibraries(); // 刷新列表
      return library;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return null;
    }
  }

  /// 更新动作库
  Future<void> updateLibrary(CustomActionLibrary library) async {
    try {
      await _service.updateLibrary(library);
      await loadLibraries(); // 刷新列表
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 删除动作库
  Future<void> deleteLibrary(String libraryId) async {
    try {
      await _service.deleteLibrary(libraryId);
      await loadLibraries(); // 刷新列表
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 选择动作库
  Future<void> selectLibrary(String? libraryId) async {
    try {
      await _service.selectLibrary(libraryId);
      await loadLibraries(); // 刷新状态
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 复制动作库
  Future<CustomActionLibrary?> duplicateLibrary(
    String sourceLibraryId,
    String newName,
  ) async {
    try {
      final library = await _service.duplicateLibrary(sourceLibraryId, newName);
      await loadLibraries(); // 刷新列表
      return library;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return null;
    }
  }

  /// 导入动作库
  Future<void> importLibrary(String jsonData) async {
    try {
      await _service.importLibrary(jsonData);
      await loadLibraries(); // 刷新列表
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 导出动作库
  Future<String?> exportLibrary(String libraryId) async {
    try {
      return await _service.exportLibrary(libraryId);
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return null;
    }
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// 运动训练会话状态
class ExerciseSessionState {
  final List<PAOExercise> exercises;
  final String? paoWord;
  final String? wordMeaning;
  final String? wordPhonetic;
  final ExerciseMode mode;
  final int duration;
  final ExerciseState sessionState;
  final int currentExerciseIndex;
  final int remainingTime;
  final int completedExercises;
  final int totalCalories;
  final int earnedCoins;
  final bool isActive;

  const ExerciseSessionState({
    this.exercises = const [],
    this.paoWord,
    this.wordMeaning,
    this.wordPhonetic,
    this.mode = ExerciseMode.single,
    this.duration = 30,
    this.sessionState = ExerciseState.ready,
    this.currentExerciseIndex = 0,
    this.remainingTime = 0,
    this.completedExercises = 0,
    this.totalCalories = 0,
    this.earnedCoins = 0,
    this.isActive = false,
  });

  ExerciseSessionState copyWith({
    List<PAOExercise>? exercises,
    String? paoWord,
    String? wordMeaning,
    String? wordPhonetic,
    ExerciseMode? mode,
    int? duration,
    ExerciseState? sessionState,
    int? currentExerciseIndex,
    int? remainingTime,
    int? completedExercises,
    int? totalCalories,
    int? earnedCoins,
    bool? isActive,
  }) {
    return ExerciseSessionState(
      exercises: exercises ?? this.exercises,
      paoWord: paoWord ?? this.paoWord,
      wordMeaning: wordMeaning ?? this.wordMeaning,
      wordPhonetic: wordPhonetic ?? this.wordPhonetic,
      mode: mode ?? this.mode,
      duration: duration ?? this.duration,
      sessionState: sessionState ?? this.sessionState,
      currentExerciseIndex: currentExerciseIndex ?? this.currentExerciseIndex,
      remainingTime: remainingTime ?? this.remainingTime,
      completedExercises: completedExercises ?? this.completedExercises,
      totalCalories: totalCalories ?? this.totalCalories,
      earnedCoins: earnedCoins ?? this.earnedCoins,
      isActive: isActive ?? this.isActive,
    );
  }
}

/// 运动训练会话状态管理器
class ExerciseSessionNotifier extends StateNotifier<ExerciseSessionState> {
  final PAOIntegrationService _paoService;

  ExerciseSessionNotifier(this._paoService)
    : super(const ExerciseSessionState());

  /// 初始化运动会话
  void initializeSession({
    required List<PAOExercise> exercises,
    String? paoWord,
    String? wordMeaning,
    String? wordPhonetic,
    ExerciseMode mode = ExerciseMode.single,
    int duration = 30,
  }) {
    state = ExerciseSessionState(
      exercises: exercises,
      paoWord: paoWord,
      wordMeaning: wordMeaning,
      wordPhonetic: wordPhonetic,
      mode: mode,
      duration: duration,
      remainingTime: duration,
      isActive: true,
    );
  }

  /// 为单词生成PAO运动会话
  void initializePAOSession(String word, {int duration = 30}) {
    final exercises = _paoService.generatePAOExercisesForWord(word);

    state = ExerciseSessionState(
      exercises: exercises,
      paoWord: word,
      mode: ExerciseMode.pao,
      duration: duration,
      remainingTime: duration,
      isActive: true,
    );
  }

  /// 开始运动
  void startExercise() {
    state = state.copyWith(
      sessionState: ExerciseState.exercising,
      remainingTime: state.duration,
    );
  }

  /// 暂停运动
  void pauseExercise() {
    state = state.copyWith(sessionState: ExerciseState.paused);
  }

  /// 继续运动
  void resumeExercise() {
    state = state.copyWith(sessionState: ExerciseState.exercising);
  }

  /// 更新倒计时
  void updateTimer(int remainingTime) {
    state = state.copyWith(remainingTime: remainingTime);
  }

  /// 完成当前动作
  void completeCurrentExercise() {
    final newCompletedCount = state.completedExercises + 1;
    final calories = _calculateCalories(
      state.exercises[state.currentExerciseIndex],
    );

    state = state.copyWith(
      completedExercises: newCompletedCount,
      totalCalories: state.totalCalories + calories,
      earnedCoins: state.earnedCoins + (calories * 0.1).round(),
    );
  }

  /// 进入下一个动作
  void nextExercise() {
    if (state.currentExerciseIndex < state.exercises.length - 1) {
      state = state.copyWith(
        currentExerciseIndex: state.currentExerciseIndex + 1,
        remainingTime: state.duration,
        sessionState: ExerciseState.ready,
      );
    } else {
      completeSession();
    }
  }

  /// 完成整个会话
  void completeSession() {
    state = state.copyWith(
      sessionState: ExerciseState.completed,
      isActive: false,
    );
  }

  /// 重新开始会话
  void restartSession() {
    state = state.copyWith(
      sessionState: ExerciseState.ready,
      currentExerciseIndex: 0,
      remainingTime: state.duration,
      completedExercises: 0,
      totalCalories: 0,
      earnedCoins: 0,
      isActive: true,
    );
  }

  /// 结束会话
  void endSession() {
    state = const ExerciseSessionState();
  }

  /// 计算动作卡路里消耗
  int _calculateCalories(PAOExercise exercise) {
    const intensityMap = {
      '健身': 8,
      '篮球技巧': 12,
      '足球技巧': 10,
      '瑜伽柔韧': 4,
      '传统养生': 3,
      '办公室拉伸': 2,
      '眼部保健': 1,
    };

    final intensity = intensityMap[exercise.category] ?? 5;
    return (intensity * state.duration / 60).round();
  }
}

/// PAO动作映射Provider - 提供当前有效的动作映射
final currentPAOExerciseMapProvider = Provider<Map<String, PAOExercise>>((ref) {
  final paoService = ref.watch(paoIntegrationServiceProvider);
  return paoService.getCurrentExerciseMap();
});

/// 动作库统计Provider
final exerciseLibraryStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final paoService = ref.watch(paoIntegrationServiceProvider);
  return paoService.getLibraryStatistics();
});
