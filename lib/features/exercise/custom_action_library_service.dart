import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'custom_action_library.dart';

/// 自定义动作库管理服务
class CustomActionLibraryService {
  static const String _storageKey = 'custom_action_libraries';
  static const String _selectedLibraryKey = 'selected_custom_library';

  List<CustomActionLibrary> _libraries = [];
  String? _selectedLibraryId;

  /// 获取所有自定义动作库
  List<CustomActionLibrary> get libraries => List.unmodifiable(_libraries);

  /// 获取当前选中的动作库ID
  String? get selectedLibraryId => _selectedLibraryId;

  /// 获取当前选中的动作库
  CustomActionLibrary? get selectedLibrary {
    if (_selectedLibraryId == null) return null;
    try {
      return _libraries.firstWhere((lib) => lib.id == _selectedLibraryId);
    } catch (e) {
      return null;
    }
  }

  /// 从本地存储加载动作库
  Future<void> loadFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 加载动作库列表
      final jsonString = prefs.getString(_storageKey);
      if (jsonString != null) {
        final jsonList = json.decode(jsonString) as List;
        _libraries = jsonList
            .map((json) => CustomActionLibrary.fromJson(json))
            .toList();
        debugPrint('📚 成功加载 ${_libraries.length} 个自定义动作库');
      } else {
        _libraries = [];
        debugPrint('📚 未找到自定义动作库数据，初始化为空列表');
      }

      // 加载选中的动作库ID
      _selectedLibraryId = prefs.getString(_selectedLibraryKey);
      if (_selectedLibraryId != null) {
        debugPrint('📚 当前选中动作库: $_selectedLibraryId');
      }
    } catch (e) {
      debugPrint('❌ 加载自定义动作库失败: $e');
      _libraries = [];
      _selectedLibraryId = null;
    }
  }

  /// 保存动作库到本地存储
  Future<void> saveToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 保存动作库列表
      final jsonList = _libraries.map((library) => library.toJson()).toList();
      final jsonString = json.encode(jsonList);
      await prefs.setString(_storageKey, jsonString);

      // 保存选中的动作库ID
      if (_selectedLibraryId != null) {
        await prefs.setString(_selectedLibraryKey, _selectedLibraryId!);
      } else {
        await prefs.remove(_selectedLibraryKey);
      }

      debugPrint('💾 成功保存 ${_libraries.length} 个自定义动作库');
    } catch (e) {
      debugPrint('❌ 保存自定义动作库失败: $e');
    }
  }

  /// 创建新的动作库
  Future<CustomActionLibrary> createLibrary({
    required String name,
    required String description,
    String? category, // 新增分类参数
  }) async {
    final library = CustomActionLibrary(
      id: _generateId(),
      name: name,
      description: description,
      category: category ?? '默认分类', // 提供默认分类
      createdAt: DateTime.now(),
      lastModified: DateTime.now(),
    );

    _libraries.add(library);
    await saveToStorage();

    debugPrint('✅ 创建新动作库: ${library.name}');
    return library;
  }

  /// 更新动作库
  Future<void> updateLibrary(CustomActionLibrary updatedLibrary) async {
    final index = _libraries.indexWhere((lib) => lib.id == updatedLibrary.id);
    if (index != -1) {
      // 确保保留当前的动作数据，并更新最后修改时间
      _libraries[index] = updatedLibrary.copyWith(
        lastModified: DateTime.now(),
        actions: Map.from(updatedLibrary.actions), // 确保动作数据被正确复制
      );
      await saveToStorage();
      debugPrint(
        '✅ 更新动作库: ${updatedLibrary.name}, 动作数量: ${updatedLibrary.actions.length}',
      );
    } else {
      debugPrint('❌ 未找到要更新的动作库: ${updatedLibrary.id}');
    }
  }

  /// 删除动作库
  Future<void> deleteLibrary(String libraryId) async {
    _libraries.removeWhere((lib) => lib.id == libraryId);

    // 如果删除的是当前选中的动作库，清除选择
    if (_selectedLibraryId == libraryId) {
      _selectedLibraryId = null;
    }

    await saveToStorage();
    debugPrint('🗑️ 删除动作库: $libraryId');
  }

  /// 选择动作库
  Future<void> selectLibrary(String? libraryId) async {
    _selectedLibraryId = libraryId;
    await saveToStorage();
    debugPrint('📌 选择动作库: $libraryId');
  }

  /// 更新动作库中的动作
  Future<void> updateActionInLibrary(
    String libraryId,
    String letter,
    CustomPAOAction action,
  ) async {
    final library = findLibraryById(libraryId);
    if (library != null) {
      library.setActionForLetter(letter, action);
      await updateLibrary(library);
      debugPrint('✅ 更新动作库 ${library.name} 中字母 $letter 的动作');
    }
  }

  /// 删除动作库中的动作
  Future<void> removeActionFromLibrary(String libraryId, String letter) async {
    final library = findLibraryById(libraryId);
    if (library != null) {
      library.removeActionForLetter(letter);
      await updateLibrary(library);
      debugPrint('🗑️ 删除动作库 ${library.name} 中字母 $letter 的动作');
    }
  }

  /// 根据ID查找动作库
  CustomActionLibrary? findLibraryById(String id) {
    try {
      return _libraries.firstWhere((lib) => lib.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 根据名称查找动作库
  CustomActionLibrary? findLibraryByName(String name) {
    try {
      return _libraries.firstWhere((lib) => lib.name == name);
    } catch (e) {
      return null;
    }
  }

  /// 检查动作库名称是否已存在
  bool isLibraryNameExists(String name, {String? excludeId}) {
    return _libraries.any(
      (lib) => lib.name == name && (excludeId == null || lib.id != excludeId),
    );
  }

  /// 获取所有分类
  List<String> getAllCategories() {
    final categories = _libraries.map((lib) => lib.category).toSet().toList();
    categories.sort();
    return categories;
  }

  /// 根据分类获取动作库
  List<CustomActionLibrary> getLibrariesByCategory(String category) {
    return _libraries.where((lib) => lib.category == category).toList();
  }

  /// 获取分类统计信息
  Map<String, int> getCategoryStats() {
    final stats = <String, int>{};
    for (final library in _libraries) {
      stats[library.category] = (stats[library.category] ?? 0) + 1;
    }
    return stats;
  }

  /// 获取所有动作库名称
  List<String> getAllLibraryNames() {
    return _libraries.map((lib) => lib.name).toList();
  }

  /// 复制动作库
  Future<CustomActionLibrary> duplicateLibrary(
    String sourceLibraryId,
    String newName,
  ) async {
    final sourceLibrary = findLibraryById(sourceLibraryId);
    if (sourceLibrary == null) {
      throw Exception('源动作库不存在');
    }

    final duplicatedLibrary = CustomActionLibrary(
      id: _generateId(),
      name: newName,
      description: '${sourceLibrary.description} (副本)',
      category: sourceLibrary.category, // 保持相同分类
      createdAt: DateTime.now(),
      lastModified: DateTime.now(),
      actions: Map.from(sourceLibrary.actions),
    );

    _libraries.add(duplicatedLibrary);
    await saveToStorage();

    debugPrint('📋 复制动作库: ${sourceLibrary.name} -> $newName');
    return duplicatedLibrary;
  }

  /// 导出动作库为JSON
  Future<String> exportLibrary(String libraryId) async {
    final library = findLibraryById(libraryId);
    if (library == null) {
      throw Exception('动作库不存在');
    }

    final exportData = {
      'version': '1.0',
      'exportedAt': DateTime.now().toIso8601String(),
      'library': library.toJson(),
    };

    return json.encode(exportData);
  }

  /// 从JSON导入动作库
  Future<CustomActionLibrary> importLibrary(String jsonData) async {
    try {
      final data = json.decode(jsonData) as Map<String, dynamic>;
      final libraryData = data['library'] as Map<String, dynamic>;

      // 创建新的ID以避免冲突
      libraryData['id'] = _generateId();
      libraryData['createdAt'] = DateTime.now().toIso8601String();
      libraryData['lastModified'] = DateTime.now().toIso8601String();

      // 如果名称已存在，添加后缀
      String originalName = libraryData['name'] as String;
      String newName = originalName;
      int counter = 1;
      while (isLibraryNameExists(newName)) {
        newName = '$originalName ($counter)';
        counter++;
      }
      libraryData['name'] = newName;

      final library = CustomActionLibrary.fromJson(libraryData);
      _libraries.add(library);
      await saveToStorage();

      debugPrint('📥 导入动作库: ${library.name}');
      return library;
    } catch (e) {
      debugPrint('❌ 导入动作库失败: $e');
      throw Exception('导入失败：数据格式错误');
    }
  }

  /// 保存动作库到文件
  Future<File> saveLibraryToFile(String libraryId) async {
    final library = findLibraryById(libraryId);
    if (library == null) {
      throw Exception('动作库不存在');
    }

    final directory = await getApplicationDocumentsDirectory();
    final fileName =
        '${library.name}_${DateTime.now().millisecondsSinceEpoch}.json';
    final file = File('${directory.path}/$fileName');

    final jsonData = await exportLibrary(libraryId);
    await file.writeAsString(jsonData);

    debugPrint('💾 保存动作库到文件: ${file.path}');
    return file;
  }

  /// 生成唯一ID
  String _generateId() {
    return '${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecond}';
  }

  /// 获取统计信息
  Map<String, dynamic> getStatistics() {
    int totalLibraries = _libraries.length;
    int totalActions = _libraries.fold(
      0,
      (sum, lib) => sum + lib.completedActionsCount,
    );
    double averageCompletion = totalLibraries > 0
        ? _libraries.fold(0.0, (sum, lib) => sum + lib.completionPercentage) /
              totalLibraries
        : 0.0;

    return {
      'totalLibraries': totalLibraries,
      'totalActions': totalActions,
      'averageCompletion': averageCompletion,
      'selectedLibrary': selectedLibrary?.name,
    };
  }
}
