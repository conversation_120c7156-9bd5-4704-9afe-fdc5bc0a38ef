import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';

// 导入动觉动作模型
import '../../core/data/pao_exercises_data.dart';

// 运动模式枚举
enum ExerciseMode {
  single, // 单个动作练习
  sequence, // 顺序练习
  pao, // 动觉记忆训练
  quick, // 快速训练
}

// 运动状态枚举
enum ExerciseState {
  ready, // 准备状态
  exercising, // 运动中
  paused, // 暂停
  completed, // 完成
}

// 运动界面页面 - 专为考研党设计的动觉运动训练系统
// 支持单个动作练习和动觉组合训练，提供完整的运动指导和激励反馈
class ExerciseSessionPage extends StatefulWidget {
  final List<PAOExercise> exercises; // 运动动作列表
  final String? paoWord; // 动觉单词（可选）
  final String? wordMeaning; // 单词含义
  final String? wordPhonetic; // 音标
  final ExerciseMode mode; // 运动模式
  final int duration; // 每个动作时长（秒）

  const ExerciseSessionPage({
    super.key,
    required this.exercises,
    this.paoWord,
    this.wordMeaning,
    this.wordPhonetic,
    this.mode = ExerciseMode.single,
    this.duration = 30,
  });

  @override
  State<ExerciseSessionPage> createState() => _ExerciseSessionPageState();
}

class _ExerciseSessionPageState extends State<ExerciseSessionPage>
    with TickerProviderStateMixin {
  // 动画控制器
  late AnimationController _progressController;
  late AnimationController _celebrationController;

  // 状态管理
  Timer? _countdownTimer;
  int _currentExerciseIndex = 0;
  int _remainingTime = 0;
  ExerciseState _state = ExerciseState.ready;

  // 统计数据
  int _completedExercises = 0;
  int _totalCalories = 0;
  int _earnedCoins = 0;

  // 背景音乐和音效
  bool _isMusicEnabled = true;
  bool _isSoundEnabled = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeSession();
    _preventScreenSleep();
  }

  @override
  void dispose() {
    _disposeAnimations();
    _countdownTimer?.cancel();
    _restoreScreenSleep();
    super.dispose();
  }

  // 初始化动画
  void _initializeAnimations() {
    _progressController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );

    _celebrationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
  }

  // 销毁动画
  void _disposeAnimations() {
    _progressController.dispose();
    _celebrationController.dispose();
  }

  // 初始化运动会话
  void _initializeSession() {
    _remainingTime = widget.duration;

    // 计算预估卡路里（基于动作强度和时长）
    _totalCalories = widget.exercises.fold(
      0,
      (sum, exercise) => sum + _calculateCalories(exercise),
    );

    // 计算奖励金币
    _earnedCoins = (_totalCalories * 0.1).round();
  }

  // 防止息屏
  void _preventScreenSleep() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
  }

  // 恢复息屏设置
  void _restoreScreenSleep() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }

  // 计算动作卡路里消耗
  int _calculateCalories(PAOExercise exercise) {
    // 根据动作分类和强度计算卡路里
    const intensityMap = {
      '健身': 8,
      '篮球技巧': 12,
      '足球技巧': 10,
      '瑜伽柔韧': 4,
      '传统养生': 3,
      '办公室拉伸': 2,
      '眼部保健': 1,
    };

    final intensity = intensityMap[exercise.category] ?? 5;
    return (intensity * widget.duration / 60).round();
  }

  // 计算当前卡路里
  int _calculateCurrentCalories() {
    int calories = 0;
    for (int i = 0; i < _completedExercises; i++) {
      calories += _calculateCalories(widget.exercises[i]);
    }
    return calories;
  }

  // 计算当前金币
  int _calculateCurrentCoins() {
    return (_calculateCurrentCalories() * 0.1).round();
  }

  // 开始运动
  void _startExercise() {
    setState(() {
      _state = ExerciseState.exercising;
    });

    _startCountdown();
    _progressController.forward();

    // 触觉反馈
    HapticFeedback.lightImpact();
  }

  // 暂停运动
  void _pauseExercise() {
    setState(() {
      _state = ExerciseState.paused;
    });

    _countdownTimer?.cancel();
    _progressController.stop();

    HapticFeedback.lightImpact();
  }

  // 继续运动
  void _resumeExercise() {
    setState(() {
      _state = ExerciseState.exercising;
    });

    _startCountdown();
    _progressController.forward();

    HapticFeedback.lightImpact();
  }

  // 跳过当前动作
  void _skipExercise() {
    _nextExercise();
    HapticFeedback.mediumImpact();
  }

  // 开始倒计时
  void _startCountdown() {
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingTime > 0) {
          _remainingTime--;

          // 最后3秒震动提醒
          if (_remainingTime <= 3 && _remainingTime > 0) {
            HapticFeedback.heavyImpact();
          }
        } else {
          _completeCurrentExercise();
        }
      });
    });
  }

  // 完成当前动作
  void _completeCurrentExercise() {
    _countdownTimer?.cancel();

    setState(() {
      _completedExercises++;
    });

    // 庆祝动画
    _celebrationController.forward().then((_) {
      _celebrationController.reset();
    });

    // 完成震动
    HapticFeedback.heavyImpact();

    // 延迟进入下一个动作
    Timer(const Duration(milliseconds: 1500), () {
      _nextExercise();
    });
  }

  // 进入下一个动作
  void _nextExercise() {
    if (_currentExerciseIndex < widget.exercises.length - 1) {
      setState(() {
        _currentExerciseIndex++;
        _remainingTime = widget.duration;
        _state = ExerciseState.ready;
      });
      _progressController.reset();
    } else {
      _completeSession();
    }
  }

  // 完成整个运动会话
  void _completeSession() {
    setState(() {
      _state = ExerciseState.completed;
    });

    _countdownTimer?.cancel();
    _progressController.reset();

    // 显示完成对话框
    _showCompletionDialog();
  }

  // 显示完成对话框
  void _showCompletionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false,
        child: AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Row(
            children: [
              Icon(Icons.celebration, color: Color(0xFF2E7EED), size: 28),
              SizedBox(width: 12),
              Text(
                '运动完成！',
                style: TextStyle(
                  color: Color(0xFF37352F),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildStatItem(
                '完成动作',
                '$_completedExercises个',
                Icons.fitness_center,
              ),
              _buildStatItem(
                '消耗卡路里',
                '$_totalCalories大卡',
                Icons.local_fire_department,
              ),
              _buildStatItem('获得金币', '+$_earnedCoins', Icons.monetization_on),
              if (widget.paoWord != null)
                _buildStatItem('动觉单词', widget.paoWord!, Icons.psychology),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  '坚持运动，身体健康，学习更高效！💪',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF2E7EED),
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                Navigator.pop(context, {
                  'completed': true,
                  'exercises': _completedExercises,
                  'calories': _totalCalories,
                  'coins': _earnedCoins,
                });
              },
              child: const Text('完成'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _restartSession();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2E7EED),
                foregroundColor: Colors.white,
              ),
              child: const Text('再来一轮'),
            ),
          ],
        ),
      ),
    );
  }

  // 重新开始会话
  void _restartSession() {
    setState(() {
      _currentExerciseIndex = 0;
      _remainingTime = widget.duration;
      _state = ExerciseState.ready;
      _completedExercises = 0;
    });
    _progressController.reset();
  }

  // 构建统计项
  Widget _buildStatItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: const Color(0xFF9B9A97)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(fontSize: 14, color: Color(0xFF9B9A97)),
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentExercise = widget.exercises[_currentExerciseIndex];

    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3), // Notion风格浅灰背景
      body: SafeArea(
        child: Column(
          children: [
            _buildTopInfoBar(currentExercise),
            Expanded(child: _buildMainContent(currentExercise)),
            _buildBottomControlBar(),
          ],
        ),
      ),
    );
  }

  // 构建顶部信息栏 - Notion风格
  Widget _buildTopInfoBar(PAOExercise exercise) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: const Color(0xFF37352F).withValues(alpha: 0.1),
          ),
        ),
      ),
      child: Row(
        children: [
          // 返回按钮
          IconButton(
            onPressed: () => _showExitDialog(),
            icon: const Icon(Icons.close, color: Color(0xFF37352F), size: 24),
            tooltip: '退出运动',
          ),
          const SizedBox(width: 12),

          // 运动信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  exercise.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                  ),
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Text(
                      '${_currentExerciseIndex + 1}/${widget.exercises.length}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF787774),
                      ),
                    ),
                    if (widget.paoWord != null) ...[
                      const SizedBox(width: 12),
                      const Icon(
                        Icons.psychology,
                        size: 14,
                        color: Color(0xFF2E7EED),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        widget.paoWord!,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF2E7EED),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          // 卡路里显示（紧凑）
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: const Color(0xFFD9730D).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.local_fire_department,
                  size: 14,
                  color: Color(0xFFD9730D),
                ),
                const SizedBox(width: 4),
                Text(
                  '${_calculateCurrentCalories()}卡',
                  style: const TextStyle(
                    fontSize: 11,
                    color: Color(0xFFD9730D),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建主要内容区
  Widget _buildMainContent(PAOExercise exercise) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 20),
            // 进度指示器
            _buildProgressIndicator(),
            const SizedBox(height: 20),

            // 单词显示区（如果有单词）
            if (widget.paoWord != null) _buildWordSection(),
            if (widget.paoWord != null) const SizedBox(height: 20),

            // 字母选择界面
            if (widget.paoWord != null) _buildLetterSelector(),
            if (widget.paoWord != null) const SizedBox(height: 20),

            // 动作描述区（简化版）
            _buildExerciseDescription(exercise),
            const SizedBox(height: 20),

            // 计时器区
            _buildTimerSection(),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // 构建进度指示器
  Widget _buildProgressIndicator() {
    final progress =
        (_currentExerciseIndex +
            (_state == ExerciseState.exercising
                ? (widget.duration - _remainingTime) / widget.duration
                : 0)) /
        widget.exercises.length;

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '运动进度',
              style: const TextStyle(fontSize: 12, color: Color(0xFF787774)),
            ),
            Text(
              '${(progress * 100).toInt()}%',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2E7EED),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: const Color(0xFF37352F).withValues(alpha: 0.1),
          valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF2E7EED)),
          minHeight: 5,
          borderRadius: BorderRadius.circular(2.5),
        ),
      ],
    );
  }

  // 构建单词显示区 - Notion风格
  Widget _buildWordSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE3E2DE)),
      ),
      child: Column(
        children: [
          Text(
            widget.paoWord!.toUpperCase(),
            style: const TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
              letterSpacing: 1,
            ),
            textAlign: TextAlign.center,
          ),
          if (widget.wordPhonetic != null) ...[
            const SizedBox(height: 4),
            Text(
              widget.wordPhonetic!,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF9B9A97),
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
          if (widget.wordMeaning != null) ...[
            const SizedBox(height: 8),
            Text(
              widget.wordMeaning!,
              style: const TextStyle(
                fontSize: 13,
                color: Color(0xFF37352F),
                height: 1.3,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  // 构建字母选择界面 - 键盘布局，Notion风格
  Widget _buildLetterSelector() {
    // 键盘布局
    const keyboardRows = [
      ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
      ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L'],
      ['Z', 'X', 'C', 'V', 'B', 'N', 'M'],
    ];

    // 检查当前单词中包含的字母
    Set<String> highlightedLetters = {};
    if (widget.paoWord != null) {
      highlightedLetters = widget.paoWord!.toUpperCase().split('').toSet();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE3E2DE)),
      ),
      child: Column(
        children: keyboardRows.map((row) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: row.map((letter) {
                final isHighlighted = highlightedLetters.contains(letter);
                return Container(
                  width: 28,
                  height: 32,
                  margin: const EdgeInsets.symmetric(horizontal: 1.5),
                  decoration: BoxDecoration(
                    color: isHighlighted
                        ? const Color(0xFF2E7EED)
                        : Colors.white,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: isHighlighted
                          ? const Color(0xFF2E7EED)
                          : const Color(0xFFE3E2DE),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.03),
                        offset: const Offset(0, 1),
                        blurRadius: 1,
                      ),
                    ],
                  ),
                  child: Center(
                    child: Text(
                      letter,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: isHighlighted
                            ? Colors.white
                            : const Color(0xFF37352F),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          );
        }).toList(),
      ),
    );
  }

  // 构建动作描述区（简化版，无图标）
  Widget _buildExerciseDescription(PAOExercise exercise) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2DE)),
      ),
      child: Column(
        children: [
          Text(
            exercise.description,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF37352F),
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          _buildExerciseInfo(exercise),
        ],
      ),
    );
  }

  // 构建动作信息
  Widget _buildExerciseInfo(PAOExercise exercise) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildInfoChip(
          Icons.timer,
          '${widget.duration}秒',
          const Color(0xFF0F7B6C),
        ),
        _buildInfoChip(
          Icons.fitness_center,
          exercise.bodyPart,
          const Color(0xFFE03E3E),
        ),
        _buildInfoChip(
          Icons.local_fire_department,
          '${_calculateCalories(exercise)}卡',
          const Color(0xFFD9730D),
        ),
      ],
    );
  }

  // 构建信息标签
  Widget _buildInfoChip(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // 构建计时器区域
  Widget _buildTimerSection() {
    return Column(
      children: [
        // 倒计时显示
        Container(
          width: 160,
          height: 160,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white,
            border: Border.all(color: const Color(0xFFE3E2DE), width: 2),
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // 进度圆环
              SizedBox(
                width: 150,
                height: 150,
                child: AnimatedBuilder(
                  animation: _progressController,
                  builder: (context, child) {
                    return CircularProgressIndicator(
                      value: _state == ExerciseState.exercising
                          ? (widget.duration - _remainingTime) / widget.duration
                          : 0.0,
                      strokeWidth: 5,
                      backgroundColor: const Color(
                        0xFF2E7EED,
                      ).withValues(alpha: 0.1),
                      valueColor: const AlwaysStoppedAnimation<Color>(
                        Color(0xFF2E7EED),
                      ),
                    );
                  },
                ),
              ),

              // 时间文字
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _formatTime(_remainingTime),
                    style: const TextStyle(
                      fontSize: 40,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  Text(
                    _getStateText(),
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF787774),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // 统计信息
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildStatDisplay(
              '已完成',
              '$_completedExercises',
              Icons.check_circle_outline,
            ),
            _buildStatDisplay(
              '卡路里',
              '${_calculateCurrentCalories()}',
              Icons.local_fire_department_outlined,
            ),
            _buildStatDisplay(
              '金币',
              '+${_calculateCurrentCoins()}',
              Icons.monetization_on_outlined,
            ),
          ],
        ),
      ],
    );
  }

  // 构建统计显示
  Widget _buildStatDisplay(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: const Color(0xFF9B9A97), size: 18),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 10, color: Color(0xFF787774)),
        ),
      ],
    );
  }

  // 构建底部控制栏
  Widget _buildBottomControlBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: const Color(0xFF37352F).withValues(alpha: 0.1),
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 跳过按钮
          _buildControlButton(
            icon: Icons.skip_next,
            label: '跳过',
            onPressed:
                _state == ExerciseState.exercising ||
                    _state == ExerciseState.paused
                ? _skipExercise
                : null,
            backgroundColor: const Color(0xFFD9730D).withValues(alpha: 0.1),
            iconColor: const Color(0xFFD9730D),
          ),

          // 主控制按钮
          _buildMainControlButton(),

          // 设置按钮
          _buildControlButton(
            icon: Icons.settings_outlined,
            label: '设置',
            onPressed: _showSettingsDialog,
            backgroundColor: const Color(0xFF37352F).withValues(alpha: 0.1),
            iconColor: const Color(0xFF787774),
          ),
        ],
      ),
    );
  }

  // 构建主控制按钮
  Widget _buildMainControlButton() {
    IconData icon;
    String label;
    VoidCallback? onPressed;
    Color backgroundColor;
    Color iconColor;

    switch (_state) {
      case ExerciseState.ready:
        icon = Icons.play_arrow;
        label = '开始';
        onPressed = _startExercise;
        backgroundColor = const Color(0xFF2E7EED);
        iconColor = Colors.white;
        break;
      case ExerciseState.exercising:
        icon = Icons.pause;
        label = '暂停';
        onPressed = _pauseExercise;
        backgroundColor = const Color(0xFFE03E3E);
        iconColor = Colors.white;
        break;
      case ExerciseState.paused:
        icon = Icons.play_arrow;
        label = '继续';
        onPressed = _resumeExercise;
        backgroundColor = const Color(0xFF0F7B6C);
        iconColor = Colors.white;
        break;
      case ExerciseState.completed:
        icon = Icons.refresh;
        label = '重新开始';
        onPressed = _restartSession;
        backgroundColor = const Color(0xFF7C3AED);
        iconColor = Colors.white;
        break;
    }

    return Container(
      width: 72,
      height: 72,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor,
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(36),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: iconColor, size: 30),
              const SizedBox(height: 2),
              Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  color: iconColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建控制按钮
  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback? onPressed,
    required Color backgroundColor,
    required Color iconColor,
  }) {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(shape: BoxShape.circle, color: backgroundColor),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(28),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: iconColor, size: 22),
              const SizedBox(height: 2),
              Text(
                label,
                style: TextStyle(
                  fontSize: 8,
                  color: iconColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 显示退出对话框
  void _showExitDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('确定要退出当前运动吗？进度将不会保存。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFE03E3E),
              foregroundColor: Colors.white,
            ),
            child: const Text('退出'),
          ),
        ],
      ),
    );
  }

  // 显示设置对话框
  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('运动设置'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SwitchListTile(
              title: const Text('背景音乐'),
              value: _isMusicEnabled,
              onChanged: (value) {
                setState(() {
                  _isMusicEnabled = value;
                });
                Navigator.pop(context);
              },
            ),
            SwitchListTile(
              title: const Text('音效提示'),
              value: _isSoundEnabled,
              onChanged: (value) {
                setState(() {
                  _isSoundEnabled = value;
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  // 格式化时间
  String _formatTime(int seconds) {
    if (seconds < 60) {
      return seconds.toString().padLeft(2, '0');
    } else {
      final minutes = seconds ~/ 60;
      final remainingSeconds = seconds % 60;
      return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
    }
  }

  // 获取状态文字
  String _getStateText() {
    switch (_state) {
      case ExerciseState.ready:
        return '准备开始';
      case ExerciseState.exercising:
        return '正在运动';
      case ExerciseState.paused:
        return '已暂停';
      case ExerciseState.completed:
        return '已完成';
    }
  }
}
