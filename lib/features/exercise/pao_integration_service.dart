import '../../core/data/pao_exercises_data.dart';
import 'custom_action_library.dart';
import 'custom_action_library_service.dart';

/// PAO集成服务 - 负责整合自定义动作库和预设动作库
class PAOIntegrationService {
  final CustomActionLibraryService _customLibraryService;

  PAOIntegrationService(this._customLibraryService);

  /// 获取当前有效的动作映射
  /// 优先使用选中的自定义动作库，否则使用预设动作库
  Map<String, PAOExercise> getCurrentExerciseMap() {
    final selectedLibrary = _customLibraryService.selectedLibrary;

    if (selectedLibrary != null && selectedLibrary.actions.isNotEmpty) {
      // 使用自定义动作库
      return _convertCustomActionsToExercises(selectedLibrary);
    } else {
      // 使用预设动作库
      return PAOExercisesData.getLetterToExerciseMap();
    }
  }

  /// 为单词生成PAO动作序列
  List<PAOExercise> generatePAOExercisesForWord(String word) {
    final exercises = <PAOExercise>[];
    final exerciseMap = getCurrentExerciseMap();

    for (int i = 0; i < word.length; i++) {
      final letter = word[i].toUpperCase();
      final exercise = exerciseMap[letter];
      if (exercise != null) {
        exercises.add(exercise);
      }
    }

    return exercises;
  }

  /// 获取字母对应的动作
  PAOExercise? getExerciseForLetter(String letter) {
    final exerciseMap = getCurrentExerciseMap();
    return exerciseMap[letter.toUpperCase()];
  }

  /// 获取所有可用的动作
  List<PAOExercise> getAllAvailableExercises() {
    return getCurrentExerciseMap().values.toList();
  }

  /// 获取指定场景的动作
  List<PAOExercise> getExercisesByScenario(String scenario) {
    return getAllAvailableExercises()
        .where((exercise) => exercise.scenarios.contains(scenario))
        .toList();
  }

  /// 获取指定分类的动作
  List<PAOExercise> getExercisesByCategory(String category) {
    return getAllAvailableExercises()
        .where((exercise) => exercise.category == category)
        .toList();
  }

  /// 检查是否有自定义动作库被选中
  bool get hasCustomLibrarySelected {
    final selectedLibrary = _customLibraryService.selectedLibrary;
    return selectedLibrary != null && selectedLibrary.actions.isNotEmpty;
  }

  /// 获取当前动作库信息
  Map<String, dynamic> getCurrentLibraryInfo() {
    final selectedLibrary = _customLibraryService.selectedLibrary;

    if (selectedLibrary != null) {
      return {
        'type': 'custom',
        'name': selectedLibrary.name,
        'description': selectedLibrary.description,
        'completedActions': selectedLibrary.completedActionsCount,
        'totalActions': 26,
        'completionPercentage': selectedLibrary.completionPercentage,
      };
    } else {
      return {
        'type': 'preset',
        'name': '系统预设动作库',
        'description': '包含健身、瑜伽、养生等多种动作类型',
        'completedActions': 26,
        'totalActions': 26,
        'completionPercentage': 1.0,
      };
    }
  }

  /// 获取动作库统计信息
  Map<String, dynamic> getLibraryStatistics() {
    final exerciseMap = getCurrentExerciseMap();
    final exercises = exerciseMap.values.toList();

    // 按分类统计
    final categoryStats = <String, int>{};
    for (final exercise in exercises) {
      categoryStats[exercise.category] =
          (categoryStats[exercise.category] ?? 0) + 1;
    }

    // 按场景统计
    final scenarioStats = <String, int>{};
    for (final exercise in exercises) {
      for (final scenario in exercise.scenarios) {
        scenarioStats[scenario] = (scenarioStats[scenario] ?? 0) + 1;
      }
    }

    return {
      'totalExercises': exercises.length,
      'categoryStats': categoryStats,
      'scenarioStats': scenarioStats,
      'libraryInfo': getCurrentLibraryInfo(),
    };
  }

  /// 验证单词是否可以生成完整的动作序列
  bool canGenerateCompleteSequence(String word) {
    final exerciseMap = getCurrentExerciseMap();

    for (int i = 0; i < word.length; i++) {
      final letter = word[i].toUpperCase();
      if (!exerciseMap.containsKey(letter)) {
        return false;
      }
    }

    return true;
  }

  /// 获取缺失动作的字母
  List<String> getMissingLettersForWord(String word) {
    final exerciseMap = getCurrentExerciseMap();
    final missingLetters = <String>[];

    for (int i = 0; i < word.length; i++) {
      final letter = word[i].toUpperCase();
      if (!exerciseMap.containsKey(letter)) {
        missingLetters.add(letter);
      }
    }

    return missingLetters.toSet().toList(); // 去重
  }

  /// 获取推荐的训练单词
  /// 基于当前动作库的完整性推荐适合的单词
  List<String> getRecommendedWords({int maxLength = 8, int count = 10}) {
    // 这里可以从词汇库中筛选出只包含可用字母的单词
    // 暂时返回一些示例单词
    final recommendedWords = <String>[];

    // 生成一些基于可用字母的示例单词
    final commonWords = [
      'CAT',
      'DOG',
      'BOOK',
      'STUDY',
      'LEARN',
      'PRACTICE',
      'FOCUS',
      'GOAL',
      'SUCCESS',
      'ACHIEVE',
      'DREAM',
      'FUTURE',
      'HOPE',
      'WORK',
      'EFFORT',
      'PROGRESS',
      'IMPROVE',
      'DEVELOP',
      'SKILL',
      'KNOWLEDGE',
    ];

    for (final word in commonWords) {
      if (word.length <= maxLength && canGenerateCompleteSequence(word)) {
        recommendedWords.add(word);
        if (recommendedWords.length >= count) break;
      }
    }

    return recommendedWords;
  }

  /// 将自定义动作转换为PAOExercise
  Map<String, PAOExercise> _convertCustomActionsToExercises(
    CustomActionLibrary library,
  ) {
    final exerciseMap = <String, PAOExercise>{};

    // 首先添加预设动作作为基础
    final presetExercises = PAOExercisesData.getLetterToExerciseMap();
    exerciseMap.addAll(presetExercises);

    // 然后用自定义动作覆盖
    library.actions.forEach((letter, customAction) {
      exerciseMap[letter] = customAction.toPAOExercise();
    });

    return exerciseMap;
  }

  /// 切换到指定的自定义动作库
  Future<void> switchToCustomLibrary(String? libraryId) async {
    await _customLibraryService.selectLibrary(libraryId);
  }

  /// 切换到预设动作库
  Future<void> switchToPresetLibrary() async {
    await _customLibraryService.selectLibrary(null);
  }

  /// 获取所有可用的动作库选项
  List<Map<String, dynamic>> getAvailableLibraryOptions() {
    final options = <Map<String, dynamic>>[];

    // 添加预设动作库选项
    options.add({
      'id': null,
      'name': '系统预设动作库',
      'description': '包含健身、瑜伽、养生等多种动作类型',
      'type': 'preset',
      'isSelected': _customLibraryService.selectedLibraryId == null,
      'completedActions': 26,
      'totalActions': 26,
    });

    // 添加自定义动作库选项
    for (final library in _customLibraryService.libraries) {
      options.add({
        'id': library.id,
        'name': library.name,
        'description': library.description,
        'type': 'custom',
        'isSelected': _customLibraryService.selectedLibraryId == library.id,
        'completedActions': library.completedActionsCount,
        'totalActions': 26,
      });
    }

    return options;
  }

  /// 检查动作库是否完整（是否包含所有26个字母的动作）
  bool isLibraryComplete() {
    final exerciseMap = getCurrentExerciseMap();

    for (int i = 0; i < 26; i++) {
      final letter = String.fromCharCode(65 + i); // A-Z
      if (!exerciseMap.containsKey(letter)) {
        return false;
      }
    }

    return true;
  }

  /// 获取缺失的字母列表
  List<String> getMissingLetters() {
    final exerciseMap = getCurrentExerciseMap();
    final missingLetters = <String>[];

    for (int i = 0; i < 26; i++) {
      final letter = String.fromCharCode(65 + i); // A-Z
      if (!exerciseMap.containsKey(letter)) {
        missingLetters.add(letter);
      }
    }

    return missingLetters;
  }
}
