import 'package:flutter/material.dart';

/// 添加到记忆对话框
/// 
/// 允许用户输入单词并添加到记忆词库，支持单个和批量添加
class AddToMemoryDialog extends StatefulWidget {
  final Function(List<String>) onWordsAdded;

  const AddToMemoryDialog({
    super.key,
    required this.onWordsAdded,
  });

  @override
  State<AddToMemoryDialog> createState() => _AddToMemoryDialogState();
}

class _AddToMemoryDialogState extends State<AddToMemoryDialog> {
  final TextEditingController _textController = TextEditingController();
  final List<String> _words = [];

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  /// 添加单词到列表
  void _addWord() {
    final text = _textController.text.trim();
    if (text.isEmpty) return;

    // 支持多个单词，用空格、逗号或换行分隔
    final words = text
        .split(RegExp(r'[,\s\n]+'))
        .map((word) => word.trim())
        .where((word) => word.isNotEmpty)
        .toList();

    setState(() {
      for (final word in words) {
        if (!_words.contains(word.toLowerCase())) {
          _words.add(word);
        }
      }
    });

    _textController.clear();
  }

  /// 移除单词
  void _removeWord(int index) {
    setState(() {
      _words.removeAt(index);
    });
  }

  /// 确认添加
  void _confirmAdd() {
    if (_words.isNotEmpty) {
      widget.onWordsAdded(_words);
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      title: const Text(
        '添加到记忆词库',
        style: TextStyle(
          color: Color(0xFF37352F),
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 输入框
            TextField(
              controller: _textController,
              decoration: InputDecoration(
                hintText: '输入单词（支持多个，用空格或逗号分隔）',
                hintStyle: const TextStyle(color: Color(0xFF9B9A97)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF2F76DA)),
                ),
              ),
              maxLines: 3,
              onSubmitted: (_) => _addWord(),
            ),

            const SizedBox(height: 8),

            // 添加按钮
            Align(
              alignment: Alignment.centerRight,
              child: TextButton.icon(
                onPressed: _addWord,
                icon: const Icon(Icons.add, size: 16),
                label: const Text('添加'),
                style: TextButton.styleFrom(
                  foregroundColor: const Color(0xFF2F76DA),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 单词列表
            if (_words.isNotEmpty) ...[
              Text(
                '待添加的单词 (${_words.length})',
                style: const TextStyle(
                  color: Color(0xFF37352F),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                constraints: const BoxConstraints(maxHeight: 200),
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: _words.length,
                  itemBuilder: (context, index) {
                    final word = _words[index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 4),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF7F6F3),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: const Color(0xFFE3E2E0)),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              word,
                              style: const TextStyle(
                                color: Color(0xFF37352F),
                                fontSize: 14,
                              ),
                            ),
                          ),
                          IconButton(
                            onPressed: () => _removeWord(index),
                            icon: const Icon(
                              Icons.close,
                              size: 16,
                              color: Color(0xFF9B9A97),
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 24,
                              minHeight: 24,
                            ),
                            padding: EdgeInsets.zero,
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text(
            '取消',
            style: TextStyle(color: Color(0xFF787774)),
          ),
        ),
        ElevatedButton(
          onPressed: _words.isNotEmpty ? _confirmAdd : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF2F76DA),
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text('添加 ${_words.length} 个单词'),
        ),
      ],
    );
  }
}
