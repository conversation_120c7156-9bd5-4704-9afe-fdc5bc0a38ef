import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/timebox_models.dart';

/// 时间盒子状态
class TimeBoxState {
  final List<TimeBoxTask> tasks;
  final bool isLoading;
  final String? error;
  final DateTime? lastUpdated;

  const TimeBoxState({
    this.tasks = const [],
    this.isLoading = false,
    this.error,
    this.lastUpdated,
  });

  TimeBoxState copyWith({
    List<TimeBoxTask>? tasks,
    bool? isLoading,
    String? error,
    DateTime? lastUpdated,
  }) {
    return TimeBoxState(
      tasks: tasks ?? this.tasks,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  /// 获取指定日期的任务
  List<TimeBoxTask> getTasksForDate(DateTime date) {
    final targetDate = DateTime(date.year, date.month, date.day);
    return tasks.where((task) {
      if (task.startTime != null) {
        final taskDate = DateTime(
          task.startTime!.year,
          task.startTime!.month,
          task.startTime!.day,
        );
        return taskDate == targetDate;
      }
      return false;
    }).toList();
  }

  /// 获取指定时间段的任务
  List<TimeBoxTask> getTasksForTimeRange(DateTime date, int hour) {
    final targetDate = DateTime(date.year, date.month, date.day);
    return tasks.where((task) {
      if (task.startTime != null) {
        final taskDate = DateTime(
          task.startTime!.year,
          task.startTime!.month,
          task.startTime!.day,
        );
        if (taskDate == targetDate) {
          final taskHour = task.startTime!.hour;
          // 检查任务是否在这个小时内开始或进行中
          if (task.endTime != null) {
            final endHour = task.endTime!.hour;
            return taskHour <= hour && hour <= endHour;
          } else {
            // 如果没有结束时间，只检查开始时间
            return taskHour == hour;
          }
        }
      }
      return false;
    }).toList();
  }
}

/// 时间盒子状态管理器
class TimeBoxNotifier extends StateNotifier<TimeBoxState> {
  static const String _storageKey = 'timebox_tasks_v1';

  TimeBoxNotifier() : super(const TimeBoxState()) {
    _loadFromStorage();
  }

  /// 从本地存储加载数据
  Future<void> _loadFromStorage() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_storageKey);

      List<TimeBoxTask> tasks = [];
      if (jsonString != null) {
        final List<dynamic> jsonList = json.decode(jsonString);
        tasks = jsonList.map((json) => TimeBoxTask.fromJson(json)).toList();

        // 检查是否需要添加默认任务（如果当前任务列表为空或只有用户创建的任务）
        if (tasks.isEmpty || !_hasDefaultTasks(tasks)) {
          print('📝 检测到缺少默认任务，正在添加...');
          final defaultTasks = _getDefaultTasks();
          // 只添加不存在的默认任务
          for (final defaultTask in defaultTasks) {
            if (!tasks.any((task) => task.id == defaultTask.id)) {
              tasks.add(defaultTask);
              print('✅ 添加默认任务: ${defaultTask.title}');
            }
          }
          // 保存更新后的任务列表
          await _saveTasksToStorage(tasks);
        } else {
          print('📝 默认任务已存在，跳过创建');
        }
      } else {
        // 如果没有存储数据，使用示例数据
        tasks = _getDefaultTasks();
        // 立即保存默认任务
        await _saveTasksToStorage(tasks);
      }

      state = state.copyWith(
        tasks: tasks,
        isLoading: false,
        lastUpdated: DateTime.now(),
      );

      print('✅ 已加载 ${tasks.length} 个时间盒子任务');
    } catch (e) {
      state = state.copyWith(isLoading: false, error: '加载任务失败: $e');
      print('❌ 加载时间盒子任务失败: $e');
    }
  }

  /// 检查是否包含默认任务
  bool _hasDefaultTasks(List<TimeBoxTask> tasks) {
    final defaultTaskIds = ['1', '2', '3', '4', '5'];
    return defaultTaskIds.any((id) => tasks.any((task) => task.id == id));
  }

  /// 直接保存任务列表到存储（内部方法）
  Future<void> _saveTasksToStorage(List<TimeBoxTask> tasks) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = tasks.map((task) => task.toJson()).toList();
      await prefs.setString(_storageKey, json.encode(jsonList));
      print('💾 已保存 ${tasks.length} 个时间盒子任务到本地存储');
    } catch (e) {
      print('❌ 保存时间盒子任务失败: $e');
    }
  }

  /// 保存数据到本地存储
  Future<void> _saveToStorage() async {
    try {
      await _saveTasksToStorage(state.tasks);
    } catch (e) {
      print('❌ 保存时间盒子任务失败: $e');
      state = state.copyWith(error: '保存数据失败: $e');
    }
  }

  /// 添加任务
  Future<void> addTask(TimeBoxTask task) async {
    final updatedTasks = [...state.tasks, task];
    state = state.copyWith(tasks: updatedTasks);
    await _saveToStorage();
  }

  /// 更新任务
  Future<void> updateTask(TimeBoxTask updatedTask) async {
    final updatedTasks = state.tasks.map((task) {
      return task.id == updatedTask.id ? updatedTask : task;
    }).toList();

    state = state.copyWith(tasks: updatedTasks);
    await _saveToStorage();
  }

  /// 删除任务
  Future<void> deleteTask(String taskId) async {
    final updatedTasks = state.tasks
        .where((task) => task.id != taskId)
        .toList();
    state = state.copyWith(tasks: updatedTasks);
    await _saveToStorage();
  }

  /// 开始任务
  Future<void> startTask(String taskId) async {
    final task = state.tasks.firstWhere((t) => t.id == taskId);
    final updatedTask = task.copyWith(
      status: TaskStatus.inProgress,
      startTime: task.startTime ?? DateTime.now(),
    );
    await updateTask(updatedTask);
  }

  /// 完成任务
  Future<void> completeTask(String taskId) async {
    final task = state.tasks.firstWhere((t) => t.id == taskId);
    final updatedTask = task.copyWith(
      status: TaskStatus.completed,
      endTime: DateTime.now(),
    );
    await updateTask(updatedTask);
  }

  /// 刷新数据
  Future<void> refresh() async {
    await _loadFromStorage();
  }

  /// 获取默认示例任务
  List<TimeBoxTask> _getDefaultTasks() {
    final now = DateTime.now();
    // 确保所有默认任务都是今天创建的，使用今天的开始时间作为基准
    final todayStart = DateTime(now.year, now.month, now.day);

    print('📝 创建默认任务 - 当前时间: $now, 今日开始: $todayStart');

    final tasks = [
      TimeBoxTask(
        id: '1',
        title: '算法复习：动态规划',
        description: '复习背包问题、最长公共子序列等经典算法',
        plannedMinutes: 120,
        status: TaskStatus.completed,
        priority: TaskPriority.high,
        category: '计算机科学',
        createdAt: todayStart.add(const Duration(hours: 8)), // 今天上午8点创建
        startTime: todayStart.add(const Duration(hours: 9)), // 今天上午9点开始
        endTime: todayStart.add(const Duration(hours: 11)), // 今天上午11点结束
      ),
      TimeBoxTask(
        id: '2',
        title: '英语单词记忆',
        description: '使用记忆宫殿法记忆GRE词汇100个',
        plannedMinutes: 60,
        status: TaskStatus.completed,
        priority: TaskPriority.medium,
        category: '英语',
        createdAt: todayStart.add(const Duration(hours: 10)), // 今天上午10点创建
        startTime: todayStart.add(const Duration(hours: 14)), // 今天下午2点开始
        endTime: todayStart.add(const Duration(hours: 15)), // 今天下午3点结束
      ),
      TimeBoxTask(
        id: '3',
        title: '数学练习：微积分',
        description: '完成教材第5章练习题',
        plannedMinutes: 90,
        status: TaskStatus.inProgress,
        priority: TaskPriority.medium,
        category: '数学',
        createdAt: todayStart.add(const Duration(hours: 12)), // 今天中午12点创建
        startTime: now.subtract(const Duration(minutes: 30)), // 30分钟前开始（正在进行中）
      ),
      TimeBoxTask(
        id: '4',
        title: '政治理论学习',
        description: '马克思主义基本原理概论重点章节复习',
        plannedMinutes: 75,
        status: TaskStatus.pending,
        priority: TaskPriority.low,
        category: '政治',
        createdAt: todayStart.add(const Duration(hours: 16)), // 今天下午4点创建
      ),
      TimeBoxTask(
        id: '5',
        title: 'PAO记忆法练习',
        description: '使用动觉记忆背单词',
        plannedMinutes: 5,
        status: TaskStatus.completed,
        priority: TaskPriority.low,
        category: '英语',
        createdAt: todayStart.add(const Duration(hours: 13)), // 今天下午1点创建
        startTime: todayStart.add(
          const Duration(hours: 15, minutes: 30),
        ), // 今天下午3:30开始
        endTime: todayStart.add(
          const Duration(hours: 15, minutes: 35),
        ), // 今天下午3:35结束
      ),
    ];

    // 输出调试信息
    for (final task in tasks) {
      print('📝 默认任务 "${task.title}":');
      print('   创建时间: ${task.createdAt}');
      print('   开始时间: ${task.startTime}');
      print('   结束时间: ${task.endTime}');
    }

    return tasks;
  }
}

/// 时间盒子Provider
final timeBoxProvider = StateNotifierProvider<TimeBoxNotifier, TimeBoxState>(
  (ref) => TimeBoxNotifier(),
);

/// 指定日期的任务Provider
final tasksForDateProvider = Provider.family<List<TimeBoxTask>, DateTime>((
  ref,
  date,
) {
  final state = ref.watch(timeBoxProvider);
  return state.getTasksForDate(date);
});

/// 指定时间段的任务Provider
final tasksForTimeRangeProvider =
    Provider.family<List<TimeBoxTask>, ({DateTime date, int hour})>((
      ref,
      params,
    ) {
      final state = ref.watch(timeBoxProvider);
      return state.getTasksForTimeRange(params.date, params.hour);
    });
