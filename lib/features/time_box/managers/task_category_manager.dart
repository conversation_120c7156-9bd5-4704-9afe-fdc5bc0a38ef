import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/timebox_models.dart';

/// 任务分类管理器
///
/// 负责管理用户自定义的任务分类，包括：
/// - 本地存储持久化
/// - 默认分类初始化
/// - 分类的增删改查操作
/// - 分类名称验证
class TaskCategoryManager {
  static const String _storageKey = 'task_categories_v1';
  static const String _versionKey = 'task_categories_version';
  static const int _currentVersion = 2; // 版本2：删除了"休息"和"其他"分类

  List<TaskCategory> _categories = [];

  List<TaskCategory> get categories => List.unmodifiable(_categories);

  /// 获取默认分类图标
  static String _getDefaultIcon(String categoryName) {
    switch (categoryName) {
      case '计算机科学':
        return 'computer';
      case '数学':
        return 'calculate';
      case '英语':
        return 'translate';
      case '政治':
        return 'account_balance';
      default:
        return 'task_alt';
    }
  }

  /// 初始化默认分类
  void _initializeDefaultCategories() {
    final now = DateTime.now();
    _categories = [
      TaskCategory(
        id: 'computer_science',
        name: '计算机科学',
        colorValue: 0xFFE03E3E,
        iconName: _getDefaultIcon('计算机科学'),
        isDefault: true,
        createdAt: now,
        lastModified: now,
      ),
      TaskCategory(
        id: 'math',
        name: '数学',
        colorValue: 0xFFFFD700,
        iconName: _getDefaultIcon('数学'),
        isDefault: true,
        createdAt: now,
        lastModified: now,
      ),
      TaskCategory(
        id: 'english',
        name: '英语',
        colorValue: 0xFF0F7B6C,
        iconName: _getDefaultIcon('英语'),
        isDefault: true,
        createdAt: now,
        lastModified: now,
      ),
      TaskCategory(
        id: 'politics',
        name: '政治',
        colorValue: 0xFF2E7EED,
        iconName: _getDefaultIcon('政治'),
        isDefault: true,
        createdAt: now,
        lastModified: now,
      ),
    ];
  }

  /// 从本地存储加载分类
  Future<void> loadFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentVersion = prefs.getInt(_versionKey) ?? 1;
      final jsonString = prefs.getString(_storageKey);

      // 检查版本，如果版本过旧则清理数据
      if (currentVersion < _currentVersion) {
        print('📦 检测到分类数据版本过旧 (v$currentVersion -> v$_currentVersion)，清理旧数据');
        await _clearOldData(prefs);
        await _initializeWithMigration();
        await prefs.setInt(_versionKey, _currentVersion);
        return;
      }

      if (jsonString != null && jsonString.isNotEmpty) {
        final jsonList = json.decode(jsonString) as List;
        _categories = jsonList
            .map((json) => TaskCategory.fromJson(json as Map<String, dynamic>))
            .toList();
        print('✅ 成功加载 ${_categories.length} 个任务分类');
      } else {
        print('📂 未找到任务分类数据，初始化默认分类');
        await _initializeWithMigration();
        await prefs.setInt(_versionKey, _currentVersion);
      }
    } catch (e) {
      print('❌ 加载任务分类数据失败，使用默认数据: $e');
      await _initializeWithMigration();
    }
  }

  /// 清理旧数据
  Future<void> _clearOldData(SharedPreferences prefs) async {
    try {
      // 清理旧的分类数据
      await prefs.remove(_storageKey);
      print('🗑️ 已清理旧的分类数据');
    } catch (e) {
      print('❌ 清理旧数据失败: $e');
    }
  }

  /// 初始化分类并处理迁移
  Future<void> _initializeWithMigration() async {
    _initializeDefaultCategories();

    // 检查是否需要迁移现有任务
    await _migrateExistingTasks();

    // 保存初始化的分类
    await saveToStorage();
  }

  /// 迁移现有任务的分类
  Future<void> _migrateExistingTasks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tasksJsonString = prefs.getString('timebox_tasks_v1');

      if (tasksJsonString != null && tasksJsonString.isNotEmpty) {
        final List<dynamic> tasksJsonList = json.decode(tasksJsonString);
        bool needsUpdate = false;

        // 收集所有现有任务中使用的分类
        Set<String> existingCategories = {};
        for (var taskJson in tasksJsonList) {
          final category = taskJson['category'] as String?;
          if (category != null && category.isNotEmpty) {
            existingCategories.add(category);
          }
        }

        // 为不在默认分类中的分类创建自定义分类
        for (String categoryName in existingCategories) {
          if (!_categories.any((cat) => cat.name == categoryName)) {
            final now = DateTime.now();
            final customCategory = TaskCategory(
              id: 'migrated_${now.millisecondsSinceEpoch}_${categoryName.hashCode}',
              name: categoryName,
              colorValue: _getColorForMigratedCategory(categoryName).toARGB32(),
              iconName: _getIconForMigratedCategory(categoryName),
              isDefault: false,
              createdAt: now,
              lastModified: now,
            );
            _categories.add(customCategory);
            needsUpdate = true;
            print('📦 迁移分类: $categoryName');
          }
        }

        if (needsUpdate) {
          print('✅ 完成任务分类迁移，共迁移 ${existingCategories.length} 个分类');
        }
      }
    } catch (e) {
      print('❌ 迁移任务分类失败: $e');
    }
  }

  /// 为迁移的分类获取颜色
  Color _getColorForMigratedCategory(String categoryName) {
    // 根据分类名称的哈希值选择颜色，确保一致性
    final colorIndex =
        categoryName.hashCode.abs() % TaskCategoryColors.defaultColors.length;
    return TaskCategoryColors.defaultColors[colorIndex];
  }

  /// 为迁移的分类获取图标
  String _getIconForMigratedCategory(String categoryName) {
    // 根据分类名称选择合适的图标
    if (categoryName.contains('学习') || categoryName.contains('学科')) {
      return 'school';
    } else if (categoryName.contains('工作') || categoryName.contains('任务')) {
      return 'work';
    } else if (categoryName.contains('运动') || categoryName.contains('健身')) {
      return 'fitness_center';
    } else if (categoryName.contains('休息') || categoryName.contains('放松')) {
      return 'self_improvement';
    } else if (categoryName.contains('娱乐') || categoryName.contains('游戏')) {
      return 'sports';
    } else {
      return 'category';
    }
  }

  /// 保存分类到本地存储
  Future<void> saveToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = _categories
          .map((category) => category.toJson())
          .toList();
      final jsonString = json.encode(jsonList);

      await prefs.setString(_storageKey, jsonString);
      print('💾 成功保存 ${_categories.length} 个任务分类');
    } catch (e) {
      print('❌ 保存任务分类失败: $e');
    }
  }

  /// 添加新分类
  Future<bool> addCategory(String name, Color color, String iconName) async {
    // 验证分类名称
    if (name.trim().isEmpty) {
      return false;
    }

    if (isCategoryNameExists(name.trim())) {
      return false;
    }

    final now = DateTime.now();
    final newCategory = TaskCategory(
      id: now.millisecondsSinceEpoch.toString(),
      name: name.trim(),
      colorValue: color.toARGB32(),
      iconName: iconName,
      isDefault: false,
      createdAt: now,
      lastModified: now,
    );

    _categories.add(newCategory);
    await saveToStorage();
    return true;
  }

  /// 更新分类
  Future<bool> updateCategory(
    String id,
    String name,
    Color color,
    String iconName,
  ) async {
    final index = _categories.indexWhere((cat) => cat.id == id);
    if (index == -1) {
      return false;
    }

    // 验证分类名称（排除当前分类）
    if (name.trim().isEmpty) {
      return false;
    }

    if (isCategoryNameExists(name.trim(), excludeId: id)) {
      return false;
    }

    final updatedCategory = _categories[index].copyWith(
      name: name.trim(),
      color: color,
      iconName: iconName,
      lastModified: DateTime.now(),
    );

    _categories[index] = updatedCategory;
    await saveToStorage();
    return true;
  }

  /// 删除分类
  Future<bool> deleteCategory(String id) async {
    final category = findCategoryById(id);
    if (category == null || category.isDefault) {
      return false; // 不能删除默认分类
    }

    _categories.removeWhere((cat) => cat.id == id);
    await saveToStorage();
    return true;
  }

  /// 根据ID查找分类
  TaskCategory? findCategoryById(String id) {
    try {
      return _categories.firstWhere((cat) => cat.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 根据名称查找分类
  TaskCategory? findCategoryByName(String name) {
    try {
      return _categories.firstWhere((cat) => cat.name == name);
    } catch (e) {
      return null;
    }
  }

  /// 检查分类名称是否已存在
  bool isCategoryNameExists(String name, {String? excludeId}) {
    return _categories.any(
      (cat) => cat.name == name && (excludeId == null || cat.id != excludeId),
    );
  }

  /// 获取所有分类名称
  List<String> getAllCategoryNames() {
    return _categories.map((cat) => cat.name).toList();
  }

  /// 根据分类名称获取颜色
  Color getCategoryColor(String categoryName) {
    final category = findCategoryByName(categoryName);
    if (category != null) {
      return category.color;
    }
    // 回退到旧的颜色系统
    return TaskCategoryColors.getCategoryColor(categoryName);
  }

  /// 根据分类名称获取半透明颜色
  Color getCategoryColorWithOpacity(String categoryName, double opacity) {
    return getCategoryColor(categoryName).withValues(alpha: opacity);
  }

  /// 获取默认分类
  List<TaskCategory> getDefaultCategories() {
    return _categories.where((cat) => cat.isDefault).toList();
  }

  /// 获取自定义分类
  List<TaskCategory> getCustomCategories() {
    return _categories.where((cat) => !cat.isDefault).toList();
  }

  /// 清空所有自定义分类（保留默认分类）
  Future<void> clearCustomCategories() async {
    _categories.removeWhere((cat) => !cat.isDefault);
    await saveToStorage();
  }

  /// 重置为默认分类
  Future<void> resetToDefaults() async {
    _initializeDefaultCategories();
    await saveToStorage();
  }

  /// 强制清理所有数据并重新初始化（用于修复缓存问题）
  Future<void> forceReset() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 清理所有相关数据
      await prefs.remove(_storageKey);
      await prefs.remove(_versionKey);

      print('🗑️ 已强制清理所有分类数据');

      // 重新初始化
      await _initializeWithMigration();
      await prefs.setInt(_versionKey, _currentVersion);

      print('✅ 强制重置完成，当前分类数量: ${_categories.length}');
    } catch (e) {
      print('❌ 强制重置失败: $e');
    }
  }

  /// 强制重置为新的4个系统分类（不进行数据迁移）
  Future<void> forceResetToNewCategories() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 清理所有相关数据，包括任务数据以避免迁移
      await prefs.remove(_storageKey);
      await prefs.remove(_versionKey);
      await prefs.remove('timebox_tasks_v1'); // 清理任务数据避免迁移

      print('🗑️ 已清理所有数据（包括任务数据）');

      // 直接初始化新的4个分类，不进行迁移
      _initializeDefaultCategories();
      await saveToStorage();
      await prefs.setInt(_versionKey, _currentVersion);

      print('✅ 已重置为新的4个系统分类');
      print('📋 当前分类: ${_categories.map((c) => c.name).join(", ")}');
    } catch (e) {
      print('❌ 重置失败: $e');
    }
  }
}

/// 全局任务分类管理器实例
final taskCategoryManager = TaskCategoryManager();
