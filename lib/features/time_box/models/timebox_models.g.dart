// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'timebox_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TimeBoxTask _$TimeBoxTaskFromJson(Map<String, dynamic> json) =>
    $checkedCreate('TimeBoxTask', json, ($checkedConvert) {
      final val = TimeBoxTask(
        id: $checkedConvert('id', (v) => v as String),
        title: $checkedConvert('title', (v) => v as String),
        description: $checkedConvert('description', (v) => v as String),
        plannedMinutes: $checkedConvert(
          'plannedMinutes',
          (v) => (v as num).toInt(),
        ),
        status: $checkedConvert(
          'status',
          (v) => $enumDecode(_$TaskStatusEnumMap, v),
        ),
        priority: $checkedConvert(
          'priority',
          (v) => $enumDecode(_$TaskPriorityEnumMap, v),
        ),
        category: $checkedConvert('category', (v) => v as String),
        createdAt: $checkedConvert(
          'createdAt',
          (v) => DateTime.parse(v as String),
        ),
        startTime: $checkedConvert(
          'startTime',
          (v) => v == null ? null : DateTime.parse(v as String),
        ),
        endTime: $checkedConvert(
          'endTime',
          (v) => v == null ? null : DateTime.parse(v as String),
        ),
      );
      return val;
    });

// ignore: unused_element
abstract class _$TimeBoxTaskPerFieldToJson {
  // ignore: unused_element
  static Object? id(String instance) => instance;
  // ignore: unused_element
  static Object? title(String instance) => instance;
  // ignore: unused_element
  static Object? description(String instance) => instance;
  // ignore: unused_element
  static Object? plannedMinutes(int instance) => instance;
  // ignore: unused_element
  static Object? status(TaskStatus instance) => _$TaskStatusEnumMap[instance]!;
  // ignore: unused_element
  static Object? priority(TaskPriority instance) =>
      _$TaskPriorityEnumMap[instance]!;
  // ignore: unused_element
  static Object? category(String instance) => instance;
  // ignore: unused_element
  static Object? createdAt(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? startTime(DateTime? instance) => instance?.toIso8601String();
  // ignore: unused_element
  static Object? endTime(DateTime? instance) => instance?.toIso8601String();
}

Map<String, dynamic> _$TimeBoxTaskToJson(TimeBoxTask instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'plannedMinutes': instance.plannedMinutes,
      'status': _$TaskStatusEnumMap[instance.status]!,
      'priority': _$TaskPriorityEnumMap[instance.priority]!,
      'category': instance.category,
      'createdAt': instance.createdAt.toIso8601String(),
      'startTime': instance.startTime?.toIso8601String(),
      'endTime': instance.endTime?.toIso8601String(),
    };

const _$TaskStatusEnumMap = {
  TaskStatus.pending: 'pending',
  TaskStatus.inProgress: 'inProgress',
  TaskStatus.completed: 'completed',
};

const _$TaskPriorityEnumMap = {
  TaskPriority.high: 'high',
  TaskPriority.medium: 'medium',
  TaskPriority.low: 'low',
};
