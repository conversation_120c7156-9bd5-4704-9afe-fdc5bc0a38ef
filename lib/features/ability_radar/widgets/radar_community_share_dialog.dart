import 'package:flutter/material.dart';
import '../models/ability_radar_models.dart';
import '../services/radar_share_service.dart';
import '../../community/community_storage_service.dart';
import '../../community/community_feed_page.dart';

/// 雷达图社区分享对话框
///
/// 提供雷达图分享到OneDay社区的功能，包括预览、添加描述等
class RadarCommunityShareDialog extends StatefulWidget {
  final GlobalKey radarChartKey;
  final AbilityRadarData data;

  const RadarCommunityShareDialog({
    super.key,
    required this.radarChartKey,
    required this.data,
  });

  /// 显示分享对话框
  static Future<bool?> show({
    required BuildContext context,
    required GlobalKey radarChartKey,
    required AbilityRadarData data,
  }) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) =>
          RadarCommunityShareDialog(radarChartKey: radarChartKey, data: data),
    );
  }

  @override
  State<RadarCommunityShareDialog> createState() =>
      _RadarCommunityShareDialogState();
}

class _RadarCommunityShareDialogState extends State<RadarCommunityShareDialog> {
  final TextEditingController _descriptionController = TextEditingController();
  final CommunityStorageService _storageService =
      CommunityStorageService.instance;

  bool _isSharing = false;
  final List<String> _selectedTags = [];

  final List<String> _availableTags = [
    '能力分析',
    '学习成长',
    '自我提升',
    '数据分享',
    '学习心得',
    '能力雷达',
    '成长记录',
    '学习效率',
  ];

  @override
  void initState() {
    super.initState();
    // 默认添加能力分析标签
    _selectedTags.add('能力分析');

    // 设置默认描述
    _descriptionController.text = _generateDefaultDescription();
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    super.dispose();
  }

  /// 生成默认描述
  String _generateDefaultDescription() {
    final data = widget.data;
    final strongest = data.strongestAbility;
    final weakest = data.weakestAbility;

    return '分享我的能力雷达图！\n\n'
        '📊 综合评分：${data.formattedOverallScore}\n'
        '💪 最强能力：${strongest.dimension.nameCn} (${strongest.formattedScore})\n'
        '📈 待提升：${weakest.dimension.nameCn} (${weakest.formattedScore})\n'
        '⚖️ 能力平衡度：${data.balanceDescription}\n\n'
        '通过OneDay持续提升自己！ 💪';
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [_buildHeader(), _buildContent(), _buildActions()],
        ),
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        border: Border(bottom: BorderSide(color: Color(0xFFE3E2E0))),
      ),
      child: Row(
        children: [
          const Icon(Icons.people_outline, color: Color(0xFF2E7EED), size: 24),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              '分享到OneDay社区',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF37352F),
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(false),
            icon: const Icon(Icons.close, color: Color(0xFF9B9A97)),
          ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    return Flexible(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildRadarPreview(),
            const SizedBox(height: 20),
            _buildDescriptionInput(),
            const SizedBox(height: 20),
            _buildTagsSection(),
          ],
        ),
      ),
    );
  }

  /// 构建雷达图预览
  Widget _buildRadarPreview() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF7F6F3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE3E2E0)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '雷达图预览',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 12),
          Container(
            height: 120,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: const Color(0xFFE3E2E0)),
            ),
            child: const Center(
              child: Text(
                '雷达图预览区域\n(实际分享时会包含完整图表)',
                textAlign: TextAlign.center,
                style: TextStyle(color: Color(0xFF9B9A97), fontSize: 12),
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '综合评分：${widget.data.formattedOverallScore}',
            style: const TextStyle(fontSize: 12, color: Color(0xFF787774)),
          ),
        ],
      ),
    );
  }

  /// 构建描述输入框
  Widget _buildDescriptionInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '分享描述',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: const Color(0xFFE3E2E0)),
          ),
          child: TextField(
            controller: _descriptionController,
            maxLines: 6,
            decoration: const InputDecoration(
              hintText: '分享你的能力分析心得...',
              hintStyle: TextStyle(color: Color(0xFF9B9A97)),
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(12),
            ),
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF37352F),
              height: 1.4,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建标签选择区域
  Widget _buildTagsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '选择标签',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _availableTags.map((tag) {
            final isSelected = _selectedTags.contains(tag);
            return FilterChip(
              label: Text(tag),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    if (!_selectedTags.contains(tag)) {
                      _selectedTags.add(tag);
                    }
                  } else {
                    _selectedTags.remove(tag);
                  }
                });
              },
              backgroundColor: Colors.white,
              selectedColor: const Color(0xFF2E7EED).withValues(alpha: 0.1),
              checkmarkColor: const Color(0xFF2E7EED),
              labelStyle: TextStyle(
                color: isSelected
                    ? const Color(0xFF2E7EED)
                    : const Color(0xFF787774),
                fontSize: 12,
              ),
              side: BorderSide(
                color: isSelected
                    ? const Color(0xFF2E7EED)
                    : const Color(0xFFE3E2E0),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        border: Border(top: BorderSide(color: Color(0xFFE3E2E0))),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextButton(
              onPressed: _isSharing
                  ? null
                  : () => Navigator.of(context).pop(false),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                backgroundColor: const Color(0xFFF7F6F3),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                '取消',
                style: TextStyle(
                  color: Color(0xFF37352F),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: _isSharing ? null : _shareToCommuity,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                backgroundColor: const Color(0xFF2E7EED),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
              child: _isSharing
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      '分享',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  /// 分享到社区
  Future<void> _shareToCommuity() async {
    if (_isSharing) return;

    setState(() {
      _isSharing = true;
    });

    try {
      // 1. 生成雷达图图片
      print('📸 开始截取雷达图...');
      final imageBytes = await RadarShareService.captureRadarChart(
        widget.radarChartKey,
      );
      String? imagePath;

      if (imageBytes != null) {
        print('✅ 雷达图截取成功，大小: ${imageBytes.length} bytes');
        // 保存图片到永久目录（社区专用）
        imagePath = await RadarShareService.saveImageToCommunity(
          imageBytes,
          'radar_chart_community',
        );
        if (imagePath != null) {
          print('💾 雷达图已保存到: $imagePath');
        }
      } else {
        print('❌ 雷达图截取失败，将创建纯文本帖子');
      }

      // 2. 创建社区帖子
      await _createCommunityPost(imagePath);

      // 3. 显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('雷达图已成功分享到社区！'),
            backgroundColor: Color(0xFF0F7B6C),
            duration: Duration(seconds: 2),
          ),
        );
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      print('❌ 分享到社区失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('分享失败，请稍后重试'),
            backgroundColor: Color(0xFFE03E3E),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSharing = false;
        });
      }
    }
  }

  /// 创建社区帖子
  Future<void> _createCommunityPost(String? imagePath) async {
    // 获取下一个帖子ID
    final postId = await _storageService.getNextPostId();

    // 创建用户信息（实际应用中应该从用户状态获取）
    final currentUser = UserInfo(
      id: 1,
      username: '我',
      avatar: '',
      isVerified: false,
    );

    // 构建帖子内容
    final content = _descriptionController.text.trim();

    // 创建新帖子
    final newPost = CommunityPost(
      id: postId,
      author: currentUser,
      content: content,
      type: PostType.achievement, // 雷达图分享属于成就类型
      tags: List.from(_selectedTags),
      images: imagePath != null ? [imagePath] : [], // 包含雷达图图片
      likeCount: 0,
      commentCount: 0,
      shareCount: 0,
      createdAt: DateTime.now(),
      isLiked: false,
      isPremium: false, // 用户分享的内容默认不是优质文章
    );

    print('📝 创建社区帖子:');
    print('   ID: ${newPost.id}');
    print('   内容长度: ${content.length}');
    print('   标签: ${newPost.tags}');
    print('   图片: ${newPost.images}');

    // 保存到本地存储
    final saveSuccess = await _storageService.addPost(newPost);
    if (!saveSuccess) {
      throw Exception('保存帖子失败');
    }

    print('✅ 雷达图已成功分享到社区: ${newPost.id}');
  }
}
