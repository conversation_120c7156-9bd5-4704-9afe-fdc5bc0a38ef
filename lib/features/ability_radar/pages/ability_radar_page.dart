import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../models/ability_radar_models.dart';
import '../widgets/ability_radar_chart.dart';
import '../widgets/radar_share_panel.dart';
import '../widgets/game_style_rank_widget.dart';
import '../providers/ability_radar_mock_providers.dart';

/// 能力雷达图主页面
///
/// 提供完整的个人能力分析功能，包括：
/// 1. 五维雷达图展示
/// 2. 详细能力分析
/// 3. 改进建议和功能引导
/// 4. 时间范围选择
/// 5. 趋势分析
class AbilityRadarPage extends ConsumerStatefulWidget {
  const AbilityRadarPage({super.key});

  @override
  ConsumerState<AbilityRadarPage> createState() => _AbilityRadarPageState();
}

class _AbilityRadarPageState extends ConsumerState<AbilityRadarPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  RadarTimeRange _selectedTimeRange = RadarTimeRange.thisMonth;

  // 雷达图截图用的GlobalKey
  final GlobalKey _radarChartKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // 初始化时生成雷达图数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _generateRadarData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(Icons.arrow_back, color: Color(0xFF37352F)),
        ),
        title: const Text(
          '能力雷达图',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          // 时间范围选择
          PopupMenuButton<RadarTimeRange>(
            icon: const Icon(Icons.date_range, color: Color(0xFF37352F)),
            onSelected: _onTimeRangeChanged,
            itemBuilder: (context) => RadarTimeRange.values.map((range) {
              return PopupMenuItem(
                value: range,
                child: Row(
                  children: [
                    Icon(
                      _selectedTimeRange == range
                          ? Icons.radio_button_checked
                          : Icons.radio_button_unchecked,
                      color: const Color(0xFF2E7EED),
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(range.name),
                  ],
                ),
              );
            }).toList(),
          ),

          // 分享按钮
          IconButton(
            onPressed: _showSharePanel,
            icon: const Icon(Icons.share_outlined, color: Color(0xFF37352F)),
            tooltip: '分享雷达图',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: const Color(0xFF2E7EED),
          unselectedLabelColor: const Color(0xFF9B9A97),
          indicatorColor: const Color(0xFF2E7EED),
          tabs: const [
            Tab(text: '雷达图'),
            Tab(text: '详细分析'),
            Tab(text: '趋势变化'),
          ],
        ),
      ),
      body: Consumer(
        builder: (context, ref, child) {
          final mockData = ref.watch(mockRadarDataProvider);
          return TabBarView(
            controller: _tabController,
            children: [
              _buildRadarTab(mockData),
              _buildAnalysisTab(mockData),
              _buildTrendTab(mockData),
            ],
          );
        },
      ),
    );
  }

  /// 构建雷达图标签页
  Widget _buildRadarTab(AbilityRadarData data) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 综合评分卡片
          _buildOverallScoreCard(data),

          const SizedBox(height: 16),

          // 雷达图卡片
          _buildRadarChartCard(data),

          const SizedBox(height: 16),

          // 能力图例
          _buildRadarLegend(data),
        ],
      ),
    );
  }

  /// 构建详细分析标签页
  Widget _buildAnalysisTab(AbilityRadarData data) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 等级评价系统说明
          _buildRankSystemCard(data),

          const SizedBox(height: 16),

          // 能力平衡度分析
          _buildBalanceAnalysisCard(),

          const SizedBox(height: 16),

          // 各维度详细分析
          ..._buildDimensionAnalysisCards(),
        ],
      ),
    );
  }

  /// 构建趋势变化标签页
  Widget _buildTrendTab(AbilityRadarData data) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 趋势图表
          _buildTrendChartCard(),

          const SizedBox(height: 16),

          // 成长建议
          _buildGrowthAdviceCard(),
        ],
      ),
    );
  }

  /// 构建综合评分卡片
  Widget _buildOverallScoreCard(AbilityRadarData data) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
      ),
      child: Column(
        children: [
          const Text(
            '综合能力评分',
            style: TextStyle(
              color: Color(0xFF37352F),
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // 分数显示
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                data.formattedOverallScore.replaceAll('分', ''),
                style: const TextStyle(
                  color: Color(0xFF2E7EED),
                  fontSize: 48,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Text(
                '分',
                style: TextStyle(
                  color: Color(0xFF37352F),
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // 游戏风格等级显示
          GameStyleRankWidget(
            score: data.overallScore,
            showAnimation: true,
            showProgress: true,
            fontSize: 18,
            showDescription: true,
          ),

          const SizedBox(height: 16),

          // 等级和平衡度
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildScoreMetric(
                data.overallLevel.icon,
                data.overallLevel.name,
                '能力等级',
              ),
              Container(width: 1, height: 40, color: const Color(0xFFE3E2E0)),
              _buildScoreMetric('⚖️', data.balanceDescription, '能力平衡'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildScoreMetric(String icon, String value, String label) {
    return Column(
      children: [
        Text(icon, style: const TextStyle(fontSize: 20)),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Color(0xFF37352F),
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          label,
          style: const TextStyle(color: Color(0xFF9B9A97), fontSize: 12),
        ),
      ],
    );
  }

  /// 构建雷达图卡片
  Widget _buildRadarChartCard(AbilityRadarData data) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
      ),
      child: Column(
        children: [
          const Text(
            '五维能力雷达图',
            style: TextStyle(
              color: Color(0xFF37352F),
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 32),

          // 雷达图 - 添加RepaintBoundary用于截图
          RepaintBoundary(
            key: _radarChartKey,
            child: AbilityRadarChart(
              data: data,
              size: 280,
              onDimensionTap: _onDimensionTap,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建雷达图图例
  Widget _buildRadarLegend(AbilityRadarData data) {
    return AbilityRadarLegend(data: data, onDimensionTap: _onDimensionTap);
  }

  /// 构建等级评价系统说明卡片
  Widget _buildRankSystemCard(AbilityRadarData data) {
    final currentRank = GameStyleRank.fromScore(data.overallScore);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          const Text(
            '等级评价系统',
            style: TextStyle(
              color: Color(0xFF37352F),
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 4),

          const Text(
            '参考游戏战绩评价体系，科学评估您的能力水平',
            style: TextStyle(
              color: Color(0xFF9B9A97),
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
          ),

          const SizedBox(height: 16),

          // 当前等级显示
          Row(
            children: [
              const Text(
                '当前等级：',
                style: TextStyle(
                  color: Color(0xFF37352F),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 8),
              GameStyleRankWidget(
                score: data.overallScore,
                showAnimation: false,
                fontSize: 16,
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 等级说明
          Column(
            children: GameStyleRank.values.map((rank) {
              final isCurrentRank = rank == currentRank;
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isCurrentRank
                      ? rank.color.withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  border: isCurrentRank
                      ? Border.all(color: rank.color.withValues(alpha: 0.3))
                      : null,
                ),
                child: Row(
                  children: [
                    // 等级标识
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: rank.color,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        rank.displayName,
                        style: TextStyle(
                          color: rank == GameStyleRank.sss
                              ? const Color(0xFF37352F)
                              : Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // 分数范围和描述
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${rank.minScore}-${rank.maxScore}分',
                            style: const TextStyle(
                              color: Color(0xFF37352F),
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            rank.description,
                            style: const TextStyle(
                              color: Color(0xFF9B9A97),
                              fontSize: 11,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // 当前等级标识
                    if (isCurrentRank)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: rank.color,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '当前',
                          style: TextStyle(
                            color: rank == GameStyleRank.sss
                                ? const Color(0xFF37352F)
                                : Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
              );
            }).toList(),
          ),

          // 升级提示
          if (currentRank != GameStyleRank.sss) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFF7F6F3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.trending_up,
                    color: currentRank.nextRank?.color ?? Colors.grey,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '距离${currentRank.nextRank?.displayName}级还需${currentRank.getScoreToNextRank(data.overallScore)}分',
                      style: const TextStyle(
                        color: Color(0xFF37352F),
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建平衡度分析卡片
  Widget _buildBalanceAnalysisCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '能力平衡度分析',
            style: TextStyle(
              color: Color(0xFF37352F),
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // 最强和最弱能力
          Row(
            children: [
              Expanded(
                child: _buildStrengthWeaknessCard(
                  '💪 最强能力',
                  '专注力',
                  '85.2分',
                  const Color(0xFF0F7B6C),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStrengthWeaknessCard(
                  '📈 待提升',
                  '运动能力',
                  '58.7分',
                  const Color(0xFFE03E3E),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStrengthWeaknessCard(
    String title,
    String ability,
    String score,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Color(0xFF9B9A97),
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            ability,
            style: const TextStyle(
              color: Color(0xFF37352F),
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            score,
            style: TextStyle(
              color: color,
              fontSize: 13,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建维度分析卡片列表
  List<Widget> _buildDimensionAnalysisCards() {
    // 暂时返回空列表，实际应该根据数据生成
    return [];
  }

  /// 构建趋势图表卡片
  Widget _buildTrendChartCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
      ),
      child: const Column(
        children: [
          Text(
            '能力发展趋势',
            style: TextStyle(
              color: Color(0xFF37352F),
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 20),

          // 趋势图占位符
          SizedBox(
            height: 200,
            child: Center(
              child: Text(
                '趋势图表开发中...',
                style: TextStyle(color: Color(0xFF9B9A97), fontSize: 14),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建成长建议卡片
  Widget _buildGrowthAdviceCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '个性化成长建议',
            style: TextStyle(
              color: Color(0xFF37352F),
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),

          // 建议列表占位符
          Text(
            '基于您的能力分析，我们将为您提供个性化的成长建议...',
            style: TextStyle(color: Color(0xFF9B9A97), fontSize: 14),
          ),
        ],
      ),
    );
  }

  // 事件处理方法
  void _onTimeRangeChanged(RadarTimeRange range) {
    setState(() {
      _selectedTimeRange = range;
    });
    _generateRadarData();
  }

  void _onDimensionTap(AbilityDimension dimension) {
    // 可以导航到详细分析页面或显示详情对话框
    _showDimensionDetail(dimension);
  }

  void _generateRadarData() {
    // 这里应该调用AbilityRadarService生成数据
    // 暂时使用模拟数据
  }

  /// 显示分享面板
  void _showSharePanel() {
    final mockData = ref.read(mockRadarDataProvider);
    RadarSharePanel.show(
      context: context,
      radarChartKey: _radarChartKey,
      data: mockData,
    );
  }

  void _showDimensionDetail(AbilityDimension dimension) {
    // 显示维度详情对话框
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${dimension.icon} ${dimension.nameCn}'),
        content: const Text('详细分析功能开发中...'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
