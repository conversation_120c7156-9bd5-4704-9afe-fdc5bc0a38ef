import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'ability_radar_models.g.dart';

/// 能力维度枚举
enum AbilityDimension {
  timeManagement('时间管理', 'Time Management', '⏰'),
  memoryCapacity('记忆能力', 'Memory Capacity', '🧠'),
  focusPower('专注力', 'Focus Power', '🎯'),
  motorSkills('运动能力', 'Motor Skills', '💪'),
  creativity('创造力', 'Creativity', '🎨');

  const AbilityDimension(this.nameCn, this.nameEn, this.icon);

  final String nameCn;
  final String nameEn;
  final String icon;
}

/// 能力等级枚举
enum AbilityLevel {
  beginner('初级', 0, 25, '🌱'),
  intermediate('中级', 26, 50, '🌿'),
  advanced('高级', 51, 75, '🌳'),
  expert('专家', 76, 100, '🏆');

  const AbilityLevel(this.name, this.minScore, this.maxScore, this.icon);

  final String name;
  final int minScore;
  final int maxScore;
  final String icon;

  static AbilityLevel fromScore(double score) {
    final intScore = score.round();
    for (final level in AbilityLevel.values) {
      if (intScore >= level.minScore && intScore <= level.maxScore) {
        return level;
      }
    }
    return AbilityLevel.beginner;
  }
}

/// 游戏风格等级评价系统（参考和平精英）
enum GameStyleRank {
  b('B', 0, 59, Color(0xFF9B9A97), '需要努力'),
  a('A', 60, 74, Color(0xFF2E7EED), '良好水平'),
  s('S', 75, 84, Color(0xFF7C3AED), '优秀表现'),
  ss('SS', 85, 94, Color(0xFFE03E3E), '卓越成就'),
  sss('SSS', 95, 100, Color(0xFFFFD700), '传奇大师');

  const GameStyleRank(
    this.displayName,
    this.minScore,
    this.maxScore,
    this.color,
    this.description,
  );

  final String displayName;
  final int minScore;
  final int maxScore;
  final Color color;
  final String description;

  /// 根据分数获取对应等级
  static GameStyleRank fromScore(double score) {
    final intScore = score.round();
    for (final rank in GameStyleRank.values) {
      if (intScore >= rank.minScore && intScore <= rank.maxScore) {
        return rank;
      }
    }
    return GameStyleRank.b;
  }

  /// 获取等级进度百分比（在当前等级内的进度）
  double getProgressInRank(double score) {
    if (score < minScore) return 0.0;
    if (score > maxScore) return 1.0;

    final rangeSize = maxScore - minScore + 1;
    final progressInRange = score - minScore;
    return progressInRange / rangeSize;
  }

  /// 获取下一等级
  GameStyleRank? get nextRank {
    final currentIndex = GameStyleRank.values.indexOf(this);
    if (currentIndex < GameStyleRank.values.length - 1) {
      return GameStyleRank.values[currentIndex + 1];
    }
    return null; // 已经是最高等级
  }

  /// 获取升级所需分数
  int? getScoreToNextRank(double currentScore) {
    if (this == GameStyleRank.sss) return null; // 已经是最高等级

    final nextRankMinScore = nextRank?.minScore;
    if (nextRankMinScore != null) {
      return (nextRankMinScore - currentScore)
          .ceil()
          .clamp(0, double.infinity)
          .toInt();
    }
    return null;
  }
}

/// 时间范围枚举
enum RadarTimeRange {
  thisWeek('本周', 7),
  thisMonth('本月', 30),
  thisQuarter('本季度', 90);

  const RadarTimeRange(this.name, this.days);

  final String name;
  final int days;
}

/// 单个能力维度的详细数据
@JsonSerializable()
class AbilityDimensionData {
  /// 能力维度
  final AbilityDimension dimension;

  /// 总分 (0-100)
  final double score;

  /// 能力等级
  final AbilityLevel level;

  /// 子指标分数
  final Map<String, double> subScores;

  /// 改进建议
  final List<String> improvements;

  /// 相关功能引导
  final List<String> featureGuides;

  /// 数据更新时间
  final DateTime lastUpdated;

  const AbilityDimensionData({
    required this.dimension,
    required this.score,
    required this.level,
    required this.subScores,
    required this.improvements,
    required this.featureGuides,
    required this.lastUpdated,
  });

  factory AbilityDimensionData.fromJson(Map<String, dynamic> json) =>
      _$AbilityDimensionDataFromJson(json);

  Map<String, dynamic> toJson() => _$AbilityDimensionDataToJson(this);

  /// 获取格式化的分数文本
  String get formattedScore => '${score.toStringAsFixed(1)}分';

  /// 获取等级颜色
  Color get levelColor {
    switch (level) {
      case AbilityLevel.beginner:
        return const Color(0xFF9B9A97);
      case AbilityLevel.intermediate:
        return const Color(0xFFD9730D);
      case AbilityLevel.advanced:
        return const Color(0xFF0F7B6C);
      case AbilityLevel.expert:
        return const Color(0xFFE03E3E);
    }
  }

  /// 获取雷达图颜色
  Color get radarColor {
    switch (dimension) {
      case AbilityDimension.timeManagement:
        return const Color(0xFF2E7EED);
      case AbilityDimension.memoryCapacity:
        return const Color(0xFF7C3AED);
      case AbilityDimension.focusPower:
        return const Color(0xFF0F7B6C);
      case AbilityDimension.motorSkills:
        return const Color(0xFFE03E3E);
      case AbilityDimension.creativity:
        return const Color(0xFFFFD700);
    }
  }
}

/// 能力雷达图完整数据
@JsonSerializable()
class AbilityRadarData {
  /// 时间范围
  final RadarTimeRange timeRange;

  /// 开始日期
  final DateTime startDate;

  /// 结束日期
  final DateTime endDate;

  /// 各维度能力数据
  final List<AbilityDimensionData> dimensions;

  /// 综合能力评分 (0-100)
  final double overallScore;

  /// 综合能力等级
  final AbilityLevel overallLevel;

  /// 能力平衡度 (0-1, 越接近1越平衡)
  final double balanceScore;

  /// 成长趋势数据
  final List<AbilityTrendData> trendData;

  /// 数据生成时间
  final DateTime generatedAt;

  const AbilityRadarData({
    required this.timeRange,
    required this.startDate,
    required this.endDate,
    required this.dimensions,
    required this.overallScore,
    required this.overallLevel,
    required this.balanceScore,
    required this.trendData,
    required this.generatedAt,
  });

  factory AbilityRadarData.fromJson(Map<String, dynamic> json) =>
      _$AbilityRadarDataFromJson(json);

  Map<String, dynamic> toJson() => _$AbilityRadarDataToJson(this);

  /// 获取指定维度的数据
  AbilityDimensionData? getDimensionData(AbilityDimension dimension) {
    try {
      return dimensions.firstWhere((d) => d.dimension == dimension);
    } catch (e) {
      return null;
    }
  }

  /// 获取最强能力
  AbilityDimensionData get strongestAbility {
    return dimensions.reduce((a, b) => a.score > b.score ? a : b);
  }

  /// 获取最弱能力
  AbilityDimensionData get weakestAbility {
    return dimensions.reduce((a, b) => a.score < b.score ? a : b);
  }

  /// 获取格式化的综合评分
  String get formattedOverallScore => '${overallScore.toStringAsFixed(1)}分';

  /// 获取平衡度描述
  String get balanceDescription {
    if (balanceScore >= 0.8) return '非常均衡';
    if (balanceScore >= 0.6) return '较为均衡';
    if (balanceScore >= 0.4) return '一般均衡';
    return '发展不均';
  }
}

/// 能力趋势数据
@JsonSerializable()
class AbilityTrendData {
  /// 日期
  final DateTime date;

  /// 各维度分数
  final Map<AbilityDimension, double> scores;

  /// 综合分数
  final double overallScore;

  const AbilityTrendData({
    required this.date,
    required this.scores,
    required this.overallScore,
  });

  factory AbilityTrendData.fromJson(Map<String, dynamic> json) =>
      _$AbilityTrendDataFromJson(json);

  Map<String, dynamic> toJson() => _$AbilityTrendDataToJson(this);
}

/// 能力分析建议
@JsonSerializable()
class AbilityAnalysisAdvice {
  /// 建议类型
  final String type;

  /// 建议标题
  final String title;

  /// 建议内容
  final String content;

  /// 相关功能
  final String? relatedFeature;

  /// 优先级 (1-5, 5最高)
  final int priority;

  const AbilityAnalysisAdvice({
    required this.type,
    required this.title,
    required this.content,
    this.relatedFeature,
    required this.priority,
  });

  factory AbilityAnalysisAdvice.fromJson(Map<String, dynamic> json) =>
      _$AbilityAnalysisAdviceFromJson(json);

  Map<String, dynamic> toJson() => _$AbilityAnalysisAdviceToJson(this);
}
