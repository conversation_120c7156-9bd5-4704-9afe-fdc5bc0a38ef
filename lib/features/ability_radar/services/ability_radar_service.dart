import 'package:flutter/foundation.dart';
import 'dart:math' as math;
import '../models/ability_radar_models.dart';
import '../../study_time/services/study_time_statistics_service.dart';
import '../../exercise/pao_integration_service.dart';
import '../../exercise/custom_action_library_service.dart';

/// 能力雷达图计算服务
///
/// 负责计算和生成个人能力雷达图数据，包括：
/// 1. 从各个服务收集原始数据
/// 2. 计算五大能力维度的评分
/// 3. 生成雷达图数据和趋势分析
/// 4. 提供能力分析和改进建议
class AbilityRadarService extends ChangeNotifier {
  final StudyTimeStatisticsService _studyTimeService;
  final PAOIntegrationService? _paoService;
  final CustomActionLibraryService? _customLibraryService;

  // 缓存的雷达图数据
  AbilityRadarData? _currentRadarData;

  // 加载状态
  bool _isLoading = false;
  String? _error;

  AbilityRadarService(
    this._studyTimeService, [
    this._paoService,
    this._customLibraryService,
  ]);

  /// 获取当前雷达图数据
  AbilityRadarData? get currentRadarData => _currentRadarData;

  /// 获取加载状态
  bool get isLoading => _isLoading;

  /// 获取错误信息
  String? get error => _error;

  /// 生成能力雷达图数据
  Future<AbilityRadarData> generateRadarData({
    RadarTimeRange timeRange = RadarTimeRange.thisMonth,
    DateTime? customStartDate,
    DateTime? customEndDate,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final dateRange = _calculateDateRange(
        timeRange,
        customStartDate,
        customEndDate,
      );
      final startDate = dateRange['start']!;
      final endDate = dateRange['end']!;

      // 并行收集各维度数据
      final futures = await Future.wait([
        _calculateTimeManagementScore(startDate, endDate),
        _calculateMemoryCapacityScore(startDate, endDate),
        _calculateFocusPowerScore(startDate, endDate),
        _calculateMotorSkillsScore(startDate, endDate),
        _calculateCreativityScore(startDate, endDate),
      ]);

      final dimensions = futures;

      // 计算综合评分和平衡度
      final overallScore = _calculateOverallScore(dimensions);
      final overallLevel = AbilityLevel.fromScore(overallScore);
      final balanceScore = _calculateBalanceScore(dimensions);

      // 生成趋势数据
      final trendData = await _generateTrendData(startDate, endDate);

      _currentRadarData = AbilityRadarData(
        timeRange: timeRange,
        startDate: startDate,
        endDate: endDate,
        dimensions: dimensions,
        overallScore: overallScore,
        overallLevel: overallLevel,
        balanceScore: balanceScore,
        trendData: trendData,
        generatedAt: DateTime.now(),
      );

      notifyListeners();
      return _currentRadarData!;
    } catch (e) {
      _setError('生成能力雷达图失败: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// 计算时间管理能力评分
  Future<AbilityDimensionData> _calculateTimeManagementScore(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final aggregation = _studyTimeService.currentAggregation;
    if (aggregation == null) {
      return _createDefaultDimensionData(AbilityDimension.timeManagement);
    }

    // 任务完成率 (40%)
    final completionRate = aggregation.todayCompletedTasks > 0
        ? math.min(1.0, aggregation.todayCompletedTasks / 8.0)
        : 0.0; // 假设每天8个任务为满分

    // 时间预估准确性 (30%) - 基于学习ROI
    final timeAccuracy = math.min(1.0, aggregation.todayLearningROI / 100.0);

    // 并行时间利用率 (20%)
    final parallelUtilization = aggregation.todayMinutes > 0
        ? math.min(
            1.0,
            aggregation.todayParallelTimeMinutes / aggregation.todayMinutes,
          )
        : 0.0;

    // 学习连续性 (10%)
    final continuity = math.min(1.0, aggregation.streakDays / 30.0); // 30天为满分

    final subScores = {
      '任务完成率': completionRate * 100,
      '时间预估准确性': timeAccuracy * 100,
      '并行时间利用率': parallelUtilization * 100,
      '学习连续性': continuity * 100,
    };

    final totalScore =
        (completionRate * 0.4 +
            timeAccuracy * 0.3 +
            parallelUtilization * 0.2 +
            continuity * 0.1) *
        100;

    return AbilityDimensionData(
      dimension: AbilityDimension.timeManagement,
      score: totalScore,
      level: AbilityLevel.fromScore(totalScore),
      subScores: subScores,
      improvements: _getTimeManagementImprovements(totalScore, subScores),
      featureGuides: _getTimeManagementFeatureGuides(subScores),
      lastUpdated: DateTime.now(),
    );
  }

  /// 计算记忆能力评分
  Future<AbilityDimensionData> _calculateMemoryCapacityScore(
    DateTime startDate,
    DateTime endDate,
  ) async {
    // 这里需要集成FSRS服务和词汇管理数据
    // 暂时使用模拟数据
    final subScores = {
      '词汇掌握率': 75.0,
      '记忆保持率': 82.0,
      '动觉记忆质量': 68.0,
      '复习效率': 71.0,
    };

    final totalScore =
        (subScores['词汇掌握率']! * 0.35 +
        subScores['记忆保持率']! * 0.25 +
        subScores['动觉记忆质量']! * 0.25 +
        subScores['复习效率']! * 0.15);

    return AbilityDimensionData(
      dimension: AbilityDimension.memoryCapacity,
      score: totalScore,
      level: AbilityLevel.fromScore(totalScore),
      subScores: subScores,
      improvements: _getMemoryCapacityImprovements(totalScore, subScores),
      featureGuides: _getMemoryCapacityFeatureGuides(subScores),
      lastUpdated: DateTime.now(),
    );
  }

  /// 计算专注力评分
  Future<AbilityDimensionData> _calculateFocusPowerScore(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final aggregation = _studyTimeService.currentAggregation;
    if (aggregation == null) {
      return _createDefaultDimensionData(AbilityDimension.focusPower);
    }

    // 专注信噪比 (40%)
    final focusRatio = aggregation.todayFocusSignalToNoiseRatio / 100.0;

    // 深度工作时长 (30%) - 基于今日学习时长
    final deepWorkScore = math.min(
      1.0,
      aggregation.todayMinutes / 480.0,
    ); // 8小时为满分

    // 任务切换频率 (20%) - 基于完成任务数推算
    final taskSwitchScore = aggregation.todayCompletedTasks > 0
        ? math.max(0.0, 1.0 - (aggregation.todayCompletedTasks - 1) / 10.0)
        : 1.0;

    // 学习效率等级 (10%)
    final efficiencyScore =
        _parseEfficiencyLevel(aggregation.todayFocusRatingLevel) / 5.0; // 5级为满分

    final subScores = {
      '专注信噪比': focusRatio * 100,
      '深度工作时长': deepWorkScore * 100,
      '任务切换频率': taskSwitchScore * 100,
      '学习效率等级': efficiencyScore * 100,
    };

    final totalScore =
        (focusRatio * 0.4 +
            deepWorkScore * 0.3 +
            taskSwitchScore * 0.2 +
            efficiencyScore * 0.1) *
        100;

    return AbilityDimensionData(
      dimension: AbilityDimension.focusPower,
      score: totalScore,
      level: AbilityLevel.fromScore(totalScore),
      subScores: subScores,
      improvements: _getFocusPowerImprovements(totalScore, subScores),
      featureGuides: _getFocusPowerFeatureGuides(subScores),
      lastUpdated: DateTime.now(),
    );
  }

  /// 计算运动能力评分
  Future<AbilityDimensionData> _calculateMotorSkillsScore(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final stats = _paoService?.getLibraryStatistics() ?? {};

    // 基于PAO动作库统计数据计算
    final totalExercises = stats['totalExercises'] as int? ?? 0;
    final categoryStats = stats['categoryStats'] as Map<String, int>? ?? {};

    final subScores = {
      '动作完成质量': 78.0, // 需要从实际训练数据获取
      '协调性评分': 72.0,
      '训练频率': math.min(100.0, totalExercises * 2.0),
      '动作多样性': math.min(100.0, categoryStats.length * 20.0),
    };

    final totalScore =
        (subScores['动作完成质量']! * 0.4 +
        subScores['协调性评分']! * 0.3 +
        subScores['训练频率']! * 0.2 +
        subScores['动作多样性']! * 0.1);

    return AbilityDimensionData(
      dimension: AbilityDimension.motorSkills,
      score: totalScore,
      level: AbilityLevel.fromScore(totalScore),
      subScores: subScores,
      improvements: _getMotorSkillsImprovements(totalScore, subScores),
      featureGuides: _getMotorSkillsFeatureGuides(subScores),
      lastUpdated: DateTime.now(),
    );
  }

  /// 计算创造力评分
  Future<AbilityDimensionData> _calculateCreativityScore(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final customLibraryStats = _customLibraryService?.getStatistics() ?? {};

    final subScores = {
      '学习方法多样性': 85.0, // 基于使用的功能模块数量
      '自定义内容创建': math.min(
        100.0,
        (customLibraryStats['totalLibraries'] as int? ?? 0) * 25.0,
      ),
      '学科交叉学习': 76.0, // 基于学科分布均匀度
      '创新使用模式': 69.0, // 基于非标准功能使用频率
    };

    final totalScore =
        (subScores['学习方法多样性']! * 0.35 +
        subScores['自定义内容创建']! * 0.25 +
        subScores['学科交叉学习']! * 0.25 +
        subScores['创新使用模式']! * 0.15);

    return AbilityDimensionData(
      dimension: AbilityDimension.creativity,
      score: totalScore,
      level: AbilityLevel.fromScore(totalScore),
      subScores: subScores,
      improvements: _getCreativityImprovements(totalScore, subScores),
      featureGuides: _getCreativityFeatureGuides(subScores),
      lastUpdated: DateTime.now(),
    );
  }

  /// 计算综合评分
  double _calculateOverallScore(List<AbilityDimensionData> dimensions) {
    if (dimensions.isEmpty) return 0.0;
    return dimensions.fold(0.0, (sum, d) => sum + d.score) / dimensions.length;
  }

  /// 计算能力平衡度
  double _calculateBalanceScore(List<AbilityDimensionData> dimensions) {
    if (dimensions.isEmpty) return 0.0;

    final scores = dimensions.map((d) => d.score).toList();
    final mean = scores.fold(0.0, (sum, score) => sum + score) / scores.length;
    final variance =
        scores.fold(0.0, (sum, score) => sum + math.pow(score - mean, 2)) /
        scores.length;
    final standardDeviation = math.sqrt(variance);

    // 标准差越小，平衡度越高
    return math.max(0.0, 1.0 - standardDeviation / 50.0);
  }

  /// 生成趋势数据
  Future<List<AbilityTrendData>> _generateTrendData(
    DateTime startDate,
    DateTime endDate,
  ) async {
    // 暂时返回模拟数据，实际应该从历史数据计算
    final trendData = <AbilityTrendData>[];
    final days = endDate.difference(startDate).inDays;

    for (int i = 0; i <= math.min(days, 30); i += 7) {
      // 每周一个数据点
      final date = startDate.add(Duration(days: i));
      trendData.add(
        AbilityTrendData(
          date: date,
          scores: {
            AbilityDimension.timeManagement: 65.0 + math.sin(i * 0.1) * 10,
            AbilityDimension.memoryCapacity: 70.0 + math.cos(i * 0.1) * 8,
            AbilityDimension.focusPower: 72.0 + math.sin(i * 0.15) * 12,
            AbilityDimension.motorSkills: 68.0 + math.cos(i * 0.12) * 9,
            AbilityDimension.creativity: 75.0 + math.sin(i * 0.08) * 7,
          },
          overallScore: 70.0 + math.sin(i * 0.1) * 5,
        ),
      );
    }

    return trendData;
  }

  // 辅助方法
  Map<String, DateTime> _calculateDateRange(
    RadarTimeRange timeRange,
    DateTime? customStartDate,
    DateTime? customEndDate,
  ) {
    if (customStartDate != null && customEndDate != null) {
      return {'start': customStartDate, 'end': customEndDate};
    }

    final now = DateTime.now();
    final endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
    final startDate = endDate.subtract(Duration(days: timeRange.days - 1));

    return {'start': startDate, 'end': endDate};
  }

  AbilityDimensionData _createDefaultDimensionData(AbilityDimension dimension) {
    return AbilityDimensionData(
      dimension: dimension,
      score: 0.0,
      level: AbilityLevel.beginner,
      subScores: {},
      improvements: ['暂无数据，请开始使用相关功能'],
      featureGuides: [],
      lastUpdated: DateTime.now(),
    );
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  /// 解析效率等级字符串为数值
  double _parseEfficiencyLevel(String level) {
    switch (level.toLowerCase()) {
      case '极高':
      case 'excellent':
        return 5.0;
      case '高':
      case 'high':
        return 4.0;
      case '中':
      case 'medium':
        return 3.0;
      case '低':
      case 'low':
        return 2.0;
      case '极低':
      case 'very low':
        return 1.0;
      default:
        return 3.0; // 默认中等
    }
  }

  // 时间管理改进建议
  List<String> _getTimeManagementImprovements(
    double score,
    Map<String, double> subScores,
  ) {
    final improvements = <String>[];

    if (score < 50) {
      improvements.add('建议每天制定明确的学习计划，使用TimeBox功能设定具体的学习目标');
    }

    if ((subScores['任务完成率'] ?? 0) < 60) {
      improvements.add('提高任务完成率：将大任务分解为小任务，设定合理的时间预期');
    }

    if ((subScores['时间预估准确性'] ?? 0) < 60) {
      improvements.add('提升时间预估能力：记录实际用时，逐步校准时间感知');
    }

    if ((subScores['并行时间利用率'] ?? 0) < 40) {
      improvements.add('增加并行学习：在休息时间进行轻量级学习活动');
    }

    if ((subScores['学习连续性'] ?? 0) < 70) {
      improvements.add('保持学习连续性：设定每日最低学习时长，培养学习习惯');
    }

    return improvements;
  }

  List<String> _getTimeManagementFeatureGuides(Map<String, double> subScores) {
    final guides = <String>[];

    if ((subScores['任务完成率'] ?? 0) < 60) {
      guides.add('使用TimeBox创建每日学习计划');
    }

    if ((subScores['时间预估准确性'] ?? 0) < 60) {
      guides.add('查看学习报告分析时间使用效率');
    }

    guides.add('使用日历功能查看学习时间分布');

    return guides;
  }

  // 记忆能力改进建议
  List<String> _getMemoryCapacityImprovements(
    double score,
    Map<String, double> subScores,
  ) {
    final improvements = <String>[];

    if (score < 50) {
      improvements.add('加强记忆训练：每天进行动觉记忆练习，结合PAO记忆法');
    }

    if ((subScores['词汇掌握率'] ?? 0) < 60) {
      improvements.add('提高词汇掌握：使用FSRS算法进行科学复习，关注遗忘曲线');
    }

    if ((subScores['动觉记忆质量'] ?? 0) < 60) {
      improvements.add('改善动觉记忆：提高动作完成质量，增强身体协调性');
    }

    if ((subScores['复习效率'] ?? 0) < 70) {
      improvements.add('优化复习策略：根据记忆强度调整复习间隔');
    }

    return improvements;
  }

  List<String> _getMemoryCapacityFeatureGuides(Map<String, double> subScores) {
    final guides = <String>[];

    guides.add('使用记忆宫殿创建视觉记忆场景');
    guides.add('进行动觉记忆训练提升记忆效果');
    guides.add('使用词汇管理功能进行科学复习');

    return guides;
  }

  // 专注力改进建议
  List<String> _getFocusPowerImprovements(
    double score,
    Map<String, double> subScores,
  ) {
    final improvements = <String>[];

    if (score < 50) {
      improvements.add('提升专注力：创造无干扰的学习环境，使用番茄钟技术');
    }

    if ((subScores['专注信噪比'] ?? 0) < 60) {
      improvements.add('减少学习干扰：关闭无关应用，设定专注学习时段');
    }

    if ((subScores['深度工作时长'] ?? 0) < 60) {
      improvements.add('延长深度工作：逐步增加单次学习时长，培养深度专注能力');
    }

    if ((subScores['任务切换频率'] ?? 0) < 70) {
      improvements.add('减少任务切换：合并相似任务，避免频繁切换学习内容');
    }

    return improvements;
  }

  List<String> _getFocusPowerFeatureGuides(Map<String, double> subScores) {
    final guides = <String>[];

    guides.add('使用TimeBox专注计时器');
    guides.add('查看学习报告中的专注度分析');
    guides.add('设置学习提醒和防打扰模式');

    return guides;
  }

  // 运动能力改进建议
  List<String> _getMotorSkillsImprovements(
    double score,
    Map<String, double> subScores,
  ) {
    final improvements = <String>[];

    if (score < 50) {
      improvements.add('加强运动训练：每天进行PAO动作练习，提升身体协调性');
    }

    if ((subScores['动作完成质量'] ?? 0) < 60) {
      improvements.add('提高动作质量：注重动作标准性，逐步提升完成度');
    }

    if ((subScores['训练频率'] ?? 0) < 60) {
      improvements.add('增加训练频率：将运动融入学习休息，形成规律训练');
    }

    if ((subScores['动作多样性'] ?? 0) < 70) {
      improvements.add('丰富动作类型：尝试不同类别的运动，全面发展身体能力');
    }

    return improvements;
  }

  List<String> _getMotorSkillsFeatureGuides(Map<String, double> subScores) {
    final guides = <String>[];

    guides.add('使用动觉记忆训练功能');
    guides.add('创建自定义动作库');
    guides.add('参与PAO记忆法训练');

    return guides;
  }

  // 创造力改进建议
  List<String> _getCreativityImprovements(
    double score,
    Map<String, double> subScores,
  ) {
    final improvements = <String>[];

    if (score < 50) {
      improvements.add('激发创造力：尝试多样化学习方法，探索跨学科知识连接');
    }

    if ((subScores['学习方法多样性'] ?? 0) < 60) {
      improvements.add('丰富学习方法：使用更多应用功能，尝试不同学习策略');
    }

    if ((subScores['自定义内容创建'] ?? 0) < 60) {
      improvements.add('增加创作活动：创建个人记忆宫殿、自定义动作库等');
    }

    if ((subScores['学科交叉学习'] ?? 0) < 70) {
      improvements.add('促进知识迁移：在不同学科间建立联系，培养综合思维');
    }

    return improvements;
  }

  List<String> _getCreativityFeatureGuides(Map<String, double> subScores) {
    final guides = <String>[];

    guides.add('创建个性化记忆宫殿');
    guides.add('设计自定义动作库');
    guides.add('参与学习社区分享创作');
    guides.add('使用优化日志记录创新想法');

    return guides;
  }
}
