import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../shared/widgets/oneday_logo.dart';
import '../../services/first_time_service.dart';

/// 引导页数据模型
class OnboardingPageData {
  final String title;
  final String subtitle;
  final String description;
  final IconData icon;
  final Color iconColor;
  final bool requiresPermission;
  final String? permissionDescription;

  const OnboardingPageData({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.icon,
    required this.iconColor,
    this.requiresPermission = false,
    this.permissionDescription,
  });
}

/// 引导页状态管理
class OnboardingState {
  final int currentPage;
  final bool isLastPage;
  final bool hasRequestedPermissions;
  final Map<String, bool> permissionStatus;

  const OnboardingState({
    this.currentPage = 0,
    this.isLastPage = false,
    this.hasRequestedPermissions = false,
    this.permissionStatus = const {},
  });

  OnboardingState copyWith({
    int? currentPage,
    bool? isLastPage,
    bool? hasRequestedPermissions,
    Map<String, bool>? permissionStatus,
  }) {
    return OnboardingState(
      currentPage: currentPage ?? this.currentPage,
      isLastPage: isLastPage ?? this.isLastPage,
      hasRequestedPermissions:
          hasRequestedPermissions ?? this.hasRequestedPermissions,
      permissionStatus: permissionStatus ?? this.permissionStatus,
    );
  }
}

/// 引导页状态通知器
class OnboardingNotifier extends StateNotifier<OnboardingState> {
  OnboardingNotifier() : super(const OnboardingState());

  void updatePage(int page, int totalPages) {
    state = state.copyWith(
      currentPage: page,
      isLastPage: page == totalPages - 1,
    );
  }

  void updatePermissionStatus(String permission, bool granted) {
    final newStatus = Map<String, bool>.from(state.permissionStatus);
    newStatus[permission] = granted;
    state = state.copyWith(permissionStatus: newStatus);
  }

  void setPermissionsRequested() {
    state = state.copyWith(hasRequestedPermissions: true);
  }
}

/// 引导页状态提供器
final onboardingProvider =
    StateNotifierProvider<OnboardingNotifier, OnboardingState>(
      (ref) => OnboardingNotifier(),
    );

/// 引导页主页面
///
/// 设计风格：Notion风格极简设计
/// - 卡片式布局
/// - 柔和的色彩系统
/// - 清晰的信息层级
/// - 优雅的交互反馈
class OnboardingPage extends ConsumerStatefulWidget {
  const OnboardingPage({super.key});

  @override
  ConsumerState<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends ConsumerState<OnboardingPage>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _fadeController;

  // 开发者入口相关变量
  int _logoTapCount = 0;
  bool _showDeveloperPanel = false;
  DateTime? _lastTapTime;

  // 引导页数据 - 简化的功能介绍
  static const List<OnboardingPageData> _pages = [
    OnboardingPageData(
      title: '时间盒子',
      subtitle: '专注每一刻',
      description: '运用时间盒子法，为学习任务设定明确时间。利用deadline效应提升专注度，让每分钟都有价值。',
      icon: Icons.schedule_rounded,
      iconColor: Color(0xFF2E7EED),
    ),
    OnboardingPageData(
      title: '记忆宫殿',
      subtitle: '高效记忆法',
      description: '上传日常照片创建记忆地点，在图片上添加记忆锚点。将抽象知识与具体场景关联，记忆效率大幅提升。',
      icon: Icons.account_balance_rounded,
      iconColor: Color(0xFF7C3AED),
    ),
    OnboardingPageData(
      title: '游戏化学习',
      subtitle: '学习即奖励',
      description: '完成任务获得虚拟工资，用于购买道具、解锁功能。让学习过程充满成就感和持续动力。',
      icon: Icons.emoji_events_rounded,
      iconColor: Color(0xFF0F7B6C),
    ),
    OnboardingPageData(
      title: '健康管理',
      subtitle: '学习与运动并行',
      description: '任务间隙智能推荐运动，将单词拆解为动作组合。在锻炼身体的同时强化记忆，学习健康两不误。',
      icon: Icons.favorite_rounded,
      iconColor: Color(0xFFD9730D),
    ),
    OnboardingPageData(
      title: '开始使用',
      subtitle: '开启高效学习之旅',
      description: '准备好体验全新的学习方式了吗？让我们一起度过每一个充实的OneDay！',
      icon: Icons.rocket_launch_rounded,
      iconColor: Color(0xFF2E7EED),
      requiresPermission: true,
      permissionDescription: '为了更好的使用体验，我们需要通知和存储权限来保存您的学习数据和及时提醒。',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeController.forward();
    _setupSystemUI();
  }

  /// 设置系统UI样式
  void _setupSystemUI() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  /// 跳转到下一页
  void _nextPage() {
    final currentPage = ref.read(onboardingProvider).currentPage;
    if (currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 250),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  /// 跳过引导
  void _skipOnboarding() {
    _pageController.animateToPage(
      _pages.length - 1,
      duration: const Duration(milliseconds: 350),
      curve: Curves.easeInOut,
    );
  }

  /// 完成引导
  void _completeOnboarding() async {
    await FirstTimeService.instance.markOnboardingCompleted();
    if (mounted) {
      context.go('/home');
    }
  }

  /// 导航到指定页面（Web 平台专用）
  void _navigateToPage(int targetPage) {
    if (!kIsWeb) return; // 仅在 Web 平台启用

    final currentPage = ref.read(onboardingProvider).currentPage;
    if (targetPage == currentPage) return; // 避免重复导航

    // 使用平滑动画跳转到目标页面
    _pageController.animateToPage(
      targetPage,
      duration: const Duration(milliseconds: 350),
      curve: Curves.easeInOut,
    );
  }

  /// 请求权限
  Future<void> _requestPermissions() async {
    ref.read(onboardingProvider.notifier).setPermissionsRequested();

    await Future.delayed(const Duration(milliseconds: 800));

    ref
        .read(onboardingProvider.notifier)
        .updatePermissionStatus('notification', true);
    ref
        .read(onboardingProvider.notifier)
        .updatePermissionStatus('storage', true);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('权限设置完成'),
          backgroundColor: const Color(0xFF0F7B6C),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(onboardingProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            // 主要内容
            Column(
              children: [
                // 顶部导航栏
                _buildTopBar(onboardingState),

                // 页面指示器
                _buildPageIndicator(onboardingState),

                // 页面内容
                Expanded(
                  child: PageView.builder(
                    controller: _pageController,
                    itemCount: _pages.length,
                    onPageChanged: (index) {
                      ref
                          .read(onboardingProvider.notifier)
                          .updatePage(index, _pages.length);
                    },
                    itemBuilder: (context, index) {
                      return _buildPageContent(_pages[index], index);
                    },
                  ),
                ),

                // 底部按钮
                _buildBottomButtons(onboardingState),
              ],
            ),

            // 开发者面板（仅在Debug模式下显示）
            if (kDebugMode && _showDeveloperPanel) _buildDeveloperPanel(),
          ],
        ),
      ),
    );
  }

  /// 构建顶部导航栏 - 简洁设计
  Widget _buildTopBar(OnboardingState state) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Logo with developer entrance (only in debug mode)
          GestureDetector(
            onTap: kDebugMode ? _onLogoTap : null,
            child: Row(
              children: [
                const OneDayLogoSimple(size: 28),
                const SizedBox(width: 8),
                const Text(
                  'OneDay',
                  style: TextStyle(
                    color: Color(0xFF37352F),
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                // 开发者模式指示器（仅在Debug模式下显示）
                if (kDebugMode && _logoTapCount > 0) ...[
                  const SizedBox(width: 4),
                  Container(
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: _logoTapCount >= 5
                          ? const Color(0xFF059669)
                          : const Color(0xFF2E7EED),
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // 跳过按钮
          if (!state.isLastPage)
            TextButton(
              onPressed: _skipOnboarding,
              style: TextButton.styleFrom(
                foregroundColor: const Color(0xFF9B9A97),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              child: const Text(
                '跳过',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              ),
            ),
        ],
      ),
    );
  }

  /// Logo点击检测 - 开发者入口
  void _onLogoTap() {
    final now = DateTime.now();

    // 重置点击计数器如果距离上次点击超过3秒
    if (_lastTapTime != null && now.difference(_lastTapTime!).inSeconds > 3) {
      _logoTapCount = 0;
    }

    _lastTapTime = now;
    _logoTapCount++;

    setState(() {});

    // 连续点击5次激活开发者面板
    if (_logoTapCount >= 5) {
      setState(() {
        _showDeveloperPanel = true;
      });

      // 轻微震动反馈
      HapticFeedback.lightImpact();

      // 显示开发者模式提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(Icons.developer_mode, color: Colors.white, size: 20),
              SizedBox(width: 8),
              Text('开发者模式已激活'),
            ],
          ),
          backgroundColor: const Color(0xFF059669),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          margin: const EdgeInsets.all(16),
          duration: const Duration(seconds: 2),
        ),
      );
    } else if (_logoTapCount == 3) {
      // 点击3次时给用户提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('再点击${5 - _logoTapCount}次激活开发者模式'),
          backgroundColor: const Color(0xFF2E7EED).withValues(alpha: 0.8),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          margin: const EdgeInsets.all(16),
          duration: const Duration(milliseconds: 800),
        ),
      );
    }
  }

  /// 构建开发者面板 - 仅在Debug模式下显示
  Widget _buildDeveloperPanel() {
    return Positioned(
      top: 80,
      right: 16,
      child: Material(
        color: Colors.transparent,
        child: Container(
          width: 200,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF37352F).withValues(alpha: 0.1),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              Row(
                children: [
                  const Icon(
                    Icons.developer_mode,
                    size: 16,
                    color: Color(0xFF059669),
                  ),
                  const SizedBox(width: 6),
                  const Expanded(
                    child: Text(
                      '开发者工具',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF37352F),
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _showDeveloperPanel = false;
                        _logoTapCount = 0;
                      });
                    },
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: const Color(0xFF37352F).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.close,
                        size: 12,
                        color: Color(0xFF9B9A97),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // 快速进入按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    context.go('/main');
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF2E7EED),
                    foregroundColor: Colors.white,
                    elevation: 0,
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  icon: const Icon(Icons.rocket_launch, size: 16),
                  label: const Text(
                    '跳转主页',
                    style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
                  ),
                ),
              ),

              const SizedBox(height: 8),

              // 跳转登录页按钮
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: () {
                    context.go('/login');
                  },
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFF7C3AED),
                    side: const BorderSide(color: Color(0xFF7C3AED), width: 1),
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  icon: const Icon(Icons.login, size: 16),
                  label: const Text(
                    '跳转登录',
                    style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
                  ),
                ),
              ),

              const SizedBox(height: 12),

              // 提示文字
              Text(
                '⚠️ 仅开发模式可见\n发布时会自动隐藏',
                style: TextStyle(
                  fontSize: 11,
                  color: const Color(0xFF9B9A97),
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建页面指示器 - 简洁的点状指示器
  Widget _buildPageIndicator(OnboardingState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(_pages.length, (index) {
          final isActive = index == state.currentPage;

          // Web 平台支持点击圆点导航
          if (kIsWeb) {
            return GestureDetector(
              onTap: () => _navigateToPage(index),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 250),
                margin: const EdgeInsets.symmetric(horizontal: 3),
                width: isActive ? 20 : 6,
                height: 6,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(3),
                  color: isActive
                      ? const Color(0xFF2E7EED)
                      : const Color(0xFF2E7EED).withValues(alpha: 0.2),
                ),
              ),
            );
          }

          // 移动端保持原有样式（无点击功能）
          return AnimatedContainer(
            duration: const Duration(milliseconds: 250),
            margin: const EdgeInsets.symmetric(horizontal: 3),
            width: isActive ? 20 : 6,
            height: 6,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(3),
              color: isActive
                  ? const Color(0xFF2E7EED)
                  : const Color(0xFF2E7EED).withValues(alpha: 0.2),
            ),
          );
        }),
      ),
    );
  }

  /// 构建页面内容 - 卡片式设计
  Widget _buildPageContent(OnboardingPageData pageData, int index) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        children: [
          const SizedBox(height: 32),

          // 功能图标卡片
          _buildFeatureCard(pageData),

          const SizedBox(height: 40),

          // 标题和描述
          _buildTextContent(pageData),

          // 权限说明（仅最后一页）
          if (pageData.requiresPermission) ...[
            const SizedBox(height: 32),
            _buildPermissionCard(pageData),
          ],

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  /// 构建功能图标卡片
  Widget _buildFeatureCard(OnboardingPageData pageData) {
    return Container(
      width: 200,
      height: 200,
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: Center(
        child: Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: pageData.iconColor,
          ),
          child: Icon(pageData.icon, size: 40, color: Colors.white),
        ),
      ),
    );
  }

  /// 构建文字内容
  Widget _buildTextContent(OnboardingPageData pageData) {
    return Column(
      children: [
        // 标题
        Text(
          pageData.title,
          style: const TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
            height: 1.2,
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 8),

        // 副标题
        Text(
          pageData.subtitle,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Color(0xFF787774),
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 24),

        // 描述
        Text(
          pageData.description,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            color: Color(0xFF787774),
            height: 1.6,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 构建权限设置卡片
  Widget _buildPermissionCard(OnboardingPageData pageData) {
    final onboardingState = ref.watch(onboardingProvider);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF2E7EED).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
            ),
            child: const Icon(
              Icons.security_rounded,
              color: Color(0xFF2E7EED),
              size: 20,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            pageData.permissionDescription ?? '',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF787774),
              height: 1.5,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
          if (onboardingState.hasRequestedPermissions) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFF0F7B6C).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color(0xFF0F7B6C),
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 12,
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    '权限设置完成',
                    style: TextStyle(
                      color: Color(0xFF0F7B6C),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建底部按钮 - 简洁的按钮设计
  Widget _buildBottomButtons(OnboardingState state) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // 主要按钮
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              onPressed: () {
                if (state.isLastPage) {
                  if (_pages[state.currentPage].requiresPermission &&
                      !state.hasRequestedPermissions) {
                    _requestPermissions();
                  } else {
                    _completeOnboarding();
                  }
                } else {
                  _nextPage();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2E7EED),
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                textStyle: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              child: Text(_getButtonText(state)),
            ),
          ),

          // 上一页按钮（非首页时显示）
          if (state.currentPage > 0) ...[
            const SizedBox(height: 12),
            TextButton(
              onPressed: () {
                _pageController.previousPage(
                  duration: const Duration(milliseconds: 250),
                  curve: Curves.easeInOut,
                );
              },
              style: TextButton.styleFrom(
                foregroundColor: const Color(0xFF9B9A97),
              ),
              child: const Text(
                '上一步',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 获取按钮文字
  String _getButtonText(OnboardingState state) {
    if (state.isLastPage) {
      if (_pages[state.currentPage].requiresPermission &&
          !state.hasRequestedPermissions) {
        return '授权并开始';
      }
      return '开始使用';
    }
    return '下一步';
  }
}

/// 可复用的功能卡片组件
class FeatureCard extends StatelessWidget {
  final IconData icon;
  final Color iconColor;
  final double size;

  const FeatureCard({
    super.key,
    required this.icon,
    required this.iconColor,
    this.size = 200,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: const Color(0xFFFAFAFA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.08),
          width: 1,
        ),
      ),
      child: Center(
        child: Container(
          width: size * 0.4,
          height: size * 0.4,
          decoration: BoxDecoration(shape: BoxShape.circle, color: iconColor),
          child: Icon(icon, size: size * 0.2, color: Colors.white),
        ),
      ),
    );
  }
}
