import 'package:json_annotation/json_annotation.dart';

part 'study_time_models.g.dart';

/// 学习时间统计数据模型
@JsonSerializable()
class StudyTimeStatistics {
  /// 日期（YYYY-MM-DD格式）
  final String date;

  /// 总学习时长（分钟）
  final int totalStudyMinutes;

  /// 完成的任务数量
  final int completedTasks;

  /// 总计划时长（分钟）
  final int totalPlannedMinutes;

  /// 获得的虚拟工资
  final double totalWage;

  /// 学习会话列表
  final List<StudySession> sessions;

  /// 最后更新时间
  final DateTime lastUpdated;

  const StudyTimeStatistics({
    required this.date,
    required this.totalStudyMinutes,
    required this.completedTasks,
    required this.totalPlannedMinutes,
    required this.totalWage,
    required this.sessions,
    required this.lastUpdated,
  });

  factory StudyTimeStatistics.fromJson(Map<String, dynamic> json) =>
      _$StudyTimeStatisticsFromJson(json);

  Map<String, dynamic> toJson() => _$StudyTimeStatisticsToJson(this);

  /// 创建空的统计数据
  factory StudyTimeStatistics.empty(String date) {
    return StudyTimeStatistics(
      date: date,
      totalStudyMinutes: 0,
      completedTasks: 0,
      totalPlannedMinutes: 0,
      totalWage: 0.0,
      sessions: [],
      lastUpdated: DateTime.now(),
    );
  }

  /// 复制并更新统计数据
  StudyTimeStatistics copyWith({
    String? date,
    int? totalStudyMinutes,
    int? completedTasks,
    int? totalPlannedMinutes,
    double? totalWage,
    List<StudySession>? sessions,
    DateTime? lastUpdated,
  }) {
    return StudyTimeStatistics(
      date: date ?? this.date,
      totalStudyMinutes: totalStudyMinutes ?? this.totalStudyMinutes,
      completedTasks: completedTasks ?? this.completedTasks,
      totalPlannedMinutes: totalPlannedMinutes ?? this.totalPlannedMinutes,
      totalWage: totalWage ?? this.totalWage,
      sessions: sessions ?? this.sessions,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// 获取学习效率百分比
  double get efficiencyPercentage {
    if (totalPlannedMinutes == 0) return 0.0;
    return (totalStudyMinutes / totalPlannedMinutes * 100).clamp(0.0, 100.0);
  }

  /// 获取格式化的学习时长
  String get formattedStudyTime {
    final hours = totalStudyMinutes ~/ 60;
    final minutes = totalStudyMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  /// 获取平均每个任务的学习时长
  double get averageSessionMinutes {
    if (completedTasks == 0) return 0.0;
    return totalStudyMinutes / completedTasks;
  }

  /// 检查是否有学习数据
  bool get hasStudyData => totalStudyMinutes > 0 || completedTasks > 0;

  /// 计算学习ROI（学习效果得分 ÷ 时间投入）
  /// 学习效果得分基于完成任务数量、质量评分等量化指标
  double get learningROI {
    if (totalStudyMinutes == 0) return 0.0;

    // 基础效果得分：完成任务数 * 10 + 效率百分比
    final effectScore = (completedTasks * 10) + efficiencyPercentage;

    // 时间投入（小时）
    final timeInvestmentHours = totalStudyMinutes / 60.0;

    return effectScore / timeInvestmentHours;
  }

  /// 计算并行时间收益（分钟）
  /// 识别同时进行多项活动的时间段，如TimeBox动觉记忆背单词期间的运动时间
  int get parallelTimeMinutes {
    // 统计休息会话中的动觉记忆时间（这些时间既学习又运动）
    final kinestheticSessions = sessions
        .where(
          (session) =>
              session.category == '休息' && session.title.contains('动觉记忆'),
        )
        .toList();

    return kinestheticSessions.fold<int>(
      0,
      (sum, session) => sum + session.durationMinutes,
    );
  }

  /// 计算信噪比
  /// 专注于目标任务的时间 ÷ 与今日目标无关的时间
  double get focusSignalToNoiseRatio {
    // 计算专注时间（非休息类别的学习时间）
    final focusMinutes = sessions
        .where((session) => session.category != '休息')
        .fold<int>(0, (sum, session) => sum + session.durationMinutes);

    // 计算噪音时间（休息时间中非学习相关的时间）
    final noiseMinutes = sessions
        .where(
          (session) =>
              session.category == '休息' && !session.title.contains('动觉记忆'),
        )
        .fold<int>(0, (sum, session) => sum + session.durationMinutes);

    if (noiseMinutes == 0) return focusMinutes > 0 ? 100.0 : 0.0;
    return focusMinutes / noiseMinutes;
  }

  /// 获取信噪比等级评价
  String get focusRatingLevel {
    final ratio = focusSignalToNoiseRatio;
    if (ratio >= 80) return '优秀';
    if (ratio >= 50) return '良好';
    return '需改进';
  }
}

/// 学习会话数据模型
@JsonSerializable()
class StudySession {
  /// 会话ID（对应时间盒子任务ID）
  final String sessionId;

  /// 任务标题
  final String title;

  /// 任务分类
  final String category;

  /// 开始时间
  final DateTime startTime;

  /// 结束时间
  final DateTime? endTime;

  /// 计划时长（分钟）
  final int plannedMinutes;

  /// 实际时长（分钟）
  final int? actualMinutes;

  /// 获得的工资
  final double wage;

  const StudySession({
    required this.sessionId,
    required this.title,
    required this.category,
    required this.startTime,
    this.endTime,
    required this.plannedMinutes,
    this.actualMinutes,
    required this.wage,
  });

  factory StudySession.fromJson(Map<String, dynamic> json) =>
      _$StudySessionFromJson(json);

  Map<String, dynamic> toJson() => _$StudySessionToJson(this);

  /// 从时间盒子任务创建学习会话
  factory StudySession.fromTimeBoxTask(dynamic task) {
    return StudySession(
      sessionId: task.id,
      title: task.title,
      category: task.category,
      startTime: task.startTime ?? DateTime.now(),
      endTime: task.endTime,
      plannedMinutes: task.plannedMinutes,
      actualMinutes: task.actualMinutes,
      wage: task.calculateWage(),
    );
  }

  /// 获取实际学习时长
  int get durationMinutes {
    return actualMinutes ?? plannedMinutes;
  }

  /// 检查是否已完成
  bool get isCompleted => endTime != null;

  /// 检查是否为今天的会话
  bool get isToday {
    final now = DateTime.now();
    return startTime.year == now.year &&
        startTime.month == now.month &&
        startTime.day == now.day;
  }

  /// 检查是否为休息会话
  bool get isRestSession => category == '休息';
}

/// 学习时间聚合数据模型
@JsonSerializable()
class StudyTimeAggregation {
  /// 今日学习时长（分钟）
  final int todayMinutes;

  /// 本周学习时长（分钟）
  final int thisWeekMinutes;

  /// 本月学习时长（分钟）
  final int thisMonthMinutes;

  /// 今日完成任务数
  final int todayCompletedTasks;

  /// 今日获得工资
  final double todayWage;

  /// 连续学习天数
  final int streakDays;

  /// 今日学习ROI值
  final double todayLearningROI;

  /// 7天平均学习ROI
  final double weeklyAverageLearningROI;

  /// 今日并行时间收益（分钟）
  final int todayParallelTimeMinutes;

  /// 今日信噪比
  final double todayFocusSignalToNoiseRatio;

  /// 今日信噪比等级
  final String todayFocusRatingLevel;

  /// 最后更新时间
  final DateTime lastUpdated;

  const StudyTimeAggregation({
    required this.todayMinutes,
    required this.thisWeekMinutes,
    required this.thisMonthMinutes,
    required this.todayCompletedTasks,
    required this.todayWage,
    required this.streakDays,
    required this.todayLearningROI,
    required this.weeklyAverageLearningROI,
    required this.todayParallelTimeMinutes,
    required this.todayFocusSignalToNoiseRatio,
    required this.todayFocusRatingLevel,
    required this.lastUpdated,
  });

  factory StudyTimeAggregation.fromJson(Map<String, dynamic> json) =>
      _$StudyTimeAggregationFromJson(json);

  Map<String, dynamic> toJson() => _$StudyTimeAggregationToJson(this);

  /// 创建空的聚合数据
  factory StudyTimeAggregation.empty() {
    return StudyTimeAggregation(
      todayMinutes: 0,
      thisWeekMinutes: 0,
      thisMonthMinutes: 0,
      todayCompletedTasks: 0,
      todayWage: 0.0,
      streakDays: 0,
      todayLearningROI: 0.0,
      weeklyAverageLearningROI: 0.0,
      todayParallelTimeMinutes: 0,
      todayFocusSignalToNoiseRatio: 0.0,
      todayFocusRatingLevel: '需改进',
      lastUpdated: DateTime.now(),
    );
  }

  /// 获取今日格式化学习时长
  String get todayFormattedTime {
    final hours = todayMinutes ~/ 60;
    final minutes = todayMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  /// 获取本周格式化学习时长
  String get thisWeekFormattedTime {
    final hours = thisWeekMinutes ~/ 60;
    final minutes = thisWeekMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  /// 获取本月格式化学习时长
  String get thisMonthFormattedTime {
    final hours = thisMonthMinutes ~/ 60;
    final minutes = thisMonthMinutes % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  /// 复制并更新聚合数据
  StudyTimeAggregation copyWith({
    int? todayMinutes,
    int? thisWeekMinutes,
    int? thisMonthMinutes,
    int? todayCompletedTasks,
    double? todayWage,
    int? streakDays,
    double? todayLearningROI,
    double? weeklyAverageLearningROI,
    int? todayParallelTimeMinutes,
    double? todayFocusSignalToNoiseRatio,
    String? todayFocusRatingLevel,
    DateTime? lastUpdated,
  }) {
    return StudyTimeAggregation(
      todayMinutes: todayMinutes ?? this.todayMinutes,
      thisWeekMinutes: thisWeekMinutes ?? this.thisWeekMinutes,
      thisMonthMinutes: thisMonthMinutes ?? this.thisMonthMinutes,
      todayCompletedTasks: todayCompletedTasks ?? this.todayCompletedTasks,
      todayWage: todayWage ?? this.todayWage,
      streakDays: streakDays ?? this.streakDays,
      todayLearningROI: todayLearningROI ?? this.todayLearningROI,
      weeklyAverageLearningROI:
          weeklyAverageLearningROI ?? this.weeklyAverageLearningROI,
      todayParallelTimeMinutes:
          todayParallelTimeMinutes ?? this.todayParallelTimeMinutes,
      todayFocusSignalToNoiseRatio:
          todayFocusSignalToNoiseRatio ?? this.todayFocusSignalToNoiseRatio,
      todayFocusRatingLevel:
          todayFocusRatingLevel ?? this.todayFocusRatingLevel,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}
