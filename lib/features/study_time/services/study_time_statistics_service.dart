import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/study_time_models.dart';
import '../../time_box/models/timebox_models.dart';

/// 学习时间统计服务
///
/// 负责聚合和计算学习时间统计数据，包括：
/// 1. 从时间盒子任务中提取学习时间数据
/// 2. 计算每日、每周、每月的学习统计
/// 3. 管理学习时间数据的持久化
/// 4. 提供实时的学习时间更新
class StudyTimeStatisticsService extends ChangeNotifier {
  static const String _storageKey = 'study_time_statistics_v1';
  static const String _aggregationKey = 'study_time_aggregation_v1';

  // 缓存的统计数据
  final Map<String, StudyTimeStatistics> _dailyStatistics = {};
  StudyTimeAggregation? _currentAggregation;

  // 加载状态
  bool _isLoading = false;
  String? _error;
  bool _isDisposed = false;

  /// 获取加载状态
  bool get isLoading => _isLoading;

  /// 获取错误信息
  String? get error => _error;

  /// 获取当前聚合数据
  StudyTimeAggregation? get currentAggregation => _currentAggregation;

  /// 初始化服务
  Future<void> initialize() async {
    await _loadFromStorage();
  }

  /// 从本地存储加载数据
  Future<void> _loadFromStorage() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      final prefs = await SharedPreferences.getInstance();

      // 加载每日统计数据
      final statisticsJson = prefs.getString(_storageKey);
      if (statisticsJson != null) {
        final Map<String, dynamic> data = json.decode(statisticsJson);
        _dailyStatistics.clear();
        data.forEach((date, statsJson) {
          _dailyStatistics[date] = StudyTimeStatistics.fromJson(statsJson);
        });
      }

      // 加载聚合数据
      final aggregationJson = prefs.getString(_aggregationKey);
      if (aggregationJson != null) {
        _currentAggregation = StudyTimeAggregation.fromJson(
          json.decode(aggregationJson),
        );
      } else {
        _currentAggregation = StudyTimeAggregation.empty();
      }

      _isLoading = false;
      notifyListeners();

      print('✅ 学习时间统计服务：已加载 ${_dailyStatistics.length} 天的统计数据');
    } catch (e) {
      _isLoading = false;
      _error = '加载学习时间统计失败: $e';
      notifyListeners();
      print('❌ 加载学习时间统计失败: $e');
    }
  }

  /// 保存数据到本地存储
  Future<void> _saveToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 保存每日统计数据
      final statisticsData = <String, dynamic>{};
      _dailyStatistics.forEach((date, stats) {
        statisticsData[date] = stats.toJson();
      });
      await prefs.setString(_storageKey, json.encode(statisticsData));

      // 保存聚合数据
      if (_currentAggregation != null) {
        await prefs.setString(
          _aggregationKey,
          json.encode(_currentAggregation!.toJson()),
        );
      }

      print('💾 学习时间统计服务：已保存统计数据');
    } catch (e) {
      print('❌ 保存学习时间统计失败: $e');
      _error = '保存学习时间统计失败: $e';
      notifyListeners();
    }
  }

  /// 从时间盒子任务列表更新统计数据
  Future<void> updateFromTimeBoxTasks(List<TimeBoxTask> tasks) async {
    try {
      print('🔄 学习时间统计服务：开始更新统计数据，任务数量: ${tasks.length}');

      // 按日期分组已完成的任务
      final Map<String, List<TimeBoxTask>> tasksByDate = {};

      for (final task in tasks) {
        if (task.isCompleted && task.startTime != null) {
          final date = _formatDate(task.startTime!);
          tasksByDate.putIfAbsent(date, () => []).add(task);
        }
      }

      // 更新每日统计
      for (final entry in tasksByDate.entries) {
        final date = entry.key;
        final dayTasks = entry.value;

        await _updateDailyStatistics(date, dayTasks);
      }

      // 重新计算聚合数据
      await _recalculateAggregation();

      // 保存到本地存储
      await _saveToStorage();

      notifyListeners();

      print('✅ 学习时间统计服务：统计数据更新完成');
    } catch (e) {
      print('❌ 更新学习时间统计失败: $e');
      _error = '更新学习时间统计失败: $e';
      notifyListeners();
    }
  }

  /// 更新指定日期的统计数据
  Future<void> _updateDailyStatistics(
    String date,
    List<TimeBoxTask> tasks,
  ) async {
    final sessions = tasks
        .map((task) => StudySession.fromTimeBoxTask(task))
        .toList();

    final totalStudyMinutes = sessions.fold<int>(
      0,
      (sum, session) => sum + session.durationMinutes,
    );
    final completedTasks = sessions.length;
    final totalPlannedMinutes = sessions.fold<int>(
      0,
      (sum, session) => sum + session.plannedMinutes,
    );
    final totalWage = sessions.fold<double>(
      0,
      (sum, session) => sum + session.wage,
    );

    final statistics = StudyTimeStatistics(
      date: date,
      totalStudyMinutes: totalStudyMinutes,
      completedTasks: completedTasks,
      totalPlannedMinutes: totalPlannedMinutes,
      totalWage: totalWage,
      sessions: sessions,
      lastUpdated: DateTime.now(),
    );

    _dailyStatistics[date] = statistics;

    print(
      '📊 更新日期 $date 的统计：学习时长 ${statistics.formattedStudyTime}，完成任务 $completedTasks 个',
    );
  }

  /// 重新计算聚合数据
  Future<void> _recalculateAggregation() async {
    final now = DateTime.now();
    final today = _formatDate(now);
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final startOfMonth = DateTime(now.year, now.month, 1);

    // 计算今日数据
    final todayStats = _dailyStatistics[today];
    final todayMinutes = todayStats?.totalStudyMinutes ?? 0;
    final todayCompletedTasks = todayStats?.completedTasks ?? 0;
    final todayWage = todayStats?.totalWage ?? 0.0;

    // 计算本周数据
    int thisWeekMinutes = 0;
    for (int i = 0; i < 7; i++) {
      final date = _formatDate(startOfWeek.add(Duration(days: i)));
      final stats = _dailyStatistics[date];
      if (stats != null) {
        thisWeekMinutes += stats.totalStudyMinutes;
      }
    }

    // 计算本月数据
    int thisMonthMinutes = 0;
    final daysInMonth = DateTime(now.year, now.month + 1, 0).day;
    for (int i = 0; i < daysInMonth; i++) {
      final date = _formatDate(startOfMonth.add(Duration(days: i)));
      final stats = _dailyStatistics[date];
      if (stats != null) {
        thisMonthMinutes += stats.totalStudyMinutes;
      }
    }

    // 计算连续学习天数
    final streakDays = _calculateStreakDays(now);

    // 计算新的学习效率指标
    final todayLearningROI = todayStats?.learningROI ?? 0.0;
    final weeklyAverageLearningROI = _calculateWeeklyAverageLearningROI(now);
    final todayParallelTimeMinutes = todayStats?.parallelTimeMinutes ?? 0;
    final todayFocusSignalToNoiseRatio =
        todayStats?.focusSignalToNoiseRatio ?? 0.0;
    final todayFocusRatingLevel = todayStats?.focusRatingLevel ?? '需改进';

    _currentAggregation = StudyTimeAggregation(
      todayMinutes: todayMinutes,
      thisWeekMinutes: thisWeekMinutes,
      thisMonthMinutes: thisMonthMinutes,
      todayCompletedTasks: todayCompletedTasks,
      todayWage: todayWage,
      streakDays: streakDays,
      todayLearningROI: todayLearningROI,
      weeklyAverageLearningROI: weeklyAverageLearningROI,
      todayParallelTimeMinutes: todayParallelTimeMinutes,
      todayFocusSignalToNoiseRatio: todayFocusSignalToNoiseRatio,
      todayFocusRatingLevel: todayFocusRatingLevel,
      lastUpdated: DateTime.now(),
    );

    print(
      '📈 聚合数据更新：今日 ${_currentAggregation!.todayFormattedTime}，本周 ${_currentAggregation!.thisWeekFormattedTime}，连续 $streakDays 天',
    );
  }

  /// 计算连续学习天数
  int _calculateStreakDays(DateTime endDate) {
    int streak = 0;
    DateTime currentDate = endDate;

    while (true) {
      final dateStr = _formatDate(currentDate);
      final stats = _dailyStatistics[dateStr];

      if (stats != null && stats.hasStudyData) {
        streak++;
        currentDate = currentDate.subtract(const Duration(days: 1));
      } else {
        break;
      }

      // 防止无限循环，最多检查365天
      if (streak >= 365) break;
    }

    return streak;
  }

  /// 获取指定日期的统计数据
  StudyTimeStatistics? getDailyStatistics(DateTime date) {
    final dateStr = _formatDate(date);
    return _dailyStatistics[dateStr];
  }

  /// 获取今日统计数据
  StudyTimeStatistics? get todayStatistics {
    return getDailyStatistics(DateTime.now());
  }

  /// 当时间盒子任务完成时调用此方法
  Future<void> onTaskCompleted(TimeBoxTask task) async {
    if (task.isCompleted && task.startTime != null) {
      print('✅ 学习时间统计服务：任务完成 - ${task.title}');

      // 立即重新计算今日统计数据
      await _updateSingleTaskStatistics(task);

      // 重新计算聚合数据
      await _recalculateAggregation();

      // 保存到本地存储
      await _saveToStorage();

      // 触发通知，让监听者知道数据已更新
      notifyListeners();

      print('📊 学习时间统计服务：已实时更新任务完成数据');
    }
  }

  /// 测试方法：模拟任务完成（仅用于开发测试）
  Future<void> simulateTaskCompletion() async {
    print('🧪 模拟任务完成测试');

    // 创建一个模拟的已完成任务
    final mockTask = TimeBoxTask(
      id: 'test_${DateTime.now().millisecondsSinceEpoch}',
      title: '测试任务',
      description: '这是一个测试任务，用于验证数据同步',
      category: '计算机科学',
      plannedMinutes: 25,
      status: TaskStatus.completed,
      priority: TaskPriority.medium,
      createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
      startTime: DateTime.now().subtract(const Duration(minutes: 30)),
      endTime: DateTime.now(),
    );

    // 调用任务完成处理
    await onTaskCompleted(mockTask);

    print('🧪 模拟任务完成测试结束');
  }

  /// 更新单个任务的统计数据（实时更新）
  Future<void> _updateSingleTaskStatistics(TimeBoxTask completedTask) async {
    final date = _formatDate(completedTask.startTime!);

    // 获取当前日期的现有统计数据
    var currentStats = _dailyStatistics[date];

    // 创建新的学习会话
    final newSession = StudySession.fromTimeBoxTask(completedTask);

    if (currentStats == null) {
      // 如果没有现有数据，创建新的统计数据
      currentStats = StudyTimeStatistics(
        date: date,
        totalStudyMinutes: newSession.durationMinutes,
        completedTasks: 1,
        totalPlannedMinutes: newSession.plannedMinutes,
        totalWage: newSession.wage,
        sessions: [newSession],
        lastUpdated: DateTime.now(),
      );
    } else {
      // 检查是否已经包含这个任务（避免重复计算）
      final existingSessionIndex = currentStats.sessions.indexWhere(
        (session) => session.sessionId == completedTask.id,
      );

      List<StudySession> updatedSessions;
      if (existingSessionIndex >= 0) {
        // 更新现有会话
        updatedSessions = List.from(currentStats.sessions);
        updatedSessions[existingSessionIndex] = newSession;
      } else {
        // 添加新会话
        updatedSessions = [...currentStats.sessions, newSession];
      }

      // 重新计算统计数据
      final totalStudyMinutes = updatedSessions.fold<int>(
        0,
        (sum, session) => sum + session.durationMinutes,
      );
      final totalPlannedMinutes = updatedSessions.fold<int>(
        0,
        (sum, session) => sum + session.plannedMinutes,
      );
      final totalWage = updatedSessions.fold<double>(
        0,
        (sum, session) => sum + session.wage,
      );

      currentStats = StudyTimeStatistics(
        date: date,
        totalStudyMinutes: totalStudyMinutes,
        completedTasks: updatedSessions.length,
        totalPlannedMinutes: totalPlannedMinutes,
        totalWage: totalWage,
        sessions: updatedSessions,
        lastUpdated: DateTime.now(),
      );
    }

    _dailyStatistics[date] = currentStats;

    print(
      '📊 实时更新日期 $date 的统计：学习时长 ${currentStats.formattedStudyTime}，完成任务 ${currentStats.completedTasks} 个',
    );
  }

  /// 格式化日期为字符串
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 清除错误状态
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// 刷新统计数据
  Future<void> refresh() async {
    await _loadFromStorage();
  }

  /// 强制刷新所有数据并通知监听者
  Future<void> forceRefresh() async {
    try {
      print('🔄 学习时间统计服务：开始强制刷新');

      // 重新计算聚合数据
      await _recalculateAggregation();

      // 保存到本地存储
      await _saveToStorage();

      // 强制触发通知，确保UI立即更新
      notifyListeners();

      // 延迟再次通知，确保数据传播到所有监听者
      Future.delayed(const Duration(milliseconds: 100), () {
        if (!_isDisposed) {
          notifyListeners();
        }
      });

      print('✅ 学习时间统计服务：强制刷新完成');
    } catch (e) {
      print('❌ 学习时间统计服务：强制刷新失败 - $e');
      _error = '强制刷新失败: $e';
      notifyListeners();
    }
  }

  /// 计算7天平均学习ROI
  double _calculateWeeklyAverageLearningROI(DateTime endDate) {
    double totalROI = 0.0;
    int validDays = 0;

    // 计算过去7天的平均ROI
    for (int i = 0; i < 7; i++) {
      final date = endDate.subtract(Duration(days: i));
      final dateStr = _formatDate(date);
      final stats = _dailyStatistics[dateStr];

      if (stats != null && stats.hasStudyData) {
        totalROI += stats.learningROI;
        validDays++;
      }
    }

    return validDays > 0 ? totalROI / validDays : 0.0;
  }

  /// 清除所有统计数据（用于修复数据问题）
  Future<void> clearAllStatistics() async {
    try {
      print('🗑️ 学习时间统计服务：开始清除所有统计数据');

      final prefs = await SharedPreferences.getInstance();

      // 清除本地存储的数据
      await prefs.remove(_storageKey);
      await prefs.remove(_aggregationKey);

      // 清除内存中的数据
      _dailyStatistics.clear();
      _currentAggregation = StudyTimeAggregation.empty();

      // 通知监听者
      notifyListeners();

      print('✅ 学习时间统计服务：所有统计数据已清除');
    } catch (e) {
      print('❌ 清除统计数据失败: $e');
      _error = '清除统计数据失败: $e';
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }
}
