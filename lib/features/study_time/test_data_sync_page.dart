import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'providers/study_time_providers.dart';
import '../time_box/providers/timebox_provider.dart';
import '../../services/global_timer_service.dart';

/// 数据同步测试页面
///
/// 用于验证TimeBox任务完成时数据是否能实时同步到今日概览
class TestDataSyncPage extends ConsumerStatefulWidget {
  const TestDataSyncPage({super.key});

  @override
  ConsumerState<TestDataSyncPage> createState() => _TestDataSyncPageState();
}

class _TestDataSyncPageState extends ConsumerState<TestDataSyncPage> {
  final GlobalTimerService _globalTimerService = GlobalTimerService();

  @override
  Widget build(BuildContext context) {
    final studySummary = ref.watch(todayStudySummaryProvider);
    final aggregation = ref.watch(studyTimeAggregationProvider);
    final timeBoxState = ref.watch(timeBoxProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('数据同步测试'),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF37352F),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 今日概览数据
            _buildSectionCard(
              title: '今日概览数据',
              child: Column(
                children: [
                  _buildDataRow('学习时长', studySummary['studyTime'] ?? '0m'),
                  _buildDataRow(
                    '完成任务',
                    studySummary['completedTasks'] ?? '0/0',
                  ),
                  _buildDataRow('学习ROI', studySummary['learningROI'] ?? '0.0'),
                  _buildDataRow('并行时间', studySummary['parallelTime'] ?? '0分钟'),
                  _buildDataRow('信噪比', studySummary['focusRatio'] ?? '0.0'),
                  _buildDataRow('专注等级', studySummary['focusLevel'] ?? '需改进'),
                  _buildDataRow('连续天数', studySummary['streakDays'] ?? '0天'),
                  _buildDataRow('虚拟工资', studySummary['wage'] ?? '¥0'),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // 聚合数据状态
            _buildSectionCard(
              title: '聚合数据状态',
              child: aggregation.when(
                data: (data) {
                  if (data == null) {
                    return const Text('聚合数据为空');
                  }
                  return Column(
                    children: [
                      _buildDataRow('今日分钟数', '${data.todayMinutes}分钟'),
                      _buildDataRow('本周分钟数', '${data.thisWeekMinutes}分钟'),
                      _buildDataRow(
                        '今日ROI',
                        data.todayLearningROI.toStringAsFixed(1),
                      ),
                      _buildDataRow(
                        '周平均ROI',
                        data.weeklyAverageLearningROI.toStringAsFixed(1),
                      ),
                      _buildDataRow(
                        '并行时间',
                        '${data.todayParallelTimeMinutes}分钟',
                      ),
                      _buildDataRow(
                        '信噪比',
                        data.todayFocusSignalToNoiseRatio.toStringAsFixed(1),
                      ),
                      _buildDataRow(
                        '最后更新',
                        data.lastUpdated.toString().substring(11, 19),
                      ),
                    ],
                  );
                },
                loading: () => const CircularProgressIndicator(),
                error: (error, stack) => Text('错误: $error'),
              ),
            ),

            const SizedBox(height: 16),

            // TimeBox任务状态
            _buildSectionCard(
              title: 'TimeBox任务状态',
              child: Column(
                children: [
                  _buildDataRow('总任务数', '${timeBoxState.tasks.length}'),
                  _buildDataRow(
                    '已完成任务',
                    '${timeBoxState.tasks.where((t) => t.isCompleted).length}',
                  ),
                  _buildDataRow(
                    '进行中任务',
                    '${timeBoxState.tasks.where((t) => t.isInProgress).length}',
                  ),
                  _buildDataRow('加载状态', timeBoxState.isLoading ? '加载中' : '已加载'),
                  if (timeBoxState.error != null)
                    _buildDataRow('错误', timeBoxState.error!),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // 全局计时器状态
            _buildSectionCard(
              title: '全局计时器状态',
              child: Column(
                children: [
                  _buildDataRow(
                    '是否运行',
                    _globalTimerService.isTimerRunning ? '是' : '否',
                  ),
                  _buildDataRow(
                    '当前任务',
                    _globalTimerService.currentTask?.title ?? '无',
                  ),
                  _buildDataRow(
                    '剩余时间',
                    '${(_globalTimerService.remainingSeconds / 60).toStringAsFixed(1)}分钟',
                  ),
                  _buildDataRow(
                    '番茄钟状态',
                    _globalTimerService.pomodoroState.toString(),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // 测试按钮
            _buildTestButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard({required String title, required Widget child}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 12),
          child,
        ],
      ),
    );
  }

  Widget _buildDataRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 14, color: Color(0xFF9B9A97)),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFF37352F),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              // 刷新数据
              ref.invalidate(studyTimeAggregationProvider);
              ref.invalidate(todayStudySummaryProvider);
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('已刷新数据')));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7EED),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            child: const Text('刷新数据'),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              // 手动触发数据更新
              final service = ref.read(studyTimeStatisticsServiceProvider);
              final timeBoxState = ref.read(timeBoxProvider);
              service.updateFromTimeBoxTasks(timeBoxState.tasks);
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('已手动更新统计数据')));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF0F7B6C),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            child: const Text('手动更新统计'),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () async {
              // 模拟任务完成
              final service = ref.read(studyTimeStatisticsServiceProvider);
              await service.simulateTaskCompletion();
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('已模拟任务完成，数据应该实时更新')),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFE03E3E),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            child: const Text('模拟任务完成'),
          ),
        ),
      ],
    );
  }
}
