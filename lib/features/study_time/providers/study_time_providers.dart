import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/study_time_models.dart';
import '../services/study_time_statistics_service.dart';
import '../../time_box/providers/timebox_provider.dart';

/// 学习时间统计服务Provider
final studyTimeStatisticsServiceProvider = Provider<StudyTimeStatisticsService>(
  (ref) {
    print('🔄 studyTimeStatisticsServiceProvider：初始化');
    final service = StudyTimeStatisticsService();

    // 监听时间盒子任务变化，自动更新统计数据
    ref.listen(timeBoxProvider, (previous, next) {
      print('📢 studyTimeStatisticsServiceProvider：接收到timeBoxProvider变化通知');
      print('  - 加载状态: ${next.isLoading}');
      print('  - 错误状态: ${next.error}');
      print('  - 任务数量: ${next.tasks.length}');

      if (!next.isLoading && next.error == null) {
        print('🔄 studyTimeStatisticsServiceProvider：开始更新统计数据');
        // 当时间盒子任务数据变化时，更新学习时间统计
        service
            .updateFromTimeBoxTasks(next.tasks)
            .then((_) {
              print('✅ studyTimeStatisticsServiceProvider：统计数据更新完成');
            })
            .catchError((error) {
              print('❌ studyTimeStatisticsServiceProvider：统计数据更新失败 - $error');
            });
      } else {
        print('⚠️ studyTimeStatisticsServiceProvider：跳过更新（加载中或有错误）');
      }
    });

    // 初始化服务
    print('🔄 studyTimeStatisticsServiceProvider：初始化服务');
    service.initialize();

    return service;
  },
);

/// 学习时间聚合数据Provider
final studyTimeAggregationProvider = StreamProvider<StudyTimeAggregation?>((
  ref,
) {
  print('🔄 studyTimeAggregationProvider：初始化');
  final service = ref.watch(studyTimeStatisticsServiceProvider);

  // 创建一个基于ChangeNotifier的Stream来实时监听服务变化
  late StreamController<StudyTimeAggregation?> controller;

  void listener() {
    print('📢 studyTimeAggregationProvider：接收到服务通知');
    if (!controller.isClosed) {
      final aggregation = service.currentAggregation;
      print(
        '📊 studyTimeAggregationProvider：发送聚合数据 - ${aggregation != null ? "有数据" : "数据为空"}',
      );
      if (aggregation != null) {
        print(
          '📈 聚合数据内容: 今日学习=${aggregation.todayMinutes}分钟, 完成任务=${aggregation.todayCompletedTasks}个',
        );
      }
      controller.add(aggregation);
    } else {
      print('⚠️ studyTimeAggregationProvider：控制器已关闭，无法发送数据');
    }
  }

  controller = StreamController<StudyTimeAggregation?>(
    onListen: () {
      print('👂 studyTimeAggregationProvider：开始监听');
      // 立即发送当前值
      final currentAggregation = service.currentAggregation;
      print(
        '📊 studyTimeAggregationProvider：发送初始数据 - ${currentAggregation != null ? "有数据" : "数据为空"}',
      );
      controller.add(currentAggregation);
      // 监听服务变化
      service.addListener(listener);
    },
    onCancel: () {
      print('🛑 studyTimeAggregationProvider：取消监听');
      service.removeListener(listener);
    },
  );

  ref.onDispose(() {
    print('🗑️ studyTimeAggregationProvider：销毁');
    service.removeListener(listener);
    controller.close();
  });

  return controller.stream.distinct().asyncMap((aggregation) async {
    print(
      '🔄 studyTimeAggregationProvider：处理聚合数据 - ${aggregation != null ? "有数据" : "数据为空"}',
    );

    // 如果聚合数据为空，尝试从时间盒子任务重新计算
    if (aggregation == null) {
      print('⚠️ studyTimeAggregationProvider：聚合数据为空，尝试重新计算');
      final timeBoxState = ref.read(timeBoxProvider);
      if (!timeBoxState.isLoading && timeBoxState.error == null) {
        print('🔄 studyTimeAggregationProvider：从时间盒子任务重新计算聚合数据');
        await service.updateFromTimeBoxTasks(timeBoxState.tasks);
        final newAggregation = service.currentAggregation;
        print(
          '📊 studyTimeAggregationProvider：重新计算结果 - ${newAggregation != null ? "有数据" : "仍为空"}',
        );
        return newAggregation;
      } else {
        print('⚠️ studyTimeAggregationProvider：时间盒子状态不可用，无法重新计算');
      }
    }
    return aggregation;
  });
});

/// 今日学习时间Provider
final todayStudyTimeProvider = Provider<StudyTimeStatistics?>((ref) {
  final service = ref.watch(studyTimeStatisticsServiceProvider);
  return service.todayStatistics;
});

/// 指定日期学习时间Provider
final dailyStudyTimeProvider = Provider.family<StudyTimeStatistics?, DateTime>((
  ref,
  date,
) {
  final service = ref.watch(studyTimeStatisticsServiceProvider);
  return service.getDailyStatistics(date);
});

/// 学习时间加载状态Provider
final studyTimeLoadingProvider = Provider<bool>((ref) {
  final service = ref.watch(studyTimeStatisticsServiceProvider);
  return service.isLoading;
});

/// 学习时间错误状态Provider
final studyTimeErrorProvider = Provider<String?>((ref) {
  final service = ref.watch(studyTimeStatisticsServiceProvider);
  return service.error;
});

/// 今日学习统计摘要Provider
/// 返回用于首页显示的格式化数据
final todayStudySummaryProvider = Provider<Map<String, dynamic>>((ref) {
  print('🔄 todayStudySummaryProvider：开始重新计算');

  // 监听聚合数据的异步状态
  final aggregationAsync = ref.watch(studyTimeAggregationProvider);
  final timeBoxState = ref.watch(timeBoxProvider);

  print(
    '📊 todayStudySummaryProvider：聚合数据状态 - ${aggregationAsync.runtimeType}',
  );
  print('📋 todayStudySummaryProvider：时间盒子任务数量 - ${timeBoxState.tasks.length}');

  // 处理异步状态
  final aggregation = aggregationAsync.when(
    data: (data) {
      print(
        '✅ todayStudySummaryProvider：聚合数据获取成功 - ${data != null ? "有数据" : "数据为空"}',
      );
      if (data != null) {
        print(
          '📈 聚合数据详情: 今日学习时长=${data.todayMinutes}分钟, 完成任务=${data.todayCompletedTasks}个',
        );
      }
      return data;
    },
    loading: () {
      print('⏳ todayStudySummaryProvider：聚合数据加载中');
      return null;
    },
    error: (error, stack) {
      print('❌ todayStudySummaryProvider：聚合数据错误 - $error');
      return null;
    },
  );

  if (aggregation == null) {
    print('⚠️ todayStudySummaryProvider：聚合数据为空，返回默认值');
    return {
      'studyTime': '0m',
      'wage': '¥0',
      'completedTasks': '0/0',
      'streakDays': '0天',
      'learningROI': '0.0',
      'weeklyAverageROI': '0.0',
      'parallelTime': '0分钟',
      'focusRatio': '0.0',
      'focusLevel': '需改进',
    };
  }

  // 计算今日总任务数（包括未完成的）- 使用startTime而不是createdAt
  final today = DateTime.now();
  final todayTasks = timeBoxState.tasks.where((task) {
    if (task.startTime == null) return false;
    return task.startTime!.year == today.year &&
        task.startTime!.month == today.month &&
        task.startTime!.day == today.day;
  }).length;

  print(
    '📅 todayStudySummaryProvider：今日任务统计 - 总任务数=$todayTasks, 已完成=${aggregation.todayCompletedTasks}',
  );

  final result = {
    'studyTime': aggregation.todayFormattedTime,
    'wage': '¥${aggregation.todayWage.toStringAsFixed(0)}',
    'completedTasks': '${aggregation.todayCompletedTasks}/$todayTasks',
    'streakDays': '${aggregation.streakDays}天',
    'learningROI': aggregation.todayLearningROI.toStringAsFixed(1),
    'weeklyAverageROI': aggregation.weeklyAverageLearningROI.toStringAsFixed(1),
    'parallelTime': '${aggregation.todayParallelTimeMinutes}分钟',
    'focusRatio': aggregation.todayFocusSignalToNoiseRatio.toStringAsFixed(1),
    'focusLevel': aggregation.todayFocusRatingLevel,
  };

  print(
    '📊 todayStudySummaryProvider：最终结果 - 学习时长: ${result['studyTime']}, 完成任务: ${result['completedTasks']}, 工资: ${result['wage']}',
  );

  return result;
});

/// 学习时间趋势数据Provider
/// 返回最近7天的学习时间数据，用于图表显示
final studyTimeTrendProvider = Provider<List<Map<String, dynamic>>>((ref) {
  final service = ref.watch(studyTimeStatisticsServiceProvider);
  final now = DateTime.now();
  final trendData = <Map<String, dynamic>>[];

  for (int i = 6; i >= 0; i--) {
    final date = now.subtract(Duration(days: i));
    final stats = service.getDailyStatistics(date);

    trendData.add({
      'date': date,
      'minutes': stats?.totalStudyMinutes ?? 0,
      'formattedTime': stats?.formattedStudyTime ?? '0m',
      'tasks': stats?.completedTasks ?? 0,
      'wage': stats?.totalWage ?? 0.0,
    });
  }

  return trendData;
});

/// 本周学习统计Provider
final weeklyStudyStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final aggregation = ref.watch(studyTimeAggregationProvider).value;

  if (aggregation == null) {
    return {
      'totalMinutes': 0,
      'formattedTime': '0m',
      'averagePerDay': '0m',
      'daysStudied': 0,
    };
  }

  final service = ref.watch(studyTimeStatisticsServiceProvider);
  final now = DateTime.now();
  final startOfWeek = now.subtract(Duration(days: now.weekday - 1));

  // 计算本周学习天数
  int daysStudied = 0;
  for (int i = 0; i < 7; i++) {
    final date = startOfWeek.add(Duration(days: i));
    final stats = service.getDailyStatistics(date);
    if (stats != null && stats.hasStudyData) {
      daysStudied++;
    }
  }

  final averageMinutes = daysStudied > 0
      ? aggregation.thisWeekMinutes ~/ daysStudied
      : 0;
  final averageHours = averageMinutes ~/ 60;
  final averageRemainingMinutes = averageMinutes % 60;

  String averageFormatted;
  if (averageHours > 0) {
    averageFormatted = '${averageHours}h ${averageRemainingMinutes}m';
  } else {
    averageFormatted = '${averageRemainingMinutes}m';
  }

  return {
    'totalMinutes': aggregation.thisWeekMinutes,
    'formattedTime': aggregation.thisWeekFormattedTime,
    'averagePerDay': averageFormatted,
    'daysStudied': daysStudied,
  };
});

/// 本月学习统计Provider
final monthlyStudyStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final aggregation = ref.watch(studyTimeAggregationProvider).value;

  if (aggregation == null) {
    return {
      'totalMinutes': 0,
      'formattedTime': '0m',
      'averagePerDay': '0m',
      'daysStudied': 0,
    };
  }

  final service = ref.watch(studyTimeStatisticsServiceProvider);
  final now = DateTime.now();
  final startOfMonth = DateTime(now.year, now.month, 1);
  final daysInMonth = DateTime(now.year, now.month + 1, 0).day;

  // 计算本月学习天数
  int daysStudied = 0;
  for (int i = 0; i < daysInMonth; i++) {
    final date = startOfMonth.add(Duration(days: i));
    final stats = service.getDailyStatistics(date);
    if (stats != null && stats.hasStudyData) {
      daysStudied++;
    }
  }

  final averageMinutes = daysStudied > 0
      ? aggregation.thisMonthMinutes ~/ daysStudied
      : 0;
  final averageHours = averageMinutes ~/ 60;
  final averageRemainingMinutes = averageMinutes % 60;

  String averageFormatted;
  if (averageHours > 0) {
    averageFormatted = '${averageHours}h ${averageRemainingMinutes}m';
  } else {
    averageFormatted = '${averageRemainingMinutes}m';
  }

  return {
    'totalMinutes': aggregation.thisMonthMinutes,
    'formattedTime': aggregation.thisMonthFormattedTime,
    'averagePerDay': averageFormatted,
    'daysStudied': daysStudied,
  };
});

/// 学习效率统计Provider
final studyEfficiencyProvider = Provider<Map<String, dynamic>>((ref) {
  final todayStats = ref.watch(todayStudyTimeProvider);
  final weeklyStats = ref.watch(weeklyStudyStatsProvider);

  if (todayStats == null) {
    return {
      'todayEfficiency': 0.0,
      'weeklyAverage': 0.0,
      'trend': 'stable', // 'up', 'down', 'stable'
    };
  }

  final todayEfficiency = todayStats.efficiencyPercentage;
  final weeklyMinutes = weeklyStats['totalMinutes'] as int;
  final daysStudied = weeklyStats['daysStudied'] as int;

  // 计算本周平均效率（简化计算）
  final weeklyAverage = daysStudied > 0
      ? (weeklyMinutes / (daysStudied * 60) * 100).clamp(0.0, 100.0)
      : 0.0;

  String trend = 'stable';
  if (todayEfficiency > weeklyAverage + 10) {
    trend = 'up';
  } else if (todayEfficiency < weeklyAverage - 10) {
    trend = 'down';
  }

  return {
    'todayEfficiency': todayEfficiency,
    'weeklyAverage': weeklyAverage,
    'trend': trend,
  };
});
