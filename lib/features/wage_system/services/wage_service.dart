import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/wage_transaction.dart';

/// 工资系统服务
class WageService {
  static const String _userBalanceKey = 'user_balance';
  static const String _transactionsKey = 'wage_transactions';

  /// 获取用户余额
  Future<double> getUserBalance() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getDouble(_userBalanceKey) ?? 0.0;
    } catch (e) {
      debugPrint('获取用户余额失败: $e');
      return 0.0;
    }
  }

  /// 保存用户余额
  Future<void> _saveUserBalance(double balance) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(_userBalanceKey, balance);
    } catch (e) {
      debugPrint('保存用户余额失败: $e');
    }
  }

  /// 获取交易记录
  Future<List<WageTransaction>> getTransactions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final transactionsJson = prefs.getString(_transactionsKey);

      if (transactionsJson != null) {
        final transactionsList = json.decode(transactionsJson) as List<dynamic>;
        return transactionsList
            .map((transaction) => WageTransaction.fromJson(transaction))
            .toList();
      }

      return [];
    } catch (e) {
      debugPrint('获取交易记录失败: $e');
      return [];
    }
  }

  /// 保存交易记录
  Future<void> _saveTransactions(List<WageTransaction> transactions) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final transactionsJson = json.encode(
        transactions.map((transaction) => transaction.toJson()).toList(),
      );
      await prefs.setString(_transactionsKey, transactionsJson);
    } catch (e) {
      debugPrint('保存交易记录失败: $e');
    }
  }

  /// 添加工资奖励
  Future<bool> addWageReward(
    double amount, {
    required String description,
    String? relatedTaskId,
  }) async {
    try {
      // 获取当前余额和交易记录
      final currentBalance = await getUserBalance();
      final transactions = await getTransactions();

      // 创建新的交易记录
      final newTransaction = WageTransaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        amount: amount,
        type: TransactionType.achievement,
        description: description,
        timestamp: DateTime.now(),
        relatedTaskId: relatedTaskId,
      );

      // 更新余额和交易记录
      final newBalance = currentBalance + amount;
      transactions.insert(0, newTransaction); // 插入到最前面

      // 保存数据
      await _saveUserBalance(newBalance);
      await _saveTransactions(transactions);

      debugPrint('💰 工资奖励添加成功: +¥$amount, 新余额: ¥$newBalance');
      return true;
    } catch (e) {
      debugPrint('添加工资奖励失败: $e');
      return false;
    }
  }

  /// 添加学习收入
  Future<bool> addStudyIncome(
    double amount, {
    required String description,
    String? relatedTaskId,
  }) async {
    try {
      final currentBalance = await getUserBalance();
      final transactions = await getTransactions();

      final newTransaction = WageTransaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        amount: amount,
        type: TransactionType.studyIncome,
        description: description,
        timestamp: DateTime.now(),
        relatedTaskId: relatedTaskId,
      );

      final newBalance = currentBalance + amount;
      transactions.insert(0, newTransaction);

      await _saveUserBalance(newBalance);
      await _saveTransactions(transactions);

      debugPrint('📚 学习收入添加成功: +¥$amount, 新余额: ¥$newBalance');
      return true;
    } catch (e) {
      debugPrint('添加学习收入失败: $e');
      return false;
    }
  }

  /// 添加奖励收入
  Future<bool> addBonusIncome(
    double amount, {
    required String description,
    String? relatedTaskId,
  }) async {
    try {
      final currentBalance = await getUserBalance();
      final transactions = await getTransactions();

      final newTransaction = WageTransaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        amount: amount,
        type: TransactionType.bonus,
        description: description,
        timestamp: DateTime.now(),
        relatedTaskId: relatedTaskId,
      );

      final newBalance = currentBalance + amount;
      transactions.insert(0, newTransaction);

      await _saveUserBalance(newBalance);
      await _saveTransactions(transactions);

      debugPrint('🎁 奖励收入添加成功: +¥$amount, 新余额: ¥$newBalance');
      return true;
    } catch (e) {
      debugPrint('添加奖励收入失败: $e');
      return false;
    }
  }

  /// 扣除费用（购买道具等）
  Future<bool> deductAmount(
    double amount, {
    required String description,
    String? relatedTaskId,
  }) async {
    try {
      final currentBalance = await getUserBalance();

      // 检查余额是否足够
      if (currentBalance < amount) {
        debugPrint('余额不足: 当前余额 ¥$currentBalance, 需要 ¥$amount');
        return false;
      }

      final transactions = await getTransactions();

      final newTransaction = WageTransaction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        amount: -amount, // 负数表示支出
        type: TransactionType.itemPurchase,
        description: description,
        timestamp: DateTime.now(),
        relatedTaskId: relatedTaskId,
      );

      final newBalance = currentBalance - amount;
      transactions.insert(0, newTransaction);

      await _saveUserBalance(newBalance);
      await _saveTransactions(transactions);

      debugPrint('💸 费用扣除成功: -¥$amount, 新余额: ¥$newBalance');
      return true;
    } catch (e) {
      debugPrint('扣除费用失败: $e');
      return false;
    }
  }

  /// 获取统计数据
  Future<Map<String, dynamic>> getStatistics() async {
    try {
      final transactions = await getTransactions();
      final balance = await getUserBalance();
      final now = DateTime.now();

      // 计算时间范围
      final startOfToday = DateTime(now.year, now.month, now.day);
      final startOfWeek = startOfToday.subtract(Duration(days: now.weekday - 1));
      final startOfMonth = DateTime(now.year, now.month, 1);

      // 计算各时间段收入
      final todayIncome = transactions
          .where((t) => t.timestamp.isAfter(startOfToday) && t.amount > 0)
          .fold(0.0, (sum, t) => sum + t.amount);

      final weekIncome = transactions
          .where((t) => t.timestamp.isAfter(startOfWeek) && t.amount > 0)
          .fold(0.0, (sum, t) => sum + t.amount);

      final monthIncome = transactions
          .where((t) => t.timestamp.isAfter(startOfMonth) && t.amount > 0)
          .fold(0.0, (sum, t) => sum + t.amount);

      final totalIncome = transactions
          .where((t) => t.amount > 0)
          .fold(0.0, (sum, t) => sum + t.amount);

      return {
        'balance': balance,
        'todayIncome': todayIncome,
        'weekIncome': weekIncome,
        'monthIncome': monthIncome,
        'totalIncome': totalIncome,
        'transactionCount': transactions.length,
      };
    } catch (e) {
      debugPrint('获取统计数据失败: $e');
      return {};
    }
  }

  /// 重置所有数据（仅用于测试）
  Future<void> resetAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userBalanceKey);
      await prefs.remove(_transactionsKey);
      debugPrint('🔄 工资系统数据已重置');
    } catch (e) {
      debugPrint('重置工资数据失败: $e');
    }
  }
}
