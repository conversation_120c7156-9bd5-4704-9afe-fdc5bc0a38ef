import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 优质文章访问权限服务
///
/// 管理用户的优质文章访问权限，包括权限激活、验证和持久化存储
class PremiumArticleService {
  static const String _premiumAccessKey = 'oneday_premium_article_access';
  static const String _activationHistoryKey =
      'oneday_premium_activation_history';

  static PremiumArticleService? _instance;
  static PremiumArticleService get instance =>
      _instance ??= PremiumArticleService._();

  PremiumArticleService._();

  SharedPreferences? _prefs;
  bool _hasPremiumAccess = false;
  List<PremiumActivationRecord> _activationHistory = [];

  /// 初始化服务
  Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
    await _loadPremiumAccessStatus();
    await _loadActivationHistory();
  }

  /// 检查用户是否有优质文章访问权限
  bool get hasPremiumAccess => _hasPremiumAccess;

  /// 获取激活历史记录
  List<PremiumActivationRecord> get activationHistory =>
      List.from(_activationHistory);

  /// 激活优质文章访问权限
  ///
  /// [activationType] 激活类型（购买、赠送等）
  /// [duration] 权限持续时间，null表示永久
  /// [itemId] 相关道具ID（如果是通过道具激活）
  Future<bool> activatePremiumAccess({
    required PremiumActivationType activationType,
    Duration? duration,
    String? itemId,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      await _ensureInitialized();

      final now = DateTime.now();
      DateTime? expiresAt;

      if (duration != null) {
        expiresAt = now.add(duration);
      }

      // 创建激活记录
      final activationRecord = PremiumActivationRecord(
        id: now.millisecondsSinceEpoch.toString(),
        activationType: activationType,
        activatedAt: now,
        expiresAt: expiresAt,
        itemId: itemId,
        metadata: metadata ?? {},
      );

      // 添加到激活历史
      _activationHistory.insert(0, activationRecord);

      // 更新权限状态
      _hasPremiumAccess = true;

      // 保存到本地存储
      await _savePremiumAccessStatus();
      await _saveActivationHistory();

      debugPrint('✅ 优质文章访问权限已激活: ${activationRecord.id}');
      return true;
    } catch (e) {
      debugPrint('❌ 激活优质文章访问权限失败: $e');
      return false;
    }
  }

  /// 检查权限是否过期并更新状态
  Future<void> checkAndUpdatePermissionStatus() async {
    await _ensureInitialized();

    if (!_hasPremiumAccess) return;

    // 检查是否有任何有效的激活记录
    final now = DateTime.now();
    bool hasValidAccess = false;

    for (final record in _activationHistory) {
      if (record.isValid(now)) {
        hasValidAccess = true;
        break;
      }
    }

    if (_hasPremiumAccess != hasValidAccess) {
      _hasPremiumAccess = hasValidAccess;
      await _savePremiumAccessStatus();

      if (!hasValidAccess) {
        debugPrint('⏰ 优质文章访问权限已过期');
      }
    }
  }

  /// 获取当前有效的激活记录
  PremiumActivationRecord? getCurrentActiveRecord() {
    final now = DateTime.now();

    for (final record in _activationHistory) {
      if (record.isValid(now)) {
        return record;
      }
    }

    return null;
  }

  /// 获取权限剩余时间
  Duration? getRemainingTime() {
    final activeRecord = getCurrentActiveRecord();
    return activeRecord?.getRemainingTime();
  }

  /// 检查特定文章是否需要优质权限
  bool isArticlePremium(String articleId) {
    // 这里可以根据文章ID或其他标识来判断是否为优质文章
    // 暂时使用简单的规则：ID包含"premium"或以特定数字开头
    return articleId.contains('premium') ||
        articleId.startsWith('1') ||
        articleId.startsWith('2') ||
        articleId.startsWith('3');
  }

  /// 验证用户是否可以访问特定文章
  Future<ArticleAccessResult> validateArticleAccess(String articleId) async {
    await checkAndUpdatePermissionStatus();

    if (!isArticlePremium(articleId)) {
      return ArticleAccessResult.allowed();
    }

    if (_hasPremiumAccess) {
      final activeRecord = getCurrentActiveRecord();
      return ArticleAccessResult.allowed(
        isPremium: true,
        activationRecord: activeRecord,
      );
    } else {
      return ArticleAccessResult.denied(
        reason: '此文章需要优质文章访问权限',
        suggestedAction: '前往商城购买优质文章访问权限',
      );
    }
  }

  /// 重置权限（用于测试或管理）
  Future<void> resetPremiumAccess() async {
    await _ensureInitialized();

    _hasPremiumAccess = false;
    _activationHistory.clear();

    await _savePremiumAccessStatus();
    await _saveActivationHistory();

    debugPrint('🔄 优质文章访问权限已重置');
  }

  /// 确保服务已初始化
  Future<void> _ensureInitialized() async {
    if (_prefs == null) {
      await initialize();
    }
  }

  /// 从本地存储加载权限状态
  Future<void> _loadPremiumAccessStatus() async {
    try {
      _hasPremiumAccess = _prefs?.getBool(_premiumAccessKey) ?? false;
      debugPrint('📱 加载优质文章访问权限状态: $_hasPremiumAccess');
    } catch (e) {
      debugPrint('❌ 加载权限状态失败: $e');
      _hasPremiumAccess = false;
    }
  }

  /// 保存权限状态到本地存储
  Future<void> _savePremiumAccessStatus() async {
    try {
      await _prefs?.setBool(_premiumAccessKey, _hasPremiumAccess);
      debugPrint('💾 保存优质文章访问权限状态: $_hasPremiumAccess');
    } catch (e) {
      debugPrint('❌ 保存权限状态失败: $e');
    }
  }

  /// 从本地存储加载激活历史
  Future<void> _loadActivationHistory() async {
    try {
      final jsonString = _prefs?.getString(_activationHistoryKey);
      if (jsonString != null) {
        final List<dynamic> jsonList = json.decode(jsonString);
        _activationHistory = jsonList
            .map((json) => PremiumActivationRecord.fromJson(json))
            .toList();
        debugPrint('📱 加载激活历史记录: ${_activationHistory.length} 条');
      }
    } catch (e) {
      debugPrint('❌ 加载激活历史失败: $e');
      _activationHistory = [];
    }
  }

  /// 保存激活历史到本地存储
  Future<void> _saveActivationHistory() async {
    try {
      final jsonList = _activationHistory
          .map((record) => record.toJson())
          .toList();
      await _prefs?.setString(_activationHistoryKey, json.encode(jsonList));
      debugPrint('💾 保存激活历史记录: ${_activationHistory.length} 条');
    } catch (e) {
      debugPrint('❌ 保存激活历史失败: $e');
    }
  }
}

/// 优质文章激活类型
enum PremiumActivationType {
  purchase, // 购买激活
  gift, // 赠送激活
  promotion, // 促销激活
  trial, // 试用激活
}

/// 优质文章激活记录
class PremiumActivationRecord {
  final String id;
  final PremiumActivationType activationType;
  final DateTime activatedAt;
  final DateTime? expiresAt;
  final String? itemId;
  final Map<String, dynamic> metadata;

  PremiumActivationRecord({
    required this.id,
    required this.activationType,
    required this.activatedAt,
    this.expiresAt,
    this.itemId,
    this.metadata = const {},
  });

  /// 检查激活记录是否有效
  bool isValid(DateTime now) {
    if (expiresAt == null) return true; // 永久有效
    return now.isBefore(expiresAt!);
  }

  /// 获取剩余时间
  Duration? getRemainingTime() {
    if (expiresAt == null) return null; // 永久有效
    final now = DateTime.now();
    if (now.isAfter(expiresAt!)) return Duration.zero;
    return expiresAt!.difference(now);
  }

  /// 是否为永久权限
  bool get isPermanent => expiresAt == null;

  factory PremiumActivationRecord.fromJson(Map<String, dynamic> json) {
    return PremiumActivationRecord(
      id: json['id'] as String,
      activationType: PremiumActivationType.values.firstWhere(
        (e) => e.name == json['activationType'],
        orElse: () => PremiumActivationType.purchase,
      ),
      activatedAt: DateTime.parse(json['activatedAt'] as String),
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
      itemId: json['itemId'] as String?,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'activationType': activationType.name,
      'activatedAt': activatedAt.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'itemId': itemId,
      'metadata': metadata,
    };
  }
}

/// 文章访问结果
class ArticleAccessResult {
  final bool isAllowed;
  final bool isPremium;
  final String? deniedReason;
  final String? suggestedAction;
  final PremiumActivationRecord? activationRecord;

  ArticleAccessResult._({
    required this.isAllowed,
    this.isPremium = false,
    this.deniedReason,
    this.suggestedAction,
    this.activationRecord,
  });

  factory ArticleAccessResult.allowed({
    bool isPremium = false,
    PremiumActivationRecord? activationRecord,
  }) {
    return ArticleAccessResult._(
      isAllowed: true,
      isPremium: isPremium,
      activationRecord: activationRecord,
    );
  }

  factory ArticleAccessResult.denied({
    required String reason,
    String? suggestedAction,
  }) {
    return ArticleAccessResult._(
      isAllowed: false,
      deniedReason: reason,
      suggestedAction: suggestedAction,
    );
  }
}
