import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/item_effect_models.dart';
import '../store_page.dart';

/// 道具效果管理服务
/// 
/// 负责管理道具的使用、效果激活、持续时间跟踪等功能
class ItemEffectService extends ChangeNotifier {
  static final ItemEffectService _instance = ItemEffectService._internal();
  factory ItemEffectService() => _instance;
  ItemEffectService._internal();

  // 存储键
  static const String _activeEffectsKey = 'active_item_effects';
  static const String _userInventoryKey = 'user_inventory';

  // 活跃效果列表
  List<ActiveItemEffect> _activeEffects = [];
  
  // 用户背包
  Map<String, int> _userInventory = {};

  // 定时器用于清理过期效果
  Timer? _cleanupTimer;

  /// 获取活跃效果列表
  List<ActiveItemEffect> get activeEffects => List.unmodifiable(_activeEffects);

  /// 获取用户背包
  Map<String, int> get userInventory => Map.unmodifiable(_userInventory);

  /// 初始化服务
  Future<void> initialize() async {
    await _loadActiveEffects();
    await _loadUserInventory();
    _startCleanupTimer();
    debugPrint('🎯 道具效果服务已初始化');
  }

  /// 使用道具
  Future<ItemUsageResult> useItem(ShopItem item) async {
    try {
      // 检查背包中是否有该道具
      final currentCount = _userInventory[item.id] ?? 0;
      if (currentCount <= 0) {
        return ItemUsageResult.failure(
          '背包中没有${item.name}',
          errorCode: 'ITEM_NOT_FOUND',
        );
      }

      // 获取道具效果类型
      final effectType = ItemEffectConfig.getEffectType(item.id);
      if (effectType == null) {
        return ItemUsageResult.failure(
          '该道具暂不支持使用',
          errorCode: 'EFFECT_NOT_SUPPORTED',
        );
      }

      // 检查是否已有相同类型的效果激活
      final existingEffect = _activeEffects.firstWhere(
        (effect) => effect.effectType == effectType && !effect.isExpired,
        orElse: () => ActiveItemEffect(
          id: '',
          itemId: '',
          itemName: '',
          effectType: effectType,
          effectValue: 0,
          activatedAt: DateTime.now(),
        ),
      );

      if (existingEffect.id.isNotEmpty) {
        return ItemUsageResult.failure(
          '${item.name}效果已激活，请等待效果结束后再使用',
          errorCode: 'EFFECT_ALREADY_ACTIVE',
        );
      }

      // 创建新的活跃效果
      final newEffect = _createActiveEffect(item, effectType);
      
      // 添加到活跃效果列表
      _activeEffects.add(newEffect);
      
      // 从背包中移除道具
      _userInventory[item.id] = currentCount - 1;
      if (_userInventory[item.id]! <= 0) {
        _userInventory.remove(item.id);
      }

      // 保存数据
      await _saveActiveEffects();
      await _saveUserInventory();

      // 处理特殊效果
      await _handleSpecialEffects(newEffect);

      // 通知监听者
      notifyListeners();

      debugPrint('✅ 道具使用成功: ${item.name}');
      return ItemUsageResult.success(
        '${item.name}使用成功！效果已激活',
        effect: newEffect,
      );

    } catch (e) {
      debugPrint('❌ 道具使用失败: $e');
      return ItemUsageResult.failure(
        '道具使用失败，请稍后重试',
        errorCode: 'USAGE_ERROR',
      );
    }
  }

  /// 创建活跃效果
  ActiveItemEffect _createActiveEffect(ShopItem item, ItemEffectType effectType) {
    final now = DateTime.now();
    final effectValue = ItemEffectConfig.getEffectValue(item.id);
    
    // 计算过期时间
    DateTime? expiresAt;
    bool isPermanent = false;
    
    if (item.duration != null) {
      expiresAt = now.add(item.duration!);
    } else {
      // 某些道具是永久性的（如宫殿建造包、AI学习伙伴等）
      switch (item.id) {
        case '5': // 宫殿建造包
        case '13': // AI学习伙伴
          isPermanent = true;
          break;
        default:
          // 默认1小时有效期
          expiresAt = now.add(const Duration(hours: 1));
      }
    }

    return ActiveItemEffect(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      itemId: item.id,
      itemName: item.name,
      effectType: effectType,
      effectValue: effectValue,
      activatedAt: now,
      expiresAt: expiresAt,
      isPermanent: isPermanent,
      additionalData: {
        'itemDescription': item.description,
        'itemEffect': item.effect,
      },
    );
  }

  /// 处理特殊效果
  Future<void> _handleSpecialEffects(ActiveItemEffect effect) async {
    switch (effect.effectType) {
      case ItemEffectType.doNotDisturb:
        // 激活防打扰模式
        debugPrint('🔕 防打扰模式已激活');
        break;
      case ItemEffectType.contentAccess:
        // 激活内容访问权限
        debugPrint('📚 优质内容访问权限已激活');
        break;
      case ItemEffectType.aiAssistant:
        // 激活AI助手
        debugPrint('🤖 AI学习助手已激活');
        break;
      case ItemEffectType.memoryScenes:
        // 解锁记忆场景
        debugPrint('🏛️ 记忆场景已解锁');
        break;
      default:
        break;
    }
  }

  /// 检查是否有特定类型的活跃效果
  bool hasActiveEffect(ItemEffectType effectType) {
    return _activeEffects.any(
      (effect) => effect.effectType == effectType && !effect.isExpired,
    );
  }

  /// 获取特定类型的活跃效果
  ActiveItemEffect? getActiveEffect(ItemEffectType effectType) {
    try {
      return _activeEffects.firstWhere(
        (effect) => effect.effectType == effectType && !effect.isExpired,
      );
    } catch (e) {
      return null;
    }
  }

  /// 获取学习时长倍数
  double getStudyTimeMultiplier() {
    final effect = getActiveEffect(ItemEffectType.studyTimeMultiplier);
    return effect?.effectValue ?? 1.0;
  }

  /// 获取记忆效果倍数
  double getMemoryBoostMultiplier() {
    final effect = getActiveEffect(ItemEffectType.memoryBoost);
    return effect?.effectValue ?? 1.0;
  }

  /// 获取答题正确率加成
  double getAccuracyBoost() {
    final effect = getActiveEffect(ItemEffectType.accuracyBoost);
    return effect?.effectValue ?? 0.0;
  }

  /// 清理过期效果
  void _cleanupExpiredEffects() {
    final beforeCount = _activeEffects.length;
    _activeEffects.removeWhere((effect) => effect.isExpired);
    
    if (_activeEffects.length != beforeCount) {
      _saveActiveEffects();
      notifyListeners();
      debugPrint('🧹 清理了 ${beforeCount - _activeEffects.length} 个过期效果');
    }
  }

  /// 启动清理定时器
  void _startCleanupTimer() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _cleanupExpiredEffects();
    });
  }

  /// 加载活跃效果
  Future<void> _loadActiveEffects() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final effectsJson = prefs.getString(_activeEffectsKey);
      
      if (effectsJson != null) {
        final List<dynamic> effectsList = jsonDecode(effectsJson);
        _activeEffects = effectsList
            .map((json) => ActiveItemEffect.fromJson(json))
            .where((effect) => !effect.isExpired) // 过滤过期效果
            .toList();
      }
    } catch (e) {
      debugPrint('加载活跃效果失败: $e');
      _activeEffects = [];
    }
  }

  /// 保存活跃效果
  Future<void> _saveActiveEffects() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final effectsJson = jsonEncode(
        _activeEffects.map((effect) => effect.toJson()).toList(),
      );
      await prefs.setString(_activeEffectsKey, effectsJson);
    } catch (e) {
      debugPrint('保存活跃效果失败: $e');
    }
  }

  /// 加载用户背包
  Future<void> _loadUserInventory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final inventoryJson = prefs.getString(_userInventoryKey);
      
      if (inventoryJson != null) {
        final Map<String, dynamic> inventoryMap = jsonDecode(inventoryJson);
        _userInventory = inventoryMap.map(
          (key, value) => MapEntry(key, value as int),
        );
      }
    } catch (e) {
      debugPrint('加载用户背包失败: $e');
      _userInventory = {};
    }
  }

  /// 保存用户背包
  Future<void> _saveUserInventory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final inventoryJson = jsonEncode(_userInventory);
      await prefs.setString(_userInventoryKey, inventoryJson);
    } catch (e) {
      debugPrint('保存用户背包失败: $e');
    }
  }

  /// 更新背包（购买道具时调用）
  Future<void> updateInventory(String itemId, int count) async {
    _userInventory[itemId] = (_userInventory[itemId] ?? 0) + count;
    await _saveUserInventory();
    notifyListeners();
  }

  /// 销毁服务
  @override
  void dispose() {
    _cleanupTimer?.cancel();
    super.dispose();
  }
}
