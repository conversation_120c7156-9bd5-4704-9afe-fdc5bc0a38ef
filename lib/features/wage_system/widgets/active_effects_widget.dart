import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/item_effect_models.dart';
import '../providers/item_effect_provider.dart';

/// 活跃效果显示组件
class ActiveEffectsWidget extends ConsumerWidget {
  final bool showTitle;
  final bool isCompact;
  final int maxEffectsToShow;

  const ActiveEffectsWidget({
    super.key,
    this.showTitle = true,
    this.isCompact = false,
    this.maxEffectsToShow = 3,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final activeEffectsAsync = ref.watch(activeEffectsProvider);

    return activeEffectsAsync.when(
      data: (effects) {
        if (effects.isEmpty) {
          return const SizedBox.shrink();
        }

        final validEffects = effects.where((e) => !e.isExpired).toList();
        if (validEffects.isEmpty) {
          return const SizedBox.shrink();
        }

        final effectsToShow = validEffects.take(maxEffectsToShow).toList();

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFFE3E2E0)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.04),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (showTitle) ...[
                Row(
                  children: [
                    const Icon(
                      Icons.auto_awesome,
                      size: 16,
                      color: Color(0xFF2E7EED),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      '活跃效果',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF37352F),
                      ),
                    ),
                    const Spacer(),
                    if (validEffects.length > maxEffectsToShow)
                      Text(
                        '+${validEffects.length - maxEffectsToShow}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF9B9A97),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 8),
              ],
              ...effectsToShow.map(
                (effect) => _buildEffectItem(effect, isCompact),
              ),
            ],
          ),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (error, stack) => const SizedBox.shrink(),
    );
  }

  Widget _buildEffectItem(ActiveItemEffect effect, bool compact) {
    final remainingTime = effect.remainingTime;
    final effectDescription = ItemEffectConfig.getEffectDescription(
      effect.effectType,
      effect.effectValue,
    );

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: const Color(0xFFF7F6F3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getEffectColor(effect.effectType).withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: _getEffectColor(effect.effectType).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              _getEffectIcon(effect.effectType),
              size: 18,
              color: _getEffectColor(effect.effectType),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  effect.itemName,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (!compact) ...[
                  const SizedBox(height: 2),
                  Text(
                    effectDescription,
                    style: const TextStyle(
                      fontSize: 11,
                      color: Color(0xFF787774),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
          if (remainingTime != null && !effect.isPermanent) ...[
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getEffectColor(effect.effectType),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                _formatDuration(remainingTime),
                style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ] else if (effect.isPermanent) ...[
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: const Color(0xFFD9730D),
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Text(
                '永久',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getEffectColor(ItemEffectType effectType) {
    switch (effectType) {
      case ItemEffectType.studyTimeMultiplier:
        return const Color(0xFF2E7EED);
      case ItemEffectType.memoryBoost:
        return const Color(0xFF7C3AED);
      case ItemEffectType.accuracyBoost:
        return const Color(0xFF0F7B6C);
      case ItemEffectType.creativityBoost:
        return const Color(0xFFD9730D);
      case ItemEffectType.languageBoost:
        return const Color(0xFFE03E3E);
      case ItemEffectType.focusBoost:
        return const Color(0xFF2E7EED);
      case ItemEffectType.doNotDisturb:
        return const Color(0xFF9B9A97);
      case ItemEffectType.timePause:
        return const Color(0xFF787774);
      case ItemEffectType.contentAccess:
        return const Color(0xFF0F7B6C);
      case ItemEffectType.aiAssistant:
        return const Color(0xFFD9730D);
      case ItemEffectType.memoryScenes:
        return const Color(0xFF7C3AED);
      case ItemEffectType.aiAssociation:
        return const Color(0xFF2E7EED);
      case ItemEffectType.premiumArticleAccess:
        return const Color(0xFFD9730D);
    }
  }

  IconData _getEffectIcon(ItemEffectType effectType) {
    switch (effectType) {
      case ItemEffectType.studyTimeMultiplier:
        return Icons.local_drink;
      case ItemEffectType.memoryBoost:
        return Icons.psychology;
      case ItemEffectType.accuracyBoost:
        return Icons.speed;
      case ItemEffectType.creativityBoost:
        return Icons.lightbulb;
      case ItemEffectType.languageBoost:
        return Icons.translate;
      case ItemEffectType.focusBoost:
        return Icons.center_focus_strong;
      case ItemEffectType.doNotDisturb:
        return Icons.shield;
      case ItemEffectType.timePause:
        return Icons.pause_circle;
      case ItemEffectType.contentAccess:
        return Icons.menu_book_outlined;
      case ItemEffectType.aiAssistant:
        return Icons.smart_toy_outlined;
      case ItemEffectType.memoryScenes:
        return Icons.architecture;
      case ItemEffectType.aiAssociation:
        return Icons.connect_without_contact;
      case ItemEffectType.premiumArticleAccess:
        return Icons.workspace_premium;
    }
  }

  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h${duration.inMinutes.remainder(60)}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m';
    } else {
      return '${duration.inSeconds}s';
    }
  }
}

/// 紧凑型活跃效果指示器
class CompactActiveEffectsIndicator extends ConsumerWidget {
  const CompactActiveEffectsIndicator({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final activeEffectsAsync = ref.watch(activeEffectsProvider);

    return activeEffectsAsync.when(
      data: (effects) {
        final validEffects = effects.where((e) => !e.isExpired).toList();
        if (validEffects.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF2E7EED).withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.auto_awesome,
                size: 14,
                color: Color(0xFF2E7EED),
              ),
              const SizedBox(width: 4),
              Text(
                '${validEffects.length}个效果',
                style: const TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF2E7EED),
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (error, stack) => const SizedBox.shrink(),
    );
  }
}
