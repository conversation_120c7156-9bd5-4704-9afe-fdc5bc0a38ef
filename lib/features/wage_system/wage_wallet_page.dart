import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'models/wage_transaction.dart';

/// 工资钱包页面
///
/// 显示用户的学习收入统计和交易记录
/// 采用Notion风格设计，白纸黑字极简美学
class WageWalletPage extends StatefulWidget {
  const WageWalletPage({super.key});

  @override
  State<WageWalletPage> createState() => _WageWalletPageState();
}

class _WageWalletPageState extends State<WageWalletPage>
    with TickerProviderStateMixin {
  // 动画控制器
  late AnimationController _countAnimationController;
  late Animation<double> _countAnimation;

  // 筛选状态
  TimeFilter _selectedTimeFilter = TimeFilter.all;

  // 模拟交易记录
  List<WageTransaction> _transactions = [];

  // 统计数据
  double _totalWage = 0;
  double _todayWage = 0;
  double _thisWeekWage = 0;
  double _thisMonthWage = 0;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _loadTransactions();
    _calculateStatistics();
  }

  @override
  void dispose() {
    _countAnimationController.dispose();
    super.dispose();
  }

  /// 初始化动画
  void _initAnimations() {
    _countAnimationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _countAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _countAnimationController,
        curve: Curves.easeOutCubic,
      ),
    );

    // 启动动画
    _countAnimationController.forward();
  }

  /// 加载交易记录（模拟数据）
  void _loadTransactions() {
    final now = DateTime.now();
    _transactions = [
      WageTransaction(
        id: '1',
        amount: 200.0,
        type: TransactionType.studyIncome,
        description: '算法复习：动态规划 - 120分钟学习',
        timestamp: now.subtract(const Duration(hours: 1)),
        relatedTaskId: '1',
      ),
      WageTransaction(
        id: '2',
        amount: 100.0,
        type: TransactionType.studyIncome,
        description: '英语单词记忆 - 60分钟学习',
        timestamp: now.subtract(const Duration(hours: 3)),
        relatedTaskId: '2',
      ),
      WageTransaction(
        id: '3',
        amount: 150.0,
        type: TransactionType.studyIncome,
        description: '数学练习：微积分 - 90分钟学习',
        timestamp: now.subtract(const Duration(days: 1)),
        relatedTaskId: '3',
      ),
      WageTransaction(
        id: '4',
        amount: 50.0,
        type: TransactionType.bonus,
        description: '连续学习7天奖励',
        timestamp: now.subtract(const Duration(days: 2)),
      ),
      WageTransaction(
        id: '5',
        amount: -30.0,
        type: TransactionType.itemPurchase,
        description: '购买专注药水道具',
        timestamp: now.subtract(const Duration(days: 3)),
      ),
      WageTransaction(
        id: '6',
        amount: 125.0,
        type: TransactionType.studyIncome,
        description: '政治理论学习 - 75分钟学习',
        timestamp: now.subtract(const Duration(days: 4)),
        relatedTaskId: '4',
      ),
      WageTransaction(
        id: '7',
        amount: 100.0,
        type: TransactionType.achievement,
        description: '完成每日学习目标奖励',
        timestamp: now.subtract(const Duration(days: 5)),
      ),
    ];
  }

  /// 计算统计数据
  void _calculateStatistics() {
    final now = DateTime.now();
    final startOfToday = DateTime(now.year, now.month, now.day);
    final startOfWeek = startOfToday.subtract(Duration(days: now.weekday - 1));
    final startOfMonth = DateTime(now.year, now.month, 1);

    _totalWage = _transactions.fold(
      0.0,
      (sum, transaction) => sum + transaction.amount,
    );

    _todayWage = _transactions
        .where((t) => t.timestamp.isAfter(startOfToday))
        .fold(0.0, (sum, transaction) => sum + transaction.amount);

    _thisWeekWage = _transactions
        .where((t) => t.timestamp.isAfter(startOfWeek))
        .fold(0.0, (sum, transaction) => sum + transaction.amount);

    _thisMonthWage = _transactions
        .where((t) => t.timestamp.isAfter(startOfMonth))
        .fold(0.0, (sum, transaction) => sum + transaction.amount);
  }

  /// 获取筛选后的交易记录
  List<WageTransaction> get _filteredTransactions {
    final now = DateTime.now();
    DateTime? startDate;

    switch (_selectedTimeFilter) {
      case TimeFilter.today:
        startDate = DateTime(now.year, now.month, now.day);
        break;
      case TimeFilter.thisWeek:
        startDate = now.subtract(Duration(days: now.weekday - 1));
        startDate = DateTime(startDate.year, startDate.month, startDate.day);
        break;
      case TimeFilter.thisMonth:
        startDate = DateTime(now.year, now.month, 1);
        break;
      case TimeFilter.all:
        return _transactions;
    }

    return _transactions.where((t) => t.timestamp.isAfter(startDate!)).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF37352F)),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          '工资钱包',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.store, color: Color(0xFF9B9A97)),
            onPressed: () => context.push('/store'),
          ),
          IconButton(
            icon: const Icon(
              Icons.analytics_outlined,
              color: Color(0xFF9B9A97),
            ),
            onPressed: () => _showReportDialog(),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        color: const Color(0xFF2E7EED),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 总工资卡片
              _buildTotalWageCard(),
              const SizedBox(height: 24),

              // 统计概览
              _buildStatisticsOverview(),
              const SizedBox(height: 24),

              // 交易记录标题和筛选
              _buildTransactionHeader(),
              const SizedBox(height: 16),

              // 交易记录列表
              _buildTransactionList(),

              const SizedBox(height: 100), // 底部留白
            ],
          ),
        ),
      ),
    );
  }

  /// 构建总工资卡片
  Widget _buildTotalWageCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF2E7EED),
            const Color(0xFF2E7EED).withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF2E7EED).withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.account_balance_wallet,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF0F7B6C),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.trending_up,
                      color: Colors.white,
                      size: 14,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '+${(_todayWage).toStringAsFixed(0)}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            '总工资',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          AnimatedBuilder(
            animation: _countAnimation,
            builder: (context, child) {
              final currentValue = _totalWage * _countAnimation.value;
              return Text(
                '¥${currentValue.toStringAsFixed(0)}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 48,
                  fontWeight: FontWeight.w300,
                  fontFamily: 'monospace',
                ),
              );
            },
          ),
          const SizedBox(height: 12),
          Text(
            '今日收入 ¥${_todayWage.toStringAsFixed(0)}',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建统计概览
  Widget _buildStatisticsOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '收入概览',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                title: '今日',
                amount: _todayWage,
                icon: Icons.today,
                color: const Color(0xFF2E7EED),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                title: '本周',
                amount: _thisWeekWage,
                icon: Icons.date_range,
                color: const Color(0xFF7C3AED),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                title: '本月',
                amount: _thisMonthWage,
                icon: Icons.calendar_month,
                color: const Color(0xFF0F7B6C),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建统计卡片
  Widget _buildStatCard({
    required String title,
    required double amount,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(height: 12),
          Text(
            '¥${amount.toStringAsFixed(0)}',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Color(0xFF9B9A97)),
          ),
        ],
      ),
    );
  }

  /// 构建交易记录标题和筛选
  Widget _buildTransactionHeader() {
    return Row(
      children: [
        const Text(
          '交易记录',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
        const Spacer(),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 4),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: DropdownButton<TimeFilter>(
            value: _selectedTimeFilter,
            underline: const SizedBox.shrink(),
            icon: const Icon(Icons.keyboard_arrow_down, size: 18),
            style: const TextStyle(fontSize: 14, color: Color(0xFF37352F)),
            items: TimeFilter.values.map((filter) {
              return DropdownMenuItem(
                value: filter,
                child: Text(filter.displayName),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedTimeFilter = value!;
              });
            },
          ),
        ),
      ],
    );
  }

  /// 构建交易记录列表
  Widget _buildTransactionList() {
    final filteredTransactions = _filteredTransactions;

    if (filteredTransactions.isEmpty) {
      return _buildEmptyState();
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: filteredTransactions.length,
        separatorBuilder: (context, index) =>
            const Divider(height: 1, color: Color(0xFFEAE9E7)),
        itemBuilder: (context, index) {
          final transaction = filteredTransactions[index];
          return TransactionItem(
            transaction: transaction,
            onTap: () => _showTransactionDetail(transaction),
          );
        },
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(48),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: const Color(0xFF9B9A97).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(40),
            ),
            child: const Icon(
              Icons.receipt_long,
              size: 40,
              color: Color(0xFF9B9A97),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            '暂无${_selectedTimeFilter.displayName}交易记录',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '开始学习来获得你的第一笔收入吧',
            style: TextStyle(fontSize: 14, color: Color(0xFF9B9A97)),
          ),
        ],
      ),
    );
  }

  /// 显示交易详情
  void _showTransactionDetail(WageTransaction transaction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(transaction.type.displayName),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow(
              '金额',
              '${transaction.amount >= 0 ? '+' : ''}¥${transaction.amount.toStringAsFixed(1)}',
              transaction.amount >= 0
                  ? const Color(0xFF0F7B6C)
                  : const Color(0xFFE03E3E),
            ),
            const SizedBox(height: 8),
            _buildDetailRow(
              '时间',
              DateFormat('yyyy年MM月dd日 HH:mm').format(transaction.timestamp),
            ),
            const SizedBox(height: 8),
            _buildDetailRow('描述', transaction.description),
            if (transaction.relatedTaskId != null) ...[
              const SizedBox(height: 8),
              _buildDetailRow('关联任务', '学习任务 #${transaction.relatedTaskId}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 详情行
  Widget _buildDetailRow(String label, String value, [Color? valueColor]) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 50,
          child: Text(
            '$label:',
            style: const TextStyle(fontSize: 14, color: Color(0xFF9B9A97)),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: valueColor ?? const Color(0xFF37352F),
              fontWeight: valueColor != null
                  ? FontWeight.w600
                  : FontWeight.normal,
            ),
          ),
        ),
      ],
    );
  }

  /// 显示报告对话框
  void _showReportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('📊 收入报告'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '💰 总收入统计',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              Text('• 累计总收入：¥${_totalWage.toStringAsFixed(1)}'),
              Text('• 今日收入：¥${_todayWage.toStringAsFixed(1)}'),
              Text('• 本周收入：¥${_thisWeekWage.toStringAsFixed(1)}'),
              Text('• 本月收入：¥${_thisMonthWage.toStringAsFixed(1)}'),

              const SizedBox(height: 16),
              const Text(
                '⏰ 时薪信息',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const Text('• 当前时薪：¥200/小时'),
              Text('• 累计学习时长：${(_totalWage / 200).toStringAsFixed(1)}小时'),

              const SizedBox(height: 16),
              const Text(
                '📈 交易统计',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              Text('• 总交易数：${_transactions.length}笔'),
              Text(
                '• 学习收入：${_transactions.where((t) => t.type == TransactionType.studyIncome).length}笔',
              ),
              Text(
                '• 奖励收入：${_transactions.where((t) => t.type == TransactionType.bonus).length}笔',
              ),

              const SizedBox(height: 16),
              const Text(
                '💡 小贴士',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              const Text('• 坚持每日学习可获得额外奖励'),
              const Text('• 完成学习任务自动计算工资'),
              const Text('• 使用道具可提升学习效率'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }

  /// 处理下拉刷新
  Future<void> _handleRefresh() async {
    await Future.delayed(const Duration(seconds: 1));

    // 重新计算统计数据
    setState(() {
      _calculateStatistics();
    });

    // 重启动画
    _countAnimationController.reset();
    _countAnimationController.forward();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('数据已更新'),
          backgroundColor: Color(0xFF0F7B6C),
          duration: Duration(seconds: 1),
        ),
      );
    }
  }
}

/// 交易记录组件
class TransactionItem extends StatelessWidget {
  final WageTransaction transaction;
  final VoidCallback onTap;

  const TransactionItem({
    super.key,
    required this.transaction,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isIncome = transaction.amount >= 0;
    final color = isIncome ? const Color(0xFF0F7B6C) : const Color(0xFFE03E3E);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // 图标
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: transaction.type.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  transaction.type.icon,
                  color: transaction.type.color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),

              // 内容
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      transaction.type.displayName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF37352F),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      transaction.description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF787774),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      DateFormat('MM-dd HH:mm').format(transaction.timestamp),
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF9B9A97),
                      ),
                    ),
                  ],
                ),
              ),

              // 金额
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${isIncome ? '+' : ''}¥${transaction.amount.abs().toStringAsFixed(0)}',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: color,
                    ),
                  ),
                  if (isIncome)
                    Container(
                      margin: const EdgeInsets.only(top: 4),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Text(
                        '收入',
                        style: TextStyle(
                          fontSize: 10,
                          color: Color(0xFF0F7B6C),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 时间筛选枚举
enum TimeFilter {
  today, // 今日
  thisWeek, // 本周
  thisMonth, // 本月
  all; // 全部

  String get displayName {
    switch (this) {
      case TimeFilter.today:
        return '今日';
      case TimeFilter.thisWeek:
        return '本周';
      case TimeFilter.thisMonth:
        return '本月';
      case TimeFilter.all:
        return '全部';
    }
  }
}
