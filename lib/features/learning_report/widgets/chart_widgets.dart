import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/learning_report_models.dart';

/// 学习时长趋势折线图
class StudyTimeTrendChart extends StatelessWidget {
  final List<DailyStudyData> data;
  final String title;
  final Color lineColor;

  const StudyTimeTrendChart({
    super.key,
    required this.data,
    this.title = '学习时长趋势',
    this.lineColor = const Color(0xFF2E7EED),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: false,
                  horizontalInterval: 1,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: const Color(0xFFE3E2E0),
                      strokeWidth: 1,
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      interval: 1,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        final index = value.toInt();
                        if (index >= 0 && index < data.length) {
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              data[index].formattedDate,
                              style: const TextStyle(
                                color: Color(0xFF9B9A97),
                                fontSize: 12,
                              ),
                            ),
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      interval: 1,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        return Text(
                          '${value.toInt()}h',
                          style: const TextStyle(
                            color: Color(0xFF9B9A97),
                            fontSize: 12,
                          ),
                        );
                      },
                      reservedSize: 42,
                    ),
                  ),
                ),
                borderData: FlBorderData(
                  show: true,
                  border: Border.all(color: const Color(0xFFE3E2E0), width: 1),
                ),
                minX: 0,
                maxX: data.length.toDouble() - 1,
                minY: 0,
                maxY: _getMaxStudyHours() + 1,
                lineBarsData: [
                  LineChartBarData(
                    spots: data.asMap().entries.map((entry) {
                      return FlSpot(
                        entry.key.toDouble(),
                        entry.value.studyHours,
                      );
                    }).toList(),
                    isCurved: true,
                    color: lineColor,
                    barWidth: 3,
                    isStrokeCapRound: true,
                    dotData: FlDotData(
                      show: true,
                      getDotPainter: (spot, percent, barData, index) {
                        return FlDotCirclePainter(
                          radius: 4,
                          color: lineColor,
                          strokeWidth: 2,
                          strokeColor: Colors.white,
                        );
                      },
                    ),
                    belowBarData: BarAreaData(
                      show: true,
                      color: lineColor.withValues(alpha: 0.1),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  double _getMaxStudyHours() {
    if (data.isEmpty) return 8.0;
    return data.map((d) => d.studyHours).reduce((a, b) => a > b ? a : b);
  }
}

/// 学科分布饼图
class SubjectDistributionPieChart extends StatelessWidget {
  final List<SubjectDistribution> data;
  final String title;

  const SubjectDistributionPieChart({
    super.key,
    required this.data,
    this.title = '学科分布',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: PieChart(
                    PieChartData(
                      sectionsSpace: 2,
                      centerSpaceRadius: 40,
                      sections: _generatePieSections(),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(flex: 1, child: _buildLegend()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<PieChartSectionData> _generatePieSections() {
    final totalMinutes = data.fold<int>(
      0,
      (sum, item) => sum + item.studyMinutes,
    );

    return data.map((item) {
      final percentage = item.getPercentage(totalMinutes);
      return PieChartSectionData(
        color: _parseColor(item.colorHex),
        value: percentage,
        title: '${percentage.toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  Widget _buildLegend() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: data.map((item) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: _parseColor(item.colorHex),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  item.subject,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF37352F),
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Color _parseColor(String colorHex) {
    try {
      return Color(int.parse(colorHex.replaceFirst('#', '0xFF')));
    } catch (e) {
      return const Color(0xFF9B9A97);
    }
  }
}

/// ROI趋势双线图
class ROITrendChart extends StatelessWidget {
  final List<ROITrendData> data;
  final String title;

  const ROITrendChart({super.key, required this.data, this.title = '学习ROI趋势'});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              _buildLegendItem('实际ROI', const Color(0xFF2E7EED)),
              const SizedBox(width: 16),
              _buildLegendItem('7天均线', const Color(0xFF0F7B6C)),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: false,
                  horizontalInterval: _getROIInterval(),
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: const Color(0xFFE3E2E0),
                      strokeWidth: 1,
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 30,
                      interval: 1,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        final index = value.toInt();
                        if (index >= 0 && index < data.length) {
                          final date = data[index].date;
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              '${date.month}/${date.day}',
                              style: const TextStyle(
                                color: Color(0xFF9B9A97),
                                fontSize: 12,
                              ),
                            ),
                          );
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      interval: _getROIInterval(),
                      getTitlesWidget: (double value, TitleMeta meta) {
                        // 只显示关键的标签值，防止过度拥挤
                        if (!_shouldShowLabel(value)) {
                          return const SizedBox.shrink();
                        }

                        // 格式化ROI标签，确保清晰可读
                        return Container(
                          padding: const EdgeInsets.only(right: 10),
                          child: Text(
                            _formatROILabel(value),
                            style: const TextStyle(
                              color: Color(0xFF37352F), // 使用更深的颜色提高对比度
                              fontSize: 12, // 增加字体大小到12px
                              fontWeight: FontWeight.w600, // 增加字体粗细
                              height: 1.2, // 设置行高，防止文字重叠
                            ),
                            textAlign: TextAlign.right,
                          ),
                        );
                      },
                      reservedSize: 55, // 进一步增加预留空间
                    ),
                  ),
                ),
                borderData: FlBorderData(
                  show: true,
                  border: Border.all(color: const Color(0xFFE3E2E0), width: 1),
                ),
                minX: 0,
                maxX: data.length.toDouble() - 1,
                minY: _getMinROI(),
                maxY: _getMaxROI(),
                lineBarsData: [
                  // 实际ROI线
                  LineChartBarData(
                    spots: data.asMap().entries.map((entry) {
                      return FlSpot(entry.key.toDouble(), entry.value.roiValue);
                    }).toList(),
                    isCurved: true,
                    color: const Color(0xFF2E7EED),
                    barWidth: 3,
                    isStrokeCapRound: true,
                    dotData: FlDotData(show: false),
                  ),
                  // 移动平均线
                  LineChartBarData(
                    spots: data.asMap().entries.map((entry) {
                      return FlSpot(
                        entry.key.toDouble(),
                        entry.value.movingAverage,
                      );
                    }).toList(),
                    isCurved: true,
                    color: const Color(0xFF0F7B6C),
                    barWidth: 2,
                    isStrokeCapRound: true,
                    dotData: FlDotData(show: false),
                    dashArray: [5, 5],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(width: 12, height: 2, color: color),
        const SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Color(0xFF9B9A97)),
        ),
      ],
    );
  }

  /// 获取ROI数据的最小值（用于Y轴下限）
  double _getMinROI() {
    if (data.isEmpty) return 0.0;
    final minActual = data
        .map((d) => d.roiValue)
        .reduce((a, b) => a < b ? a : b);
    final minAverage = data
        .map((d) => d.movingAverage)
        .reduce((a, b) => a < b ? a : b);
    final minValue = [minActual, minAverage].reduce((a, b) => a < b ? a : b);

    // 确保最小值不小于0，并留出一些边距
    final adjustedMin = (minValue * 0.9).clamp(0.0, double.infinity);
    return _roundToNiceNumber(adjustedMin, false);
  }

  /// 获取ROI数据的最大值（用于Y轴上限）
  double _getMaxROI() {
    if (data.isEmpty) return 100.0;
    final maxActual = data
        .map((d) => d.roiValue)
        .reduce((a, b) => a > b ? a : b);
    final maxAverage = data
        .map((d) => d.movingAverage)
        .reduce((a, b) => a > b ? a : b);
    final maxValue = [maxActual, maxAverage].reduce((a, b) => a > b ? a : b);

    // 在最大值基础上增加10%的边距，确保数据点不会贴着顶部
    final adjustedMax = maxValue * 1.1;
    return _roundToNiceNumber(adjustedMax, true);
  }

  /// 获取合适的ROI间隔值
  /// 优化间隔算法，确保标签之间有足够的垂直空间，防止重叠
  double _getROIInterval() {
    final range = _getMaxROI() - _getMinROI();

    // 计算图表高度可容纳的最大标签数量
    // 图表高度200px，每个标签至少需要25px垂直空间
    const chartHeight = 200.0;
    const minLabelSpacing = 25.0;
    final maxLabels = (chartHeight / minLabelSpacing).floor();

    // 根据最大标签数量计算最小间隔
    final minInterval = range / (maxLabels - 1);

    // 选择合适的"好看"间隔值，确保标签不会过密
    if (range <= 30) {
      return _selectNiceInterval(minInterval, [10.0, 15.0, 20.0]);
    }
    if (range <= 60) {
      return _selectNiceInterval(minInterval, [15.0, 20.0, 30.0]);
    }
    if (range <= 120) {
      return _selectNiceInterval(minInterval, [20.0, 25.0, 30.0, 40.0]);
    }
    if (range <= 200) {
      return _selectNiceInterval(minInterval, [30.0, 40.0, 50.0]);
    }
    return _selectNiceInterval(minInterval, [50.0, 75.0, 100.0]);
  }

  /// 从候选间隔中选择第一个大于等于最小间隔的值
  double _selectNiceInterval(double minInterval, List<double> candidates) {
    for (final candidate in candidates) {
      if (candidate >= minInterval) {
        return candidate;
      }
    }
    return candidates.last;
  }

  /// 判断是否应该显示该标签值
  /// 进一步限制标签数量，确保不会过度拥挤
  bool _shouldShowLabel(double value) {
    final minValue = _getMinROI();
    final maxValue = _getMaxROI();
    final interval = _getROIInterval();

    // 确保显示最小值和最大值
    if (value == minValue || value == maxValue) {
      return true;
    }

    // 只显示符合间隔的值
    final normalizedValue = value - minValue;
    final remainder = normalizedValue % interval;

    // 允许小的浮点数误差
    return remainder.abs() < 0.01 || (interval - remainder).abs() < 0.01;
  }

  /// 格式化ROI标签显示
  String _formatROILabel(double value) {
    // 如果是整数，不显示小数点
    if (value == value.toInt()) {
      return '${value.toInt()}';
    }
    // 显示一位小数
    return value.toStringAsFixed(1);
  }

  /// 将数值四舍五入到合适的"好看"数字
  double _roundToNiceNumber(double value, bool ceiling) {
    if (value <= 0) return 0.0;

    final magnitude = math.pow(10, (math.log(value) / math.ln10).floor());
    final normalized = value / magnitude;

    double nice;
    if (normalized <= 1) {
      nice = 1;
    } else if (normalized <= 2) {
      nice = 2;
    } else if (normalized <= 5) {
      nice = 5;
    } else {
      nice = 10;
    }

    final result = nice * magnitude;
    return ceiling ? result : (result > value ? result - magnitude : result);
  }
}

/// 专注度分析柱状图
class FocusAnalysisBarChart extends StatelessWidget {
  final List<FocusAnalysisData> data;
  final String title;

  const FocusAnalysisBarChart({
    super.key,
    required this.data,
    this.title = '专注度分析',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              _buildLegendItem('专注时间', const Color(0xFF0F7B6C)),
              const SizedBox(width: 16),
              _buildLegendItem('干扰时间', const Color(0xFFE03E3E)),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: BarChart(
              BarChartData(
                alignment: BarChartAlignment.spaceAround,
                maxY: _getMaxMinutes() + 60,
                barTouchData: BarTouchData(
                  touchTooltipData: BarTouchTooltipData(
                    getTooltipItem: (group, groupIndex, rod, rodIndex) {
                      final data = this.data[groupIndex];
                      final isFocus = rodIndex == 0;
                      final minutes = isFocus
                          ? data.focusMinutes
                          : data.distractionMinutes;
                      final label = isFocus ? '专注' : '干扰';
                      return BarTooltipItem(
                        '$label: $minutes分钟',
                        const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                  ),
                ),
                titlesData: FlTitlesData(
                  show: true,
                  rightTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: const AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        final index = value.toInt();
                        if (index >= 0 && index < data.length) {
                          final date = data[index].date;
                          return SideTitleWidget(
                            axisSide: meta.axisSide,
                            child: Text(
                              '${date.month}/${date.day}',
                              style: const TextStyle(
                                color: Color(0xFF9B9A97),
                                fontSize: 12,
                              ),
                            ),
                          );
                        }
                        return const Text('');
                      },
                      reservedSize: 30,
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      reservedSize: 42,
                      getTitlesWidget: (double value, TitleMeta meta) {
                        return Text(
                          '${value.toInt()}m',
                          style: const TextStyle(
                            color: Color(0xFF9B9A97),
                            fontSize: 12,
                          ),
                        );
                      },
                    ),
                  ),
                ),
                borderData: FlBorderData(
                  show: true,
                  border: Border.all(color: const Color(0xFFE3E2E0), width: 1),
                ),
                barGroups: data.asMap().entries.map((entry) {
                  return BarChartGroupData(
                    x: entry.key,
                    barRods: [
                      BarChartRodData(
                        toY: entry.value.focusMinutes.toDouble(),
                        color: const Color(0xFF0F7B6C),
                        width: 12,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(4),
                          topRight: Radius.circular(4),
                        ),
                      ),
                      BarChartRodData(
                        toY: entry.value.distractionMinutes.toDouble(),
                        color: const Color(0xFFE03E3E),
                        width: 12,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(4),
                          topRight: Radius.circular(4),
                        ),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Color(0xFF9B9A97)),
        ),
      ],
    );
  }

  double _getMaxMinutes() {
    if (data.isEmpty) return 240.0;
    return data
        .map((d) => (d.focusMinutes + d.distractionMinutes).toDouble())
        .reduce((a, b) => a > b ? a : b);
  }
}

/// 学习效率热力图
class EfficiencyHeatmapChart extends StatelessWidget {
  final List<EfficiencyHeatmapData> data;
  final String title;

  const EfficiencyHeatmapChart({
    super.key,
    required this.data,
    this.title = '学习效率热力图',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '颜色越深表示学习效率越高',
            style: TextStyle(
              fontSize: 12,
              color: const Color(0xFF9B9A97).withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(height: 200, child: _buildHeatmap()),
        ],
      ),
    );
  }

  Widget _buildHeatmap() {
    // 创建7x24的网格（7天x24小时）
    final Map<String, EfficiencyHeatmapData> dataMap = {};
    for (final item in data) {
      final key = '${item.dayOfWeek}_${item.hour}';
      dataMap[key] = item;
    }

    return Column(
      children: [
        // 小时标签
        Row(
          children: [
            const SizedBox(width: 40), // 为星期标签留空间
            ...List.generate(24, (hour) {
              if (hour % 4 == 0) {
                return Expanded(
                  child: Text(
                    hour.toString().padLeft(2, '0'),
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 10,
                      color: Color(0xFF9B9A97),
                    ),
                  ),
                );
              }
              return const Expanded(child: SizedBox());
            }),
          ],
        ),
        const SizedBox(height: 4),
        // 热力图网格
        Expanded(
          child: Row(
            children: [
              // 星期标签
              SizedBox(
                width: 40,
                child: Column(
                  children: List.generate(7, (day) {
                    final dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
                    return Expanded(
                      child: Center(
                        child: Text(
                          dayNames[day],
                          style: const TextStyle(
                            fontSize: 10,
                            color: Color(0xFF9B9A97),
                          ),
                        ),
                      ),
                    );
                  }),
                ),
              ),
              // 热力图格子
              Expanded(
                child: Column(
                  children: List.generate(7, (dayIndex) {
                    final dayOfWeek = dayIndex + 1;
                    return Expanded(
                      child: Row(
                        children: List.generate(24, (hour) {
                          final key = '${dayOfWeek}_$hour';
                          final heatmapData = dataMap[key];
                          final efficiency =
                              heatmapData?.efficiencyValue ?? 0.0;

                          return Expanded(
                            child: Container(
                              margin: const EdgeInsets.all(0.5),
                              decoration: BoxDecoration(
                                color: _getHeatmapColor(efficiency),
                                borderRadius: BorderRadius.circular(2),
                              ),
                              child: AspectRatio(
                                aspectRatio: 1,
                                child: Container(),
                              ),
                            ),
                          );
                        }),
                      ),
                    );
                  }),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        // 颜色图例
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              '低',
              style: TextStyle(fontSize: 10, color: Color(0xFF9B9A97)),
            ),
            const SizedBox(width: 4),
            ...List.generate(5, (index) {
              final efficiency = index * 25.0;
              return Container(
                width: 12,
                height: 12,
                margin: const EdgeInsets.symmetric(horizontal: 1),
                decoration: BoxDecoration(
                  color: _getHeatmapColor(efficiency),
                  borderRadius: BorderRadius.circular(2),
                ),
              );
            }),
            const SizedBox(width: 4),
            const Text(
              '高',
              style: TextStyle(fontSize: 10, color: Color(0xFF9B9A97)),
            ),
          ],
        ),
      ],
    );
  }

  Color _getHeatmapColor(double efficiency) {
    if (efficiency == 0) {
      return const Color(0xFFF7F6F3); // 无数据时的背景色
    }

    // 根据效率值生成颜色，从浅绿到深绿
    final intensity = (efficiency / 100).clamp(0.0, 1.0);
    return Color.lerp(
      const Color(0xFFE8F5E8), // 浅绿
      const Color(0xFF0F7B6C), // 深绿
      intensity,
    )!;
  }
}
