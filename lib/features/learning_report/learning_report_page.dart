import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'models/learning_report_models.dart';
import 'services/learning_report_service.dart';
import 'services/learning_report_export_service.dart';
import 'widgets/chart_widgets.dart';
import '../study_time/providers/study_time_providers.dart';

/// 学习报告页面
///
/// 提供全面的学习数据分析和可视化展示
/// 采用Notion风格设计，简洁清晰的卡片式布局
class LearningReportPage extends ConsumerStatefulWidget {
  const LearningReportPage({super.key});

  @override
  ConsumerState<LearningReportPage> createState() => _LearningReportPageState();
}

class _LearningReportPageState extends ConsumerState<LearningReportPage>
    with TickerProviderStateMixin {
  late LearningReportService _reportService;
  late LearningReportExportService _exportService;
  late TabController _tabController;

  // 用于截图的GlobalKey
  final GlobalKey _repaintBoundaryKey = GlobalKey();

  TimeRange _selectedTimeRange = TimeRange.thisWeek;
  LearningReportData? _reportData;
  LearningHabitAnalysis? _habitAnalysis;
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _exportService = LearningReportExportService();
    _initializeService();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _initializeService() {
    final studyTimeService = ref.read(studyTimeStatisticsServiceProvider);
    _reportService = LearningReportService(studyTimeService);
    _loadReportData();
  }

  Future<void> _loadReportData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final reportData = await _reportService.generateReport(
        _selectedTimeRange,
      );
      final habitAnalysis = await _reportService.generateHabitAnalysis();

      setState(() {
        _reportData = reportData;
        _habitAnalysis = habitAnalysis;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.arrow_back, color: Color(0xFF37352F)),
        ),
        title: const Text(
          '学习报告',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _showExportOptions,
            icon: const Icon(Icons.share_outlined, color: Color(0xFF37352F)),
          ),
          IconButton(
            onPressed: _loadReportData,
            icon: const Icon(Icons.refresh_outlined, color: Color(0xFF37352F)),
          ),
        ],
      ),
      body: Column(
        children: [
          // 时间范围选择器
          _buildTimeRangeSelector(),

          // 主要内容区域
          Expanded(
            child: _isLoading
                ? _buildLoadingState()
                : _error != null
                ? _buildErrorState()
                : RepaintBoundary(
                    key: _repaintBoundaryKey,
                    child: _buildContentTabs(),
                  ),
          ),
        ],
      ),
    );
  }

  /// 构建时间范围选择器
  Widget _buildTimeRangeSelector() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          const Text(
            '时间范围：',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: TimeRange.values.map((range) {
                  final isSelected = range == _selectedTimeRange;
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(range.label),
                      selected: isSelected,
                      onSelected: (selected) {
                        if (selected) {
                          setState(() {
                            _selectedTimeRange = range;
                          });
                          _loadReportData();
                        }
                      },
                      backgroundColor: Colors.white,
                      selectedColor: const Color(
                        0xFF2E7EED,
                      ).withValues(alpha: 0.1),
                      checkmarkColor: const Color(0xFF2E7EED),
                      labelStyle: TextStyle(
                        color: isSelected
                            ? const Color(0xFF2E7EED)
                            : const Color(0xFF9B9A97),
                        fontSize: 12,
                        fontWeight: isSelected
                            ? FontWeight.w600
                            : FontWeight.normal,
                      ),
                      side: BorderSide(
                        color: isSelected
                            ? const Color(0xFF2E7EED)
                            : const Color(0xFFE3E2E0),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建内容标签页
  Widget _buildContentTabs() {
    return Column(
      children: [
        Container(
          color: Colors.white,
          child: TabBar(
            controller: _tabController,
            labelColor: const Color(0xFF2E7EED),
            unselectedLabelColor: const Color(0xFF9B9A97),
            indicatorColor: const Color(0xFF2E7EED),
            indicatorWeight: 2,
            labelStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
            ),
            tabs: const [
              Tab(text: '数据概览'),
              Tab(text: '趋势分析'),
              Tab(text: '习惯分析'),
            ],
          ),
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildTrendAnalysisTab(),
              _buildHabitAnalysisTab(),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建概览标签页
  Widget _buildOverviewTab() {
    if (_reportData == null) return const SizedBox();

    return RefreshIndicator(
      onRefresh: _loadReportData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 核心指标卡片
            _buildCoreMetricsCard(),

            const SizedBox(height: 16),

            // 学科分布饼图
            SubjectDistributionPieChart(data: _reportData!.subjectDistribution),

            const SizedBox(height: 16),

            // 学习时长趋势
            StudyTimeTrendChart(data: _reportData!.dailyStudyData),
          ],
        ),
      ),
    );
  }

  /// 构建趋势分析标签页
  Widget _buildTrendAnalysisTab() {
    if (_reportData == null) return const SizedBox();

    return RefreshIndicator(
      onRefresh: _loadReportData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // ROI趋势图
            ROITrendChart(data: _reportData!.roiTrendData),

            const SizedBox(height: 16),

            // 专注度分析
            FocusAnalysisBarChart(data: _reportData!.focusAnalysisData),

            const SizedBox(height: 16),

            // 学习效率热力图
            EfficiencyHeatmapChart(data: _reportData!.efficiencyHeatmapData),
          ],
        ),
      ),
    );
  }

  /// 构建习惯分析标签页
  Widget _buildHabitAnalysisTab() {
    if (_habitAnalysis == null) return const SizedBox();

    return RefreshIndicator(
      onRefresh: _loadReportData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildHabitSummaryCard(),
            const SizedBox(height: 16),
            _buildLearningPatternsCard(),
            const SizedBox(height: 16),
            _buildImprovementSuggestionsCard(),
          ],
        ),
      ),
    );
  }

  /// 构建核心指标卡片
  Widget _buildCoreMetricsCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '核心指标',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildMetricItem(
                  '总学习时长',
                  _reportData!.formattedTotalStudyTime,
                  Icons.timer_outlined,
                  const Color(0xFF2E7EED),
                ),
              ),
              Expanded(
                child: _buildMetricItem(
                  '完成任务',
                  '${_reportData!.totalCompletedTasks}个',
                  Icons.task_alt_outlined,
                  const Color(0xFF0F7B6C),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildMetricItem(
                  '学习ROI',
                  _reportData!.averageLearningROI.toStringAsFixed(1),
                  Icons.trending_up_outlined,
                  const Color(0xFFFFD700),
                ),
              ),
              Expanded(
                child: _buildMetricItem(
                  '信噪比',
                  _reportData!.averageFocusSignalToNoiseRatio.toStringAsFixed(
                    1,
                  ),
                  Icons.psychology_outlined,
                  const Color(0xFF7C3AED),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建指标项
  Widget _buildMetricItem(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Text(
                title,
                style: const TextStyle(fontSize: 12, color: Color(0xFF9B9A97)),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建习惯总结卡片
  Widget _buildHabitSummaryCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '学习习惯总结',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 16),
          _buildHabitItem(
            '最佳学习时间',
            _habitAnalysis!.bestStudyTimeDescription,
            Icons.schedule_outlined,
          ),
          const SizedBox(height: 12),
          _buildHabitItem(
            '平均每日学习',
            '${(_habitAnalysis!.averageDailyStudyMinutes / 60).toStringAsFixed(1)}小时',
            Icons.timer_outlined,
          ),
          const SizedBox(height: 12),
          _buildHabitItem(
            '当前连续天数',
            '${_habitAnalysis!.currentStreak}天',
            Icons.local_fire_department_outlined,
          ),
          const SizedBox(height: 12),
          _buildHabitItem(
            '最长连续天数',
            '${_habitAnalysis!.longestStreak}天',
            Icons.emoji_events_outlined,
          ),
        ],
      ),
    );
  }

  /// 构建学习模式卡片
  Widget _buildLearningPatternsCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '学习模式分析',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildScoreCard(
                  '一致性评分',
                  _habitAnalysis!.consistencyScore,
                  const Color(0xFF0F7B6C),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildScoreCard(
                  '强度评分',
                  _habitAnalysis!.intensityScore,
                  const Color(0xFF2E7EED),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _getHabitLevelColor(
                _habitAnalysis!.habitLevel,
              ).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  _getHabitLevelIcon(_habitAnalysis!.habitLevel),
                  color: _getHabitLevelColor(_habitAnalysis!.habitLevel),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '学习习惯等级：${_habitAnalysis!.habitLevel}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: _getHabitLevelColor(_habitAnalysis!.habitLevel),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建改进建议卡片
  Widget _buildImprovementSuggestionsCard() {
    final suggestions = _generateImprovementSuggestions();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '改进建议',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 16),
          ...suggestions.map(
            (suggestion) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    margin: const EdgeInsets.only(top: 6),
                    decoration: const BoxDecoration(
                      color: Color(0xFF2E7EED),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      suggestion,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF37352F),
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建习惯项
  Widget _buildHabitItem(String title, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: const Color(0xFF9B9A97)),
        const SizedBox(width: 8),
        Text(
          title,
          style: const TextStyle(fontSize: 14, color: Color(0xFF9B9A97)),
        ),
        const Spacer(),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
      ],
    );
  }

  /// 构建评分卡片
  Widget _buildScoreCard(String title, double score, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Color(0xFF9B9A97)),
          ),
          const SizedBox(height: 4),
          Text(
            '${score.toStringAsFixed(0)}分',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Color(0xFF2E7EED)),
          SizedBox(height: 16),
          Text(
            '正在生成学习报告...',
            style: TextStyle(fontSize: 14, color: Color(0xFF9B9A97)),
          ),
        ],
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 48, color: Color(0xFFE03E3E)),
          const SizedBox(height: 16),
          Text(
            _error ?? '生成报告失败',
            style: const TextStyle(fontSize: 14, color: Color(0xFF9B9A97)),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadReportData,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7EED),
              foregroundColor: Colors.white,
            ),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  /// 显示导出选项
  void _showExportOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '导出学习报告',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 24),
            ListTile(
              leading: const Icon(
                Icons.picture_as_pdf,
                color: Color(0xFFE03E3E),
              ),
              title: const Text('导出为PDF'),
              onTap: () {
                Navigator.pop(context);
                _exportAsPDF();
              },
            ),
            ListTile(
              leading: const Icon(Icons.image, color: Color(0xFF0F7B6C)),
              title: const Text('导出为图片'),
              onTap: () {
                Navigator.pop(context);
                _exportAsImage();
              },
            ),
            ListTile(
              leading: const Icon(Icons.share, color: Color(0xFF2E7EED)),
              title: const Text('分享报告'),
              onTap: () {
                Navigator.pop(context);
                _shareReport();
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 导出为PDF
  void _exportAsPDF() {
    if (_reportData == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('暂无数据可导出'),
          backgroundColor: Color(0xFFE03E3E),
        ),
      );
      return;
    }

    _exportService.exportToPDF(
      context: context,
      reportData: _reportData!,
      habitAnalysis: _habitAnalysis,
    );
  }

  /// 导出为图片
  void _exportAsImage() {
    if (_reportData == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('暂无数据可导出'),
          backgroundColor: Color(0xFFE03E3E),
        ),
      );
      return;
    }

    _exportService.exportToImage(
      context: context,
      repaintBoundaryKey: _repaintBoundaryKey,
    );
  }

  /// 分享报告
  void _shareReport() {
    if (_reportData == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('暂无数据可分享'),
          backgroundColor: Color(0xFFE03E3E),
        ),
      );
      return;
    }

    _exportService.shareReport(
      context: context,
      reportData: _reportData!,
      habitAnalysis: _habitAnalysis,
    );
  }

  /// 获取习惯等级颜色
  Color _getHabitLevelColor(String level) {
    switch (level) {
      case '优秀':
        return const Color(0xFF0F7B6C);
      case '良好':
        return const Color(0xFF2E7EED);
      case '一般':
        return const Color(0xFFFFD700);
      default:
        return const Color(0xFFE03E3E);
    }
  }

  /// 获取习惯等级图标
  IconData _getHabitLevelIcon(String level) {
    switch (level) {
      case '优秀':
        return Icons.emoji_events;
      case '良好':
        return Icons.thumb_up;
      case '一般':
        return Icons.trending_up;
      default:
        return Icons.warning;
    }
  }

  /// 生成改进建议
  List<String> _generateImprovementSuggestions() {
    final suggestions = <String>[];

    if (_habitAnalysis == null || _reportData == null) return suggestions;

    // 基于一致性评分的建议
    if (_habitAnalysis!.consistencyScore < 60) {
      suggestions.add('建议制定固定的学习时间表，提高学习的一致性');
    }

    // 基于强度评分的建议
    if (_habitAnalysis!.intensityScore < 60) {
      suggestions.add('可以适当增加每日学习时长，提升学习强度');
    }

    // 基于信噪比的建议
    if (_reportData!.averageFocusSignalToNoiseRatio < 50) {
      suggestions.add('建议减少学习过程中的干扰，提高专注度');
    }

    // 基于ROI的建议
    if (_reportData!.averageLearningROI < 60) {
      suggestions.add('可以尝试调整学习方法，提高学习效率');
    }

    // 基于连续天数的建议
    if (_habitAnalysis!.currentStreak < 7) {
      suggestions.add('坚持每日学习，培养良好的学习习惯');
    }

    // 如果没有具体建议，给出通用建议
    if (suggestions.isEmpty) {
      suggestions.addAll([
        '保持当前的学习节奏，继续努力',
        '可以尝试在最佳学习时间段安排重要任务',
        '定期回顾学习进度，及时调整学习计划',
      ]);
    }

    return suggestions;
  }
}
