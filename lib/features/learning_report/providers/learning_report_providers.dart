import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/learning_report_service.dart';
import '../services/learning_report_export_service.dart';
import '../models/learning_report_models.dart';
import '../../study_time/providers/study_time_providers.dart';

/// 学习报告服务Provider
final learningReportServiceProvider = Provider<LearningReportService>((ref) {
  final studyTimeService = ref.watch(studyTimeStatisticsServiceProvider);
  return LearningReportService(studyTimeService);
});

/// 学习报告导出服务Provider
final learningReportExportServiceProvider = Provider<LearningReportExportService>((ref) {
  return LearningReportExportService();
});

/// 当前学习报告数据Provider
final currentLearningReportProvider = StateNotifierProvider<LearningReportNotifier, LearningReportState>((ref) {
  final service = ref.watch(learningReportServiceProvider);
  return LearningReportNotifier(service);
});

/// 学习报告状态管理
class LearningReportState {
  final LearningReportData? reportData;
  final LearningHabitAnalysis? habitAnalysis;
  final bool isLoading;
  final String? error;
  final TimeRange selectedTimeRange;
  final DateTime? customStartDate;
  final DateTime? customEndDate;

  const LearningReportState({
    this.reportData,
    this.habitAnalysis,
    this.isLoading = false,
    this.error,
    this.selectedTimeRange = TimeRange.thisWeek,
    this.customStartDate,
    this.customEndDate,
  });

  LearningReportState copyWith({
    LearningReportData? reportData,
    LearningHabitAnalysis? habitAnalysis,
    bool? isLoading,
    String? error,
    TimeRange? selectedTimeRange,
    DateTime? customStartDate,
    DateTime? customEndDate,
  }) {
    return LearningReportState(
      reportData: reportData ?? this.reportData,
      habitAnalysis: habitAnalysis ?? this.habitAnalysis,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      selectedTimeRange: selectedTimeRange ?? this.selectedTimeRange,
      customStartDate: customStartDate ?? this.customStartDate,
      customEndDate: customEndDate ?? this.customEndDate,
    );
  }
}

/// 学习报告状态管理器
class LearningReportNotifier extends StateNotifier<LearningReportState> {
  final LearningReportService _service;

  LearningReportNotifier(this._service) : super(const LearningReportState()) {
    // 监听服务状态变化
    _service.addListener(_onServiceStateChanged);
  }

  @override
  void dispose() {
    _service.removeListener(_onServiceStateChanged);
    super.dispose();
  }

  /// 监听服务状态变化
  void _onServiceStateChanged() {
    state = state.copyWith(
      reportData: _service.currentReportData,
      habitAnalysis: _service.currentHabitAnalysis,
      isLoading: _service.isLoading,
      error: _service.error,
    );
  }

  /// 生成学习报告
  Future<void> generateReport({
    TimeRange? timeRange,
    DateTime? customStartDate,
    DateTime? customEndDate,
  }) async {
    final selectedTimeRange = timeRange ?? state.selectedTimeRange;
    
    state = state.copyWith(
      isLoading: true,
      error: null,
      selectedTimeRange: selectedTimeRange,
      customStartDate: customStartDate,
      customEndDate: customEndDate,
    );

    try {
      final reportData = await _service.generateReport(
        selectedTimeRange,
        customStartDate: customStartDate,
        customEndDate: customEndDate,
      );
      
      state = state.copyWith(
        reportData: reportData,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 生成学习习惯分析
  Future<void> generateHabitAnalysis() async {
    try {
      final habitAnalysis = await _service.generateHabitAnalysis();
      state = state.copyWith(habitAnalysis: habitAnalysis);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 刷新数据
  Future<void> refresh() async {
    await generateReport();
    await generateHabitAnalysis();
  }

  /// 更新时间范围
  void updateTimeRange(TimeRange timeRange) {
    state = state.copyWith(selectedTimeRange: timeRange);
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// 学习报告快速访问Provider - 提供常用的数据访问
final learningReportQuickAccessProvider = Provider<LearningReportQuickAccess>((ref) {
  final reportState = ref.watch(currentLearningReportProvider);
  return LearningReportQuickAccess(reportState);
});

/// 学习报告快速访问类
class LearningReportQuickAccess {
  final LearningReportState _state;

  const LearningReportQuickAccess(this._state);

  /// 是否有数据
  bool get hasData => _state.reportData != null;

  /// 是否正在加载
  bool get isLoading => _state.isLoading;

  /// 错误信息
  String? get error => _state.error;

  /// 总学习时长（分钟）
  int get totalStudyMinutes => _state.reportData?.totalStudyMinutes ?? 0;

  /// 总完成任务数
  int get totalCompletedTasks => _state.reportData?.totalCompletedTasks ?? 0;

  /// 平均学习ROI
  double get averageLearningROI => _state.reportData?.averageLearningROI ?? 0.0;

  /// 平均信噪比
  double get averageFocusRatio => _state.reportData?.averageFocusSignalToNoiseRatio ?? 0.0;

  /// 连续学习天数
  int get streakDays => _state.reportData?.streakDays ?? 0;

  /// 学习效率等级
  String get efficiencyLevel => _state.reportData?.efficiencyLevel ?? '暂无数据';

  /// 格式化的总学习时长
  String get formattedTotalStudyTime => _state.reportData?.formattedTotalStudyTime ?? '0m';

  /// 任务完成率
  double get taskCompletionRate => _state.reportData?.taskCompletionRate ?? 0.0;

  /// 并行时间收益率
  double get parallelTimeBenefitRate => _state.reportData?.parallelTimeBenefitRate ?? 0.0;

  /// 学习习惯分析
  LearningHabitAnalysis? get habitAnalysis => _state.habitAnalysis;

  /// 最佳学习时间段描述
  String get bestStudyTimeDescription => 
      _state.habitAnalysis?.bestStudyTimeDescription ?? '暂无数据';

  /// 学习习惯等级
  String get habitLevel => _state.habitAnalysis?.habitLevel ?? '暂无数据';

  /// 平均每日学习时长（分钟）
  double get averageDailyStudyMinutes => 
      _state.habitAnalysis?.averageDailyStudyMinutes ?? 0.0;

  /// 学习一致性评分
  double get consistencyScore => _state.habitAnalysis?.consistencyScore ?? 0.0;

  /// 学习强度评分
  double get intensityScore => _state.habitAnalysis?.intensityScore ?? 0.0;
}
