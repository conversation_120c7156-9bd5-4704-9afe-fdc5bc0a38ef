import 'package:flutter/foundation.dart';
import 'achievement_service.dart';
import '../models/user_level.dart';

/// 成就触发器服务 - 负责监听应用事件并触发相应成就
class AchievementTriggerService {
  final AchievementService _achievementService;
  
  // 用于跟踪学习会话的状态
  DateTime? _studySessionStart;
  int _todayStudyMinutes = 0;
  int _consecutiveDays = 0;
  
  AchievementTriggerService(this._achievementService);

  /// 学习相关触发器
  
  /// 开始学习会话
  Future<void> onStudySessionStart() async {
    _studySessionStart = DateTime.now();
    debugPrint('📚 学习会话开始');
  }

  /// 结束学习会话
  Future<void> onStudySessionEnd(int studyMinutes) async {
    if (_studySessionStart == null) return;
    
    final sessionDuration = DateTime.now().difference(_studySessionStart!).inMinutes;
    _studySessionStart = null;
    
    // 更新今日学习时长
    _todayStudyMinutes += studyMinutes;
    
    // 奖励基础经验值 (每分钟1经验)
    await _achievementService.addExperience(studyMinutes, source: '学习时长');
    
    // 奖励时间管理技能经验
    await _achievementService.addSkillExperience(SkillType.timeManagement, studyMinutes ~/ 5);
    
    // 检查学习相关成就
    await _checkLearningAchievements(studyMinutes, sessionDuration);
    
    debugPrint('📚 学习会话结束: $studyMinutes分钟');
  }

  /// 检查学习相关成就
  Future<void> _checkLearningAchievements(int studyMinutes, int sessionDuration) async {
    // 初出茅庐 - 完成第一个时间盒子
    await _achievementService.updateAchievementProgress('learning_001', 1);
    
    // 专注战士 - 连续学习2小时
    if (sessionDuration >= 120) {
      await _achievementService.unlockAchievement('learning_002');
    }
    
    // 时间刺客 - 单日学习8小时
    if (_todayStudyMinutes >= 480) {
      await _achievementService.unlockAchievement('learning_003');
    }
    
    // 深夜学者 - 23:00-01:00学习
    final now = DateTime.now();
    if (now.hour >= 23 || now.hour <= 1) {
      await _achievementService.updateAchievementProgress('learning_006', studyMinutes);
    }
    
    // 早起鸟儿 - 06:00-08:00学习
    if (now.hour >= 6 && now.hour <= 8) {
      await _achievementService.updateAchievementProgress('learning_007', studyMinutes);
    }
  }

  /// 连续打卡
  Future<void> onDailyCheckIn() async {
    _consecutiveDays++;
    
    // 连击王者 - 连续30天
    if (_consecutiveDays >= 30) {
      await _achievementService.unlockAchievement('learning_005');
    }
    
    // 奖励打卡经验
    await _achievementService.addExperience(20, source: '每日打卡');
    
    debugPrint('✅ 每日打卡: 连续$_consecutiveDays天');
  }

  /// 虚拟工资相关触发器
  
  /// 工资收入
  Future<void> onWageEarned(double amount) async {
    // 百万富翁成就
    // 这里需要获取累计工资，暂时用简单逻辑
    if (amount >= 1000000) {
      await _achievementService.unlockAchievement('learning_004');
    }
    
    debugPrint('💰 获得工资: ¥$amount');
  }

  /// 记忆宫殿相关触发器
  
  /// 创建记忆宫殿
  Future<void> onMemoryPalaceCreated() async {
    // 建筑师 - 创建第一个记忆宫殿
    await _achievementService.updateAchievementProgress('memory_001', 1);
    
    // 奖励记忆技能经验
    await _achievementService.addSkillExperience(SkillType.memory, 10);
    
    debugPrint('🏗️ 创建记忆宫殿');
  }

  /// 添加记忆锚点
  Future<void> onMemoryAnchorAdded(String palaceId) async {
    // 宫殿领主 - 拥有10个记忆宫殿
    await _achievementService.updateAchievementProgress('memory_002', 1);
    
    // 记忆帝王 - 单个宫殿100个锚点 (这里需要传入该宫殿的锚点总数)
    // await _achievementService.updateAchievementProgress('memory_003', anchorCount);
    
    // 奖励记忆技能经验
    await _achievementService.addSkillExperience(SkillType.memory, 2);
    
    debugPrint('📍 添加记忆锚点');
  }

  /// 使用AR功能
  Future<void> onARFeatureUsed() async {
    // 空间魔法师 - 使用AR功能
    await _achievementService.unlockAchievement('memory_004');
    
    debugPrint('🔮 使用AR功能');
  }

  /// 健康运动相关触发器
  
  /// 完成PAO动作
  Future<void> onPAOActionCompleted(String letter) async {
    // 动作收集家 - 解锁26个字母动作
    await _achievementService.updateAchievementProgress('health_001', 1);
    
    // 健身达人 - 累计1000个动作
    await _achievementService.updateAchievementProgress('health_002', 1);
    
    // 奖励健身技能经验
    await _achievementService.addSkillExperience(SkillType.fitness, 1);
    
    debugPrint('💪 完成PAO动作: $letter');
  }

  /// 完成眼保健操
  Future<void> onEyeExerciseCompleted() async {
    // 护眼卫士 - 100次眼保健操
    await _achievementService.updateAchievementProgress('health_003', 1);
    
    // 奖励健身技能经验
    await _achievementService.addSkillExperience(SkillType.fitness, 2);
    
    debugPrint('👁️ 完成眼保健操');
  }

  /// 社交相关触发器
  
  /// 添加好友
  Future<void> onFriendAdded() async {
    // 社交新手 - 添加第一个好友
    await _achievementService.updateAchievementProgress('social_001', 1);
    
    // 人气王者 - 50个好友
    await _achievementService.updateAchievementProgress('social_002', 1);
    
    debugPrint('👥 添加好友');
  }

  /// 发起挑战
  Future<void> onChallengeInitiated() async {
    // 挑战者 - 发起10次挑战
    await _achievementService.updateAchievementProgress('social_003', 1);
    
    debugPrint('⚡ 发起挑战');
  }

  /// 反思日志相关触发器
  
  /// 写反思日志
  Future<void> onReflectionWritten(int wordCount) async {
    // 奖励反思技能经验 (根据字数)
    final exp = (wordCount / 50).ceil(); // 每50字1经验
    await _achievementService.addSkillExperience(SkillType.reflection, exp);
    
    debugPrint('🤔 写反思日志: $wordCount字');
  }

  /// 词汇学习相关触发器
  
  /// 学习单词
  Future<void> onWordLearned() async {
    // 奖励词汇技能经验
    await _achievementService.addSkillExperience(SkillType.vocabulary, 1);
    
    debugPrint('📚 学习单词');
  }

  /// 特殊事件触发器
  
  /// 注册当天学习
  Future<void> onFirstDayStudy(int studyMinutes) async {
    // 首日勇士 - 注册当天学习5小时
    if (studyMinutes >= 300) {
      await _achievementService.unlockAchievement('special_001');
    }
    
    debugPrint('🛡️ 首日学习: $studyMinutes分钟');
  }

  /// 使用所有功能模块
  Future<void> onAllFeaturesUsed() async {
    // 探索者 - 使用所有功能
    await _achievementService.unlockAchievement('special_003');
    
    debugPrint('🧭 使用所有功能');
  }

  /// 重置每日数据 (在新的一天开始时调用)
  void resetDailyData() {
    _todayStudyMinutes = 0;
    debugPrint('🔄 重置每日数据');
  }

  /// 获取今日学习时长
  int get todayStudyMinutes => _todayStudyMinutes;
  
  /// 获取连续天数
  int get consecutiveDays => _consecutiveDays;
}
