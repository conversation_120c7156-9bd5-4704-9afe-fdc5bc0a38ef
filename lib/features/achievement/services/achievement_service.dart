import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/achievement.dart';
import '../models/user_level.dart';
import '../models/badge.dart';
import '../data/achievements_data.dart';
import '../../wage_system/services/wage_service.dart';

/// 成就系统服务
class AchievementService {
  static const String _userLevelKey = 'user_level';
  static const String _achievementProgressKey = 'achievement_progress';
  static const String _userBadgesKey = 'user_badges';
  static const String _challengeProgressKey = 'challenge_progress';
  static const String _skillLevelsKey = 'skill_levels';

  // 工资系统服务实例
  final WageService _wageService = WageService();

  /// 获取用户等级信息
  Future<UserLevel> getUserLevel() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final levelJson = prefs.getString(_userLevelKey);

      if (levelJson != null) {
        final levelMap = json.decode(levelJson);
        return UserLevel.fromJson(levelMap);
      }

      // 创建初始等级
      const userId = 'default_user'; // 实际应用中应该从认证服务获取
      final initialLevel = UserLevel.initial(userId);
      await _saveUserLevel(initialLevel);
      return initialLevel;
    } catch (e) {
      debugPrint('获取用户等级失败: $e');
      const userId = 'default_user';
      return UserLevel.initial(userId);
    }
  }

  /// 保存用户等级
  Future<void> _saveUserLevel(UserLevel userLevel) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final levelJson = json.encode(userLevel.toJson());
      await prefs.setString(_userLevelKey, levelJson);
    } catch (e) {
      debugPrint('保存用户等级失败: $e');
    }
  }

  /// 添加经验值
  Future<UserLevel> addExperience(int exp, {String? source}) async {
    try {
      final currentLevel = await getUserLevel();
      final newLevel = currentLevel.addExperience(exp);

      await _saveUserLevel(newLevel);

      // 检查是否升级
      if (newLevel.level > currentLevel.level) {
        await _handleLevelUp(currentLevel.level, newLevel.level);
      }

      // 检查段位成就
      await _checkRankAchievements(newLevel);

      debugPrint('添加经验值: $exp, 来源: $source, 新等级: ${newLevel.level}');
      return newLevel;
    } catch (e) {
      debugPrint('添加经验值失败: $e');
      return await getUserLevel();
    }
  }

  /// 处理升级事件
  Future<void> _handleLevelUp(int oldLevel, int newLevel) async {
    debugPrint('🎉 恭喜升级！从 $oldLevel 级升到 $newLevel 级');

    // 检查里程碑成就
    await _checkMilestoneAchievements(newLevel);

    // 可以在这里添加升级奖励、通知等逻辑
  }

  /// 检查段位成就
  Future<void> _checkRankAchievements(UserLevel userLevel) async {
    final badgeId = 'rank_${userLevel.rank.name}';
    await _unlockBadge(badgeId);
  }

  /// 检查里程碑成就
  Future<void> _checkMilestoneAchievements(int level) async {
    final milestoneAchievements = {
      10: 'milestone_001',
      30: 'milestone_002',
      50: 'milestone_003',
      100: 'milestone_004',
    };

    final achievementId = milestoneAchievements[level];
    if (achievementId != null) {
      await unlockAchievement(achievementId);
    }
  }

  /// 获取所有成就
  List<Achievement> getAllAchievements() {
    return AchievementsData.getAllAchievements();
  }

  /// 获取用户成就进度
  Future<Map<String, UserAchievementProgress>>
  getUserAchievementProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressJson = prefs.getString(_achievementProgressKey);

      if (progressJson != null) {
        final progressMap = json.decode(progressJson) as Map<String, dynamic>;
        return progressMap.map(
          (key, value) =>
              MapEntry(key, UserAchievementProgress.fromJson(value)),
        );
      }

      return {};
    } catch (e) {
      debugPrint('获取成就进度失败: $e');
      return {};
    }
  }

  /// 保存成就进度
  Future<void> _saveAchievementProgress(
    Map<String, UserAchievementProgress> progress,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressMap = progress.map(
        (key, value) => MapEntry(key, value.toJson()),
      );
      final progressJson = json.encode(progressMap);
      await prefs.setString(_achievementProgressKey, progressJson);
    } catch (e) {
      debugPrint('保存成就进度失败: $e');
    }
  }

  /// 更新成就进度
  Future<bool> updateAchievementProgress(
    String achievementId,
    int progress,
  ) async {
    try {
      final allProgress = await getUserAchievementProgress();
      final achievement = getAllAchievements().firstWhere(
        (a) => a.id == achievementId,
      );

      final currentProgress =
          allProgress[achievementId] ??
          UserAchievementProgress.create(achievementId);

      // 检查是否应该解锁成就
      final shouldUnlock = progress >= achievement.maxProgress;

      final newProgress = currentProgress.updateProgress(
        progress,
        unlock: shouldUnlock,
      );

      allProgress[achievementId] = newProgress;
      await _saveAchievementProgress(allProgress);

      // 检查是否解锁成就
      if (newProgress.isUnlocked && !currentProgress.isUnlocked) {
        await _handleAchievementUnlock(achievement);
        return true; // 返回true表示新解锁了成就
      }

      return false;
    } catch (e) {
      debugPrint('更新成就进度失败: $e');
      return false;
    }
  }

  /// 解锁成就
  Future<bool> unlockAchievement(String achievementId) async {
    try {
      final achievement = getAllAchievements().firstWhere(
        (a) => a.id == achievementId,
      );
      final allProgress = await getUserAchievementProgress();

      final currentProgress =
          allProgress[achievementId] ??
          UserAchievementProgress.create(achievementId);

      if (!currentProgress.isUnlocked) {
        final newProgress = currentProgress.unlock();
        allProgress[achievementId] = newProgress;
        await _saveAchievementProgress(allProgress);

        await _handleAchievementUnlock(achievement);
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('解锁成就失败: $e');
      return false;
    }
  }

  /// 处理成就解锁
  Future<void> _handleAchievementUnlock(Achievement achievement) async {
    debugPrint('🏆 解锁成就: ${achievement.name}');

    // 奖励经验值
    if (achievement.experienceReward > 0) {
      await addExperience(achievement.experienceReward, source: '成就奖励');
    }

    // 奖励虚拟货币
    if (achievement.wageReward > 0) {
      final success = await _wageService.addWageReward(
        achievement.wageReward,
        description: '成就奖励: ${achievement.name}',
      );

      if (success) {
        debugPrint('💰 获得工资奖励: ¥${achievement.wageReward}');
      } else {
        debugPrint('❌ 工资奖励发放失败: ¥${achievement.wageReward}');
      }
    }

    // 触发成就解锁通知回调
    _onAchievementUnlocked?.call(achievement);
  }

  /// 成就解锁回调
  Function(Achievement)? _onAchievementUnlocked;

  /// 设置成就解锁回调
  void setOnAchievementUnlocked(Function(Achievement) callback) {
    _onAchievementUnlocked = callback;
  }

  /// 获取用户徽章
  Future<List<UserBadge>> getUserBadges() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final badgesJson = prefs.getString(_userBadgesKey);

      if (badgesJson != null) {
        final badgesList = json.decode(badgesJson) as List<dynamic>;
        return badgesList.map((badge) => UserBadge.fromJson(badge)).toList();
      }

      return [];
    } catch (e) {
      debugPrint('获取用户徽章失败: $e');
      return [];
    }
  }

  /// 解锁徽章
  Future<bool> _unlockBadge(String badgeId) async {
    try {
      final userBadges = await getUserBadges();

      // 检查是否已拥有该徽章
      if (userBadges.any((badge) => badge.badgeId == badgeId)) {
        return false;
      }

      final newBadge = UserBadge.create(badgeId);
      userBadges.add(newBadge);

      await _saveUserBadges(userBadges);
      debugPrint('🎖️ 解锁徽章: $badgeId');
      return true;
    } catch (e) {
      debugPrint('解锁徽章失败: $e');
      return false;
    }
  }

  /// 保存用户徽章
  Future<void> _saveUserBadges(List<UserBadge> badges) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final badgesJson = json.encode(
        badges.map((badge) => badge.toJson()).toList(),
      );
      await prefs.setString(_userBadgesKey, badgesJson);
    } catch (e) {
      debugPrint('保存用户徽章失败: $e');
    }
  }

  /// 获取专业技能等级
  Future<Map<SkillType, SkillLevel>> getSkillLevels() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final skillsJson = prefs.getString(_skillLevelsKey);

      if (skillsJson != null) {
        final skillsMap = json.decode(skillsJson) as Map<String, dynamic>;
        return skillsMap.map(
          (key, value) => MapEntry(
            SkillType.values.firstWhere((e) => e.name == key),
            SkillLevel.fromJson(value),
          ),
        );
      }

      // 创建初始技能等级
      final initialSkills = <SkillType, SkillLevel>{};
      for (final skillType in SkillType.values) {
        initialSkills[skillType] = SkillLevel.initial(skillType);
      }

      await _saveSkillLevels(initialSkills);
      return initialSkills;
    } catch (e) {
      debugPrint('获取技能等级失败: $e');
      return {};
    }
  }

  /// 保存技能等级
  Future<void> _saveSkillLevels(Map<SkillType, SkillLevel> skills) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final skillsMap = skills.map(
        (key, value) => MapEntry(key.name, value.toJson()),
      );
      final skillsJson = json.encode(skillsMap);
      await prefs.setString(_skillLevelsKey, skillsJson);
    } catch (e) {
      debugPrint('保存技能等级失败: $e');
    }
  }

  /// 添加技能经验
  Future<SkillLevel> addSkillExperience(SkillType skillType, int exp) async {
    try {
      final skills = await getSkillLevels();
      final currentSkill = skills[skillType] ?? SkillLevel.initial(skillType);

      // 简单的技能升级逻辑
      final newTotalExp = currentSkill.totalExp + exp;
      final newCurrentExp = currentSkill.currentExp + exp;

      int newLevel = currentSkill.level;
      int remainingExp = newCurrentExp;
      int requiredExp = currentSkill.requiredExp;

      // 检查是否升级
      while (remainingExp >= requiredExp) {
        remainingExp -= requiredExp;
        newLevel++;
        requiredExp = 50 * newLevel; // 简单的升级公式
      }

      final newSkill = SkillLevel(
        skillType: skillType,
        level: newLevel,
        currentExp: remainingExp,
        requiredExp: requiredExp,
        totalExp: newTotalExp,
        updatedAt: DateTime.now(),
      );

      skills[skillType] = newSkill;
      await _saveSkillLevels(skills);

      debugPrint('技能升级: ${skillType.name} +$exp exp, 等级: $newLevel');
      return newSkill;
    } catch (e) {
      debugPrint('添加技能经验失败: $e');
      return SkillLevel.initial(skillType);
    }
  }

  /// 获取用户统计数据
  Future<Map<String, dynamic>> getUserStats() async {
    final userLevel = await getUserLevel();
    final achievements = await getUserAchievementProgress();
    final badges = await getUserBadges();
    final skills = await getSkillLevels();

    final unlockedAchievements = achievements.values
        .where((a) => a.isUnlocked)
        .length;
    final totalAchievements = getAllAchievements().length;

    return {
      'level': userLevel.level,
      'rank': userLevel.rankName,
      'totalExp': userLevel.totalExp,
      'unlockedAchievements': unlockedAchievements,
      'totalAchievements': totalAchievements,
      'achievementRate': totalAchievements > 0
          ? (unlockedAchievements / totalAchievements * 100).round()
          : 0,
      'badgeCount': badges.length,
      'skillLevels': skills.map(
        (key, value) => MapEntry(key.name, value.level),
      ),
    };
  }

  /// 重置所有数据 (仅用于测试)
  Future<void> resetAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userLevelKey);
      await prefs.remove(_achievementProgressKey);
      await prefs.remove(_userBadgesKey);
      await prefs.remove(_challengeProgressKey);
      await prefs.remove(_skillLevelsKey);
      debugPrint('🔄 成就系统数据已重置');
    } catch (e) {
      debugPrint('重置数据失败: $e');
    }
  }
}
