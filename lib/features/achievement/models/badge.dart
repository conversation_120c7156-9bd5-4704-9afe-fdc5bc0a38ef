import 'package:json_annotation/json_annotation.dart';
import 'achievement.dart';

part 'badge.g.dart';

/// 徽章类型枚举
enum BadgeType {
  @JsonValue('achievement')
  achievement,   // 成就徽章
  @JsonValue('rank')
  rank,         // 段位徽章
  @JsonValue('seasonal')
  seasonal,     // 赛季徽章
  @JsonValue('special')
  special,      // 特殊徽章
  @JsonValue('event')
  event,        // 活动徽章
}

/// 徽章模型
@JsonSerializable()
class Badge {
  /// 徽章ID
  final String id;
  
  /// 徽章名称
  final String name;
  
  /// 徽章描述
  final String description;
  
  /// 徽章图标
  final String icon;
  
  /// 徽章类型
  final BadgeType type;
  
  /// 徽章稀有度
  final AchievementRarity rarity;
  
  /// 获得条件描述
  final String obtainCondition;
  
  /// 是否为限时徽章
  final bool isLimited;
  
  /// 限时结束时间
  final DateTime? limitedUntil;
  
  /// 创建时间
  final DateTime createdAt;

  const Badge({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.type,
    required this.rarity,
    required this.obtainCondition,
    this.isLimited = false,
    this.limitedUntil,
    required this.createdAt,
  });

  factory Badge.fromJson(Map<String, dynamic> json) => _$BadgeFromJson(json);
  Map<String, dynamic> toJson() => _$BadgeToJson(this);

  /// 获取稀有度颜色
  String get rarityColor {
    switch (rarity) {
      case AchievementRarity.common:
        return '#9B9A97';
      case AchievementRarity.rare:
        return '#2E7EED';
      case AchievementRarity.epic:
        return '#7C3AED';
      case AchievementRarity.legendary:
        return '#F59E0B';
      case AchievementRarity.mythic:
        return '#E03E3E';
    }
  }

  /// 获取类型名称
  String get typeName {
    switch (type) {
      case BadgeType.achievement:
        return '成就徽章';
      case BadgeType.rank:
        return '段位徽章';
      case BadgeType.seasonal:
        return '赛季徽章';
      case BadgeType.special:
        return '特殊徽章';
      case BadgeType.event:
        return '活动徽章';
    }
  }

  /// 是否已过期
  bool get isExpired {
    if (!isLimited || limitedUntil == null) return false;
    return DateTime.now().isAfter(limitedUntil!);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Badge &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Badge(id: $id, name: $name, type: $type)';
}

/// 用户徽章
@JsonSerializable()
class UserBadge {
  /// 徽章ID
  final String badgeId;
  
  /// 获得时间
  final DateTime obtainedAt;
  
  /// 是否展示在个人资料
  final bool isDisplayed;
  
  /// 展示顺序
  final int displayOrder;

  const UserBadge({
    required this.badgeId,
    required this.obtainedAt,
    this.isDisplayed = false,
    this.displayOrder = 0,
  });

  factory UserBadge.fromJson(Map<String, dynamic> json) => _$UserBadgeFromJson(json);
  Map<String, dynamic> toJson() => _$UserBadgeToJson(this);

  /// 创建新的用户徽章
  factory UserBadge.create(String badgeId) {
    return UserBadge(
      badgeId: badgeId,
      obtainedAt: DateTime.now(),
    );
  }

  /// 设置展示状态
  UserBadge setDisplayed(bool displayed, {int? order}) {
    return UserBadge(
      badgeId: badgeId,
      obtainedAt: obtainedAt,
      isDisplayed: displayed,
      displayOrder: order ?? displayOrder,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserBadge &&
          runtimeType == other.runtimeType &&
          badgeId == other.badgeId;

  @override
  int get hashCode => badgeId.hashCode;

  @override
  String toString() => 'UserBadge(badgeId: $badgeId, displayed: $isDisplayed)';
}

/// 挑战模型
@JsonSerializable()
class Challenge {
  /// 挑战ID
  final String id;
  
  /// 挑战名称
  final String name;
  
  /// 挑战描述
  final String description;
  
  /// 挑战图标
  final String icon;
  
  /// 挑战类型 (daily, weekly, monthly, special)
  final String type;
  
  /// 目标值
  final int targetValue;
  
  /// 奖励经验值
  final int experienceReward;
  
  /// 奖励虚拟货币
  final double wageReward;
  
  /// 奖励徽章ID (可选)
  final String? rewardBadgeId;
  
  /// 开始时间
  final DateTime startTime;
  
  /// 结束时间
  final DateTime endTime;
  
  /// 是否激活
  final bool isActive;

  const Challenge({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.type,
    required this.targetValue,
    required this.experienceReward,
    required this.wageReward,
    this.rewardBadgeId,
    required this.startTime,
    required this.endTime,
    this.isActive = true,
  });

  factory Challenge.fromJson(Map<String, dynamic> json) => _$ChallengeFromJson(json);
  Map<String, dynamic> toJson() => _$ChallengeToJson(this);

  /// 是否为每日挑战
  bool get isDaily => type == 'daily';
  
  /// 是否为每周挑战
  bool get isWeekly => type == 'weekly';
  
  /// 是否为每月挑战
  bool get isMonthly => type == 'monthly';
  
  /// 是否已过期
  bool get isExpired => DateTime.now().isAfter(endTime);
  
  /// 剩余时间（小时）
  int get remainingHours {
    final now = DateTime.now();
    if (now.isAfter(endTime)) return 0;
    return endTime.difference(now).inHours;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Challenge &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Challenge(id: $id, name: $name, type: $type)';
}

/// 用户挑战进度
@JsonSerializable()
class UserChallengeProgress {
  /// 挑战ID
  final String challengeId;
  
  /// 当前进度
  final int currentProgress;
  
  /// 是否已完成
  final bool isCompleted;
  
  /// 完成时间
  final DateTime? completedAt;
  
  /// 开始时间
  final DateTime startedAt;
  
  /// 最后更新时间
  final DateTime lastUpdatedAt;

  const UserChallengeProgress({
    required this.challengeId,
    required this.currentProgress,
    required this.isCompleted,
    this.completedAt,
    required this.startedAt,
    required this.lastUpdatedAt,
  });

  factory UserChallengeProgress.fromJson(Map<String, dynamic> json) => 
      _$UserChallengeProgressFromJson(json);
  Map<String, dynamic> toJson() => _$UserChallengeProgressToJson(this);

  /// 创建新的挑战进度
  factory UserChallengeProgress.create(String challengeId) {
    final now = DateTime.now();
    return UserChallengeProgress(
      challengeId: challengeId,
      currentProgress: 0,
      isCompleted: false,
      startedAt: now,
      lastUpdatedAt: now,
    );
  }

  /// 更新进度
  UserChallengeProgress updateProgress(int newProgress, int targetValue) {
    final now = DateTime.now();
    final completed = newProgress >= targetValue;
    
    return UserChallengeProgress(
      challengeId: challengeId,
      currentProgress: newProgress,
      isCompleted: completed,
      completedAt: completed && !isCompleted ? now : completedAt,
      startedAt: startedAt,
      lastUpdatedAt: now,
    );
  }

  /// 获取进度百分比
  double getProgressPercentage(int targetValue) {
    if (targetValue <= 0) return 0.0;
    return (currentProgress / targetValue).clamp(0.0, 1.0);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserChallengeProgress &&
          runtimeType == other.runtimeType &&
          challengeId == other.challengeId;

  @override
  int get hashCode => challengeId.hashCode;

  @override
  String toString() => 
      'UserChallengeProgress(id: $challengeId, progress: $currentProgress, completed: $isCompleted)';
}
