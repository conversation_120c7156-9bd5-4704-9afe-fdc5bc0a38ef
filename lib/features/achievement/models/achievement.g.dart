// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'achievement.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Achievement _$AchievementFromJson(Map<String, dynamic> json) =>
    $checkedCreate('Achievement', json, ($checkedConvert) {
      final val = Achievement(
        id: $checkedConvert('id', (v) => v as String),
        name: $checkedConvert('name', (v) => v as String),
        description: $checkedConvert('description', (v) => v as String),
        icon: $checkedConvert('icon', (v) => v as String),
        type: $checkedConvert(
          'type',
          (v) => $enumDecode(_$AchievementTypeEnumMap, v),
        ),
        rarity: $checkedConvert(
          'rarity',
          (v) => $enumDecode(_$AchievementRarityEnumMap, v),
        ),
        experienceReward: $checkedConvert(
          'experienceReward',
          (v) => (v as num).toInt(),
        ),
        wageReward: $checkedConvert('wageReward', (v) => (v as num).toDouble()),
        unlockCondition: $checkedConvert('unlockCondition', (v) => v as String),
        isHidden: $checkedConvert('isHidden', (v) => v as bool? ?? false),
        maxProgress: $checkedConvert(
          'maxProgress',
          (v) => (v as num?)?.toInt() ?? 1,
        ),
        createdAt: $checkedConvert(
          'createdAt',
          (v) => DateTime.parse(v as String),
        ),
      );
      return val;
    });

// ignore: unused_element
abstract class _$AchievementPerFieldToJson {
  // ignore: unused_element
  static Object? id(String instance) => instance;
  // ignore: unused_element
  static Object? name(String instance) => instance;
  // ignore: unused_element
  static Object? description(String instance) => instance;
  // ignore: unused_element
  static Object? icon(String instance) => instance;
  // ignore: unused_element
  static Object? type(AchievementType instance) =>
      _$AchievementTypeEnumMap[instance]!;
  // ignore: unused_element
  static Object? rarity(AchievementRarity instance) =>
      _$AchievementRarityEnumMap[instance]!;
  // ignore: unused_element
  static Object? experienceReward(int instance) => instance;
  // ignore: unused_element
  static Object? wageReward(double instance) => instance;
  // ignore: unused_element
  static Object? unlockCondition(String instance) => instance;
  // ignore: unused_element
  static Object? isHidden(bool instance) => instance;
  // ignore: unused_element
  static Object? maxProgress(int instance) => instance;
  // ignore: unused_element
  static Object? createdAt(DateTime instance) => instance.toIso8601String();
}

Map<String, dynamic> _$AchievementToJson(Achievement instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'icon': instance.icon,
      'type': _$AchievementTypeEnumMap[instance.type]!,
      'rarity': _$AchievementRarityEnumMap[instance.rarity]!,
      'experienceReward': instance.experienceReward,
      'wageReward': instance.wageReward,
      'unlockCondition': instance.unlockCondition,
      'isHidden': instance.isHidden,
      'maxProgress': instance.maxProgress,
      'createdAt': instance.createdAt.toIso8601String(),
    };

const _$AchievementTypeEnumMap = {
  AchievementType.learning: 'learning',
  AchievementType.memory: 'memory',
  AchievementType.health: 'health',
  AchievementType.social: 'social',
  AchievementType.milestone: 'milestone',
  AchievementType.special: 'special',
};

const _$AchievementRarityEnumMap = {
  AchievementRarity.common: 'common',
  AchievementRarity.rare: 'rare',
  AchievementRarity.epic: 'epic',
  AchievementRarity.legendary: 'legendary',
  AchievementRarity.mythic: 'mythic',
};

UserAchievementProgress _$UserAchievementProgressFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('UserAchievementProgress', json, ($checkedConvert) {
  final val = UserAchievementProgress(
    achievementId: $checkedConvert('achievementId', (v) => v as String),
    currentProgress: $checkedConvert(
      'currentProgress',
      (v) => (v as num).toInt(),
    ),
    isUnlocked: $checkedConvert('isUnlocked', (v) => v as bool),
    unlockedAt: $checkedConvert(
      'unlockedAt',
      (v) => v == null ? null : DateTime.parse(v as String),
    ),
    firstProgressAt: $checkedConvert(
      'firstProgressAt',
      (v) => DateTime.parse(v as String),
    ),
    lastProgressAt: $checkedConvert(
      'lastProgressAt',
      (v) => DateTime.parse(v as String),
    ),
  );
  return val;
});

// ignore: unused_element
abstract class _$UserAchievementProgressPerFieldToJson {
  // ignore: unused_element
  static Object? achievementId(String instance) => instance;
  // ignore: unused_element
  static Object? currentProgress(int instance) => instance;
  // ignore: unused_element
  static Object? isUnlocked(bool instance) => instance;
  // ignore: unused_element
  static Object? unlockedAt(DateTime? instance) => instance?.toIso8601String();
  // ignore: unused_element
  static Object? firstProgressAt(DateTime instance) =>
      instance.toIso8601String();
  // ignore: unused_element
  static Object? lastProgressAt(DateTime instance) =>
      instance.toIso8601String();
}

Map<String, dynamic> _$UserAchievementProgressToJson(
  UserAchievementProgress instance,
) => <String, dynamic>{
  'achievementId': instance.achievementId,
  'currentProgress': instance.currentProgress,
  'isUnlocked': instance.isUnlocked,
  'unlockedAt': instance.unlockedAt?.toIso8601String(),
  'firstProgressAt': instance.firstProgressAt.toIso8601String(),
  'lastProgressAt': instance.lastProgressAt.toIso8601String(),
};
