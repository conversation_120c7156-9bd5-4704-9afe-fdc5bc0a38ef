import 'package:json_annotation/json_annotation.dart';

part 'achievement.g.dart';

/// 成就类型枚举
enum AchievementType {
  @JsonValue('learning')
  learning,      // 学习成就
  @JsonValue('memory')
  memory,        // 记忆宫殿成就
  @JsonValue('health')
  health,        // 健康运动成就
  @JsonValue('social')
  social,        // 社交成就
  @JsonValue('milestone')
  milestone,     // 里程碑成就
  @JsonValue('special')
  special,       // 特殊成就
}

/// 成就稀有度枚举
enum AchievementRarity {
  @JsonValue('common')
  common,        // 普通 (白色)
  @JsonValue('rare')
  rare,          // 稀有 (蓝色)
  @JsonValue('epic')
  epic,          // 史诗 (紫色)
  @JsonValue('legendary')
  legendary,     // 传说 (橙色)
  @JsonValue('mythic')
  mythic,        // 神话 (红色)
}

/// 成就模型
@JsonSerializable()
class Achievement {
  /// 成就ID
  final String id;
  
  /// 成就名称
  final String name;
  
  /// 成就描述
  final String description;
  
  /// 成就图标
  final String icon;
  
  /// 成就类型
  final AchievementType type;
  
  /// 成就稀有度
  final AchievementRarity rarity;
  
  /// 奖励经验值
  final int experienceReward;
  
  /// 奖励虚拟货币
  final double wageReward;
  
  /// 解锁条件描述
  final String unlockCondition;
  
  /// 是否为隐藏成就
  final bool isHidden;
  
  /// 成就进度最大值 (0表示一次性成就)
  final int maxProgress;
  
  /// 创建时间
  final DateTime createdAt;

  const Achievement({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.type,
    required this.rarity,
    required this.experienceReward,
    required this.wageReward,
    required this.unlockCondition,
    this.isHidden = false,
    this.maxProgress = 1,
    required this.createdAt,
  });

  factory Achievement.fromJson(Map<String, dynamic> json) => _$AchievementFromJson(json);
  Map<String, dynamic> toJson() => _$AchievementToJson(this);

  /// 获取稀有度颜色
  String get rarityColor {
    switch (rarity) {
      case AchievementRarity.common:
        return '#9B9A97';
      case AchievementRarity.rare:
        return '#2E7EED';
      case AchievementRarity.epic:
        return '#7C3AED';
      case AchievementRarity.legendary:
        return '#F59E0B';
      case AchievementRarity.mythic:
        return '#E03E3E';
    }
  }

  /// 获取稀有度名称
  String get rarityName {
    switch (rarity) {
      case AchievementRarity.common:
        return '普通';
      case AchievementRarity.rare:
        return '稀有';
      case AchievementRarity.epic:
        return '史诗';
      case AchievementRarity.legendary:
        return '传说';
      case AchievementRarity.mythic:
        return '神话';
    }
  }

  /// 获取类型名称
  String get typeName {
    switch (type) {
      case AchievementType.learning:
        return '学习成就';
      case AchievementType.memory:
        return '记忆成就';
      case AchievementType.health:
        return '健康成就';
      case AchievementType.social:
        return '社交成就';
      case AchievementType.milestone:
        return '里程碑';
      case AchievementType.special:
        return '特殊成就';
    }
  }

  /// 是否为进度型成就
  bool get isProgressAchievement => maxProgress > 1;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Achievement &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'Achievement(id: $id, name: $name, rarity: $rarity)';
}

/// 用户成就进度
@JsonSerializable()
class UserAchievementProgress {
  /// 成就ID
  final String achievementId;
  
  /// 当前进度
  final int currentProgress;
  
  /// 是否已解锁
  final bool isUnlocked;
  
  /// 解锁时间
  final DateTime? unlockedAt;
  
  /// 首次进度更新时间
  final DateTime firstProgressAt;
  
  /// 最后进度更新时间
  final DateTime lastProgressAt;

  const UserAchievementProgress({
    required this.achievementId,
    required this.currentProgress,
    required this.isUnlocked,
    this.unlockedAt,
    required this.firstProgressAt,
    required this.lastProgressAt,
  });

  factory UserAchievementProgress.fromJson(Map<String, dynamic> json) => 
      _$UserAchievementProgressFromJson(json);
  Map<String, dynamic> toJson() => _$UserAchievementProgressToJson(this);

  /// 创建新的进度记录
  factory UserAchievementProgress.create(String achievementId) {
    final now = DateTime.now();
    return UserAchievementProgress(
      achievementId: achievementId,
      currentProgress: 0,
      isUnlocked: false,
      firstProgressAt: now,
      lastProgressAt: now,
    );
  }

  /// 更新进度
  UserAchievementProgress updateProgress(int newProgress, {bool? unlock}) {
    final now = DateTime.now();
    return UserAchievementProgress(
      achievementId: achievementId,
      currentProgress: newProgress,
      isUnlocked: unlock ?? isUnlocked,
      unlockedAt: unlock == true ? now : unlockedAt,
      firstProgressAt: firstProgressAt,
      lastProgressAt: now,
    );
  }

  /// 解锁成就
  UserAchievementProgress unlock() {
    return updateProgress(currentProgress, unlock: true);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserAchievementProgress &&
          runtimeType == other.runtimeType &&
          achievementId == other.achievementId;

  @override
  int get hashCode => achievementId.hashCode;

  @override
  String toString() => 
      'UserAchievementProgress(id: $achievementId, progress: $currentProgress, unlocked: $isUnlocked)';
}
