// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_level.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserLevel _$UserLevelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('UserLevel', json, ($checkedConvert) {
  final val = UserLevel(
    userId: $checkedConvert('userId', (v) => v as String),
    level: $checkedConvert('level', (v) => (v as num).toInt()),
    currentExp: $checkedConvert('currentExp', (v) => (v as num).toInt()),
    requiredExp: $checkedConvert('requiredExp', (v) => (v as num).toInt()),
    rank: $checkedConvert('rank', (v) => $enumDecode(_$ScholarRankEnumMap, v)),
    rankLevel: $checkedConvert('rankLevel', (v) => (v as num).toInt()),
    totalExp: $checkedConvert('totalExp', (v) => (v as num).toInt()),
    createdAt: $checkedConvert('createdAt', (v) => DateTime.parse(v as String)),
    updatedAt: $checkedConvert('updatedAt', (v) => DateTime.parse(v as String)),
  );
  return val;
});

// ignore: unused_element
abstract class _$UserLevelPerFieldToJson {
  // ignore: unused_element
  static Object? userId(String instance) => instance;
  // ignore: unused_element
  static Object? level(int instance) => instance;
  // ignore: unused_element
  static Object? currentExp(int instance) => instance;
  // ignore: unused_element
  static Object? requiredExp(int instance) => instance;
  // ignore: unused_element
  static Object? rank(ScholarRank instance) => _$ScholarRankEnumMap[instance]!;
  // ignore: unused_element
  static Object? rankLevel(int instance) => instance;
  // ignore: unused_element
  static Object? totalExp(int instance) => instance;
  // ignore: unused_element
  static Object? createdAt(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? updatedAt(DateTime instance) => instance.toIso8601String();
}

Map<String, dynamic> _$UserLevelToJson(UserLevel instance) => <String, dynamic>{
  'userId': instance.userId,
  'level': instance.level,
  'currentExp': instance.currentExp,
  'requiredExp': instance.requiredExp,
  'rank': _$ScholarRankEnumMap[instance.rank]!,
  'rankLevel': instance.rankLevel,
  'totalExp': instance.totalExp,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

const _$ScholarRankEnumMap = {
  ScholarRank.bronze: 'bronze',
  ScholarRank.silver: 'silver',
  ScholarRank.gold: 'gold',
  ScholarRank.platinum: 'platinum',
  ScholarRank.diamond: 'diamond',
  ScholarRank.master: 'master',
  ScholarRank.grandmaster: 'grandmaster',
  ScholarRank.king: 'king',
};

SkillLevel _$SkillLevelFromJson(Map<String, dynamic> json) =>
    $checkedCreate('SkillLevel', json, ($checkedConvert) {
      final val = SkillLevel(
        skillType: $checkedConvert(
          'skillType',
          (v) => $enumDecode(_$SkillTypeEnumMap, v),
        ),
        level: $checkedConvert('level', (v) => (v as num).toInt()),
        currentExp: $checkedConvert('currentExp', (v) => (v as num).toInt()),
        requiredExp: $checkedConvert('requiredExp', (v) => (v as num).toInt()),
        totalExp: $checkedConvert('totalExp', (v) => (v as num).toInt()),
        updatedAt: $checkedConvert(
          'updatedAt',
          (v) => DateTime.parse(v as String),
        ),
      );
      return val;
    });

// ignore: unused_element
abstract class _$SkillLevelPerFieldToJson {
  // ignore: unused_element
  static Object? skillType(SkillType instance) => _$SkillTypeEnumMap[instance]!;
  // ignore: unused_element
  static Object? level(int instance) => instance;
  // ignore: unused_element
  static Object? currentExp(int instance) => instance;
  // ignore: unused_element
  static Object? requiredExp(int instance) => instance;
  // ignore: unused_element
  static Object? totalExp(int instance) => instance;
  // ignore: unused_element
  static Object? updatedAt(DateTime instance) => instance.toIso8601String();
}

Map<String, dynamic> _$SkillLevelToJson(SkillLevel instance) =>
    <String, dynamic>{
      'skillType': _$SkillTypeEnumMap[instance.skillType]!,
      'level': instance.level,
      'currentExp': instance.currentExp,
      'requiredExp': instance.requiredExp,
      'totalExp': instance.totalExp,
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$SkillTypeEnumMap = {
  SkillType.memory: 'memory',
  SkillType.timeManagement: 'timeManagement',
  SkillType.vocabulary: 'vocabulary',
  SkillType.fitness: 'fitness',
  SkillType.reflection: 'reflection',
};
