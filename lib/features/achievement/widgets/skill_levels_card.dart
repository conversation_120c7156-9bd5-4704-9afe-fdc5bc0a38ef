import 'dart:math';
import 'package:flutter/material.dart';
import '../models/user_level.dart';

/// 技能等级卡片组件
class SkillLevelsCard extends StatelessWidget {
  final Map<SkillType, SkillLevel> skillLevels;

  const SkillLevelsCard({super.key, required this.skillLevels});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '专业技能',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 16),

        ...SkillType.values.map((skillType) {
          final skillLevel = skillLevels[skillType];
          if (skillLevel == null) return const SizedBox();

          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: SkillLevelItem(skillLevel: skillLevel),
          );
        }),
      ],
    );
  }
}

/// 技能等级项目组件
class SkillLevelItem extends StatelessWidget {
  final SkillLevel skillLevel;

  const SkillLevelItem({super.key, required this.skillLevel});

  @override
  Widget build(BuildContext context) {
    final progressPercentage = skillLevel.currentExp / skillLevel.requiredExp;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF37352F).withValues(alpha: 0.1)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              // 技能图标
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: _getSkillColor().withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Center(
                  child: Text(
                    skillLevel.skillIcon,
                    style: const TextStyle(fontSize: 20),
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // 技能信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          skillLevel.skillName,
                          style: const TextStyle(
                            color: Color(0xFF37352F),
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getSkillColor().withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            'LV.${skillLevel.level}',
                            style: TextStyle(
                              color: _getSkillColor(),
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    // 经验值进度
                    Row(
                      children: [
                        Text(
                          '${skillLevel.currentExp}',
                          style: TextStyle(
                            color: _getSkillColor(),
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.symmetric(horizontal: 8),
                            height: 6,
                            decoration: BoxDecoration(
                              color: _getSkillColor().withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(3),
                            ),
                            child: FractionallySizedBox(
                              alignment: Alignment.centerLeft,
                              widthFactor: progressPercentage,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: _getSkillColor(),
                                  borderRadius: BorderRadius.circular(3),
                                ),
                              ),
                            ),
                          ),
                        ),
                        Text(
                          '${skillLevel.requiredExp}',
                          style: const TextStyle(
                            color: Color(0xFF9B9A97),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // 技能描述和统计
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _getSkillDescription(),
                style: const TextStyle(color: Color(0xFF9B9A97), fontSize: 12),
              ),
              Text(
                '总经验: ${skillLevel.totalExp}',
                style: const TextStyle(color: Color(0xFF9B9A97), fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 获取技能颜色
  Color _getSkillColor() {
    switch (skillLevel.skillType) {
      case SkillType.memory:
        return const Color(0xFF7C3AED);
      case SkillType.timeManagement:
        return const Color(0xFF2E7EED);
      case SkillType.vocabulary:
        return const Color(0xFF0F7B6C);
      case SkillType.fitness:
        return const Color(0xFFE03E3E);
      case SkillType.reflection:
        return const Color(0xFFF59E0B);
    }
  }

  /// 获取技能描述
  String _getSkillDescription() {
    switch (skillLevel.skillType) {
      case SkillType.memory:
        return '通过使用记忆宫殿提升记忆技能';
      case SkillType.timeManagement:
        return '通过完成时间盒子提升时间管理技能';
      case SkillType.vocabulary:
        return '通过学习单词提升词汇技能';
      case SkillType.fitness:
        return '通过完成运动动作提升健身技能';
      case SkillType.reflection:
        return '通过写反思日志提升反思技能';
    }
  }
}

/// 技能等级雷达图组件
class SkillRadarChart extends StatelessWidget {
  final Map<SkillType, SkillLevel> skillLevels;
  final double size;

  const SkillRadarChart({
    super.key,
    required this.skillLevels,
    this.size = 200,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF37352F).withValues(alpha: 0.1)),
      ),
      child: CustomPaint(
        painter: _SkillRadarPainter(skillLevels),
        child: const SizedBox.expand(),
      ),
    );
  }
}

/// 技能雷达图绘制器
class _SkillRadarPainter extends CustomPainter {
  final Map<SkillType, SkillLevel> skillLevels;

  _SkillRadarPainter(this.skillLevels);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 20;

    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1
      ..color = const Color(0xFF9B9A97).withValues(alpha: 0.3);

    // 绘制背景网格
    for (int i = 1; i <= 5; i++) {
      canvas.drawCircle(center, radius * i / 5, paint);
    }

    // 绘制技能轴线
    final skillCount = SkillType.values.length;
    final angleStep = 2 * 3.14159 / skillCount;

    for (int i = 0; i < skillCount; i++) {
      final angle = -3.14159 / 2 + i * angleStep;
      final endPoint = Offset(
        center.dx + radius * 0.8 * cos(angle),
        center.dy + radius * 0.8 * sin(angle),
      );
      canvas.drawLine(center, endPoint, paint);
    }

    // 绘制技能数据
    final skillPaint = Paint()
      ..style = PaintingStyle.fill
      ..color = const Color(0xFF2E7EED).withValues(alpha: 0.3);

    final skillStrokePaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2
      ..color = const Color(0xFF2E7EED);

    final path = Path();
    bool isFirst = true;

    for (int i = 0; i < skillCount; i++) {
      final skillType = SkillType.values[i];
      final skillLevel = skillLevels[skillType];
      final level = skillLevel?.level ?? 1;
      final normalizedLevel = (level / 10).clamp(0.0, 1.0); // 假设最大等级为10

      final angle = -3.14159 / 2 + i * angleStep;
      final point = Offset(
        center.dx + radius * 0.8 * normalizedLevel * cos(angle),
        center.dy + radius * 0.8 * normalizedLevel * sin(angle),
      );

      if (isFirst) {
        path.moveTo(point.dx, point.dy);
        isFirst = false;
      } else {
        path.lineTo(point.dx, point.dy);
      }

      // 绘制技能点
      canvas.drawCircle(point, 4, skillStrokePaint);
    }

    path.close();
    canvas.drawPath(path, skillPaint);
    canvas.drawPath(path, skillStrokePaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// 迷你技能显示组件
class MiniSkillDisplay extends StatelessWidget {
  final SkillType skillType;
  final SkillLevel skillLevel;

  const MiniSkillDisplay({
    super.key,
    required this.skillType,
    required this.skillLevel,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getSkillColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(skillLevel.skillIcon, style: const TextStyle(fontSize: 12)),
          const SizedBox(width: 4),
          Text(
            'LV.${skillLevel.level}',
            style: TextStyle(
              color: _getSkillColor(),
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Color _getSkillColor() {
    switch (skillType) {
      case SkillType.memory:
        return const Color(0xFF7C3AED);
      case SkillType.timeManagement:
        return const Color(0xFF2E7EED);
      case SkillType.vocabulary:
        return const Color(0xFF0F7B6C);
      case SkillType.fitness:
        return const Color(0xFFE03E3E);
      case SkillType.reflection:
        return const Color(0xFFF59E0B);
    }
  }
}
