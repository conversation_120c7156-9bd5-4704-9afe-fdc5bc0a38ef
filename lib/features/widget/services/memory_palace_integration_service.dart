import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/widget_models.dart';

/// 记忆锚点类型枚举
enum AnchorType {
  study, // 学习
  life, // 生活
  work, // 工作
  inspiration, // 灵感
}

/// 记忆锚点数据模型
class MemoryAnchor {
  final String id;
  double xRatio; // X坐标比例 (0.0-1.0)
  double yRatio; // Y坐标比例 (0.0-1.0)
  String content; // 改为可变，支持编辑
  final String? authorName;
  int likes;
  final DateTime createdAt;
  final AnchorType type;

  // 新增字段用于小组件集成
  final String? location; // 位置信息
  final String? emotion; // 情感标签
  final List<String>? tags; // 标签列表

  MemoryAnchor({
    required this.id,
    required this.xRatio,
    required this.yRatio,
    required this.content,
    this.authorName,
    required this.likes,
    required this.createdAt,
    required this.type,
    this.location,
    this.emotion,
    this.tags,
  });

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'xRatio': xRatio,
      'yRatio': yRatio,
      'content': content,
      'authorName': authorName,
      'likes': likes,
      'createdAt': createdAt.toIso8601String(),
      'type': type.index,
      'location': location,
      'emotion': emotion,
      'tags': tags,
    };
  }

  /// 从JSON创建对象
  static MemoryAnchor fromJson(Map<String, dynamic> json) {
    return MemoryAnchor(
      id: json['id'] as String,
      xRatio: (json['xRatio'] as num).toDouble(),
      yRatio: (json['yRatio'] as num).toDouble(),
      content: json['content'] as String,
      authorName: json['authorName'] as String?,
      likes: json['likes'] as int,
      createdAt: DateTime.parse(json['createdAt'] as String),
      type: AnchorType.values[json['type'] as int],
      location: json['location'] as String?,
      emotion: json['emotion'] as String?,
      tags: (json['tags'] as List?)?.cast<String>(),
    );
  }

  /// 复制并修改
  MemoryAnchor copyWith({
    String? id,
    double? xRatio,
    double? yRatio,
    String? content,
    String? authorName,
    int? likes,
    DateTime? createdAt,
    AnchorType? type,
    String? location,
    String? emotion,
    List<String>? tags,
  }) {
    return MemoryAnchor(
      id: id ?? this.id,
      xRatio: xRatio ?? this.xRatio,
      yRatio: yRatio ?? this.yRatio,
      content: content ?? this.content,
      authorName: authorName ?? this.authorName,
      likes: likes ?? this.likes,
      createdAt: createdAt ?? this.createdAt,
      type: type ?? this.type,
      location: location ?? this.location,
      emotion: emotion ?? this.emotion,
      tags: tags ?? this.tags,
    );
  }
}

/// 记忆宫殿集成服务
///
/// 专门处理小组件与记忆宫殿系统的集成
class MemoryPalaceIntegrationService {
  static final MemoryPalaceIntegrationService _instance =
      MemoryPalaceIntegrationService._internal();
  factory MemoryPalaceIntegrationService() => _instance;
  MemoryPalaceIntegrationService._internal();

  // static const String _anchorsKey = 'widget_memory_anchors'; // 暂未使用

  /// 为拍照图片创建记忆锚点
  ///
  /// [imagePath] 图片路径
  /// [albumId] 相册ID
  /// [annotation] 用户标注
  /// [location] 位置信息（可选）
  /// [emotion] 情感标签（可选）
  Future<WidgetActionResult> createMemoryAnchor({
    required String imagePath,
    required String albumId,
    required String annotation,
    String? location,
    String? emotion,
    List<String>? tags,
  }) async {
    try {
      print('📍 为图片创建记忆锚点: $imagePath');

      // 创建记忆锚点
      final anchor = MemoryAnchor(
        id: 'widget_${DateTime.now().millisecondsSinceEpoch}',
        xRatio: 0.5, // 默认居中位置
        yRatio: 0.5,
        content: annotation.isNotEmpty ? annotation : '随手拍记录',
        authorName: '我',
        likes: 0,
        createdAt: DateTime.now(),
        type: AnchorType.life, // 小组件拍照默认为生活类型
        location: location,
        emotion: emotion,
        tags: tags ?? ['随手拍'],
      );

      // 保存锚点到对应的场景
      await _saveAnchorToScene(albumId, imagePath, anchor);

      print('✅ 记忆锚点创建成功: ${anchor.id}');

      return WidgetActionResult.success(
        message: '记忆锚点创建成功',
        data: {
          'anchor_id': anchor.id,
          'album_id': albumId,
          'image_path': imagePath,
        },
      );
    } catch (e) {
      print('❌ 创建记忆锚点失败: $e');
      return WidgetActionResult.failure(
        message: '创建记忆锚点失败: ${e.toString()}',
        errorCode: 'ANCHOR_CREATION_FAILED',
      );
    }
  }

  /// 保存锚点到场景
  Future<void> _saveAnchorToScene(
    String albumId,
    String imagePath,
    MemoryAnchor anchor,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 生成场景存储键
      final sceneKey = _getSceneStorageKey(albumId, imagePath);

      // 获取现有锚点
      final existingAnchors = await _loadSceneAnchors(sceneKey);

      // 添加新锚点
      existingAnchors.add(anchor);

      // 保存更新后的锚点列表
      final jsonList = existingAnchors
          .map((anchor) => anchor.toJson())
          .toList();
      await prefs.setString(sceneKey, json.encode(jsonList));

      print('💾 锚点已保存到场景: $sceneKey');
    } catch (e) {
      print('❌ 保存锚点到场景失败: $e');
      rethrow;
    }
  }

  /// 加载场景锚点
  Future<List<MemoryAnchor>> _loadSceneAnchors(String sceneKey) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(sceneKey);

      if (jsonString == null) {
        return [];
      }

      final List<dynamic> jsonList = json.decode(jsonString);
      return jsonList.map((json) => MemoryAnchor.fromJson(json)).toList();
    } catch (e) {
      print('❌ 加载场景锚点失败: $e');
      return [];
    }
  }

  /// 生成场景存储键
  String _getSceneStorageKey(String albumId, String imagePath) {
    // 使用相册ID和图片路径的哈希值作为键
    final imageHash = imagePath.hashCode.abs().toString();
    return 'scene_${albumId}_$imageHash';
  }

  /// 批量创建记忆锚点
  ///
  /// 用于处理多张图片的情况
  Future<WidgetActionResult> batchCreateMemoryAnchors({
    required List<String> imagePaths,
    required String albumId,
    required List<String> annotations,
    String? location,
    String? emotion,
    List<String>? tags,
  }) async {
    try {
      print('📍 批量创建记忆锚点，数量: ${imagePaths.length}');

      final List<String> createdAnchorIds = [];

      for (int i = 0; i < imagePaths.length; i++) {
        final imagePath = imagePaths[i];
        final annotation = i < annotations.length ? annotations[i] : '随手拍记录';

        final result = await createMemoryAnchor(
          imagePath: imagePath,
          albumId: albumId,
          annotation: annotation,
          location: location,
          emotion: emotion,
          tags: tags,
        );

        if (result.isSuccess) {
          createdAnchorIds.add(result.data!['anchor_id'] as String);
        } else {
          print('⚠️ 第${i + 1}张图片锚点创建失败: ${result.message}');
        }
      }

      if (createdAnchorIds.isEmpty) {
        return WidgetActionResult.failure(
          message: '所有记忆锚点创建失败',
          errorCode: 'BATCH_CREATION_FAILED',
        );
      }

      return WidgetActionResult.success(
        message: '成功创建 ${createdAnchorIds.length} 个记忆锚点',
        data: {
          'created_anchor_ids': createdAnchorIds,
          'total_count': createdAnchorIds.length,
        },
      );
    } catch (e) {
      print('❌ 批量创建记忆锚点失败: $e');
      return WidgetActionResult.failure(
        message: '批量创建失败: ${e.toString()}',
        errorCode: 'BATCH_CREATION_ERROR',
      );
    }
  }

  /// 获取图片的记忆锚点
  Future<List<MemoryAnchor>> getImageAnchors(
    String albumId,
    String imagePath,
  ) async {
    try {
      final sceneKey = _getSceneStorageKey(albumId, imagePath);
      return await _loadSceneAnchors(sceneKey);
    } catch (e) {
      print('❌ 获取图片锚点失败: $e');
      return [];
    }
  }

  /// 更新记忆锚点
  Future<WidgetActionResult> updateMemoryAnchor({
    required String albumId,
    required String imagePath,
    required String anchorId,
    String? content,
    double? xRatio,
    double? yRatio,
    String? location,
    String? emotion,
    List<String>? tags,
  }) async {
    try {
      final sceneKey = _getSceneStorageKey(albumId, imagePath);
      final anchors = await _loadSceneAnchors(sceneKey);

      // 查找并更新锚点
      bool found = false;
      for (int i = 0; i < anchors.length; i++) {
        if (anchors[i].id == anchorId) {
          anchors[i] = anchors[i].copyWith(
            content: content,
            xRatio: xRatio,
            yRatio: yRatio,
            location: location,
            emotion: emotion,
            tags: tags,
          );
          found = true;
          break;
        }
      }

      if (!found) {
        return WidgetActionResult.failure(
          message: '记忆锚点不存在',
          errorCode: 'ANCHOR_NOT_FOUND',
        );
      }

      // 保存更新后的锚点列表
      final prefs = await SharedPreferences.getInstance();
      final jsonList = anchors.map((anchor) => anchor.toJson()).toList();
      await prefs.setString(sceneKey, json.encode(jsonList));

      return WidgetActionResult.success(
        message: '记忆锚点更新成功',
        data: {'anchor_id': anchorId},
      );
    } catch (e) {
      print('❌ 更新记忆锚点失败: $e');
      return WidgetActionResult.failure(
        message: '更新失败: ${e.toString()}',
        errorCode: 'ANCHOR_UPDATE_FAILED',
      );
    }
  }

  /// 删除记忆锚点
  Future<WidgetActionResult> deleteMemoryAnchor({
    required String albumId,
    required String imagePath,
    required String anchorId,
  }) async {
    try {
      final sceneKey = _getSceneStorageKey(albumId, imagePath);
      final anchors = await _loadSceneAnchors(sceneKey);

      // 删除指定锚点
      final updatedAnchors = anchors
          .where((anchor) => anchor.id != anchorId)
          .toList();

      if (updatedAnchors.length == anchors.length) {
        return WidgetActionResult.failure(
          message: '记忆锚点不存在',
          errorCode: 'ANCHOR_NOT_FOUND',
        );
      }

      // 保存更新后的锚点列表
      final prefs = await SharedPreferences.getInstance();
      final jsonList = updatedAnchors.map((anchor) => anchor.toJson()).toList();
      await prefs.setString(sceneKey, json.encode(jsonList));

      return WidgetActionResult.success(
        message: '记忆锚点删除成功',
        data: {'deleted_anchor_id': anchorId},
      );
    } catch (e) {
      print('❌ 删除记忆锚点失败: $e');
      return WidgetActionResult.failure(
        message: '删除失败: ${e.toString()}',
        errorCode: 'ANCHOR_DELETE_FAILED',
      );
    }
  }

  /// 获取统计信息
  Future<Map<String, dynamic>> getAnchorStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs
          .getKeys()
          .where((key) => key.startsWith('scene_'))
          .toList();

      int totalAnchors = 0;
      int totalScenes = keys.length;

      for (final key in keys) {
        final anchors = await _loadSceneAnchors(key);
        totalAnchors += anchors.length;
      }

      return {
        'total_anchors': totalAnchors,
        'total_scenes': totalScenes,
        'average_anchors_per_scene': totalScenes > 0
            ? totalAnchors / totalScenes
            : 0,
      };
    } catch (e) {
      print('❌ 获取锚点统计失败: $e');
      return {
        'total_anchors': 0,
        'total_scenes': 0,
        'average_anchors_per_scene': 0,
        'error': e.toString(),
      };
    }
  }
}
