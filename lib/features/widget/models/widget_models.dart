/// Widget操作结果类
///
/// 用于统一处理Widget相关操作的返回结果
class WidgetActionResult {
  /// 操作是否成功
  final bool isSuccess;

  /// 结果消息
  final String message;

  /// 错误代码（失败时使用）
  final String? errorCode;

  /// 返回数据（成功时使用）
  final Map<String, dynamic>? data;

  /// 时间戳
  final DateTime timestamp;

  const WidgetActionResult._({
    required this.isSuccess,
    required this.message,
    this.errorCode,
    this.data,
    required this.timestamp,
  });

  /// 创建成功结果
  factory WidgetActionResult.success({
    required String message,
    Map<String, dynamic>? data,
  }) {
    return WidgetActionResult._(
      isSuccess: true,
      message: message,
      data: data,
      timestamp: DateTime.now(),
    );
  }

  /// 创建失败结果
  factory WidgetActionResult.failure({
    required String message,
    String? errorCode,
    Map<String, dynamic>? data,
  }) {
    return WidgetActionResult._(
      isSuccess: false,
      message: message,
      errorCode: errorCode,
      data: data,
      timestamp: DateTime.now(),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'isSuccess': isSuccess,
      'message': message,
      'errorCode': errorCode,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// 从JSON创建
  factory WidgetActionResult.fromJson(Map<String, dynamic> json) {
    return WidgetActionResult._(
      isSuccess: json['isSuccess'] ?? false,
      message: json['message'] ?? '',
      errorCode: json['errorCode'],
      data: json['data'],
      timestamp: DateTime.parse(
        json['timestamp'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  @override
  String toString() {
    return 'WidgetActionResult(isSuccess: $isSuccess, message: $message, errorCode: $errorCode, data: $data, timestamp: $timestamp)';
  }
}

/// Widget类型枚举
enum WidgetType { quickCamera, memoryPalace, timeBox }

/// Widget配置类
class WidgetConfig {
  final String id;
  final WidgetType type;
  final String title;
  final bool enabled;
  final DateTime createdAt;
  final DateTime lastUpdated;

  const WidgetConfig({
    required this.id,
    required this.type,
    required this.title,
    required this.enabled,
    required this.createdAt,
    required this.lastUpdated,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'enabled': enabled,
      'createdAt': createdAt.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory WidgetConfig.fromJson(Map<String, dynamic> json) {
    return WidgetConfig(
      id: json['id'] ?? '',
      type: WidgetType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => WidgetType.quickCamera,
      ),
      title: json['title'] ?? '',
      enabled: json['enabled'] ?? false,
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      lastUpdated: DateTime.parse(
        json['lastUpdated'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }
}

/// 快速拍照Widget数据类
class QuickCameraWidgetData {
  final String? lastImagePath;
  final DateTime? lastCaptureTime;
  final int todayCount;
  final int monthlyCount;

  const QuickCameraWidgetData({
    this.lastImagePath,
    this.lastCaptureTime,
    required this.todayCount,
    required this.monthlyCount,
  });

  factory QuickCameraWidgetData.defaultData() {
    return const QuickCameraWidgetData(todayCount: 0, monthlyCount: 0);
  }

  QuickCameraWidgetData copyWith({
    String? lastImagePath,
    DateTime? lastCaptureTime,
    int? todayCount,
    int? monthlyCount,
  }) {
    return QuickCameraWidgetData(
      lastImagePath: lastImagePath ?? this.lastImagePath,
      lastCaptureTime: lastCaptureTime ?? this.lastCaptureTime,
      todayCount: todayCount ?? this.todayCount,
      monthlyCount: monthlyCount ?? this.monthlyCount,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'lastImagePath': lastImagePath,
      'lastCaptureTime': lastCaptureTime?.toIso8601String(),
      'todayCount': todayCount,
      'monthlyCount': monthlyCount,
    };
  }

  factory QuickCameraWidgetData.fromJson(Map<String, dynamic> json) {
    return QuickCameraWidgetData(
      lastImagePath: json['lastImagePath'],
      lastCaptureTime: json['lastCaptureTime'] != null
          ? DateTime.parse(json['lastCaptureTime'])
          : null,
      todayCount: json['todayCount'] ?? 0,
      monthlyCount: json['monthlyCount'] ?? 0,
    );
  }
}
