import 'package:flutter/material.dart';

/// 用户指南页面
///
/// 提供详细的应用使用指南，包括快速入门、功能详解、学习方法等
/// 采用Notion风格设计，白色背景，12px圆角边框
class UserGuidePage extends StatefulWidget {
  final String section;
  
  const UserGuidePage({
    super.key,
    required this.section,
  });

  @override
  State<UserGuidePage> createState() => _UserGuidePageState();
}

class _UserGuidePageState extends State<UserGuidePage> {
  late String _currentSection;
  final ScrollController _scrollController = ScrollController();

  // 指南内容数据
  final Map<String, Map<String, dynamic>> _guideContent = {
    'quick_start': {
      'title': '快速入门',
      'icon': Icons.rocket_launch_outlined,
      'sections': [
        {
          'title': '欢迎使用OneDay',
          'content': '''OneDay是一款专注于提升学习效率的应用，结合了时间管理、记忆训练和学习统计等功能。

让我们开始您的学习之旅吧！''',
        },
        {
          'title': '创建您的第一个时间盒子',
          'content': '''1. 点击底部导航栏的"时间盒子"
2. 点击右上角的"+"按钮
3. 填写任务名称（如：背单词）
4. 选择任务分类（如：英语）
5. 设置专注时长（建议25分钟）
6. 点击"创建"开始您的专注时光''',
        },
        {
          'title': '使用知忆相册',
          'content': '''知忆相册基于记忆宫殿法，帮助您更好地记忆知识点：

1. 拍摄或选择一张熟悉的场景照片
2. 在照片上点击添加知识点标记
3. 输入要记忆的内容
4. 通过空间位置关联记忆内容
5. 定期回顾巩固记忆效果''',
        },
      ],
    },
    'features': {
      'title': '功能详解',
      'icon': Icons.explore_outlined,
      'sections': [
        {
          'title': '时间盒子功能',
          'content': '''时间盒子采用番茄工作法原理，帮助您保持专注：

• 自定义专注时长（15-120分钟）
• 四种任务分类（计算机、数学、英语、政治）
• 休息提醒和动觉记忆训练
• 任务进度跟踪和统计分析
• 浮窗计时器，支持后台运行''',
        },
        {
          'title': '知忆相册功能',
          'content': '''基于记忆宫殿法的视觉记忆训练：

• 场景图片管理和分类
• 知识点标记和编辑
• 空间位置记忆关联
• 记忆效果测试和评估
• 学习进度统计和分析''',
        },
        {
          'title': '动觉记忆训练',
          'content': '''结合身体动作的记忆训练方法：

• 自定义动作库管理
• PAO记忆法训练
• 词汇记忆训练
• 动作与知识点关联
• 训练效果统计分析''',
        },
      ],
    },
    'methods': {
      'title': '学习方法',
      'icon': Icons.psychology_outlined,
      'sections': [
        {
          'title': '番茄工作法',
          'content': '''番茄工作法是一种时间管理方法：

1. 选择一个任务
2. 设定25分钟的专注时间
3. 专注工作，避免干扰
4. 休息5分钟
5. 每4个番茄钟后，休息15-30分钟

OneDay的时间盒子功能完美支持这种方法。''',
        },
        {
          'title': '记忆宫殿法',
          'content': '''记忆宫殿法是一种古老而有效的记忆技巧：

1. 选择一个熟悉的地点（如您的家）
2. 在这个地点中规划一条路线
3. 将要记忆的信息放置在路线的各个位置
4. 通过想象走过这条路线来回忆信息

知忆相册功能让这个方法变得更加直观和易用。''',
        },
        {
          'title': '动觉记忆法',
          'content': '''动觉记忆法通过身体动作来增强记忆：

1. 为每个要记忆的内容设计一个动作
2. 重复练习动作与内容的关联
3. 通过身体记忆来回忆信息
4. 结合视觉和听觉刺激增强效果

动觉记忆训练功能提供了完整的训练体系。''',
        },
      ],
    },
    'faq': {
      'title': '常见问题',
      'icon': Icons.quiz_outlined,
      'sections': [
        {
          'title': '时间盒子相关问题',
          'content': '''Q: 为什么我的计时器停止了？
A: 请检查手机的省电模式设置，建议将OneDay添加到白名单中。

Q: 如何修改任务分类？
A: 在设置页面可以自定义任务分类名称和颜色。

Q: 休息时间可以跳过吗？
A: 可以，点击休息界面的"跳过"按钮即可提前结束休息。''',
        },
        {
          'title': '知忆相册相关问题',
          'content': '''Q: 支持哪些图片格式？
A: 支持JPG、PNG、WEBP等常见格式，建议使用JPG格式以获得最佳性能。

Q: 如何备份我的相册数据？
A: 在设置页面的数据管理中可以导出相册数据。

Q: 知识点标记数量有限制吗？
A: 每张图片最多支持50个知识点标记。''',
        },
        {
          'title': '其他问题',
          'content': '''Q: 如何同步数据到其他设备？
A: 目前支持本地备份，云同步功能正在开发中。

Q: 应用占用存储空间过大怎么办？
A: 可以在设置中清理缓存，或删除不需要的图片和数据。

Q: 如何联系客服？
A: 可以通过帮助与反馈页面的联系方式联系我们。''',
        },
      ],
    },
  };

  @override
  void initState() {
    super.initState();
    _currentSection = widget.section;
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final content = _guideContent[_currentSection];
    if (content == null) {
      return _buildErrorPage();
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        centerTitle: false,
        title: Row(
          children: [
            Icon(
              content['icon'] as IconData,
              color: const Color(0xFF2E7EED),
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(
              content['title'] as String,
              style: const TextStyle(
                color: Color(0xFF37352F),
                fontSize: 22,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.arrow_back, color: Color(0xFF37352F)),
        ),
        actions: [
          // 切换指南类型的下拉菜单
          PopupMenuButton<String>(
            icon: const Icon(Icons.menu_book, color: Color(0xFF37352F)),
            onSelected: (value) {
              setState(() {
                _currentSection = value;
              });
              _scrollController.animateTo(
                0,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            },
            itemBuilder: (context) => _guideContent.entries.map((entry) {
              return PopupMenuItem<String>(
                value: entry.key,
                child: Row(
                  children: [
                    Icon(
                      entry.value['icon'] as IconData,
                      color: const Color(0xFF2E7EED),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(entry.value['title'] as String),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 指南内容
            ...(content['sections'] as List<Map<String, String>>).map(
              (section) => _buildContentSection(
                section['title']!,
                section['content']!,
              ),
            ),
            
            const SizedBox(height: 32),
            
            // 底部导航提示
            _buildNavigationHint(),
          ],
        ),
      ),
    );
  }

  /// 构建内容章节
  Widget _buildContentSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFF37352F).withValues(alpha: 0.1)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  color: Color(0xFF37352F),
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                content,
                style: TextStyle(
                  color: const Color(0xFF37352F).withValues(alpha: 0.8),
                  fontSize: 16,
                  height: 1.6,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建导航提示
  Widget _buildNavigationHint() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF2E7EED).withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.lightbulb_outline,
            color: const Color(0xFF2E7EED),
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              '点击右上角菜单可以切换到其他指南内容',
              style: TextStyle(
                color: const Color(0xFF2E7EED),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建错误页面
  Widget _buildErrorPage() {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        title: const Text(
          '用户指南',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.arrow_back, color: Color(0xFF37352F)),
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: const Color(0xFF37352F).withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              '指南内容不存在',
              style: TextStyle(
                color: const Color(0xFF37352F).withValues(alpha: 0.7),
                fontSize: 18,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
