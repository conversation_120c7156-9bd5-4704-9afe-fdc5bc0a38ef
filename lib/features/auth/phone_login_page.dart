import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../shared/widgets/oneday_logo.dart';
import 'login_page.dart'; // 导入登录页面的组件
import 'providers/phone_login_provider.dart';

/// 手机号登录页 - 手机号+验证码认证入口
class PhoneLoginPage extends ConsumerStatefulWidget {
  const PhoneLoginPage({super.key});

  @override
  ConsumerState<PhoneLoginPage> createState() => _PhoneLoginPageState();
}

class _PhoneLoginPageState extends ConsumerState<PhoneLoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _codeController = TextEditingController();
  final _phoneFocusNode = FocusNode();
  final _codeFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // 监听手机号输入变化
    _phoneController.addListener(() {
      ref.read(phoneLoginProvider.notifier).updatePhoneNumber(_phoneController.text);
    });
    // 监听验证码输入变化
    _codeController.addListener(() {
      ref.read(phoneLoginProvider.notifier).updateVerificationCode(_codeController.text);
    });
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _codeController.dispose();
    _phoneFocusNode.dispose();
    _codeFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF37352F)),
          onPressed: () => context.pop(),
        ),
        title: Text(
          '手机号登录',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF37352F),
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(child: _buildBody()),
    );
  }

  /// 构建页面主体
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          const SizedBox(height: 20),
          _buildHeader(),
          const SizedBox(height: 40),
          _buildLoginForm(),
          const SizedBox(height: 32),
          _buildFooter(),
        ],
      ),
    );
  }

  /// 构建页面头部
  Widget _buildHeader() {
    return Column(
      children: [
        // OneDay Logo
        const OneDayLogo(size: 48, enableAnimation: false),
        const SizedBox(height: 20),
        // 标题文字
        Text(
          '手机号快速登录',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF37352F),
            height: 1.2,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '输入手机号码，我们将发送验证码到您的手机',
          style: TextStyle(
            fontSize: 14,
            color: const Color(0xFF787774),
            height: 1.5,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 构建登录表单
  Widget _buildLoginForm() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.16),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildPhoneField(),
            const SizedBox(height: 16),
            _buildCodeField(),
            const SizedBox(height: 24),
            _buildLoginButton(),
          ],
        ),
      ),
    );
  }

  /// 构建手机号输入框
  Widget _buildPhoneField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '手机号码',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            // 国家代码选择器
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
              decoration: BoxDecoration(
                border: Border.all(
                  color: const Color(0xFF37352F).withValues(alpha: 0.20),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '+86',
                style: TextStyle(
                  fontSize: 16,
                  color: const Color(0xFF37352F),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const SizedBox(width: 12),
            // 手机号输入框
            Expanded(
              child: CustomTextField(
                controller: _phoneController,
                hintText: '请输入手机号码',
                keyboardType: TextInputType.phone,
                focusNode: _phoneFocusNode,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(11),
                ],
                validator: _validatePhone,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建验证码输入框
  Widget _buildCodeField() {
    return Consumer(
      builder: (context, ref, child) {
        final state = ref.watch(phoneLoginProvider);
        final canSendCode = ref.watch(canSendCodeProvider);
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '验证码',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                // 验证码输入框
                Expanded(
                  child: CustomTextField(
                    controller: _codeController,
                    hintText: '请输入6位验证码',
                    keyboardType: TextInputType.number,
                    focusNode: _codeFocusNode,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(6),
                    ],
                    validator: _validateCode,
                  ),
                ),
                const SizedBox(width: 12),
                // 发送验证码按钮
                SizedBox(
                  width: 100,
                  height: 48,
                  child: ElevatedButton(
                    onPressed: canSendCode ? _handleSendCode : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2E7EED),
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shadowColor: Colors.transparent,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      disabledBackgroundColor: const Color(0xFF9B9A97),
                    ),
                    child: state.isLoading
                        ? SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            state.isCountingDown ? '${state.countdown}s' : '发送',
                            style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                          ),
                  ),
                ),
              ],
            ),
            // 错误提示
            if (state.error != null) ...[
              const SizedBox(height: 8),
              Text(
                state.error!,
                style: TextStyle(
                  fontSize: 12,
                  color: const Color(0xFFE03E3E),
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  /// 构建登录按钮
  Widget _buildLoginButton() {
    return Consumer(
      builder: (context, ref, child) {
        final state = ref.watch(phoneLoginProvider);
        final canLogin = ref.watch(canLoginProvider);
        
        return CustomButton(
          text: '登录',
          isLoading: state.isVerifying,
          onPressed: canLogin ? _handleLogin : null,
        );
      },
    );
  }

  /// 构建页面底部
  Widget _buildFooter() {
    return Column(
      children: [
        Text(
          '登录即表示您同意我们的服务条款和隐私政策',
          style: TextStyle(
            fontSize: 12,
            color: const Color(0xFF9B9A97),
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 验证手机号
  String? _validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入手机号码';
    }
    if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value)) {
      return '请输入有效的手机号码';
    }
    return null;
  }

  /// 验证验证码
  String? _validateCode(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入验证码';
    }
    if (value.length != 6) {
      return '验证码应为6位数字';
    }
    return null;
  }

  /// 处理发送验证码
  Future<void> _handleSendCode() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final success = await ref
        .read(phoneLoginProvider.notifier)
        .sendVerificationCode();

    if (success && mounted) {
      _showMessage('验证码已发送到您的手机');
      // 自动聚焦到验证码输入框
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          _codeFocusNode.requestFocus();
        }
      });
    }
  }

  /// 处理登录
  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final success = await ref
        .read(phoneLoginProvider.notifier)
        .verifyAndLogin();

    if (success && mounted) {
      context.go('/main');
    }
  }

  /// 显示消息提示
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF2E7EED),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
