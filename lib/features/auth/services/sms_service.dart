import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// SMS验证码服务
class SmsService {
  static final SmsService _instance = SmsService._internal();
  factory SmsService() => _instance;
  SmsService._internal();

  static const String _dailyCountKey = 'sms_daily_count';
  static const String _lastSendDateKey = 'sms_last_send_date';
  static const String _lastSendTimeKey = 'sms_last_send_time';
  static const int _maxDailyCount = 10; // 每日最大发送次数
  static const int _minIntervalSeconds = 60; // 最小发送间隔（秒）

  // 存储验证码的临时缓存（实际项目中应该在服务端验证）
  final Map<String, _SmsCode> _codeCache = {};

  /// 发送验证码
  Future<SmsResult> sendCode(String phoneNumber, String countryCode) async {
    try {
      // 检查发送频率限制
      final rateLimitResult = await _checkRateLimit();
      if (!rateLimitResult.success) {
        return SmsResult.failure(rateLimitResult.message);
      }

      // 生成6位验证码
      final code = _generateCode();

      // 模拟API调用延迟
      await Future.delayed(const Duration(milliseconds: 1500));

      // 在开发模式下打印验证码
      if (kDebugMode) {
        debugPrint('📱 SMS验证码: $code (手机号: $countryCode$phoneNumber)');
      }

      // 存储验证码（5分钟有效期）
      _codeCache[phoneNumber] = _SmsCode(
        code: code,
        expireTime: DateTime.now().add(const Duration(minutes: 5)),
        attempts: 0,
      );

      // 更新发送记录
      await _updateSendRecord();

      return SmsResult.success('验证码已发送');
    } catch (e) {
      debugPrint('发送验证码失败: $e');
      return SmsResult.failure('发送失败，请稍后重试');
    }
  }

  /// 验证验证码
  Future<SmsResult> verifyCode(String phoneNumber, String code) async {
    try {
      final cachedCode = _codeCache[phoneNumber];

      if (cachedCode == null) {
        return SmsResult.failure('验证码已过期，请重新获取');
      }

      // 检查是否过期
      if (DateTime.now().isAfter(cachedCode.expireTime)) {
        _codeCache.remove(phoneNumber);
        return SmsResult.failure('验证码已过期，请重新获取');
      }

      // 检查尝试次数
      if (cachedCode.attempts >= 3) {
        _codeCache.remove(phoneNumber);
        return SmsResult.failure('验证失败次数过多，请重新获取验证码');
      }

      // 验证码错误
      if (cachedCode.code != code) {
        cachedCode.attempts++;
        return SmsResult.failure('验证码错误，请重新输入');
      }

      // 验证成功，清除缓存
      _codeCache.remove(phoneNumber);
      return SmsResult.success('验证成功');
    } catch (e) {
      debugPrint('验证验证码失败: $e');
      return SmsResult.failure('验证失败，请稍后重试');
    }
  }

  /// 检查发送频率限制
  Future<SmsResult> _checkRateLimit() async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();

    // 检查最小发送间隔
    final lastSendTime = prefs.getInt(_lastSendTimeKey);
    if (lastSendTime != null) {
      final lastSend = DateTime.fromMillisecondsSinceEpoch(lastSendTime);
      final timeDiff = now.difference(lastSend).inSeconds;
      if (timeDiff < _minIntervalSeconds) {
        final remainingTime = _minIntervalSeconds - timeDiff;
        return SmsResult.failure('请等待$remainingTime秒后再试');
      }
    }

    // 检查每日发送次数
    final lastSendDate = prefs.getString(_lastSendDateKey);
    final todayStr = _formatDate(now);

    if (lastSendDate == todayStr) {
      final dailyCount = prefs.getInt(_dailyCountKey) ?? 0;
      if (dailyCount >= _maxDailyCount) {
        return SmsResult.failure('今日发送次数已达上限，请明天再试');
      }
    }

    return SmsResult.success('检查通过');
  }

  /// 更新发送记录
  Future<void> _updateSendRecord() async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();
    final todayStr = _formatDate(now);

    // 更新最后发送时间
    await prefs.setInt(_lastSendTimeKey, now.millisecondsSinceEpoch);

    // 更新每日发送次数
    final lastSendDate = prefs.getString(_lastSendDateKey);
    if (lastSendDate == todayStr) {
      final dailyCount = prefs.getInt(_dailyCountKey) ?? 0;
      await prefs.setInt(_dailyCountKey, dailyCount + 1);
    } else {
      await prefs.setString(_lastSendDateKey, todayStr);
      await prefs.setInt(_dailyCountKey, 1);
    }
  }

  /// 生成6位验证码
  String _generateCode() {
    final random = Random();
    return (100000 + random.nextInt(900000)).toString();
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 获取今日剩余发送次数
  Future<int> getRemainingCount() async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now();
    final todayStr = _formatDate(now);
    final lastSendDate = prefs.getString(_lastSendDateKey);

    if (lastSendDate == todayStr) {
      final dailyCount = prefs.getInt(_dailyCountKey) ?? 0;
      return _maxDailyCount - dailyCount;
    }

    return _maxDailyCount;
  }

  /// 获取下次可发送时间
  Future<Duration?> getNextSendTime() async {
    final prefs = await SharedPreferences.getInstance();
    final lastSendTime = prefs.getInt(_lastSendTimeKey);

    if (lastSendTime != null) {
      final lastSend = DateTime.fromMillisecondsSinceEpoch(lastSendTime);
      final now = DateTime.now();
      final timeDiff = now.difference(lastSend).inSeconds;

      if (timeDiff < _minIntervalSeconds) {
        return Duration(seconds: _minIntervalSeconds - timeDiff);
      }
    }

    return null;
  }

  /// 清除验证码缓存（用于测试）
  void clearCache() {
    _codeCache.clear();
  }

  /// 重置发送限制（用于测试）
  Future<void> resetLimits() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_dailyCountKey);
    await prefs.remove(_lastSendDateKey);
    await prefs.remove(_lastSendTimeKey);
  }
}

/// SMS操作结果
class SmsResult {
  final bool success;
  final String message;

  const SmsResult._(this.success, this.message);

  factory SmsResult.success(String message) => SmsResult._(true, message);
  factory SmsResult.failure(String message) => SmsResult._(false, message);
}

/// 验证码缓存项
class _SmsCode {
  final String code;
  final DateTime expireTime;
  int attempts;

  _SmsCode({required this.code, required this.expireTime, this.attempts = 0});
}

/// SMS服务异常
class SmsException implements Exception {
  final String message;
  final String? code;

  const SmsException(this.message, [this.code]);

  @override
  String toString() =>
      'SmsException: $message${code != null ? ' (code: $code)' : ''}';
}

/// SMS配置
class SmsConfig {
  static const String apiUrl = 'https://api.example.com/sms';
  static const String apiKey = 'your_api_key_here';
  static const int timeout = 30; // 超时时间（秒）
  static const int codeLength = 6; // 验证码长度
  static const int expireMinutes = 5; // 验证码有效期（分钟）
  static const int maxDailyCount = 10; // 每日最大发送次数
  static const int minIntervalSeconds = 60; // 最小发送间隔（秒）
  static const int maxAttempts = 3; // 最大验证尝试次数
}

/// SMS服务提供者
final smsServiceProvider = Provider<SmsService>((ref) {
  return SmsService();
});
