import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/sms_service.dart';

/// 手机号登录状态
class PhoneLoginState {
  final String phoneNumber;
  final String countryCode;
  final String verificationCode;
  final bool isCodeSent;
  final bool isLoading;
  final bool isVerifying;
  final int countdown;
  final String? error;
  final bool canResend;

  const PhoneLoginState({
    this.phoneNumber = '',
    this.countryCode = '+86',
    this.verificationCode = '',
    this.isCodeSent = false,
    this.isLoading = false,
    this.isVerifying = false,
    this.countdown = 0,
    this.error,
    this.canResend = true,
  });

  PhoneLoginState copyWith({
    String? phoneNumber,
    String? countryCode,
    String? verificationCode,
    bool? isCodeSent,
    bool? isLoading,
    bool? isVerifying,
    int? countdown,
    String? error,
    bool? canResend,
    bool clearError = false,
  }) {
    return PhoneLoginState(
      phoneNumber: phoneNumber ?? this.phoneNumber,
      countryCode: countryCode ?? this.countryCode,
      verificationCode: verificationCode ?? this.verificationCode,
      isCodeSent: isCodeSent ?? this.isCodeSent,
      isLoading: isLoading ?? this.isLoading,
      isVerifying: isVerifying ?? this.isVerifying,
      countdown: countdown ?? this.countdown,
      error: clearError ? null : (error ?? this.error),
      canResend: canResend ?? this.canResend,
    );
  }

  /// 是否可以发送验证码
  bool get canSendCode => phoneNumber.isNotEmpty && !isLoading && canResend;

  /// 是否正在倒计时
  bool get isCountingDown => countdown > 0;

  /// 是否有效（可以登录）
  bool get isValid => phoneNumber.isNotEmpty && verificationCode.length == 6;
}

/// 手机号登录状态管理器
class PhoneLoginNotifier extends StateNotifier<PhoneLoginState> {
  final SmsService _smsService;
  Timer? _countdownTimer;

  PhoneLoginNotifier(this._smsService) : super(const PhoneLoginState());

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  /// 更新手机号
  void updatePhoneNumber(String phoneNumber) {
    state = state.copyWith(
      phoneNumber: phoneNumber,
      clearError: true,
    );
  }

  /// 更新国家代码
  void updateCountryCode(String countryCode) {
    state = state.copyWith(
      countryCode: countryCode,
      clearError: true,
    );
  }

  /// 更新验证码
  void updateVerificationCode(String code) {
    state = state.copyWith(
      verificationCode: code,
      clearError: true,
    );
  }

  /// 发送验证码
  Future<bool> sendVerificationCode() async {
    if (!state.canSendCode) return false;

    state = state.copyWith(
      isLoading: true,
      clearError: true,
    );

    try {
      final result = await _smsService.sendCode(
        state.phoneNumber,
        state.countryCode,
      );

      if (result.success) {
        state = state.copyWith(
          isCodeSent: true,
          isLoading: false,
          countdown: 60,
          canResend: false,
        );
        _startCountdown();
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.message,
        );
        return false;
      }
    } catch (e) {
      debugPrint('发送验证码失败: $e');
      state = state.copyWith(
        isLoading: false,
        error: '发送失败，请稍后重试',
      );
      return false;
    }
  }

  /// 验证验证码并登录
  Future<bool> verifyAndLogin() async {
    if (!state.isValid) return false;

    state = state.copyWith(
      isVerifying: true,
      clearError: true,
    );

    try {
      final result = await _smsService.verifyCode(
        state.phoneNumber,
        state.verificationCode,
      );

      if (result.success) {
        state = state.copyWith(isVerifying: false);
        return true;
      } else {
        state = state.copyWith(
          isVerifying: false,
          error: result.message,
        );
        return false;
      }
    } catch (e) {
      debugPrint('验证验证码失败: $e');
      state = state.copyWith(
        isVerifying: false,
        error: '验证失败，请稍后重试',
      );
      return false;
    }
  }

  /// 重新发送验证码
  Future<bool> resendVerificationCode() async {
    if (state.isCountingDown) return false;
    return await sendVerificationCode();
  }

  /// 开始倒计时
  void _startCountdown() {
    _countdownTimer?.cancel();
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.countdown <= 1) {
        timer.cancel();
        state = state.copyWith(countdown: 0, canResend: true);
      } else {
        state = state.copyWith(countdown: state.countdown - 1);
      }
    });
  }

  /// 重置状态
  void reset() {
    _countdownTimer?.cancel();
    state = const PhoneLoginState();
  }
}

/// 手机号登录状态提供者
final phoneLoginProvider = StateNotifierProvider<PhoneLoginNotifier, PhoneLoginState>((ref) {
  final smsService = ref.watch(smsServiceProvider);
  return PhoneLoginNotifier(smsService);
});

/// 手机号验证状态提供者
final phoneValidationProvider = Provider<bool>((ref) {
  final state = ref.watch(phoneLoginProvider);
  final phoneNumber = state.phoneNumber;
  
  if (phoneNumber.isEmpty) return false;
  
  // 中国手机号验证
  if (state.countryCode == '+86') {
    return phoneNumber.length == 11 && RegExp(r'^1[3-9]\d{9}$').hasMatch(phoneNumber);
  }
  
  // 其他国家简单长度验证
  return phoneNumber.length >= 7 && phoneNumber.length <= 15;
});

/// 验证码验证状态提供者
final codeValidationProvider = Provider<bool>((ref) {
  final state = ref.watch(phoneLoginProvider);
  return state.verificationCode.length == 6;
});

/// 可以发送验证码状态提供者
final canSendCodeProvider = Provider<bool>((ref) {
  final state = ref.watch(phoneLoginProvider);
  final isPhoneValid = ref.watch(phoneValidationProvider);
  return isPhoneValid && !state.isLoading && state.canResend;
});

/// 可以登录状态提供者
final canLoginProvider = Provider<bool>((ref) {
  final state = ref.watch(phoneLoginProvider);
  final isPhoneValid = ref.watch(phoneValidationProvider);
  final isCodeValid = ref.watch(codeValidationProvider);
  return isPhoneValid && isCodeValid && state.isCodeSent && !state.isVerifying;
});
