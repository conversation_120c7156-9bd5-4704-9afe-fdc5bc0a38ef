import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/wechat_auth_service.dart';

/// 微信登录状态
class WechatLoginState {
  final bool isLoading;
  final bool isInitialized;
  final String? error;
  final WechatAuthData? authData;

  const WechatLoginState({
    this.isLoading = false,
    this.isInitialized = false,
    this.error,
    this.authData,
  });

  WechatLoginState copyWith({
    bool? isLoading,
    bool? isInitialized,
    String? error,
    WechatAuthData? authData,
    bool clearError = false,
    bool clearAuthData = false,
  }) {
    return WechatLoginState(
      isLoading: isLoading ?? this.isLoading,
      isInitialized: isInitialized ?? this.isInitialized,
      error: clearError ? null : (error ?? this.error),
      authData: clearAuthData ? null : (authData ?? this.authData),
    );
  }

  /// 是否可以进行登录
  bool get canLogin => isInitialized && !isLoading;

  @override
  String toString() {
    return 'WechatLoginState(isLoading: $isLoading, isInitialized: $isInitialized, error: $error, authData: $authData)';
  }
}

/// 微信登录状态管理器
class WechatLoginNotifier extends StateNotifier<WechatLoginState> {
  final WechatAuthService _wechatAuthService;

  WechatLoginNotifier(this._wechatAuthService) : super(const WechatLoginState()) {
    _initialize();
  }

  /// 初始化微信SDK
  Future<void> _initialize() async {
    try {
      state = state.copyWith(isLoading: true, clearError: true);
      
      final success = await _wechatAuthService.initialize();
      
      state = state.copyWith(
        isLoading: false,
        isInitialized: success,
        error: success ? null : '微信SDK初始化失败',
      );

      if (success) {
        debugPrint('✅ 微信登录提供者初始化成功');
      } else {
        debugPrint('❌ 微信登录提供者初始化失败');
      }
    } catch (e) {
      debugPrint('微信登录提供者初始化异常: $e');
      state = state.copyWith(
        isLoading: false,
        isInitialized: false,
        error: '初始化失败，请稍后重试',
      );
    }
  }

  /// 重新初始化
  Future<void> reinitialize() async {
    await _initialize();
  }

  /// 微信登录
  Future<bool> login() async {
    if (!state.canLogin) {
      debugPrint('❌ 微信登录条件不满足: ${state.toString()}');
      return false;
    }

    try {
      state = state.copyWith(isLoading: true, clearError: true, clearAuthData: true);

      final result = await _wechatAuthService.login();

      if (result.success && result.data != null) {
        state = state.copyWith(
          isLoading: false,
          authData: result.data,
        );
        debugPrint('✅ 微信登录成功');
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.message ?? '登录失败',
        );
        debugPrint('❌ 微信登录失败: ${result.message}');
        return false;
      }
    } catch (e) {
      debugPrint('微信登录异常: $e');
      state = state.copyWith(
        isLoading: false,
        error: '登录失败，请稍后重试',
      );
      return false;
    }
  }

  /// 检查微信是否已安装
  Future<bool> isWechatInstalled() async {
    try {
      return await _wechatAuthService.isWechatInstalled();
    } catch (e) {
      debugPrint('检查微信安装状态失败: $e');
      return false;
    }
  }

  /// 清除错误信息
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  /// 清除认证数据
  void clearAuthData() {
    state = state.copyWith(clearAuthData: true);
  }

  /// 重置状态
  void reset() {
    state = state.copyWith(
      isLoading: false,
      clearError: true,
      clearAuthData: true,
    );
  }

  @override
  void dispose() {
    _wechatAuthService.dispose();
    super.dispose();
  }
}

/// 微信登录状态提供者
final wechatLoginProvider = StateNotifierProvider<WechatLoginNotifier, WechatLoginState>((ref) {
  final wechatAuthService = ref.watch(wechatAuthServiceProvider);
  return WechatLoginNotifier(wechatAuthService);
});

/// 微信是否可用状态提供者
final wechatAvailableProvider = Provider<bool>((ref) {
  final state = ref.watch(wechatLoginProvider);
  return state.isInitialized && !state.isLoading;
});

/// 微信登录按钮是否可用状态提供者
final wechatLoginButtonEnabledProvider = Provider<bool>((ref) {
  final state = ref.watch(wechatLoginProvider);
  return state.canLogin;
});
