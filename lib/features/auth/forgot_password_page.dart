import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../shared/widgets/oneday_logo.dart';
import 'login_page.dart'; // 导入登录页面的组件

/// 忘记密码页 - 用户密码重置入口
class ForgotPasswordPage extends ConsumerStatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  ConsumerState<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends ConsumerState<ForgotPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  bool _isLoading = false;
  bool _emailSent = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Color(0xFF37352F),
            size: 20,
          ),
          onPressed: () {
            if (context.canPop()) {
              context.pop();
            } else {
              context.go('/login');
            }
          },
        ),
        title: Text(
          '忘记密码',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF37352F),
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(child: _buildBody()),
    );
  }

  /// 构建页面主体
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          const SizedBox(height: 40),
          _buildHeader(),
          const SizedBox(height: 48),
          if (!_emailSent) ...[
            _buildEmailForm(),
          ] else ...[
            _buildSuccessMessage(),
            const SizedBox(height: 24),
            _buildResendButton(),
          ],
        ],
      ),
    );
  }

  /// 构建页面头部
  Widget _buildHeader() {
    return Column(
      children: [
        // OneDay Logo
        const OneDayLogo(size: 64, enableAnimation: true),
        const SizedBox(height: 24),
        // 标题文字
        Text(
          _emailSent ? '邮件已发送' : '重置密码',
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF37352F),
            height: 1.2,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          _emailSent
              ? '我们已向您的邮箱发送了密码重置链接，请查收邮件并按照指示重置密码'
              : '请输入您的邮箱地址，我们将向您发送密码重置链接',
          style: TextStyle(
            fontSize: 16,
            color: const Color(0xFF787774),
            height: 1.5,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 构建邮箱表单
  Widget _buildEmailForm() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFF37352F).withValues(alpha: 0.16),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildEmailField(),
            const SizedBox(height: 24),
            _buildSendButton(),
          ],
        ),
      ),
    );
  }

  /// 构建邮箱输入框
  Widget _buildEmailField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '邮箱',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 8),
        CustomTextField(
          controller: _emailController,
          hintText: '请输入您注册时使用的邮箱地址',
          keyboardType: TextInputType.emailAddress,
          prefixIcon: Icons.email_outlined,
          validator: _validateEmail,
        ),
      ],
    );
  }

  /// 构建发送按钮
  Widget _buildSendButton() {
    return CustomButton(
      text: '发送重置链接',
      isLoading: _isLoading,
      onPressed: _handleSendResetEmail,
    );
  }

  /// 构建成功消息
  Widget _buildSuccessMessage() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: const Color(0xFF2E7EED).withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFF2E7EED).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.mark_email_read_outlined,
            size: 48,
            color: const Color(0xFF2E7EED),
          ),
          const SizedBox(height: 16),
          Text(
            '重置链接已发送',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '请检查您的邮箱 ${_emailController.text}，点击邮件中的链接重置密码。如果没有收到邮件，请检查垃圾邮件文件夹。',
            style: TextStyle(
              fontSize: 14,
              color: const Color(0xFF787774),
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 构建重新发送按钮
  Widget _buildResendButton() {
    return CustomButton(
      text: '重新发送',
      backgroundColor: Colors.white,
      textColor: const Color(0xFF2E7EED),
      onPressed: _handleResendEmail,
    );
  }

  /// 验证邮箱
  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入邮箱地址';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return '请输入有效的邮箱地址';
    }
    return null;
  }

  /// 处理发送重置邮件
  Future<void> _handleSendResetEmail() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 模拟发送重置邮件请求
      await Future.delayed(const Duration(seconds: 2));

      // 发送成功
      if (mounted) {
        setState(() {
          _emailSent = true;
        });
      }
    } catch (e) {
      // 显示错误提示
      if (mounted) {
        _showMessage('发送失败，请稍后重试', isError: true);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 处理重新发送邮件
  Future<void> _handleResendEmail() async {
    await _handleSendResetEmail();
    if (mounted && _emailSent) {
      _showMessage('重置链接已重新发送');
    }
  }

  /// 显示消息
  void _showMessage(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError
            ? const Color(0xFFE03E3E)
            : const Color(0xFF2E7EED),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
