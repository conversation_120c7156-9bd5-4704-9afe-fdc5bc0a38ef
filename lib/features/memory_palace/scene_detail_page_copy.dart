import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 记忆场景详情页 - AR实景记忆锚点系统
class SceneDetailPage extends ConsumerStatefulWidget {
  final String sceneId;
  final String sceneTitle;
  final String sceneImagePath;
  final List<String>? palaceImagePaths; // 新增：当前宫殿的所有图片

  const SceneDetailPage({
    super.key,
    required this.sceneId,
    required this.sceneTitle,
    required this.sceneImagePath,
    this.palaceImagePaths, // 可选参数，用于显示底部预览
  });

  @override
  ConsumerState<SceneDetailPage> createState() => _SceneDetailPageState();
}

class _SceneDetailPageState extends ConsumerState<SceneDetailPage>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  final TransformationController _transformationController =
      TransformationController();

  bool _isEditMode = false;
  bool _showAllAnchors = true;
  MemoryAnchor? _selectedAnchor;
  bool _showAddKnowledgePanel = false; // 控制添加知识点面板的显示
  // 位置选择模式相关状态
  bool _isPositionSelectionMode = false; // 是否处于位置选择模式

  // 键盘规避相关
  double _keyboardHeight = 0.0;
  bool _isKeyboardVisible = false;
  late AnimationController _keyboardAnimationController;
  late Animation<double> _keyboardAnimation;

  // 知识点数据
  List<MemoryAnchor> _anchors = [];

  // 用于存储每个场景的知识点数据的Map
  final Map<String, List<MemoryAnchor>> _sceneAnchorsCache = {};

  // 相关场景数据 - 根据传入的宫殿数据动态生成
  List<RelatedScene> _relatedScenes = [];

  // 当前场景信息
  String _currentSceneTitle = '图书馆';
  String _currentSceneImagePath =
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400';

  bool _isProcessingTap = false; // 添加防抖标记
  bool _showGuidanceBubble = false; // 控制指导气泡显示 - 只在选择知识点后显示

  final TextEditingController _knowledgeController = TextEditingController();
  final FocusNode _knowledgeFocusNode = FocusNode();
  Offset? _tapPosition;

  // 记录拖拽开始时的位置
  bool _isRepositioning = false;
  MemoryAnchor? _anchorToReposition;

  @override
  void initState() {
    super.initState();

    // 初始化键盘动画控制器
    _keyboardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 250), // 与系统键盘动画同步
      vsync: this,
    );

    _keyboardAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _keyboardAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // 添加键盘监听器
    WidgetsBinding.instance.addObserver(this);

    // 初始化当前场景信息
    _currentSceneTitle = widget.sceneTitle;
    _currentSceneImagePath = widget.sceneImagePath;

    // 根据传入的宫殿数据生成相关场景列表
    _initializeRelatedScenes();

    // 加载持久化的知识点数据
    _loadPersistedData();

    // 移除自动显示指导气泡的逻辑
  }

  /// 初始化相关场景数据
  void _initializeRelatedScenes() {
    if (widget.palaceImagePaths != null &&
        widget.palaceImagePaths!.isNotEmpty) {
      // 使用传入的宫殿图片数据，按照选择顺序排列
      _relatedScenes = widget.palaceImagePaths!.asMap().entries.map((entry) {
        final index = entry.key;
        final imagePath = entry.value;
        return RelatedScene(
          id: '${widget.sceneId}_$index',
          title: '${widget.sceneTitle} ${index + 1}',
          imagePath: imagePath,
          isSelected: imagePath == widget.sceneImagePath, // 当前图片标记为选中
        );
      }).toList();
    } else {
      // 如果没有传入图片数据，使用当前场景
      _relatedScenes = [
        RelatedScene(
          id: widget.sceneId,
          title: widget.sceneTitle,
          imagePath: widget.sceneImagePath,
          isSelected: true,
        ),
      ];
    }
  }

  @override
  void dispose() {
    // 保存当前场景的数据
    _saveCurrentSceneData();

    // 清理资源
    _transformationController.dispose();
    _knowledgeController.dispose();
    _knowledgeFocusNode.dispose();
    _keyboardAnimationController.dispose();

    // 移除键盘监听器
    WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }

  /// 监听系统指标变化（键盘弹出/收起）
  @override
  void didChangeMetrics() {
    super.didChangeMetrics();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      final newKeyboardHeight = MediaQuery.of(context).viewInsets.bottom;
      final wasKeyboardVisible = _isKeyboardVisible;
      final isKeyboardVisible = newKeyboardHeight > 0;

      if (wasKeyboardVisible != isKeyboardVisible) {
        setState(() {
          _isKeyboardVisible = isKeyboardVisible;
          _keyboardHeight = newKeyboardHeight;
        });

        if (isKeyboardVisible) {
          // 键盘即将显示
          _onKeyboardWillShow(newKeyboardHeight);
        } else {
          // 键盘即将隐藏
          _onKeyboardWillHide();
        }
      } else if (_keyboardHeight != newKeyboardHeight) {
        // 键盘高度发生变化（比如输入法切换）
        setState(() {
          _keyboardHeight = newKeyboardHeight;
        });
      }
    });
  }

  /// 键盘即将显示事件
  void _onKeyboardWillShow(double keyboardHeight) {
    print('🎹 键盘即将显示 - 高度: ${keyboardHeight}px');
    _keyboardAnimationController.forward();
  }

  /// 键盘即将隐藏事件
  void _onKeyboardWillHide() {
    print('🎹 键盘即将隐藏');
    _keyboardAnimationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3), // Notion风格背景色
      resizeToAvoidBottomInset: true, // 确保键盘出现时调整布局
      body: Stack(
        children: [
          // 主要场景视图
          _buildSceneViewer(),

          // 顶部工具栏
          _buildTopToolbar(),

          // 底部场景选择器 - 只在没有选中知识点或添加知识点时显示
          if (_selectedAnchor == null && !_showAddKnowledgePanel)
            _buildBottomSceneSelector(),

          // 知识点详情面板
          if (_selectedAnchor != null) _buildKnowledgeDetailPanel(),

          // 知识点添加输入面板
          if (_showAddKnowledgePanel) _buildAddKnowledgePanel(),

          // 编辑模式工具栏
          if (_isEditMode) _buildEditToolbar(),
        ],
      ),
    );
  }

  /// 构建场景查看器
  Widget _buildSceneViewer() {
    return InteractiveViewer(
      transformationController: _transformationController,
      minScale: 0.5,
      maxScale: 4.0,
      constrained: false, // 允许自由平移
      child: GestureDetector(
        onTap: () => _handleTap(),
        onTapDown: (details) => _recordTapPosition(details),
        onLongPress: () => _enterEditMode(),
        child: Container(
          color: Colors.white, // 图片区域背景
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: Stack(
            fit: StackFit.expand,
            children: [
              // 背景图片
              _buildSceneImage(_currentSceneImagePath),

              // 锚点覆盖层 - 位置选择模式下隐藏
              if (_showAllAnchors && !_isPositionSelectionMode)
                ..._buildAnchorOverlay(),

              // 临时位置选择气泡
              if (_isPositionSelectionMode && _tapPosition != null)
                _buildTempPositionBubble(),

              // 指导性知识气泡 - 显示在图片上方中央
              if (_showAllAnchors && _showGuidanceBubble)
                _buildGuidanceBubble(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建锚点覆盖层 - 使用ValueListenableBuilder实现高性能位置跟随
  List<Widget> _buildAnchorOverlay() {
    return _anchors.map((anchor) {
      // 如果当前锚点正在被重定位，固定在屏幕中心
      if (_isRepositioning && _anchorToReposition?.id == anchor.id) {
        final screenSize = MediaQuery.of(context).size;
        return Positioned(
          left: screenSize.width / 2 - 30, // 固定在屏幕中心
          top: screenSize.height / 2 - 15,
          child: KnowledgePointBubble(
            anchor: anchor,
            isSelected: _selectedAnchor?.id == anchor.id,
            isEditMode: _isEditMode,
            onRepositionStart: (anchor) => _onRepositionStart(anchor),
            onRepositionUpdate: (details) => _onRepositionUpdate(details),
            onRepositionEnd: () => _onRepositionEnd(),
            onTap: () => _selectAnchor(anchor),
          ),
        );
      }

      return ValueListenableBuilder<Matrix4>(
        valueListenable: _transformationController,
        builder: (context, transform, child) {
          // 计算当前变换状态
          final scale = transform.getMaxScaleOnAxis();
          final translation = transform.getTranslation();

          // 获取屏幕和图片尺寸
          final screenSize = MediaQuery.of(context).size;
          final imageWidth = screenSize.width;
          final imageHeight = screenSize.height;

          // 计算锚点在屏幕上的实际位置（这是圆点应该在的位置）
          final anchorScreenX =
              (anchor.xRatio * imageWidth * scale) + translation.x;
          final anchorScreenY =
              (anchor.yRatio * imageHeight * scale) + translation.y;

          // 计算反缩放因子以保持气泡尺寸稳定
          final inverseScale = 1.0 / scale;

          // 气泡的完整尺寸（包含文本框+指针+圆点）
          const textBoxHeight = 20.0; // 文本框高度
          const pointerHeight = 8.0; // 指针连线高度
          const dotHeight = 4.0; // 定位圆点高度
          const totalBubbleHeight =
              textBoxHeight + pointerHeight + dotHeight; // 32.0
          const bubbleWidth = 60.0; // 根据内容动态变化，这里是估算

          // 使用正确的居中定位，因为气泡已恢复为Column布局，圆点位于中心线
          double rawLeft =
              anchorScreenX - (bubbleWidth * inverseScale / 2); // 水平居中对齐圆点

          // 计算顶部位置 - 让圆点精准对准锚点位置
          double rawTop = anchorScreenY - (totalBubbleHeight * inverseScale);

          // Clamp 位置，避免溢出屏幕
          final maxLeft = screenSize.width - (bubbleWidth * inverseScale);
          final maxTop = screenSize.height - (totalBubbleHeight * inverseScale);

          final clampedLeft = rawLeft.clamp(0.0, maxLeft);
          final clampedTop = rawTop.clamp(0.0, maxTop);

          return Positioned(
            left: clampedLeft,
            top: clampedTop,
            child: Transform.scale(
              scale: inverseScale.clamp(0.5, 2.0), // 限制缩放范围，避免极端情况
              alignment: Alignment.bottomCenter, // 关键：使用底部中心作为缩放锚点
              child: RepaintBoundary(
                // 使用RepaintBoundary缓存绘制结果，提升性能
                child: KnowledgePointBubble(
                  anchor: anchor,
                  isSelected: _selectedAnchor?.id == anchor.id,
                  isEditMode: _isEditMode,
                  onRepositionStart: (anchor) => _onRepositionStart(anchor),
                  onRepositionUpdate: (details) => _onRepositionUpdate(details),
                  onRepositionEnd: () => _onRepositionEnd(),
                  onTap: () => _selectAnchor(anchor),
                ),
              ),
            ),
          );
        },
      );
    }).toList();
  }

  /// 构建临时位置选择气泡 - 具备尺寸稳定性
  Widget _buildTempPositionBubble() {
    return ValueListenableBuilder<Matrix4>(
      valueListenable: _transformationController,
      builder: (context, transform, child) {
        // 如果没有原始点击位置，不显示气泡
        if (_tapPosition == null) return const SizedBox.shrink();

        // 计算当前变换状态
        final scale = transform.getMaxScaleOnAxis();

        // 直接使用原始点击位置，不进行任何坐标转换
        // 这样气泡的针头就会精确对准用户点击的位置
        final clickScreenX = _tapPosition!.dx;
        final clickScreenY = _tapPosition!.dy;

        // 获取屏幕尺寸
        final screenSize = MediaQuery.of(context).size;

        // 计算反缩放因子以保持气泡尺寸稳定
        final inverseScale = 1.0 / scale;

        // 详细尺寸参数 - 必须与临时气泡组件完全匹配
        const textBoxHeight = 20.0; // 文本框高度
        const pointerHeight = 8.0; // 指针连线高度
        const dotHeight = 4.0; // 定位圆点高度
        const totalBubbleHeight =
            textBoxHeight + pointerHeight + dotHeight; // 32.0
        const bubbleWidth = 180.0; // 根据实际文本"移动图层选择记忆锚点"宽度调整

        // 关键修复：让针头（圆点）精确对准点击位置
        // 针头位于气泡底部中心，所以气泡左侧位置 = 点击位置X - 气泡宽度的一半
        double rawLeft = clickScreenX - (bubbleWidth * inverseScale / 2);

        // 计算顶部位置 - 让圆点精准对准点击位置
        double rawTop = clickScreenY - (totalBubbleHeight * inverseScale);

        // 防止气泡溢出屏幕
        final maxLeft = screenSize.width - (bubbleWidth * inverseScale);
        final maxTop = screenSize.height - (totalBubbleHeight * inverseScale);
        final clampedLeft = rawLeft.clamp(0.0, maxLeft);
        final clampedTop = rawTop.clamp(0.0, maxTop);

        // 计算针头（圆点）的实际屏幕位置
        final pointerScreenX =
            clampedLeft + (bubbleWidth * inverseScale / 2); // 气泡中心的X坐标
        final pointerScreenY =
            clampedTop + (totalBubbleHeight * inverseScale); // 气泡底部圆点的Y坐标

        // 调试定位位置和针头偏移
        print(
          '📍 临时气泡定位: 点击坐标($clickScreenX, $clickScreenY) → 气泡位置($clampedLeft, $clampedTop)',
        );
        print(
          '🎯 针头定位: 点击坐标($clickScreenX, $clickScreenY) → 针头坐标($pointerScreenX, $pointerScreenY)',
        );
        print(
          '❌ 针头偏移: X轴偏移=${(pointerScreenX - clickScreenX).toStringAsFixed(1)}px, Y轴偏移=${(pointerScreenY - clickScreenY).toStringAsFixed(1)}px',
        );

        return Positioned(
          left: clampedLeft,
          top: clampedTop,
          child: Transform.scale(
            scale: inverseScale.clamp(0.5, 2.0),
            alignment: Alignment.bottomCenter, // 关键：使用底部中心作为缩放锚点
            child: RepaintBoundary(child: const _TempPositionBubble()),
          ),
        );
      },
    );
  }

  /// 构建指导性知识气泡 - Notion风格
  Widget _buildGuidanceBubble() {
    return Positioned(
      left: 16,
      right: 16,
      top: MediaQuery.of(context).padding.top + 80, // 在顶部工具栏下方
      child: AnimatedOpacity(
        opacity: _showGuidanceBubble ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 300),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF37352F).withValues(alpha: 0.12),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.06),
                blurRadius: 16,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              // 左侧提示图标
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(18),
                ),
                child: const Icon(
                  Icons.lightbulb_outline,
                  color: Color(0xFF2E7EED),
                  size: 20,
                ),
              ),

              const SizedBox(width: 12),

              // 提示文本
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      '操作提示',
                      style: TextStyle(
                        color: Color(0xFF37352F),
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '您可以移动图层来浏览不同角度，选择记忆锚点查看详情，或点击空白处添加新的知识点',
                      style: TextStyle(
                        color: const Color(0xFF37352F).withValues(alpha: 0.8),
                        fontSize: 13,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),

              // 关闭按钮
              GestureDetector(
                onTap: () {
                  setState(() {
                    _showGuidanceBubble = false;
                  });
                },
                child: Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: const Color(0xFF37352F).withValues(alpha: 0.06),
                    borderRadius: BorderRadius.circular(14),
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Color(0xFF9B9A97),
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建顶部工具栏
  Widget _buildTopToolbar() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top,
          left: 16,
          right: 16,
          bottom: 12,
        ),
        color: const Color(0xFFF7F6F3), // Notion风格背景
        child: Row(
          children: [
            // 返回按钮
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(18),
                  border: Border.all(color: const Color(0xFFEAE9E7)),
                ),
                child: const Icon(
                  Icons.arrow_back_ios_new,
                  color: Color(0xFF37352F),
                  size: 18,
                ),
              ),
            ),

            const SizedBox(width: 12),

            // 场景标题
            Expanded(
              child: Text(
                _currentSceneTitle,
                style: const TextStyle(
                  color: Color(0xFF37352F),
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // 功能按钮
            IconButton(
              onPressed: _toggleAnchorVisibility,
              icon: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(18),
                  border: Border.all(color: const Color(0xFFEAE9E7)),
                ),
                child: Icon(
                  _showAllAnchors ? Icons.visibility : Icons.visibility_off,
                  color: const Color(0xFF37352F),
                  size: 18,
                ),
              ),
            ),

            const SizedBox(width: 8),

            IconButton(
              onPressed: _shareScene,
              icon: Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(18),
                  border: Border.all(color: const Color(0xFFEAE9E7)),
                ),
                child: const Icon(
                  Icons.share,
                  color: Color(0xFF37352F),
                  size: 18,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建底部场景选择器
  Widget _buildBottomSceneSelector() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        height: 120,
        padding: const EdgeInsets.only(bottom: 20, top: 10),
        decoration: BoxDecoration(
          color: const Color(0xFFF7F6F3),
          border: Border(
            top: BorderSide(
              color: const Color(0xFF37352F).withValues(alpha: 0.1),
              width: 1,
            ),
          ),
        ),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: _relatedScenes.length,
          itemBuilder: (context, index) {
            final scene = _relatedScenes[index];
            return Padding(
              padding: const EdgeInsets.only(right: 12),
              child: SceneCard(scene: scene, onTap: () => _switchScene(scene)),
            );
          },
        ),
      ),
    );
  }

  /// 构建知识点详情面板
  Widget _buildKnowledgeDetailPanel() {
    if (_selectedAnchor == null) return const SizedBox.shrink();

    return Positioned(
      left: 16,
      right: 16,
      bottom: 20, // 调整为底部20px，因为底部预览图已隐藏
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: const Color(0xFF37352F).withValues(alpha: 0.1),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: _getAnchorTypeColor(_selectedAnchor!.type),
                  child: Text(
                    _selectedAnchor!.authorName?.substring(0, 1) ?? '?',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _selectedAnchor!.authorName ?? '匿名用户',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF37352F),
                        ),
                      ),
                      Text(
                        _formatTime(_selectedAnchor!.createdAt),
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF787774),
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => setState(() => _selectedAnchor = null),
                  icon: const Icon(
                    Icons.close,
                    color: Color(0xFF9B9A97),
                    size: 20,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Text(
              _selectedAnchor!.content,
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF37352F),
                height: 1.5,
              ),
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getAnchorTypeColor(
                      _selectedAnchor!.type,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getAnchorTypeLabel(_selectedAnchor!.type),
                    style: TextStyle(
                      fontSize: 12,
                      color: _getAnchorTypeColor(_selectedAnchor!.type),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: () => _editKnowledgePoint(_selectedAnchor!),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2E7EED),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.edit_outlined,
                          size: 16,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 4),
                        const Text(
                          '编辑知识点',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建知识点添加面板 - 带键盘规避的Notion风格白色输入框
  Widget _buildAddKnowledgePanel() {
    return AnimatedBuilder(
      animation: _keyboardAnimation,
      builder: (context, child) {
        // 实时获取键盘高度
        final currentKeyboardHeight = MediaQuery.of(context).viewInsets.bottom;

        // 计算动态底部位置
        // 当键盘可见时，输入框容器的底边缘应与键盘顶边缘完全贴合（gap = 0）
        final targetBottom = _isKeyboardVisible
            ? currentKeyboardHeight // 紧贴键盘
            : 20.0; // 键盘隐藏时保持20px间距

        final animatedBottom = Tween<double>(
          begin: 20.0,
          end: targetBottom,
        ).animate(_keyboardAnimation).value;

        return AnimatedPositioned(
          duration: const Duration(milliseconds: 250), // 与键盘动画同步
          curve: Curves.easeInOut,
          left: 16,
          right: 16,
          bottom: animatedBottom,
          child: Material(
            color: Colors.transparent,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 250),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: const Color(0xFF37352F).withValues(alpha: 0.2),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(
                      alpha: _isKeyboardVisible ? 0.15 : 0.1,
                    ),
                    blurRadius: _isKeyboardVisible ? 16 : 12,
                    offset: Offset(0, _isKeyboardVisible ? 6 : 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // 输入框
                  Expanded(
                    child: TextField(
                      controller: _knowledgeController,
                      focusNode: _knowledgeFocusNode,
                      maxLines: 1,
                      textInputAction: TextInputAction.done,
                      onSubmitted: (_) => _saveKnowledge(),
                      style: const TextStyle(
                        color: Color(0xFF37352F), // 深色文字
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      decoration: const InputDecoration(
                        hintText: '留下要记忆的知识点',
                        hintStyle: TextStyle(
                          color: Color(0xFF9B9A97), // 灰色提示文字
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(horizontal: 4),
                        isDense: true,
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // 取消按钮
                  GestureDetector(
                    onTap: () => _cancelAddKnowledge(),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF5F5F4), // Notion灰色背景
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: const Text(
                        '取消',
                        style: TextStyle(
                          color: Color(0xFF787774), // 灰色文字
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 8),

                  // 保存按钮
                  GestureDetector(
                    onTap: () => _saveKnowledge(),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF2E7EED),
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(
                              0xFF2E7EED,
                            ).withValues(alpha: 0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Text(
                        '保存',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建编辑模式工具栏 - Notion风格
  Widget _buildEditToolbar() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 60,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: const Color(0xFF37352F).withValues(alpha: 0.15),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            IconButton(
              onPressed: () => _exitEditMode(),
              icon: const Icon(
                Icons.check,
                color: Color(0xFF059669), // 绿色表示完成
                size: 24,
              ),
            ),
            const SizedBox(height: 4),
            IconButton(
              onPressed: () => _addNewAnchor(),
              icon: const Icon(
                Icons.add,
                color: Color(0xFF2E7EED), // 蓝色表示添加
                size: 24,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 选择锚点
  void _selectAnchor(MemoryAnchor anchor) {
    setState(() {
      _selectedAnchor = _selectedAnchor?.id == anchor.id ? null : anchor;

      // 当选择知识点时显示指导气泡
      if (_selectedAnchor != null && !_showGuidanceBubble) {
        _showGuidanceBubble = true;

        // 5秒后自动隐藏指导气泡
        Future.delayed(const Duration(seconds: 5), () {
          if (mounted && _showGuidanceBubble) {
            setState(() {
              _showGuidanceBubble = false;
            });
          }
        });
      }
    });
  }

  /// 进入编辑模式
  void _enterEditMode() {
    setState(() {
      _isEditMode = true;
      _selectedAnchor = null;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('进入编辑模式，可拖拽调整锚点位置'),
        backgroundColor: const Color(0xFF2E7EED),
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).size.height - 200,
          left: 20,
          right: 20,
        ),
      ),
    );
  }

  /// 退出编辑模式
  void _exitEditMode() {
    setState(() {
      _isEditMode = false;
    });
  }

  // 记录点击位置（在onTapDown中调用）
  Offset? _lastTapPosition;

  /// 记录点击位置 - 仅记录，不处理
  void _recordTapPosition(TapDownDetails details) {
    _lastTapPosition = details.localPosition;
  }

  /// 处理点击事件 - 两阶段创建流程（只在真正的tap时触发）
  void _handleTap() {
    if (_isProcessingTap) return;
    if (_lastTapPosition == null) return;

    _isProcessingTap = true;
    Future.delayed(
      const Duration(milliseconds: 300),
      () => _isProcessingTap = false,
    );

    // 如果正在选择位置，忽略点击
    if (_isPositionSelectionMode) return;

    if (_selectedAnchor != null || _showAddKnowledgePanel) {
      setState(() {
        _selectedAnchor = null;
        _showAddKnowledgePanel = false;
        _knowledgeController.clear();
      });
      _lastTapPosition = null;
      return;
    }

    // 获取当前变换状态
    final transform = _transformationController.value;
    final scale = transform.getMaxScaleOnAxis();
    final translation = transform.getTranslation();
    final screenSize = MediaQuery.of(context).size;

    // 将点击位置转换为图片比例坐标（与保存逻辑一致）
    final clickPosition = _lastTapPosition!;
    final imageWidth = screenSize.width;
    final imageHeight = screenSize.height;

    // 将屏幕坐标转换为图片坐标（考虑缩放和平移）
    final imageX = (clickPosition.dx - translation.x) / scale;
    final imageY = (clickPosition.dy - translation.y) / scale;

    // 转换为比例坐标（相对于图片尺寸）
    final xRatio = (imageX / imageWidth).clamp(0.0, 1.0);
    final yRatio = (imageY / imageHeight).clamp(0.0, 1.0);

    print(
      '🎯 进入编辑模式: 点击位置(${clickPosition.dx.toStringAsFixed(1)}, ${clickPosition.dy.toStringAsFixed(1)}) → 比例坐标(${xRatio.toStringAsFixed(3)}, ${yRatio.toStringAsFixed(3)})',
    );

    // 一步到位：同时显示临时气泡和文本输入框
    setState(() {
      _isPositionSelectionMode = true;
      _selectedAnchor = null;
      _showAddKnowledgePanel = true; // 同时显示文本输入面板
      // 保存原始点击位置用于保存时使用
      _tapPosition = clickPosition;
    });

    // 自动弹出键盘
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        FocusScope.of(context).requestFocus(_knowledgeFocusNode);
      }
    });

    // 清空记录的位置
    _lastTapPosition = null;
  }

  /// 添加新锚点
  void _addNewAnchor() {
    // 允许在任意位置添加，这里可以弹出一个居中输入框或使用预设位置
    _showComingSoon('添加新锚点');
  }

  /// 切换锚点可见性
  void _toggleAnchorVisibility() {
    setState(() {
      _showAllAnchors = !_showAllAnchors;
      if (!_showAllAnchors) {
        _selectedAnchor = null;
      }
    });
  }

  /// 分享场景
  void _shareScene() {
    _showComingSoon('分享场景');
  }

  /// 切换场景
  void _switchScene(RelatedScene scene) async {
    if (scene.isSelected) return; // 如果已经是当前场景，不执行切换

    // 先保存当前场景的数据
    await _saveCurrentSceneData();

    // 清空当前内存数据，避免污染
    setState(() {
      _anchors.clear();
      _selectedAnchor = null;
      _knowledgeController.clear();
      _tapPosition = null;
    });

    setState(() {
      // 更新选中状态
      for (int i = 0; i < _relatedScenes.length; i++) {
        _relatedScenes[i] = RelatedScene(
          id: _relatedScenes[i].id,
          title: _relatedScenes[i].title,
          imagePath: _relatedScenes[i].imagePath,
          isSelected: _relatedScenes[i].id == scene.id,
        );
      }

      // 更新当前场景信息
      _currentSceneTitle = scene.title;
      _currentSceneImagePath = scene.imagePath;

      // 重置缩放
      _transformationController.value = Matrix4.identity();
    });

    // 等待一个微任务，确保UI更新完成
    await Future.microtask(() {});

    // 加载新场景的知识点数据
    await _loadSceneDataFromStorage(scene.id);
  }

  /// 编辑知识点
  void _editKnowledgePoint(MemoryAnchor anchor) {
    _knowledgeController.text = anchor.content;
    setState(() {
      _showAddKnowledgePanel = true;
      _selectedAnchor = anchor; // 标记为编辑状态
      _tapPosition = Offset(
        anchor.xRatio * MediaQuery.of(context).size.width,
        anchor.yRatio * MediaQuery.of(context).size.height,
      );
    });
  }

  /// 获取锚点类型颜色
  Color _getAnchorTypeColor(AnchorType type) {
    switch (type) {
      case AnchorType.motivation:
        return const Color(0xFF2E7EED);
      case AnchorType.goal:
        return const Color(0xFF7C3AED);
      case AnchorType.location:
        return const Color(0xFF0F7B6C);
      case AnchorType.future:
        return const Color(0xFFD9730D);
      case AnchorType.dream:
        return const Color(0xFFE03E3E);
      case AnchorType.study:
        return const Color(0xFF059669);
    }
  }

  /// 获取锚点类型标签
  String _getAnchorTypeLabel(AnchorType type) {
    switch (type) {
      case AnchorType.motivation:
        return '激励';
      case AnchorType.goal:
        return '目标';
      case AnchorType.location:
        return '位置';
      case AnchorType.future:
        return '未来';
      case AnchorType.dream:
        return '梦想';
      case AnchorType.study:
        return '学习';
    }
  }

  /// 格式化时间
  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 构建场景图片 - 智能处理网络图片和本地文件
  Widget _buildSceneImage(String imagePath) {
    // 判断是否为网络图片
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return Image.network(
        imagePath,
        fit: BoxFit.cover, // 统一使用cover模式
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            color: const Color(0xFFFFFFFF),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    value: loadingProgress.expectedTotalBytes != null
                        ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                        : null,
                    color: const Color(0xFF9B9A97),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    '加载中...',
                    style: TextStyle(color: Color(0xFF787774), fontSize: 16),
                  ),
                ],
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return _buildImageErrorWidget('网络图片加载失败');
        },
      );
    } else {
      // 本地文件图片
      final file = File(imagePath);
      return Image.file(
        file,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildImageErrorWidget('本地图片加载失败');
        },
      );
    }
  }

  /// 构建图片加载失败的占位符
  Widget _buildImageErrorWidget(String message) {
    return Container(
      color: const Color(0xFFFFFFFF),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_not_supported,
              size: 64,
              color: const Color(0xFF9B9A97).withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(color: Color(0xFF787774), fontSize: 16),
            ),
            const SizedBox(height: 8),
            const Text(
              '点击重试或更换图片',
              style: TextStyle(color: Color(0xFF9B9A97), fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示即将推出提示
  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('该功能即将推出，敬请期待'),
        backgroundColor: const Color(0xFF2E7EED),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 生成场景的存储key
  String _getSceneStorageKey(String sceneId) {
    // 使用更详细的key，包含时间戳确保唯一性
    final baseKey =
        'oneday_scene_anchors_palace_${widget.sceneId}_scene_$sceneId';
    return baseKey;
  }

  /// 加载持久化的数据
  Future<void> _loadPersistedData() async {
    try {
      // 调试：列出所有保存的keys
      await _debugListStoredKeys();

      // 确保相关场景列表已经初始化
      if (_relatedScenes.isEmpty) {
        print('⚠️ 相关场景列表为空，跳过数据加载');
        _loadDefaultAnchors();
        return;
      }

      // 获取当前选中场景的正确ID
      final currentSceneId = _getCurrentSceneId();

      // 加载当前场景的数据
      await _loadSceneDataFromStorage(currentSceneId);

      print(
        '📱 数据加载完成 - 当前场景: ${widget.sceneTitle} (ID: $currentSceneId), 知识点数量: ${_anchors.length}',
      );
    } catch (e) {
      print('❌ 加载数据失败: $e');
      // 如果加载失败，使用默认数据
      _loadDefaultAnchors();
    }
  }

  /// 调试：列出所有已保存的keys
  Future<void> _debugListStoredKeys() async {
    // 仅在Debug模式下显示调试信息
    assert(() {
      SharedPreferences.getInstance().then((prefs) {
        final keys = prefs.getKeys();
        final onedayKeys = keys
            .where((key) => key.startsWith('oneday_scene_anchors'))
            .toList();

        print('🗃️ 当前存储的OneDay相关keys (${onedayKeys.length}个):');
        for (final key in onedayKeys) {
          final data = prefs.getString(key);
          print('   📂 $key: ${data?.length ?? 0} 字符');
        }
      });
      return true;
    }());
  }

  /// 从存储加载指定场景的数据
  Future<void> _loadSceneDataFromStorage(String sceneId) async {
    final prefs = await SharedPreferences.getInstance();
    final key = _getSceneStorageKey(sceneId);
    final jsonString = prefs.getString(key);

    if (jsonString != null) {
      try {
        final List<dynamic> jsonList = json.decode(jsonString);
        final anchors = jsonList
            .map((json) => MemoryAnchor.fromJson(json))
            .toList();

        setState(() {
          _anchors = anchors;
          _sceneAnchorsCache[sceneId] = List.from(anchors);
        });

        print('✅ 成功加载场景 $sceneId 的 ${anchors.length} 个知识点');
      } catch (e) {
        print('❌ 解析场景 $sceneId 数据失败: $e');
        _loadDefaultAnchors();
      }
    } else {
      print('🆕 场景 $sceneId 暂无保存的数据，加载默认数据');
      _loadDefaultAnchors();
    }
  }

  /// 初始化空白场景（新相册从空白状态开始）
  void _loadDefaultAnchors() {
    setState(() {
      _anchors = []; // 空白状态，不加载任何模板数据
    });

    print('🆕 初始化空白场景，无预设知识点');
  }

  /// 保存当前场景的数据
  Future<void> _saveCurrentSceneData() async {
    try {
      final currentSceneId = _getCurrentSceneId();

      // 更新缓存
      _sceneAnchorsCache[currentSceneId] = List.from(_anchors);

      // 保存到本地存储
      await _saveSceneDataToStorage(currentSceneId, _anchors);

      print('💾 已保存场景 $currentSceneId 的 ${_anchors.length} 个知识点');
    } catch (e) {
      print('❌ 保存数据失败: $e');
    }
  }

  /// 将指定场景的数据保存到本地存储
  Future<void> _saveSceneDataToStorage(
    String sceneId,
    List<MemoryAnchor> anchors,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final key = _getSceneStorageKey(sceneId);
    final jsonList = anchors.map((anchor) => anchor.toJson()).toList();
    final jsonString = json.encode(jsonList);

    await prefs.setString(key, jsonString);
  }

  /// 获取当前场景ID
  String _getCurrentSceneId() {
    try {
      final currentScene = _relatedScenes.firstWhere(
        (scene) => scene.isSelected,
      );
      return currentScene.id;
    } catch (e) {
      print('❌ 获取当前场景ID失败: $e');
      print(
        '📋 可用场景列表: ${_relatedScenes.map((s) => '${s.id}(${s.isSelected ? "选中" : "未选"})').join(", ")}',
      );
      // 如果找不到选中的场景，返回第一个场景的ID
      if (_relatedScenes.isNotEmpty) {
        return _relatedScenes.first.id;
      }
      return widget.sceneId; // 兜底方案
    }
  }

  /// 取消添加知识点
  void _cancelAddKnowledge() {
    // 隐藏键盘
    FocusScope.of(context).unfocus();

    setState(() {
      _showAddKnowledgePanel = false;
      _knowledgeController.clear();
      _selectedAnchor = null;
      _tapPosition = null;
      // 同时清理位置选择模式
      _isPositionSelectionMode = false;
    });
  }

  /// 保存知识点
  void _saveKnowledge() async {
    if (_knowledgeController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请输入知识点内容'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (_selectedAnchor != null) {
      // 编辑现有知识点
      setState(() {
        _selectedAnchor!.content = _knowledgeController.text.trim();
      });
    } else if (_tapPosition != null) {
      // 添加新知识点 - 将屏幕坐标转换为图片比例坐标
      final screenSize = MediaQuery.of(context).size;
      final transform = _transformationController.value;
      final scale = transform.getMaxScaleOnAxis();
      final translation = transform.getTranslation();

      // 获取图片实际尺寸（假设图片填满屏幕）
      final imageWidth = screenSize.width;
      final imageHeight = screenSize.height;

      // 将屏幕坐标转换为图片坐标（考虑缩放和平移）
      final imageX = (_tapPosition!.dx - translation.x) / scale;
      final imageY = (_tapPosition!.dy - translation.y) / scale;

      // 转换为比例坐标（相对于图片尺寸）
      final xRatio = (imageX / imageWidth).clamp(0.0, 1.0);
      final yRatio = (imageY / imageHeight).clamp(0.0, 1.0);

      print(
        '🎯 屏幕坐标转换: 点击位置(${_tapPosition!.dx.toStringAsFixed(1)}, ${_tapPosition!.dy.toStringAsFixed(1)}) → 图片坐标(${imageX.toStringAsFixed(1)}, ${imageY.toStringAsFixed(1)}) → 比例坐标(${xRatio.toStringAsFixed(3)}, ${yRatio.toStringAsFixed(3)})',
      );

      final newAnchor = MemoryAnchor(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        xRatio: xRatio.clamp(0.0, 1.0),
        yRatio: yRatio.clamp(0.0, 1.0),
        content: _knowledgeController.text.trim(),
        authorName: '我',
        likes: 0,
        createdAt: DateTime.now(),
        type: AnchorType.study,
      );

      setState(() {
        _anchors.add(newAnchor);
      });

      print(
        '📍 新建锚点: ID=${newAnchor.id}, 位置=(${xRatio.toStringAsFixed(3)}, ${yRatio.toStringAsFixed(3)})',
      );
    }

    // 立即保存到本地存储
    await _saveCurrentSceneData();

    _cancelAddKnowledge();
  }

  /// 更新锚点位置（考虑变换矩阵）- 反向拖拽逻辑
  void _onRepositionStart(MemoryAnchor anchor) {
    setState(() {
      _isRepositioning = true;
      _anchorToReposition = anchor;
    });
  }

  void _onRepositionUpdate(DragUpdateDetails details) {
    // 移动背景图片
    _transformationController.value = _transformationController.value.clone()
      ..translate(details.delta.dx, details.delta.dy);
  }

  void _onRepositionEnd() {
    if (_anchorToReposition == null) return;

    final screenSize = MediaQuery.of(context).size;
    final screenCenter = Offset(screenSize.width / 2, screenSize.height / 2);

    final transform = _transformationController.value;
    final scale = transform.getMaxScaleOnAxis();
    final translation = transform.getTranslation();

    // 获取图片实际尺寸（假设图片填满屏幕）
    final imageWidth = screenSize.width;
    final imageHeight = screenSize.height;

    // 将屏幕中心点转换为图片上的比例坐标
    final imageX = (screenCenter.dx - translation.x) / scale;
    final imageY = (screenCenter.dy - translation.y) / scale;
    final newXRatio = imageX / imageWidth;
    final newYRatio = imageY / imageHeight;

    setState(() {
      _anchorToReposition!.xRatio = newXRatio.clamp(0.0, 1.0);
      _anchorToReposition!.yRatio = newYRatio.clamp(0.0, 1.0);

      // 退出重定位模式
      _isRepositioning = false;
      _anchorToReposition = null;
    });

    _saveCurrentSceneData(); // 保存更新后的位置
  }
}

/// 锚点类型枚举
enum AnchorType {
  motivation, // 激励
  goal, // 目标
  location, // 位置
  future, // 未来
  dream, // 梦想
  study, // 学习
}

/// 记忆锚点数据模型
class MemoryAnchor {
  final String id;
  double xRatio; // X坐标比例 (0.0-1.0)
  double yRatio; // Y坐标比例 (0.0-1.0)
  String content; // 改为可变，支持编辑
  final String? authorName;
  int likes;
  final DateTime createdAt;
  final AnchorType type;

  MemoryAnchor({
    required this.id,
    required this.xRatio,
    required this.yRatio,
    required this.content,
    this.authorName,
    required this.likes,
    required this.createdAt,
    required this.type,
  });

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'xRatio': xRatio,
      'yRatio': yRatio,
      'content': content,
      'authorName': authorName,
      'likes': likes,
      'createdAt': createdAt.toIso8601String(),
      'type': type.index,
    };
  }

  /// 从JSON创建对象
  static MemoryAnchor fromJson(Map<String, dynamic> json) {
    return MemoryAnchor(
      id: json['id'] as String,
      xRatio: (json['xRatio'] as num).toDouble(),
      yRatio: (json['yRatio'] as num).toDouble(),
      content: json['content'] as String,
      authorName: json['authorName'] as String?,
      likes: json['likes'] as int,
      createdAt: DateTime.parse(json['createdAt'] as String),
      type: AnchorType.values[json['type'] as int],
    );
  }
}

/// 临时位置选择气泡组件
class _TempPositionBubble extends StatelessWidget {
  const _TempPositionBubble();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // 简化的提示气泡
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(color: const Color(0xFF2E7EED), width: 2),
          ),
          child: const Text(
            '移动图层选择记忆锚点',
            style: TextStyle(
              fontSize: 11,
              color: Color(0xFF37352F),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ),

        // 指针连线
        Container(
          width: 1.5,
          height: 8,
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 1,
                offset: const Offset(0, 0.5),
              ),
            ],
          ),
        ),

        // 定位圆点
        Container(
          width: 4,
          height: 4,
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.15),
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// 相关场景数据模型
class RelatedScene {
  final String id;
  final String title;
  final String imagePath;
  final bool isSelected;

  RelatedScene({
    required this.id,
    required this.title,
    required this.imagePath,
    required this.isSelected,
  });
}

/// 知识点气泡组件 - 按照蓝图规格设计
class KnowledgePointBubble extends StatefulWidget {
  final MemoryAnchor anchor;
  final bool isSelected;
  final bool isEditMode;
  final Function(MemoryAnchor)? onRepositionStart;
  final Function(DragUpdateDetails)? onRepositionUpdate;
  final VoidCallback? onRepositionEnd;
  final VoidCallback? onTap;

  const KnowledgePointBubble({
    super.key,
    required this.anchor,
    this.isSelected = false,
    this.isEditMode = false,
    this.onRepositionStart,
    this.onRepositionUpdate,
    this.onRepositionEnd,
    this.onTap,
  });

  @override
  State<KnowledgePointBubble> createState() => _KnowledgePointBubbleState();
}

class _KnowledgePointBubbleState extends State<KnowledgePointBubble> {
  @override
  Widget build(BuildContext context) {
    Widget bubbleWidget = Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // 紧凑文本框 - 移除图标，紧贴文字
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 8,
            vertical: 4,
          ), // 更紧凑的内边距
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12), // 稍小的圆角
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: const Color(0xFF37352F).withValues(alpha: 0.1),
            ),
          ),
          child: Text(
            widget.anchor.content,
            style: const TextStyle(
              fontSize: 11, // 显著调小字号
              color: Color(0xFF37352F),
              fontWeight: FontWeight.w500,
              height: 1.2,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),

        // 指针连线 - 从文本框底部延伸的白色指针
        Container(
          width: 1.5,
          height: 8,
          decoration: BoxDecoration(
            color: Colors.white, // 白色指针
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 1,
                offset: const Offset(0, 0.5),
              ),
            ],
          ),
        ),

        // 定位圆点 - 指针末端的纯白色圆点
        Container(
          width: 4,
          height: 4,
          decoration: BoxDecoration(
            color: Colors.white, // 纯白色
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.15),
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
        ),
      ],
    );

    // 如果是编辑模式且有拖拽回调，使用GestureDetector实现反向拖拽和阻尼效果
    if (widget.isEditMode) {
      return GestureDetector(
        onPanStart: (details) => widget.onRepositionStart?.call(widget.anchor),
        onPanUpdate: widget.onRepositionUpdate,
        onPanEnd: (details) => widget.onRepositionEnd?.call(),
        child: bubbleWidget,
      );
    }

    // 如果有点击回调，使用GestureDetector包装
    if (widget.onTap != null) {
      return GestureDetector(onTap: widget.onTap, child: bubbleWidget);
    }

    return bubbleWidget;
  }
}

/// 场景卡片组件
class SceneCard extends StatelessWidget {
  final RelatedScene scene;
  final VoidCallback onTap;

  const SceneCard({super.key, required this.scene, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        width: 80,
        child: Column(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: scene.isSelected
                    ? Border.all(color: const Color(0xFF2E7EED), width: 2)
                    : null,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child:
                    scene.imagePath.startsWith('http://') ||
                        scene.imagePath.startsWith('https://')
                    ? Image.network(
                        scene.imagePath,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey.withValues(alpha: 0.3),
                            child: const Icon(
                              Icons.image,
                              color: Colors.white,
                              size: 24,
                            ),
                          );
                        },
                      )
                    : Image.file(
                        File(scene.imagePath),
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey.withValues(alpha: 0.3),
                            child: const Icon(
                              Icons.image,
                              color: Colors.white,
                              size: 24,
                            ),
                          );
                        },
                      ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              scene.title,
              style: TextStyle(
                color: scene.isSelected
                    ? const Color(0xFF2E7EED)
                    : const Color(0xFF37352F).withValues(alpha: 0.8),
                fontSize: 12,
                fontWeight: scene.isSelected
                    ? FontWeight.w600
                    : FontWeight.w400,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
