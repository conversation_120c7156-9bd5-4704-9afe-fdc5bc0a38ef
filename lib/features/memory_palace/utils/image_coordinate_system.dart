import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';

/// 图片坐标系统管理器
///
/// 负责管理原始图片尺寸和压缩图片尺寸之间的关系，
/// 提供标准化的坐标转换功能，确保锚点位置在不同压缩配置下保持一致。
class ImageCoordinateSystem {
  /// 图片尺寸信息
  static final Map<String, ImageSizeInfo> _imageSizeCache = {};

  /// 获取图片的完整尺寸信息（包括原始尺寸和当前尺寸）
  static Future<ImageSizeInfo?> getImageSizeInfo(String imagePath) async {
    // 检查缓存
    if (_imageSizeCache.containsKey(imagePath)) {
      final cachedInfo = _imageSizeCache[imagePath]!;
      print('📋 [坐标系统] 使用缓存的尺寸信息: ${cachedInfo.toString()}');
      return cachedInfo;
    }

    try {
      print('🔍 [坐标系统] 开始获取图片尺寸信息: $imagePath');

      final currentSize = await _getCurrentImageSize(imagePath);
      if (currentSize == null) {
        print('❌ [坐标系统] 无法获取当前图片尺寸');
        return null;
      }
      print('📐 [坐标系统] 当前图片尺寸: ${currentSize.width}x${currentSize.height}');

      final originalSize = await _getOriginalImageSize(imagePath);
      if (originalSize == null) {
        print('❌ [坐标系统] 无法获取原始图片尺寸');
        return null;
      }
      print('📐 [坐标系统] 原始图片尺寸: ${originalSize.width}x${originalSize.height}');

      final isCompressed = imagePath.contains('_compressed');
      print('🔧 [坐标系统] 是否为压缩图片: $isCompressed');

      final sizeInfo = ImageSizeInfo(
        imagePath: imagePath,
        currentSize: currentSize,
        originalSize: originalSize,
        isCompressed: isCompressed,
      );

      print('✅ [坐标系统] 图片尺寸信息创建完成: ${sizeInfo.toString()}');

      // 缓存结果
      _imageSizeCache[imagePath] = sizeInfo;
      return sizeInfo;
    } catch (e) {
      print('❌ [坐标系统] 获取图片尺寸信息失败: $e');
      return null;
    }
  }

  /// 获取当前图片的实际尺寸
  static Future<Size?> _getCurrentImageSize(String imagePath) async {
    print('🔍 [图片尺寸] 开始获取图片尺寸: $imagePath');

    // 检查是否为用户导入照片
    final isUserImported =
        imagePath.startsWith('/') ||
        imagePath.contains('file://') ||
        !imagePath.startsWith('http');
    print('🔍 [图片尺寸] 照片类型: ${isUserImported ? "用户导入" : "自带照片"}');

    final imageProvider =
        (imagePath.startsWith('http')
                ? NetworkImage(imagePath)
                : FileImage(File(imagePath)))
            as ImageProvider;

    print('🔍 [图片尺寸] 使用的ImageProvider类型: ${imageProvider.runtimeType}');

    final completer = Completer<Size?>();
    final listener = ImageStreamListener(
      (ImageInfo info, bool _) {
        if (!completer.isCompleted) {
          final size = Size(
            info.image.width.toDouble(),
            info.image.height.toDouble(),
          );
          print('✅ [图片尺寸] 获取成功: ${size.width}x${size.height}');
          completer.complete(size);
        }
      },
      onError: (exception, stackTrace) {
        print('❌ [坐标系统] 获取当前图片尺寸失败: $exception');
        if (!completer.isCompleted) {
          completer.complete(null);
        }
      },
    );

    imageProvider.resolve(const ImageConfiguration()).addListener(listener);
    return completer.future;
  }

  /// 获取原始图片尺寸（压缩前）
  static Future<Size?> _getOriginalImageSize(String imagePath) async {
    // 🔧 关键修复：统一自带照片和用户导入照片的处理逻辑
    //
    // 新策略：无论是否为压缩图片，都使用当前图片的尺寸作为"原始尺寸"
    // 这样确保：
    // 1. 自带照片（网络图片）：当前尺寸 = 原始尺寸
    // 2. 用户导入照片（压缩图片）：压缩后尺寸 = 原始尺寸
    // 3. 两者使用相同的坐标系统基准，确保定位逻辑一致

    final currentSize = await _getCurrentImageSize(imagePath);

    if (imagePath.contains('_compressed')) {
      print(
        '🔧 [坐标系统] 用户导入照片，使用压缩后尺寸作为原始尺寸: ${currentSize?.width}x${currentSize?.height}',
      );
    } else {
      print(
        '🔧 [坐标系统] 自带照片，使用当前尺寸作为原始尺寸: ${currentSize?.width}x${currentSize?.height}',
      );
    }

    return currentSize;
  }

  /// 将屏幕坐标转换为标准化坐标（基于原始图片尺寸）
  static StandardizedCoordinate screenToStandardized(
    Offset screenPoint,
    Matrix4 transformMatrix,
    ImageSizeInfo sizeInfo,
  ) {
    print('🎯 [坐标转换] 开始屏幕坐标到标准化坐标转换');
    print(
      '📍 [坐标转换] 输入屏幕坐标: (${screenPoint.dx.toStringAsFixed(1)}, ${screenPoint.dy.toStringAsFixed(1)})',
    );
    print('📐 [坐标转换] 图片尺寸信息: ${sizeInfo.toString()}');

    // 1. 屏幕坐标转换为当前图片内坐标
    final scale = transformMatrix.getMaxScaleOnAxis();
    final translation = transformMatrix.getTranslation();
    print(
      '🔍 [坐标转换] 变换参数: scale=${scale.toStringAsFixed(3)}, translation=(${translation.x.toStringAsFixed(1)}, ${translation.y.toStringAsFixed(1)})',
    );

    final currentImageX = (screenPoint.dx - translation.x) / scale;
    final currentImageY = (screenPoint.dy - translation.y) / scale;
    print(
      '📍 [坐标转换] 当前图片内坐标: (${currentImageX.toStringAsFixed(1)}, ${currentImageY.toStringAsFixed(1)})',
    );

    // 2. 当前图片坐标转换为标准化坐标（基于原始图片尺寸）
    final scaleFactorX =
        sizeInfo.originalSize.width / sizeInfo.currentSize.width;
    final scaleFactorY =
        sizeInfo.originalSize.height / sizeInfo.currentSize.height;
    print(
      '🔍 [坐标转换] 尺寸缩放因子: X=${scaleFactorX.toStringAsFixed(3)}, Y=${scaleFactorY.toStringAsFixed(3)}',
    );

    final standardizedX = currentImageX * scaleFactorX;
    final standardizedY = currentImageY * scaleFactorY;
    print(
      '📍 [坐标转换] 标准化坐标: (${standardizedX.toStringAsFixed(1)}, ${standardizedY.toStringAsFixed(1)})',
    );

    final result = StandardizedCoordinate(
      x: standardizedX,
      y: standardizedY,
      originalImageSize: sizeInfo.originalSize,
    );

    print('✅ [坐标转换] 屏幕到标准化坐标转换完成: ${result.toString()}');
    return result;
  }

  /// 将标准化坐标转换为当前图片内坐标
  static Offset standardizedToCurrentImage(
    StandardizedCoordinate standardizedCoord,
    ImageSizeInfo sizeInfo,
  ) {
    print('🎯 [坐标转换] 开始标准化坐标到当前图片坐标转换');
    print('📍 [坐标转换] 输入标准化坐标: ${standardizedCoord.toString()}');
    print('📐 [坐标转换] 图片尺寸信息: ${sizeInfo.toString()}');

    // 计算从原始尺寸到当前尺寸的缩放比例
    final scaleFactorX =
        sizeInfo.currentSize.width / sizeInfo.originalSize.width;
    final scaleFactorY =
        sizeInfo.currentSize.height / sizeInfo.originalSize.height;
    print(
      '🔍 [坐标转换] 尺寸缩放因子: X=${scaleFactorX.toStringAsFixed(3)}, Y=${scaleFactorY.toStringAsFixed(3)}',
    );

    final currentImageX = standardizedCoord.x * scaleFactorX;
    final currentImageY = standardizedCoord.y * scaleFactorY;
    print(
      '📍 [坐标转换] 当前图片内坐标: (${currentImageX.toStringAsFixed(1)}, ${currentImageY.toStringAsFixed(1)})',
    );

    final result = Offset(currentImageX, currentImageY);
    print('✅ [坐标转换] 标准化到当前图片坐标转换完成');
    return result;
  }

  /// 清除缓存
  static void clearCache() {
    _imageSizeCache.clear();
  }

  /// 清除特定图片的缓存
  static void clearCacheForImage(String imagePath) {
    _imageSizeCache.remove(imagePath);
  }
}

/// 图片尺寸信息
class ImageSizeInfo {
  final String imagePath;
  final Size currentSize; // 当前图片尺寸（可能是压缩后的）
  final Size originalSize; // 原始图片尺寸（压缩前的）
  final bool isCompressed; // 是否为压缩图片

  const ImageSizeInfo({
    required this.imagePath,
    required this.currentSize,
    required this.originalSize,
    required this.isCompressed,
  });

  /// 获取缩放比例
  double get scaleFactorX => currentSize.width / originalSize.width;
  double get scaleFactorY => currentSize.height / originalSize.height;

  /// 是否需要坐标转换
  bool get needsCoordinateTransform =>
      isCompressed && (scaleFactorX != 1.0 || scaleFactorY != 1.0);

  @override
  String toString() {
    return 'ImageSizeInfo(path: $imagePath, current: ${currentSize.width}x${currentSize.height}, original: ${originalSize.width}x${originalSize.height}, compressed: $isCompressed)';
  }
}

/// 标准化坐标（基于原始图片尺寸）
class StandardizedCoordinate {
  final double x;
  final double y;
  final Size originalImageSize;

  const StandardizedCoordinate({
    required this.x,
    required this.y,
    required this.originalImageSize,
  });

  /// 转换为比例坐标
  Offset get ratioCoordinate =>
      Offset(x / originalImageSize.width, y / originalImageSize.height);

  @override
  String toString() {
    return 'StandardizedCoordinate(x: ${x.toStringAsFixed(1)}, y: ${y.toStringAsFixed(1)}, ratio: ${ratioCoordinate.dx.toStringAsFixed(3)}, ${ratioCoordinate.dy.toStringAsFixed(3)})';
  }
}
