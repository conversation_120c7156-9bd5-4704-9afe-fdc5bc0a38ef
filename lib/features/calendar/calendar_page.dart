import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../daily_plan/models/daily_plan.dart';
import '../daily_plan/notifiers/daily_plan_notifier.dart';
import '../time_box/models/timebox_models.dart';
import '../time_box/providers/timebox_provider.dart';
import '../study_time/providers/study_time_providers.dart';
import 'package:share_plus/share_plus.dart';

/// 日历视图页面
///
/// 显示一周的学习时间表，支持时间块记录和Excel导出
/// 采用Notion风格设计，白纸黑字极简美学
class CalendarPage extends ConsumerStatefulWidget {
  const CalendarPage({super.key});

  @override
  ConsumerState<CalendarPage> createState() => _CalendarPageState();
}

class _CalendarPageState extends ConsumerState<CalendarPage> {
  // 当前周的开始日期
  DateTime _currentWeekStart = DateTime.now();

  // 时间块数据
  List<TimeBlock> _timeBlocks = [];

  // 选中的日期（用于显示详细统计）
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    _currentWeekStart = _getWeekStart(DateTime.now());
    _loadTimeBlocks();
  }

  /// 获取一周的开始日期（周一）
  DateTime _getWeekStart(DateTime date) {
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }

  /// 加载时间块数据（模拟数据）
  void _loadTimeBlocks() {
    // 模拟一些时间块数据
    _timeBlocks = [
      TimeBlock(
        id: '1',
        title: '算法复习：动态规划',
        subject: Subject.computerScience,
        startTime: DateTime(2025, 1, 6, 9, 0), // 周一9:00
        endTime: DateTime(2025, 1, 6, 11, 0), // 周一11:00
        actualMinutes: 120,
      ),
      TimeBlock(
        id: '2',
        title: '数学练习：微积分',
        subject: Subject.mathematics,
        startTime: DateTime(2025, 1, 6, 14, 0), // 周一14:00
        endTime: DateTime(2025, 1, 6, 16, 30), // 周一16:30
        actualMinutes: 150,
      ),
      TimeBlock(
        id: '3',
        title: '英语单词记忆',
        subject: Subject.english,
        startTime: DateTime(2025, 1, 7, 9, 0), // 周二9:00
        endTime: DateTime(2025, 1, 7, 10, 30), // 周二10:30
        actualMinutes: 90,
      ),
      TimeBlock(
        id: '4',
        title: '政治理论学习',
        subject: Subject.politics,
        startTime: DateTime(2025, 1, 7, 20, 0), // 周二20:00
        endTime: DateTime(2025, 1, 7, 21, 30), // 周二21:30
        actualMinutes: 90,
      ),
      TimeBlock(
        id: '5',
        title: '算法题练习',
        subject: Subject.computerScience,
        startTime: DateTime(2025, 1, 8, 21, 0), // 周三21:00
        endTime: DateTime(2025, 1, 8, 22, 30), // 周三22:30
        actualMinutes: 90,
      ),
    ];
  }

  /// 切换到上一周
  void _previousWeek() {
    setState(() {
      _currentWeekStart = _currentWeekStart.subtract(const Duration(days: 7));
      _loadTimeBlocks(); // 重新加载数据
    });
  }

  /// 切换到下一周
  void _nextWeek() {
    setState(() {
      _currentWeekStart = _currentWeekStart.add(const Duration(days: 7));
      _loadTimeBlocks(); // 重新加载数据
    });
  }

  /// 导出Excel
  Future<void> _exportToExcel() async {
    try {
      // 创建CSV格式的数据（可以被Excel打开）
      String csvContent = 'Date,Type,Content,Subject,Title,Duration(min)\n';

      // 导出每日计划和优化
      for (int i = 0; i < 7; i++) {
        final date = _currentWeekStart.add(Duration(days: i));
        final dateStr = DateFormat('yyyy-MM-dd').format(date);

        // 每日计划
        final planContent = ref.read(
          dailyPlanContentProvider((date: date, type: DailyPlanType.planning)),
        );
        if (planContent.isNotEmpty) {
          final cleanPlan = planContent
              .replaceAll(',', ';')
              .replaceAll('\n', ' | ');
          csvContent += '$dateStr,每日计划,"$cleanPlan",,,\n';
        }

        // 每日优化
        final optimizationContent = ref.read(
          dailyPlanContentProvider((
            date: date,
            type: DailyPlanType.optimization,
          )),
        );
        if (optimizationContent.isNotEmpty) {
          final cleanOptimization = optimizationContent
              .replaceAll(',', ';')
              .replaceAll('\n', ' | ');
          csvContent += '$dateStr,每日优化,"$cleanOptimization",,,\n';
        }
      }

      // 导出时间块
      for (var block in _timeBlocks) {
        final date = DateFormat('yyyy-MM-dd').format(block.startTime);
        final time =
            '${DateFormat('HH:mm').format(block.startTime)}-${DateFormat('HH:mm').format(block.endTime)}';
        final subject = block.subject.displayName;
        final title = block.title.replaceAll(',', ';'); // 替换逗号避免CSV冲突
        final duration = block.actualMinutes.toString();

        csvContent += '$date,学习时间块,$time,$subject,$title,$duration\n';
      }

      // 获取临时目录
      final directory = await getTemporaryDirectory();
      final filePath =
          '${directory.path}/study_report_${DateFormat('yyyyMMdd').format(DateTime.now())}.csv';

      // 写入文件
      final file = File(filePath);
      await file.writeAsString(csvContent);

      // 分享文件
      await Share.shareXFiles(
        [XFile(filePath)],
        text: '学习时间记录报告 - ${DateFormat('yyyy年MM月dd日').format(DateTime.now())}',
      );

      // 显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('📊 学习报告已导出并分享（包含计划和优化）'),
            backgroundColor: Color(0xFF0F7B6C),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('导出失败: $e'),
            backgroundColor: const Color(0xFFE03E3E),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false, // 移除返回按钮，避免功能混乱
        title: const Text(
          '学习日历',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline, color: Color(0xFF9B9A97)),
            onPressed: () => _showHelpDialog(),
          ),
        ],
      ),
      body: Column(
        children: [
          // 周选择器
          _buildWeekSelector(),
          // 日期标题行
          _buildDateHeaders(),
          // 时间表主体
          Expanded(child: _buildTimeTable()),
          // 选中日期的详细统计
          if (_selectedDate != null) _buildSelectedDateStats(),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        heroTag: "calendar_fab", // 唯一标签避免冲突
        onPressed: _exportToExcel,
        backgroundColor: const Color(0xFF2E7EED),
        icon: const Icon(Icons.download, color: Colors.white),
        label: const Text('导出Excel', style: TextStyle(color: Colors.white)),
      ),
    );
  }

  /// 周选择器
  Widget _buildWeekSelector() {
    final weekEnd = _currentWeekStart.add(const Duration(days: 6));
    final weekRange =
        '${DateFormat('yyyy-MM-dd').format(_currentWeekStart)} ~ ${DateFormat('yyyy-MM-dd').format(weekEnd)}';

    return Container(
      color: const Color(0xFF2E7EED),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: const Icon(Icons.chevron_left, color: Colors.white),
            onPressed: _previousWeek,
          ),
          Text(
            weekRange,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.chevron_right, color: Colors.white),
            onPressed: _nextWeek,
          ),
        ],
      ),
    );
  }

  /// 日期标题行
  Widget _buildDateHeaders() {
    return Consumer(
      builder: (context, ref, child) {
        return Container(
          color: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Row(
            children: [
              // 时间列占位
              const SizedBox(width: 60),
              // 日期列
              ...List.generate(7, (index) {
                final date = _currentWeekStart.add(Duration(days: index));
                final isToday = _isToday(date);
                final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

                // 获取该日期的学习时间统计
                final dailyStats = ref.watch(dailyStudyTimeProvider(date));
                final studyTimeText = dailyStats?.formattedStudyTime ?? '0m';

                return Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedDate = date;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      decoration: BoxDecoration(
                        color:
                            _selectedDate != null &&
                                _isSameDay(_selectedDate!, date)
                            ? const Color(0xFF0F7B6C).withValues(alpha: 0.1)
                            : isToday
                            ? const Color(0xFF2E7EED).withValues(alpha: 0.1)
                            : null,
                        borderRadius: BorderRadius.circular(8),
                        border:
                            _selectedDate != null &&
                                _isSameDay(_selectedDate!, date)
                            ? Border.all(
                                color: const Color(0xFF0F7B6C),
                                width: 1,
                              )
                            : null,
                      ),
                      child: Column(
                        children: [
                          Text(
                            DateFormat('MM-dd').format(date),
                            style: TextStyle(
                              fontSize: 10,
                              color: isToday
                                  ? const Color(0xFF2E7EED)
                                  : const Color(0xFF9B9A97),
                              fontWeight: isToday
                                  ? FontWeight.w600
                                  : FontWeight.normal,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 1),
                          Text(
                            weekdays[index],
                            style: TextStyle(
                              fontSize: 12,
                              color: isToday
                                  ? const Color(0xFF2E7EED)
                                  : const Color(0xFF37352F),
                              fontWeight: isToday
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 2),
                          // 显示学习时间总计
                          Text(
                            studyTimeText,
                            style: TextStyle(
                              fontSize: 9,
                              color:
                                  dailyStats != null && dailyStats.hasStudyData
                                  ? const Color(0xFF0F7B6C)
                                  : const Color(0xFF9B9A97),
                              fontWeight: FontWeight.w500,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }),
            ],
          ),
        );
      },
    );
  }

  /// 时间表主体
  Widget _buildTimeTable() {
    return Container(
      color: Colors.white,
      child: SingleChildScrollView(
        child: Column(
          children: [
            // 添加每日计划行（在所有时间块前）
            _buildDailyPlanningRow(),
            // 现有时间行
            ...List.generate(18, (hour) {
              final timeHour = 6 + hour; // 6:00 到 23:00
              return _buildTimeRow(timeHour);
            }),
            // 添加每日优化行（在所有时间块后）
            _buildDailyOptimizationRow(),
          ],
        ),
      ),
    );
  }

  /// 时间行
  Widget _buildTimeRow(int hour) {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // 时间标签
          Container(
            width: 60,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              '${hour.toString().padLeft(2, '0')}:00',
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF9B9A97),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          // 日期列
          ...List.generate(7, (dayIndex) {
            final date = _currentWeekStart.add(Duration(days: dayIndex));

            return Expanded(
              child: Container(
                height: 80,
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  border: Border(
                    right: BorderSide(color: Colors.grey.shade200, width: 0.5),
                  ),
                ),
                child: _buildTimeSlotContent(date, hour),
              ),
            );
          }),
        ],
      ),
    );
  }

  /// 构建时间槽内容（集成时间盒子数据）
  Widget _buildTimeSlotContent(DateTime date, int hour) {
    return Consumer(
      builder: (context, ref, child) {
        // 获取该时间段的时间盒子任务
        final tasks = ref.watch(
          tasksForTimeRangeProvider((date: date, hour: hour)),
        );

        if (tasks.isEmpty) {
          return const SizedBox.shrink();
        }

        return Stack(
          children: tasks
              .map((task) => _buildTimeBoxBlock(task, hour))
              .toList(),
        );
      },
    );
  }

  /// 构建时间盒子块
  Widget _buildTimeBoxBlock(TimeBoxTask task, int hour) {
    final heightRatio = task.getHeightRatioForHour(hour);
    if (heightRatio <= 0) return const SizedBox.shrink();

    // 优化时间块高度计算，确保有足够空间显示文本
    final blockHeight = (76 * heightRatio).clamp(12.0, 76.0); // 最小12px，确保文本可读
    final isCompleted = task.isCompleted;
    final isRestTask = task.isRestTask;

    // 原始风格的颜色和边框处理
    Color backgroundColor;
    Border? border;

    if (isCompleted) {
      // 已完成任务：使用完整颜色填充
      backgroundColor = task.categoryColor;
      border = null; // 已完成任务不需要边框
    } else {
      // 未完成任务：使用半透明颜色，边框显示
      backgroundColor = task.getCategoryColorWithOpacity(0.3);
      border = Border.all(color: task.categoryColor, width: 1);
    }

    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: GestureDetector(
        onTap: () => _showTimeBoxDetailDialog(task),
        child: Container(
          height: blockHeight,
          // 减少边距以避免重叠，遵循 Mondrian 风格的紧密排列
          margin: const EdgeInsets.symmetric(horizontal: 0.5, vertical: 0.5),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(3),
            border: border,
          ),
          child: _buildTimeBoxContent(task, isRestTask, blockHeight),
        ),
      ),
    );
  }

  /// 构建时间盒子内容
  Widget _buildTimeBoxContent(
    TimeBoxTask task,
    bool isRestTask,
    double blockHeight,
  ) {
    if (isRestTask) {
      // 休息任务显示简单标记
      return Center(
        child: Container(
          width: 6,
          height: 6,
          decoration: BoxDecoration(
            color: task.isCompleted ? Colors.white : task.categoryColor,
            shape: BoxShape.circle,
          ),
        ),
      );
    }

    // 普通任务只显示标题，颜色已经代表分类信息
    return LayoutBuilder(
      builder: (context, constraints) {
        // 根据可用空间动态调整字体大小和布局

        // 动态计算字体大小
        double fontSize;
        int maxLines;

        if (blockHeight <= 15) {
          // 极小时间块：不显示文字
          return const SizedBox.shrink();
        } else if (blockHeight <= 25) {
          // 小时间块：单行，小字体
          fontSize = 6;
          maxLines = 1;
        } else if (blockHeight <= 40) {
          // 中等时间块：单行，中字体
          fontSize = 7;
          maxLines = 1;
        } else if (blockHeight <= 60) {
          // 大时间块：双行，中字体
          fontSize = 8;
          maxLines = 2;
        } else {
          // 超大时间块：多行，大字体
          fontSize = 9;
          maxLines = 3;
        }

        // 根据原始颜色方案确定文字颜色
        Color textColor;
        if (task.isCompleted) {
          // 已完成任务使用白色文字
          textColor = Colors.white;
        } else {
          // 未完成任务根据背景颜色选择合适的文字颜色
          final bgColor = task.categoryColor;
          // 对于深色背景使用白色文字，浅色背景使用深色文字
          if (bgColor == const Color(0xFFE03E3E) || // 红色
              bgColor == const Color(0xFF2E7EED) || // 蓝色
              bgColor == const Color(0xFF0F7B6C)) {
            // 绿色
            textColor = Colors.white;
          } else {
            textColor = const Color(0xFF37352F); // 深灰色文字（适用于荧光黄和灰色背景）
          }
        }

        return Padding(
          padding: const EdgeInsets.all(2),
          child: Center(
            child: Text(
              task.title,
              style: TextStyle(
                fontSize: fontSize,
                fontWeight: FontWeight.w600,
                color: textColor,
                height: 1.1, // 稍微增加行高以提高可读性
              ),
              maxLines: maxLines,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
        );
      },
    );
  }

  /// 显示帮助对话框
  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('📚 使用说明'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('📝 每日计划与优化', style: TextStyle(fontWeight: FontWeight.w600)),
              Text(
                '• 蓝色区域：每日计划（显示在时间块前）\n• 绿色区域：每日优化（显示在时间块后）\n• 点击空白处添加内容\n• 点击已有内容编辑修改',
              ),

              SizedBox(height: 16),
              Text('📅 查看学习记录', style: TextStyle(fontWeight: FontWeight.w600)),
              Text('• 上下滑动查看不同时间段\n• 左右切换查看不同周次\n• 点击时间块查看详细信息'),

              SizedBox(height: 16),
              Text('🎨 颜色分类', style: TextStyle(fontWeight: FontWeight.w600)),
              Text(
                '• 🔴 计算机科学 - 红色\n• 🟡 数学 - 荧光黄\n• 🟢 英语 - 绿色\n• 🔵 政治 - 蓝色',
              ),

              SizedBox(height: 16),
              Text('📊 数据导出', style: TextStyle(fontWeight: FontWeight.w600)),
              Text('• 点击右下角"导出Excel"按钮\n• 导出包含计划、优化和学习记录\n• 支持分享和保存到文件'),

              SizedBox(height: 16),
              Text('💡 小贴士', style: TextStyle(fontWeight: FontWeight.w600)),
              Text('• 每日计划在早晨制定\n• 每日优化在晚上总结\n• 时间块从时间盒子计时生成\n• 支持长期学习记录追踪'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }

  /// 判断是否为今天
  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  /// 检查两个日期是否为同一天
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// 构建选中日期的详细统计
  Widget _buildSelectedDateStats() {
    return Consumer(
      builder: (context, ref, child) {
        final dailyStats = ref.watch(dailyStudyTimeProvider(_selectedDate!));
        final dateStr = DateFormat('yyyy年MM月dd日').format(_selectedDate!);

        return Container(
          color: Colors.white,
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            border: Border(top: BorderSide(color: Color(0xFFE3E2DE), width: 1)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '$dateStr 学习统计',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedDate = null;
                      });
                    },
                    child: const Icon(
                      Icons.close,
                      size: 20,
                      color: Color(0xFF9B9A97),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              if (dailyStats != null && dailyStats.hasStudyData) ...[
                Row(
                  children: [
                    Expanded(
                      child: _buildStatItem(
                        '学习时长',
                        dailyStats.formattedStudyTime,
                        Icons.schedule,
                        const Color(0xFF2E7EED),
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        '完成任务',
                        '${dailyStats.completedTasks}个',
                        Icons.task_alt,
                        const Color(0xFF7C3AED),
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        '虚拟工资',
                        '¥${dailyStats.totalWage.toStringAsFixed(0)}',
                        Icons.attach_money,
                        const Color(0xFF0F7B6C),
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        '学习效率',
                        '${dailyStats.efficiencyPercentage.toStringAsFixed(0)}%',
                        Icons.trending_up,
                        const Color(0xFFD9730D),
                      ),
                    ),
                  ],
                ),
              ] else ...[
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF7F6F3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Center(
                    child: Text(
                      '该日期暂无学习记录',
                      style: TextStyle(color: Color(0xFF9B9A97), fontSize: 14),
                    ),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  /// 构建统计项
  Widget _buildStatItem(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(right: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, size: 20, color: color),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(fontSize: 10, color: Color(0xFF9B9A97)),
          ),
        ],
      ),
    );
  }

  /// 获取每日计划/优化内容
  String _getDailyContent(DateTime date, DailyPlanType type) {
    return ref.read(dailyPlanContentProvider((date: date, type: type)));
  }

  /// 检查是否有内容
  bool _hasContent(DateTime date, DailyPlanType type) {
    return ref.read(dailyPlanHasContentProvider((date: date, type: type)));
  }

  /// 显示每日编辑对话框
  void _showDailyEditDialog(
    DateTime date,
    DailyPlanType type,
    String currentContent,
  ) {
    String content = currentContent;
    final title = type == DailyPlanType.planning ? '每日计划' : '每日优化';
    final color = type == DailyPlanType.planning
        ? const Color(0xFF2E7EED)
        : const Color(0xFF0F7B6C);
    final dateStr = DateFormat('MM月dd日').format(date);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(
          '$dateStr $title',
          style: const TextStyle(
            color: Color(0xFF37352F),
            fontWeight: FontWeight.w600,
          ),
        ),
        content: TextField(
          controller: TextEditingController(text: currentContent),
          onChanged: (value) => content = value,
          maxLines: 8,
          decoration: InputDecoration(
            hintText: type == DailyPlanType.planning
                ? '输入今日学习计划...\n例如：\n• 复习算法题 2小时\n• 背诵英语单词 100个\n• 完成数学练习题'
                : '输入今日学习优化总结...\n例如：\n• 算法理解更深入了\n• 需要加强英语听力\n• 数学解题速度有提升',
            border: const OutlineInputBorder(),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: color),
            ),
          ),
          style: const TextStyle(color: Color(0xFF37352F)),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消', style: TextStyle(color: Color(0xFF787774))),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              // 保存数据
              await ref
                  .read(dailyPlanProvider.notifier)
                  .savePlan(date: date, type: type, content: content);

              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('$dateStr $title已保存'),
                    backgroundColor: color,
                    duration: const Duration(seconds: 2),
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: Colors.white,
            ),
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  /// 显示时间盒子详情对话框
  void _showTimeBoxDetailDialog(TimeBoxTask task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: task.categoryColor,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                task.title,
                style: const TextStyle(
                  color: Color(0xFF37352F),
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 任务描述
            if (task.description.isNotEmpty) ...[
              const Text(
                '任务描述：',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF37352F),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                task.description,
                style: const TextStyle(color: Color(0xFF787774), fontSize: 14),
              ),
              const SizedBox(height: 16),
            ],

            // 时长信息
            const Text(
              '时长信息：',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.schedule, size: 16, color: Color(0xFF9B9A97)),
                const SizedBox(width: 4),
                Text(
                  '计划时长：${task.plannedMinutes}分钟',
                  style: const TextStyle(
                    color: Color(0xFF787774),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            if (task.actualMinutes != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.timer, size: 16, color: Color(0xFF9B9A97)),
                  const SizedBox(width: 4),
                  Text(
                    '实际时长：${task.actualMinutes}分钟',
                    style: const TextStyle(
                      color: Color(0xFF787774),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],

            const SizedBox(height: 16),

            // 状态和分类
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: task.status.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: task.status.color.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    task.status.displayName,
                    style: TextStyle(
                      color: task.status.color,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: task.categoryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    task.category,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭', style: TextStyle(color: Color(0xFF787774))),
          ),
        ],
      ),
    );
  }

  /// 保存每日计划/优化内容（已通过Provider实现）
  // 此方法已被Provider替代，不再需要

  /// 构建每日计划行
  Widget _buildDailyPlanningRow() {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: const Color(0xFF2E7EED).withValues(alpha: 0.05),
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // 标签
          Container(
            width: 60,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: const Icon(
              Icons.assignment,
              size: 16,
              color: Color(0xFF2E7EED),
            ),
          ),
          // 日期列
          ...List.generate(7, (dayIndex) {
            final date = _currentWeekStart.add(Duration(days: dayIndex));

            return Expanded(
              child: Container(
                height: 60,
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  border: Border(
                    right: BorderSide(color: Colors.grey.shade200, width: 0.5),
                  ),
                ),
                child: _buildDailyPlanCard(date, type: DailyPlanType.planning),
              ),
            );
          }),
        ],
      ),
    );
  }

  /// 构建每日优化行
  Widget _buildDailyOptimizationRow() {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: const Color(0xFF0F7B6C).withValues(alpha: 0.05),
        border: Border(
          top: BorderSide(color: Colors.grey.shade200, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // 标签
          Container(
            width: 60,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: const Icon(
              Icons.trending_up,
              size: 16,
              color: Color(0xFF0F7B6C),
            ),
          ),
          // 日期列
          ...List.generate(7, (dayIndex) {
            final date = _currentWeekStart.add(Duration(days: dayIndex));

            return Expanded(
              child: Container(
                height: 60,
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  border: Border(
                    right: BorderSide(color: Colors.grey.shade200, width: 0.5),
                  ),
                ),
                child: _buildDailyPlanCard(
                  date,
                  type: DailyPlanType.optimization,
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  /// 构建每日计划/优化卡片
  Widget _buildDailyPlanCard(DateTime date, {required DailyPlanType type}) {
    // 获取该日期的计划/优化内容
    final hasContent = _hasContent(date, type);
    final content = _getDailyContent(date, type);

    final color = type == DailyPlanType.planning
        ? const Color(0xFF2E7EED)
        : const Color(0xFF0F7B6C);

    return GestureDetector(
      onTap: () => _showDailyEditDialog(date, type, content),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: hasContent
              ? color.withValues(alpha: 0.08)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(4),
          border: hasContent
              ? Border.all(color: color.withValues(alpha: 0.2), width: 1)
              : null,
        ),
        child: Padding(
          padding: const EdgeInsets.all(2),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 多行文本显示
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '每日',
                    style: TextStyle(
                      fontSize: 8,
                      fontWeight: FontWeight.w600,
                      color: hasContent ? color : const Color(0xFF9B9A97),
                      height: 1.1,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  Text(
                    type == DailyPlanType.planning ? '计划' : '优化',
                    style: TextStyle(
                      fontSize: 8,
                      fontWeight: FontWeight.w600,
                      color: hasContent ? color : const Color(0xFF9B9A97),
                      height: 1.1,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              // 内容指示器或添加图标
              const SizedBox(height: 3),
              if (hasContent)
                Container(
                  width: 5,
                  height: 5,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(2.5),
                  ),
                )
              else
                Icon(
                  type == DailyPlanType.planning
                      ? Icons.add_circle_outline
                      : Icons.edit_outlined,
                  size: 12,
                  color: const Color(0xFF9B9A97),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 时间块数据模型
class TimeBlock {
  final String id;
  final String title;
  final Subject subject;
  final DateTime startTime;
  final DateTime endTime;
  final int actualMinutes;

  TimeBlock({
    required this.id,
    required this.title,
    required this.subject,
    required this.startTime,
    required this.endTime,
    required this.actualMinutes,
  });
}

/// 学科枚举
enum Subject {
  computerScience,
  mathematics,
  english,
  politics;

  String get displayName {
    switch (this) {
      case Subject.computerScience:
        return '计算机科学';
      case Subject.mathematics:
        return '数学';
      case Subject.english:
        return '英语';
      case Subject.politics:
        return '政治';
    }
  }

  Color get color {
    switch (this) {
      case Subject.computerScience:
        return const Color(0xFFE03E3E); // 红色
      case Subject.mathematics:
        return const Color(0xFFFFD700); // 荧光黄
      case Subject.english:
        return const Color(0xFF0F7B6C); // 绿色
      case Subject.politics:
        return const Color(0xFF2E7EED); // 蓝色
    }
  }
}
