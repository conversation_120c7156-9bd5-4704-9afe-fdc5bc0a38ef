import 'package:shared_preferences/shared_preferences.dart';
import '../features/time_box/managers/task_category_manager.dart';

/// 分类清理助手
/// 
/// 提供清理旧分类数据的工具方法
class CategoryCleanupHelper {
  
  /// 强制清理所有分类数据并重置为新的4个系统分类
  static Future<bool> forceResetToNewCategories() async {
    try {
      print('🧹 开始强制清理分类数据...');
      
      final prefs = await SharedPreferences.getInstance();
      
      // 清理所有相关数据
      final keysToRemove = [
        'task_categories_v1',
        'task_categories_version',
        'timebox_tasks_v1', // 清理任务数据以避免迁移
      ];
      
      for (String key in keysToRemove) {
        if (prefs.containsKey(key)) {
          await prefs.remove(key);
          print('🗑️ 已清理: $key');
        }
      }
      
      // 获取全局分类管理器实例并重新初始化
      final categoryManager = TaskCategoryManager();
      await categoryManager.loadFromStorage();
      
      print('✅ 强制清理完成！');
      print('📋 当前分类数量: ${categoryManager.categories.length}');
      print('📝 分类列表: ${categoryManager.categories.map((c) => c.name).join(", ")}');
      
      return true;
    } catch (e) {
      print('❌ 强制清理失败: $e');
      return false;
    }
  }
  
  /// 检查当前分类状态
  static Future<Map<String, dynamic>> checkCategoryStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final categoryManager = TaskCategoryManager();
      await categoryManager.loadFromStorage();
      
      return {
        'success': true,
        'totalCategories': categoryManager.categories.length,
        'systemCategories': categoryManager.getDefaultCategories().length,
        'customCategories': categoryManager.getCustomCategories().length,
        'version': prefs.getInt('task_categories_version') ?? 0,
        'hasOldData': prefs.getString('task_categories_v1') != null,
        'categoryNames': categoryManager.categories.map((c) => c.name).toList(),
        'systemCategoryNames': categoryManager.getDefaultCategories().map((c) => c.name).toList(),
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }
  
  /// 检查是否需要清理（如果仍然显示6个或更多分类）
  static Future<bool> needsCleanup() async {
    try {
      final status = await checkCategoryStatus();
      if (!status['success']) return false;
      
      final systemCategories = status['systemCategoryNames'] as List<String>;
      
      // 如果系统分类包含"休息"或"其他"，则需要清理
      return systemCategories.contains('休息') || 
             systemCategories.contains('其他') ||
             systemCategories.length > 4;
    } catch (e) {
      print('❌ 检查清理需求失败: $e');
      return false;
    }
  }
  
  /// 打印详细的诊断信息
  static Future<void> printDiagnostics() async {
    print('🔍 === 分类数据诊断 ===');
    
    final status = await checkCategoryStatus();
    
    if (status['success']) {
      print('📊 总分类数: ${status['totalCategories']}');
      print('📋 系统分类数: ${status['systemCategories']}');
      print('📝 自定义分类数: ${status['customCategories']}');
      print('🔢 数据版本: ${status['version']}');
      print('💾 有旧数据: ${status['hasOldData']}');
      print('📝 所有分类: ${(status['categoryNames'] as List).join(", ")}');
      print('🏛️ 系统分类: ${(status['systemCategoryNames'] as List).join(", ")}');
      
      final needsCleanup = await CategoryCleanupHelper.needsCleanup();
      print('🧹 需要清理: $needsCleanup');
      
      if (needsCleanup) {
        print('⚠️ 检测到需要清理的问题：');
        final systemCategories = status['systemCategoryNames'] as List<String>;
        if (systemCategories.contains('休息')) {
          print('  - 仍包含"休息"分类');
        }
        if (systemCategories.contains('其他')) {
          print('  - 仍包含"其他"分类');
        }
        if (systemCategories.length > 4) {
          print('  - 系统分类数量超过4个');
        }
      }
    } else {
      print('❌ 诊断失败: ${status['error']}');
    }
    
    print('🔍 === 诊断完成 ===');
  }
}
