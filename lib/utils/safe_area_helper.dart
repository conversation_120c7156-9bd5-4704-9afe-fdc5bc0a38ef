import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';

/// 安全区域助手工具
/// 专门处理刘海屏、动态岛等屏幕遮挡问题
class SafeAreaHelper {
  /// 获取安全区域信息
  static EdgeInsets getSafeAreaInsets(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  /// 获取屏幕尺寸信息
  static Size getScreenSize(BuildContext context) {
    return MediaQuery.of(context).size;
  }

  /// 检查是否有刘海屏或动态岛
  static bool hasNotch(BuildContext context) {
    final padding = MediaQuery.of(context).padding;
    return padding.top > 20; // iOS状态栏通常是20pt，超过则可能有刘海
  }

  /// 获取顶部安全距离
  static double getTopSafeArea(BuildContext context) {
    return MediaQuery.of(context).padding.top;
  }

  /// 获取底部安全距离
  static double getBottomSafeArea(BuildContext context) {
    return MediaQuery.of(context).padding.bottom;
  }

  /// 为照片选择界面提供安全的布局参数
  static EdgeInsets getPhotoSelectionSafeInsets(BuildContext context) {
    final padding = MediaQuery.of(context).padding;

    // 确保顶部有足够的空间避开刘海屏/动态岛
    final topPadding = padding.top + 8; // 额外8pt缓冲

    // 确保底部有足够的空间避开Home指示器
    final bottomPadding = padding.bottom + 16; // 额外16pt缓冲

    return EdgeInsets.only(
      top: topPadding,
      bottom: bottomPadding,
      left: padding.left + 8,
      right: padding.right + 8,
    );
  }

  /// 为对话框提供安全的布局参数
  static EdgeInsets getDialogSafeInsets(BuildContext context) {
    final padding = MediaQuery.of(context).padding;
    final deviceInfo = getDeviceInfo(context);

    // 🔧 修复：根据设备类型提供更精确的对话框安全区域
    double topPadding = padding.top + 20;
    double bottomPadding = padding.bottom + 20;

    // 针对动态岛设备增加更多顶部空间
    if (deviceInfo['hasDynamicIsland'] == true) {
      topPadding = padding.top + 40; // 动态岛需要更多空间
    } else if (deviceInfo['hasNotch'] == true) {
      topPadding = padding.top + 30; // 刘海屏需要适中空间
    }

    return EdgeInsets.only(
      top: topPadding,
      bottom: bottomPadding,
      left: padding.left + 16,
      right: padding.right + 16,
    );
  }

  /// 创建安全的容器Widget
  static Widget createSafeContainer({
    required BuildContext context,
    required Widget child,
    Color? backgroundColor,
    bool avoidNotch = true,
    bool avoidHomeIndicator = true,
    EdgeInsets? additionalPadding,
  }) {
    final safeInsets = MediaQuery.of(context).padding;

    EdgeInsets finalPadding = EdgeInsets.only(
      top: avoidNotch ? safeInsets.top : 0,
      bottom: avoidHomeIndicator ? safeInsets.bottom : 0,
      left: safeInsets.left,
      right: safeInsets.right,
    );

    if (additionalPadding != null) {
      finalPadding = finalPadding + additionalPadding;
    }

    return Container(
      color: backgroundColor,
      padding: finalPadding,
      child: child,
    );
  }

  /// 创建适配刘海屏的AppBar
  static PreferredSizeWidget createSafeAppBar({
    required BuildContext context,
    required String title,
    List<Widget>? actions,
    Widget? leading,
    Color? backgroundColor,
    Color? foregroundColor,
    bool automaticallyImplyLeading = true,
  }) {
    return AppBar(
      title: Text(title),
      actions: actions,
      leading: leading,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      automaticallyImplyLeading: automaticallyImplyLeading,
      // 确保AppBar内容不被刘海遮挡
      toolbarHeight: kToolbarHeight,
      elevation: 0,
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness:
            (backgroundColor?.computeLuminance() ?? 0) > 0.5
            ? Brightness.dark
            : Brightness.light,
      ),
    );
  }

  /// 为底部按钮提供安全的布局
  static Widget createSafeBottomButton({
    required BuildContext context,
    required Widget button,
    EdgeInsets? margin,
  }) {
    final bottomSafeArea = MediaQuery.of(context).padding.bottom;

    return Container(
      margin: margin ?? const EdgeInsets.all(16),
      padding: EdgeInsets.only(bottom: bottomSafeArea),
      child: button,
    );
  }

  /// 检查设备类型和屏幕特征
  static Map<String, dynamic> getDeviceInfo(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final padding = MediaQuery.of(context).padding;
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    
    // 🔧 新增：用于存储调整后的安全区域值
    EdgeInsets adjustedPadding = padding;

    // 检测可能的设备类型
    String deviceType = 'unknown';
    bool hasNotch = false;
    bool hasDynamicIsland = false;

    print('🔧 [SafeArea] getDeviceInfo 开始检测设备信息');
    print('   - Platform.isIOS: ${Platform.isIOS}');
    print('   - Platform.isAndroid: ${Platform.isAndroid}');
    print('   - Platform.isMacOS: ${Platform.isMacOS}');
    print('   - Platform.isWindows: ${Platform.isWindows}');
    print('   - Platform.isLinux: ${Platform.isLinux}');
    print('   - 屏幕尺寸: ${size.width}x${size.height}');
    print('   - 安全区域: top=${padding.top}, bottom=${padding.bottom}');
    print('   - 设备像素比: $devicePixelRatio');

    // Device detection for Dynamic Island and notch handling
    if (Platform.isIOS) {
      print('🔧 [SafeArea] 检测到iOS平台，开始详细设备检测');
      
      // 🔧 修复：当padding.top为0时，基于屏幕尺寸强制判断设备类型
      // 这种情况通常发生在模拟器或某些特殊场景下
      if (padding.top == 0.0) {
        print('⚠️ [SafeArea] 检测到padding.top为0，使用屏幕尺寸进行设备判断');
        
        // iPhone 15 Pro / 14 Pro 系列（Dynamic Island）
        if ((size.height >= 870 && size.height <= 880 && size.width >= 400 && size.width <= 410) || // iPhone 15 Pro
            (size.height >= 844 && size.height <= 854 && size.width >= 385 && size.width <= 395)) { // iPhone 14 Pro
          hasDynamicIsland = true;
          deviceType = 'iPhone Pro with Dynamic Island (尺寸推断)';
          adjustedPadding = EdgeInsets.fromLTRB(0, 59.0, 0, 34.0); // 强制设置标准Dynamic Island安全区域
          print('🔧 [SafeArea] 基于屏幕尺寸判断为Dynamic Island设备，强制设置padding.top=59.0');
        }
        // iPhone 15 Pro Max / 14 Pro Max 系列（Dynamic Island）
        else if ((size.height >= 930 && size.height <= 940 && size.width >= 428 && size.width <= 435)) {
          hasDynamicIsland = true;
          deviceType = 'iPhone Pro Max with Dynamic Island (尺寸推断)';
          adjustedPadding = EdgeInsets.fromLTRB(0, 59.0, 0, 34.0);
          print('🔧 [SafeArea] 基于屏幕尺寸判断为Dynamic Island Pro Max设备');
        }
        // iPhone X/11/12/13 系列（刘海屏）
        else if (size.height >= 812) {
          hasNotch = true;
          deviceType = 'iPhone with Notch (尺寸推断)';
          adjustedPadding = EdgeInsets.fromLTRB(0, 44.0, 0, 34.0); // 强制设置标准刘海屏安全区域
          print('🔧 [SafeArea] 基于屏幕尺寸判断为刘海屏设备，强制设置padding.top=44.0');
        }
        // 其他iPhone（无刘海）
        else {
          deviceType = 'iPhone (标准)';
          adjustedPadding = EdgeInsets.fromLTRB(0, 20.0, 0, 0); // 标准状态栏高度
          print('🔧 [SafeArea] 基于屏幕尺寸判断为标准iPhone');
        }
      }
      // 正常情况：padding.top有值
      else if (padding.top >= 59.0) {
        hasDynamicIsland = true;
        deviceType = 'iPhone with Dynamic Island';
        // Dynamic Island detected - enhanced safe area handling applied

        // 进一步区分 Pro 和 Pro Max
        if (size.height >= 932 && size.width >= 430) {
          deviceType = 'iPhone 15 Pro Max with Dynamic Island';
        } else if (size.height >= 926 && size.width >= 428) {
          deviceType = 'iPhone 14 Pro Max with Dynamic Island';
        } else if (size.height >= 852 && size.width >= 393) {
          deviceType = 'iPhone 15 Pro with Dynamic Island';
        } else if (size.height >= 844 && size.width >= 390) {
          deviceType = 'iPhone 14 Pro with Dynamic Island';
        }
      }
      // iPhone X系列刘海屏检测 (X/XS/XR/11/12/13系列)
      else if (padding.top >= 44.0 && padding.top < 59.0) {
        hasNotch = true;
        deviceType = 'iPhone with Notch';

        // 根据屏幕尺寸进一步判断具体机型
        if (size.height >= 926) {
          deviceType = 'iPhone 12/13 Pro Max with Notch';
        } else if (size.height >= 896) {
          deviceType = 'iPhone 11/XR/XS Max with Notch';
        } else if (size.height >= 844) {
          deviceType = 'iPhone 12/13 Pro with Notch';
        } else if (size.height >= 812) {
          deviceType = 'iPhone X/XS/11 Pro with Notch';
        }
      }
      // 传统iPhone (iPhone 8及以下，iPhone SE系列)
      else if (padding.top >= 20.0) {
        deviceType = 'Traditional iPhone';

        if (size.height >= 736) {
          deviceType = 'iPhone 6/7/8 Plus';
        } else if (size.height >= 667) {
          deviceType = 'iPhone 6/7/8';
        } else if (size.height >= 568) {
          deviceType = 'iPhone SE (1st/2nd/3rd gen)';
        } else {
          deviceType = 'iPhone 5/5s/5c';
        }
      }
    } else {
      // 🔧 非iOS平台的处理
      print('🔧 [SafeArea] 非iOS平台检测');
      if (Platform.isAndroid) {
        deviceType = 'Android Device';
        // Android设备可能也有刘海屏，根据顶部安全区域判断
        if (padding.top > 24.0) {
          hasNotch = true;
          deviceType = 'Android with Notch/Cutout';
        }
      } else if (Platform.isMacOS) {
        deviceType = 'macOS Device';
      } else if (Platform.isWindows) {
        deviceType = 'Windows Device';
      } else if (Platform.isLinux) {
        deviceType = 'Linux Device';
      } else {
        deviceType = 'Unknown Platform';
      }
      
      print('🔧 [SafeArea] 非iOS平台设备类型: $deviceType');
    }

    return {
      'deviceType': deviceType,
      'screenSize': size,
      'safeAreaInsets': adjustedPadding,  // 使用调整后的padding
      'devicePixelRatio': devicePixelRatio,
      'hasNotch': hasNotch,
      'hasDynamicIsland': hasDynamicIsland,
      'topSafeArea': adjustedPadding.top,  // 使用调整后的padding
      'bottomSafeArea': adjustedPadding.bottom,  // 使用调整后的padding
      'isLandscape': size.width > size.height,
    };
  }

  /// 为照片选择提供优化的布局建议
  static Map<String, dynamic> getPhotoSelectionLayoutAdvice(
    BuildContext context,
  ) {
    final deviceInfo = getDeviceInfo(context);
    final size = MediaQuery.of(context).size;
    final padding = MediaQuery.of(context).padding;

    // 计算可用的安全区域
    final availableHeight = size.height - padding.top - padding.bottom;
    final availableWidth = size.width - padding.left - padding.right;

    // 建议的布局参数
    final suggestions = <String, dynamic>{
      'availableHeight': availableHeight,
      'availableWidth': availableWidth,
      'recommendedTopMargin': padding.top + 16,
      'recommendedBottomMargin': padding.bottom + 16,
      'recommendedSideMargin': padding.left + 16,
      'maxDialogHeight': availableHeight * 0.8,
      'maxDialogWidth': availableWidth * 0.9,
    };

    // 🔧 优化：根据设备类型提供精确的布局建议
    if (deviceInfo['hasDynamicIsland'] == true) {
      suggestions['specialNote'] = '检测到灵动岛，已优化顶部布局';
      suggestions['recommendedTopMargin'] = padding.top + 32; // 灵动岛需要更多空间
      suggestions['recommendedAppBarHeight'] = kToolbarHeight + 20;
      suggestions['safeAreaTopPadding'] = 12.0; // 额外的安全距离
      suggestions['pickerTopPadding'] = 16.0; // 照片选择器专用的顶部距离
    } else if (deviceInfo['hasNotch'] == true) {
      suggestions['specialNote'] = '检测到刘海屏，已优化顶部布局';
      suggestions['recommendedTopMargin'] = padding.top + 24; // 刘海屏需要适中空间
      suggestions['recommendedAppBarHeight'] = kToolbarHeight + 12;
      suggestions['safeAreaTopPadding'] = 8.0;
      suggestions['pickerTopPadding'] = 12.0;
    } else {
      suggestions['specialNote'] = '传统屏幕设备，使用标准布局';
      suggestions['recommendedTopMargin'] = padding.top + 16; // 标准设备的标准边距
      suggestions['recommendedAppBarHeight'] = kToolbarHeight;
      suggestions['safeAreaTopPadding'] = 4.0;
      suggestions['pickerTopPadding'] = 8.0;
    }

    // 🔧 新增：针对照片选择的专用配置
    suggestions['photoPickerConfig'] = {
      'safeAreaInsets': EdgeInsets.only(
        top: suggestions['pickerTopPadding'] as double,
        bottom: padding.bottom + 16,
        left: padding.left + 8,
        right: padding.right + 8,
      ),
      'gridPadding': const EdgeInsets.all(4.0),
      'itemSpacing': 2.0,
      'crossAxisCount': 4,
    };

    return {'deviceInfo': deviceInfo, 'layoutSuggestions': suggestions};
  }

  /// 为系统对话框（如照片选择器）提供安全的调用方式
  static Future<T?> showSystemDialogSafely<T>(
    BuildContext context,
    Future<T?> Function() systemDialogCall, {
    bool bypassWrapper = false, // 新增：允许直接绕过包装器
  }) async {
    // 🔧 临时应急开关：直接透传系统调用，完全绕过包装器逻辑
    if (bypassWrapper) {
      return await systemDialogCall();
    }
    // 获取当前的安全区域信息
    final deviceInfo = getDeviceInfo(context);
    
    print('🔧 [SafeArea] 准备安全调用系统对话框');
    print('   - 设备类型: ${deviceInfo['deviceType']}');
    print('   - 顶部安全区域: ${deviceInfo['topSafeArea']}');
    print('   - 是否有灵动岛: ${deviceInfo['hasDynamicIsland']}');
    print('   - 是否有刘海屏: ${deviceInfo['hasNotch']}');

    // 🔧 针对灵动岛和iOS设备优化：确保系统UI有足够空间
    // 即使检测失败，对于所有iOS设备都应用基本的状态栏保护
    if (Platform.isIOS) {
      try {
        // 轻量化策略：保持 edgeToEdge，透明状态栏，避免触发布局重排
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
        SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
          systemNavigationBarColor: Colors.transparent,
          systemNavigationBarIconBrightness: Brightness.dark,
          systemNavigationBarDividerColor: Colors.transparent,
        ));
        // 极短等待，避免二次跳动
        await Future.delayed(const Duration(milliseconds: 50));
      } catch (e) {
        print('⚠️ [SafeArea] 状态栏样式设置失败: $e');
      }
    }

    // 如果检测到动态岛或刘海屏，添加延迟确保UI稳定
    // 或者顶部安全区域被强制设置（padding=0的修复情况）
    // 移除长延迟，避免系统选择器弹出后再发生布局跳变

    try {
      print('🔧 [SafeArea] 开始调用系统对话框...');
      
      // 调用系统对话框
      final result = await systemDialogCall();

      print('🔧 [SafeArea] 系统对话框调用完成');

      // 系统对话框关闭后，再次短暂延迟确保UI状态正确
      if (deviceInfo['hasDynamicIsland'] == true ||
          deviceInfo['hasNotch'] == true) {
        await Future.delayed(Duration(
          milliseconds: deviceInfo['hasDynamicIsland'] == true ? 150 : 100,
        ));
      }

      // 🔧 恢复状态栏样式到应用默认状态（保持 edgeToEdge，防止再次覆盖通知栏）
      if (Platform.isIOS) {
        try {
          print('🔧 [SafeArea] 开始恢复应用默认状态栏样式');
          
          // 极短延迟，避免抖动
          await Future.delayed(const Duration(milliseconds: 50));
          
          // 恢复应用默认状态栏样式（透明 + 深色图标）
          SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.dark,  // 应用默认：深色图标
            statusBarBrightness: Brightness.light,  // 应用默认：浅色背景
            systemNavigationBarColor: Colors.transparent,
            systemNavigationBarIconBrightness: Brightness.dark,
          ));
          
          // 保持 edgeToEdge，避免布局突变
          SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
          
          print('🔧 [SafeArea] 已恢复应用默认状态栏样式');
        } catch (e) {
          print('⚠️ [SafeArea] 状态栏样式恢复失败: $e');
        }
      }

      return result;
    } catch (e) {
      print('⚠️ [SafeArea] 系统对话框调用失败: $e');
      
      // 🔧 确保即使出错也恢复状态栏样式
      if (Platform.isIOS) {
        try {
          SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.dark,
            statusBarBrightness: Brightness.light,
          ));
          SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
        } catch (_) {}
      }
      
      rethrow;
    }
  }

  /// 创建调试信息Widget
  static Widget createDebugInfoWidget(BuildContext context) {
    final info = getPhotoSelectionLayoutAdvice(context);
    final deviceInfo = info['deviceInfo'] as Map<String, dynamic>;
    final suggestions = info['layoutSuggestions'] as Map<String, dynamic>;

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            '设备布局信息',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '设备类型: ${deviceInfo['deviceType']}',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
          Text(
            '屏幕尺寸: ${deviceInfo['screenSize'].width.toInt()}x${deviceInfo['screenSize'].height.toInt()}',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
          Text(
            '顶部安全区: ${deviceInfo['topSafeArea']}pt',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
          Text(
            '底部安全区: ${deviceInfo['bottomSafeArea']}pt',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
          Text(
            '可用高度: ${suggestions['availableHeight'].toInt()}pt',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
          if (suggestions['specialNote'] != null) ...[
            const SizedBox(height: 4),
            Text(
              '⚠️ ${suggestions['specialNote']}',
              style: const TextStyle(color: Colors.orange, fontSize: 12),
            ),
          ],
        ],
      ),
    );
  }
}

/// 安全区域感知的Scaffold（优化了灵动岛支持）
class SafeAreaScaffold extends StatelessWidget {
  final String title;
  final Widget body;
  final List<Widget>? actions;
  final Widget? floatingActionButton;
  final Color? backgroundColor;
  final Color? appBarBackgroundColor;
  final Color? appBarForegroundColor;
  final bool avoidNotch;
  final bool avoidHomeIndicator;
  final bool optimizeForPhotoSelection; // 新增：是否为照片选择优化

  const SafeAreaScaffold({
    super.key,
    required this.title,
    required this.body,
    this.actions,
    this.floatingActionButton,
    this.backgroundColor,
    this.appBarBackgroundColor,
    this.appBarForegroundColor,
    this.avoidNotch = true,
    this.avoidHomeIndicator = true,
    this.optimizeForPhotoSelection = false,
  });

  @override
  Widget build(BuildContext context) {
    // 🔧 获取设备信息用于优化
    final deviceInfo = SafeAreaHelper.getDeviceInfo(context);
    final hasDynamicIsland = deviceInfo['hasDynamicIsland'] == true;
    
    // 🔧 针对照片选择页面的特殊优化
    Widget optimizedBody = body;
    if (optimizeForPhotoSelection) {
      print('🔧 [SafeAreaScaffold] 优化照片选择界面');
      
      if (hasDynamicIsland) {
        print('🔧 [SafeAreaScaffold] 为灵动岛设备优化照片选择界面 - 向下推送内容');
        // 🔧 为照片选择页面添加额外的顶部边距，让内容向下移动
        // 这样系统照片选择器的顶部控件就不会被灵动岛遮挡
        optimizedBody = Container(
          margin: const EdgeInsets.only(top: 16.0), // 增加边距，避开灵动岛
          child: body,
        );
      } else if (deviceInfo['hasNotch'] == true) {
        print('🔧 [SafeAreaScaffold] 为刘海屏设备优化照片选择界面');
        // 🔧 为刘海屏设备提供适当的顶部边距
        optimizedBody = Container(
          margin: const EdgeInsets.only(top: 8.0), // 适中边距，避开刘海
          child: body,
        );
      }
    }

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: SafeAreaHelper.createSafeAppBar(
        context: context,
        title: title,
        actions: actions,
        backgroundColor: appBarBackgroundColor,
        foregroundColor: appBarForegroundColor,
      ),
      body: SafeAreaHelper.createSafeContainer(
        context: context,
        child: optimizedBody,
        avoidNotch: avoidNotch,
        avoidHomeIndicator: avoidHomeIndicator,
        additionalPadding: optimizeForPhotoSelection && hasDynamicIsland
            ? const EdgeInsets.only(top: 4.0) // 灵动岛额外安全距离
            : null,
      ),
      floatingActionButton: floatingActionButton,
    );
  }
}
