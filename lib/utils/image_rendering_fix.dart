import 'dart:io';
import 'package:flutter/material.dart';

/// 图片渲染修复工具
/// 专门用于解决iOS模拟器图片重影和渲染问题
class ImageRenderingFix {
  /// 创建一个优化的Image.file组件，防止重影问题
  static Widget buildOptimizedImageFile(
    File file, {
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
    Widget? errorWidget,
    bool enableRepaintBoundary = true,
    FilterQuality filterQuality = FilterQuality.high,
  }) {
    Widget imageWidget = Image.file(
      file,
      fit: fit,
      width: width,
      height: height,
      filterQuality: filterQuality,
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ??
            Container(
              color: const Color(0xFFF5F5F5),
              child: const Icon(
                Icons.broken_image,
                color: Color(0xFF9E9E9E),
                size: 32,
              ),
            );
      },
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (frame == null) {
          return Container(
            color: const Color(0xFFF5F5F5),
            child: const Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Color(0xFF9E9E9E),
              ),
            ),
          );
        }
        return child;
      },
    );

    // 在iOS模拟器中使用RepaintBoundary防止重影
    if (enableRepaintBoundary && Platform.isIOS) {
      imageWidget = RepaintBoundary(child: imageWidget);
    }

    return imageWidget;
  }

  /// 创建一个优化的Image.network组件，防止重影问题
  static Widget buildOptimizedImageNetwork(
    String url, {
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
    Widget? errorWidget,
    Widget? loadingWidget,
    bool enableRepaintBoundary = true,
    FilterQuality filterQuality = FilterQuality.high,
  }) {
    Widget imageWidget = Image.network(
      url,
      fit: fit,
      width: width,
      height: height,
      filterQuality: filterQuality,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return loadingWidget ??
            Container(
              color: const Color(0xFFFFFFFF),
              child: Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                  color: const Color(0xFF9B9A97),
                ),
              ),
            );
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ??
            Container(
              color: const Color(0xFFF5F5F5),
              child: const Icon(
                Icons.broken_image,
                color: Color(0xFF9E9E9E),
                size: 32,
              ),
            );
      },
    );

    // 在iOS模拟器中使用RepaintBoundary防止重影
    if (enableRepaintBoundary && Platform.isIOS) {
      imageWidget = RepaintBoundary(child: imageWidget);
    }

    return imageWidget;
  }

  /// 创建一个防重影的容器，用于包装图片
  static Widget buildAntiGhostContainer({
    required Widget child,
    double? width,
    double? height,
    BorderRadius? borderRadius,
    BoxBorder? border,
    List<BoxShadow>? boxShadow,
    Color? backgroundColor,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.transparent,
        borderRadius: borderRadius,
        border: border,
        boxShadow: boxShadow,
      ),
      child: ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.zero,
        child: RepaintBoundary(child: child),
      ),
    );
  }

  /// 检查是否需要应用重影修复
  static bool shouldApplyGhostingFix() {
    // 在iOS平台上应用修复
    return Platform.isIOS;
  }

  /// 获取推荐的图片渲染配置
  static Map<String, dynamic> getRecommendedImageConfig() {
    return {
      'filterQuality': FilterQuality.high,
      'enableRepaintBoundary': Platform.isIOS,
      'fit': BoxFit.cover,
      'cacheWidth': null, // 让Flutter自动处理
      'cacheHeight': null, // 让Flutter自动处理
    };
  }
}
