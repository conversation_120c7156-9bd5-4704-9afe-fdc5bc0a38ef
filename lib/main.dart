import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'router/app_router.dart';
import 'services/floating_timer_service.dart';
import 'services/first_time_service.dart';
import 'services/locale_service.dart';
import 'l10n/app_localizations.dart';

void main() async {
  // 确保Flutter绑定已初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化首次使用检测服务
  await FirstTimeService.instance.initialize();

  // 初始化本地化数据，修复DateFormat的LocaleDataException错误
  await initializeDateFormatting('zh_CN', null);

  runApp(
    // 使用ProviderScope包装应用，启用Riverpod状态管理
    const ProviderScope(child: OneDay()),
  );
}

/// OneDay应用主入口
class OneDay extends ConsumerWidget {
  const OneDay({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 设置全局Navigator Key到浮动计时器服务
    FloatingTimerService.setNavigatorKey(AppRouter.rootNavigatorKey);

    // 监听当前语言设置
    final currentLocale = ref.watch(localeProvider);

    return MaterialApp.router(
      title: 'OneDay',
      debugShowCheckedModeBanner: false,

      // 国际化配置
      locale: currentLocale,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: LocaleService.supportedLocales,

      // 使用GoRouter进行路由管理
      routerConfig: AppRouter.router,

      // 应用主题配置
      theme: ThemeData(
        useMaterial3: true,
        // 浅色主题
        brightness: Brightness.light,
        // 主色调：柔和蓝
        primarySwatch: Colors.blue,
        primaryColor: const Color(0xFF2E7EED),
        // 背景色
        scaffoldBackgroundColor: Colors.white,
        // 字体配置

        // 文字主题 - Notion风格
        textTheme: const TextTheme(
          displayLarge: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 32,
            fontWeight: FontWeight.w600,
            height: 1.2,
          ),
          displayMedium: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 28,
            fontWeight: FontWeight.w600,
            height: 1.2,
          ),
          displaySmall: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 24,
            fontWeight: FontWeight.w600,
            height: 1.3,
          ),
          headlineLarge: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 22,
            fontWeight: FontWeight.w600,
            height: 1.3,
          ),
          headlineMedium: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 20,
            fontWeight: FontWeight.w500,
            height: 1.4,
          ),
          headlineSmall: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 18,
            fontWeight: FontWeight.w500,
            height: 1.4,
          ),
          titleLarge: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 16,
            fontWeight: FontWeight.w600,
            height: 1.5,
          ),
          titleMedium: TextStyle(
            color: Color(0xFF787774),
            fontSize: 14,
            fontWeight: FontWeight.w500,
            height: 1.5,
          ),
          titleSmall: TextStyle(
            color: Color(0xFF9B9A97),
            fontSize: 12,
            fontWeight: FontWeight.w500,
            height: 1.4,
          ),
          bodyLarge: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 16,
            fontWeight: FontWeight.w400,
            height: 1.6,
          ),
          bodyMedium: TextStyle(
            color: Color(0xFF787774),
            fontSize: 14,
            fontWeight: FontWeight.w400,
            height: 1.5,
          ),
          bodySmall: TextStyle(
            color: Color(0xFF9B9A97),
            fontSize: 12,
            fontWeight: FontWeight.w400,
            height: 1.4,
          ),
        ),
        // 按钮主题 - Notion风格
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF2E7EED),
            foregroundColor: Colors.white,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            textStyle: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
          ),
        ),
        // 文本按钮主题
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            foregroundColor: const Color(0xFF2E7EED),
            textStyle: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        // 应用栏主题 - Notion风格
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.transparent, // 禁用Material 3 surface tinting
          foregroundColor: Color(0xFF37352F),
          elevation: 0,
          centerTitle: true,
          titleTextStyle: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        // 卡片主题 - 简洁设计
        cardTheme: CardThemeData(
          color: const Color(0xFFFAFAFA),
          surfaceTintColor: Colors.transparent, // 禁用Material 3 surface tinting
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(
              color: const Color(0xFF37352F).withValues(alpha: 0.08),
              width: 1,
            ),
          ),
        ),
        // 输入框主题 - 遵循白纸黑字原则
        inputDecorationTheme: InputDecorationTheme(
          filled: true,
          fillColor: Colors.white, // 纯白色背景
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(
              color: const Color(0xFF37352F).withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(
              color: const Color(0xFF37352F).withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(
              color: Color(0xFF2E7EED), // 品牌蓝色聚焦边框
              width: 2,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Color(0xFFE03E3E), width: 2),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Color(0xFFE03E3E), width: 2),
          ),
          labelStyle: const TextStyle(
            color: Color(0xFF787774), // 深色标签文字
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          hintStyle: const TextStyle(
            color: Color(0xFF9B9A97), // 灰色提示文字
            fontSize: 16,
            fontWeight: FontWeight.w400,
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 14,
          ),
        ),
        // 对话框主题 - Notion风格
        dialogTheme: DialogThemeData(
          backgroundColor: Colors.white, // 白色背景
          surfaceTintColor:
              Colors.transparent, // 禁用Material 3 surface tinting防止图片重影
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          titleTextStyle: const TextStyle(
            color: Color(0xFF37352F), // 深色标题
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
          contentTextStyle: const TextStyle(
            color: Color(0xFF787774), // 深色内容文字
            fontSize: 16,
            height: 1.5,
          ),
        ),
        // 底部导航栏主题 - Notion风格
        bottomNavigationBarTheme: const BottomNavigationBarThemeData(
          backgroundColor: Colors.white, // 白色背景
          selectedItemColor: Color(0xFF2E7EED), // 品牌蓝色
          unselectedItemColor: Color(0xFF9B9A97), // 灰色
          type: BottomNavigationBarType.fixed,
          elevation: 0,
        ),
        // 弹出菜单主题 - 确保纯白色背景
        popupMenuTheme: const PopupMenuThemeData(
          color: Colors.white, // 纯白色背景
          surfaceTintColor: Colors.transparent, // 禁用Material 3 surface tinting
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
        ),
        // SnackBar主题 - 统一样式
        snackBarTheme: SnackBarThemeData(
          backgroundColor: const Color(0xFF2E7EED), // 品牌蓝色
          contentTextStyle: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          behavior: SnackBarBehavior.floating,
          // 设置默认显示时长为1秒
          actionTextColor: Colors.white,
        ),
      ),
    );
  }
}
