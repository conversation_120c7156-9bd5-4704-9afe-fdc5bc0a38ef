# OneDay 功能实现文档

本文档记录 OneDay 应用的核心功能实现和技术方案。

## 📋 目录

- [拖拽交互系统](#拖拽交互系统)
- [TimeBox 计时功能](#timebox-计时功能)
- [图表可视化系统](#图表可视化系统)
- [成就系统](#成就系统)
- [记忆宫殿功能](#记忆宫殿功能)
- [学习统计功能](#学习统计功能)

---

## 🎯 拖拽交互系统

### 功能概述
实现了记忆宫殿中知识点气泡的精确拖拽定位系统，支持横屏/竖屏自适应。

### 核心技术实现

#### 1. 坐标系统设计
- **图片坐标系**：基于图片原始尺寸的相对坐标 (0.0-1.0)
- **屏幕坐标系**：设备屏幕的绝对像素坐标
- **变换矩阵**：处理缩放、平移、旋转的坐标转换

#### 2. 拖拽定位算法
```dart
// 核心定位逻辑
final dotGlobalPosition = Offset(
  correctedX,
  correctedY + connectionLineLength,
);

// FractionalTranslation 精确偏移
return FractionalTranslation(
  translation: Offset(-0.5, yOffset),
  child: Transform.scale(scale: inverseScale, child: child),
);
```

#### 3. 关键修复点
- **横屏适配**：解决横屏模式下的坐标偏移问题
- **定位圆点对齐**：确保拖拽圆点与显示圆点像素级重叠
- **连接线长度调整**：拖拽时连接线从8px延长到70px，避免手指遮挡
- **边界检测**：防止拖拽超出图片范围
- **偏移计算统一**：修复拖拽更新逻辑，使用与显示逻辑一致的计算公式
- **文本框拖拽设计**：将拖拽检测区域从定位圆点移动到文本框，解决手指遮挡问题

#### 4. 核心算法修复
```dart
// 统一偏移计算公式（关键修复）
yOffsetRatio = -1.0 - adjustment; // 与显示逻辑完全一致

// 文本框拖拽坐标转换
void _onTempBubbleDragUpdate(Offset textBoxGlobalPosition) {
  final connectionLineLength = 70.0 * inverseScale;
  final dotGlobalPosition = Offset(
    textBoxGlobalPosition.dx,
    textBoxGlobalPosition.dy + connectionLineLength,
  );
  final imagePosition = _convertGlobalToImagePosition(dotGlobalPosition);
  _tapPosition = Vector3(imagePosition.dx, imagePosition.dy, 0);
}
```

### 测试验证
- 支持多种图片比例（1:1, 16:9, 4:3等）
- 横屏/竖屏切换测试通过
- 缩放状态下拖拽精度验证

---

## ⏰ TimeBox 计时功能

### 功能概述
番茄钟计时系统，支持工作/休息周期管理，浮动计时器，休息时间学习功能。

### 核心功能模块

#### 1. 浮动计时器
- **尺寸**：100x40px 紧凑设计
- **透明度**：60-70% 半透明效果
- **系统级浮动**：支持跨应用显示
- **状态同步**：与主界面实时同步

#### 2. 休息时间学习
- **词汇学习**：从个人词汇库随机选择单词
- **动觉记忆训练**：结合PAO记忆法的动作训练
- **提前结束**：支持用户主动跳过休息时间

#### 3. 任务管理
- **左滑手势**：WeChat风格的编辑/删除操作
- **任务分类**：用户自定义分类系统
- **进度追踪**：任务完成状态和时间统计

### 技术实现
```dart
// 浮动计时器组件
Container(
  width: 100,
  height: 40,
  decoration: BoxDecoration(
    color: Colors.black.withOpacity(0.7),
    borderRadius: BorderRadius.circular(20),
  ),
  child: Center(
    child: Text(
      _formatTime(_remainingTime),
      style: TextStyle(color: Colors.white, fontSize: 14),
    ),
  ),
)
```

---

## 📊 图表可视化系统

### 功能概述
学习数据的可视化展示，包括雷达图、ROI图表等多种图表类型。

### 核心图表组件

#### 1. 能力雷达图
- **五边形设计**：覆盖5个学习维度
- **网格系统**：Mondrian风格的几何网格
- **数据映射**：学习数据到视觉元素的映射

#### 2. ROI学习效率图
- **Y轴修复**：解决数据显示范围问题
- **动态缩放**：根据数据范围自动调整
- **趋势分析**：学习效率变化趋势可视化

#### 3. 日历热力图
- **时间块显示**：基于任务时长的动态高度
- **颜色编码**：四色分类系统
- **完成状态**：透明度表示任务完成情况

### 设计规范
- **配色方案**：OneDay原始配色（红#E03E3E, 黄#FFD700, 绿#0F7B6C, 蓝#2E7EED）
- **网格线条**：2-3px黑色线条，Mondrian几何风格
- **响应式设计**：支持不同屏幕尺寸自适应

---

## 🏆 成就系统

### 功能概述
用户学习成就的解锁和展示系统，提供学习动机和成就感。

### 核心功能
- **成就解锁**：基于学习数据的自动解锁机制
- **进度追踪**：成就完成进度的可视化
- **分类管理**：按学习类型分类的成就体系

### 导航集成
- 解决成就页面的导航问题
- 与底部导航栏的集成
- 页面状态管理优化

### 工资系统集成
已完成成就系统与工资系统的深度集成，实现自动化奖励发放：

#### 核心服务架构
- **WageService**：统一的工资系统服务，支持余额管理、交易记录、奖励发放
- **WageTransaction**：完整的交易模型，包含学习收入、奖励、成就奖励等类型
- **自动化集成**：成就解锁时自动触发工资奖励发放

#### 工作流程
```dart
// 成就解锁自动触发工资奖励
1. AchievementService.unlockAchievement()
2. _handleAchievementUnlock() 处理奖励
3. WageService.addWageReward() 发放工资
4. 更新用户余额和交易记录
```

#### 技术实现
```dart
// 替换原有TODO，实现真正的工资奖励发放
final success = await _wageService.addWageReward(
  achievement.wageReward,
  description: '成就奖励: ${achievement.name}',
);
```

#### 数据持久化
- 使用SharedPreferences进行本地存储
- 支持余额查询和交易历史记录
- 完整的错误处理和调试日志

---

## 📁 自定义分类管理系统

### 功能概述
完整的自定义分类管理功能，支持编辑、删除、共享等操作，与系统默认分类保持功能一致性。

### 核心功能模块

#### 1. 编辑功能
- **完整表单验证**：名称必填、重名检查（排除当前分类）
- **图标选择器**：32个emoji图标可选，与创建对话框保持一致
- **描述编辑**：支持多行文本输入，可选字段
- **实时预览**：选中图标立即显示视觉反馈
- **错误处理**：完整的异常捕获和用户友好提示

#### 2. 智能删除功能
- **级联删除**：递归删除所有子分类
- **智能警告**：删除前检测子分类数量并警告用户
- **引用清理**：自动从父分类中移除引用关系
- **数据一致性**：确保删除后数据结构完整

```dart
// 递归删除实现
Future<void> _deleteCustomCategoryRecursive(CustomExerciseCategory category) async {
  // 先删除所有子分类
  final children = widget.customCategoryManager.getChildCategories(category.id);
  for (final child in children) {
    await _deleteCustomCategoryRecursive(child);
  }
  // 删除当前分类
  await widget.customCategoryManager.deleteCategory(category.id);
}
```

#### 3. 社区共享功能
- **共享确认**：与系统分类相同的确认对话框
- **加载状态**：显示共享进度和结果反馈
- **内容格式化**：自动生成包含分类信息的社区帖子
- **错误处理**：网络异常和数据异常的完整处理

### UI设计复用
- **对话框样式**：复用系统分类的AlertDialog设计（白色背景、12px圆角）
- **图标选择器**：完全复用32个emoji图标的网格布局
- **菜单布局**：三点菜单的图标、文字、颜色完全一致
- **交互流程**：确认、加载、反馈的流程与系统分类保持统一

### 功能完整性对比
| 功能特性 | 系统默认分类 | 自定义分类 | 一致性 |
|----------|-------------|-----------|--------|
| 编辑分类 | ✅ 完整支持 | ✅ 完整支持 | 100% |
| 删除分类 | ✅ 级联删除 | ✅ 级联删除 | 100% |
| 共享到社区 | ✅ 完整流程 | ✅ 完整流程 | 100% |
| 添加子分类 | ✅ 支持 | ✅ 支持 | 100% |
| UI设计 | Notion风格 | Notion风格 | 100% |

---

## 🏰 记忆宫殿功能

### 功能概述
基于空间记忆的学习方法，支持图片标注、知识点管理。

### 核心功能
- **图片压缩**：智能压缩算法，保持文字清晰度
- **知识点标注**：精确的点击定位和气泡显示
- **拖拽编辑**：支持知识点位置的拖拽调整
- **知识点删除**：支持知识点的删除操作，包含确认对话框和成功反馈
- **分类管理**：记忆宫殿的层级分类系统

### 坐标系统
- **标准化坐标**：解决不同图片尺寸的兼容性
- **压缩图片适配**：用户导入图片的坐标转换
- **横屏支持**：横屏模式下的坐标系统适配

---

## 🤝 动作库分享系统

### 功能概述
完整的动作库分类共享功能，支持自定义分类和系统默认分类的社区分享。

### 核心功能实现

#### 1. 自定义分类共享
- **内容生成**：自动格式化分类信息（名称、描述、动作数量、动作列表）
- **智能截断**：动作列表超过5个时显示省略
- **社区集成**：创建PostType.experience类型的社区帖子

#### 2. 系统分类共享
- **数据获取**：从PAOExercisesData获取系统分类的动作数据
- **统一格式**：与自定义分类保持一致的帖子内容格式
- **完整覆盖**：支持所有7个系统分类（健身、瑜伽、养生、篮球、足球、拉伸、护眼）

```dart
// 系统分类动作数据获取
Map<String, PAOExercise> _getSystemCategoryExercises(String categoryName) {
  final exercises = PAOExercisesData.getExercisesByCategory(categoryName);
  return exercises ?? {};
}
```

#### 3. 技术实现亮点
- **数据结构适配**：正确处理CustomExerciseCategory和ActionLibraryCategoryNode的差异
- **Context安全**：修复BuildContext跨async间隙使用问题
- **空值处理**：安全处理null描述和空动作列表
- **异常处理**：完整的错误捕获和用户友好提示

### 共享内容格式
```
🏃‍♂️ 动作库分类分享
📂 分类名称：[分类名]
📝 分类描述：[描述或"暂无描述"]
🎯 动作数量：[数量]个
💪 包含动作：[动作1, 动作2, ...（最多5个）]
🔗 来源：OneDay动作库
```

### 用户体验优化
- **加载状态**：共享过程中显示进度提示
- **成功反馈**：操作完成后显示成功消息
- **错误提示**：网络或数据异常时显示友好错误信息
- **一致性体验**：自定义和系统分类的共享流程完全一致

---

## 📈 学习统计功能

### 功能概述
学习数据的统计分析和效率评估系统。

### 核心指标
- **学习ROI**：学习效果得分 ÷ 时间投入
- **并行时间统计**：多任务活动节省的时间
- **专注信噪比**：专注时间 ÷ 无关时间

### 数据同步
- **实时更新**：TimeBox任务完成时触发数据更新
- **学习会话完成**：原子性数据同步，包含学习时长、成就解锁、连续天数统计
- **状态管理**：使用Riverpod进行状态管理
- **持久化存储**：本地数据存储和恢复

### 会话完成处理
- **StudySessionCompletionService**：统一处理学习会话完成后的所有数据更新
- **原子性操作**：确保数据一致性，避免部分更新失败
- **成就触发**：自动检测并解锁相关学习成就

### 学习报告导出功能
完整的学习报告导出系统，支持多种格式的数据导出和分享：

#### 核心导出功能
- **PDF导出**：将学习报告生成为PDF文档，支持中文字体渲染
- **图片导出**：截图功能，将报告界面导出为图片格式
- **文本分享**：生成格式化的学习报告摘要，支持社交分享

#### 技术实现亮点
```dart
// PDF生成服务
class LearningReportExportService {
  // 支持中文字体的PDF生成
  Future<void> exportToPDF(LearningReportData data) async {
    final font = await PdfGoogleFonts.notoSansSC();
    // PDF内容构建和导出
  }
}
```

#### 兼容性修复
- **中文字体支持**：添加Noto Sans SC字体，解决中文显示问题
- **PDF导出稳定性**：双重错误处理机制，确保导出功能可用
- **系统分享集成**：使用系统分享功能处理PDF文件
- **错误降级方案**：字体加载失败时的备用处理机制

#### 用户体验优化
- **进度提示**：导出过程中显示加载状态
- **成功反馈**：操作完成后的确认提示
- **错误处理**：友好的错误信息和解决建议
- **多格式支持**：满足不同场景的导出需求

---

## 🎯 用户反馈优化系统

### 功能概述
智能化的用户反馈机制，基于操作可见性原则优化提示策略，提升用户体验流畅性。

### 优化原则

#### 1. 移除冗余反馈
对于用户可以直观看到操作结果的功能，移除额外的SnackBar提示：
- **编辑分类**：用户可以直接看到分类信息已更新
- **添加分类/子分类**：新分类会立即出现在列表中
- **删除分类**：分类会立即从列表中消失

#### 2. 保留必要反馈
对于用户无法直观看到操作结果的功能，保留SnackBar提示：
- **共享到社区**：网络操作，用户无法直观看到结果
- **错误提示**：所有操作失败时的错误信息
- **加载状态**：操作过程中的加载动画

### 实施策略

#### 智能反馈判断
```dart
// 基于操作类型的智能反馈策略
if (operationType == OperationType.share || operationType == OperationType.error) {
  // 显示SnackBar反馈
  showFeedback();
} else {
  // 静默完成，依赖视觉变化反馈
  completeOperation();
}
```

#### 用户体验提升
- **减少干扰**：移除不必要的弹窗和提示
- **保持一致性**：统一的反馈策略和视觉设计
- **智能判断**：根据操作特性自动选择反馈方式
- **流畅交互**：减少用户等待和确认步骤

### 优化效果
- **操作流畅性提升**：减少不必要的中断和等待
- **认知负担降低**：减少冗余信息的干扰
- **交互效率提高**：更直观的操作反馈机制
- **用户满意度提升**：更符合用户期望的交互体验

---

## 👤 个人资料编辑系统

### 功能概述
完整的个人资料编辑功能，支持头像、昵称和个人简介的自定义，遵循Notion风格设计。

### 核心功能模块

#### 1. 头像管理
- **头像选择**：支持从相机拍摄或相册选择图片
- **图片裁剪**：自动裁剪为1:1比例的正方形头像
- **格式支持**：JPG、JPEG、PNG、WebP格式
- **文件大小限制**：最大5MB
- **本地存储**：头像文件保存在应用文档目录

#### 2. 基本信息编辑
- **昵称编辑**：1-20个字符，实时验证，自动保存
- **个人简介**：最多100个字符，多行输入，可选字段
- **默认值**：新用户默认昵称"学习者"，简介"让每一天都充满收获"

#### 3. 数据架构
```dart
class UserProfile {
  final String userId;        // 用户ID
  final String nickname;      // 昵称
  final String? bio;          // 个人简介（可选）
  final String? avatarPath;   // 头像本地路径（可选）
  final DateTime createdAt;   // 创建时间
  final DateTime updatedAt;   // 更新时间
}
```

### 技术实现

#### 服务层设计
- **UserProfileService**：负责数据持久化和业务逻辑
- **数据存储**：使用SharedPreferences存储用户资料JSON
- **头像管理**：使用path_provider管理头像文件
- **数据验证**：验证输入格式和长度

#### 状态管理
```dart
// 使用Riverpod进行状态管理
final userProfileProvider = StateNotifierProvider<UserProfileNotifier, UserProfileState>
final currentUserProfileProvider = Provider<UserProfile?>
final userProfileLoadingProvider = Provider<bool>
```

#### 用户交互流程
1. **编辑头像**：点击头像 → 选择来源 → 自动裁剪 → 保存更新
2. **编辑昵称**：点击输入框 → 输入内容 → 失去焦点自动保存
3. **编辑简介**：点击输入框 → 输入内容 → 失去焦点自动保存

### 用户体验优化
- **自动保存**：失去焦点时自动保存更改
- **实时验证**：输入时即时验证格式和长度
- **加载状态**：显示保存进度和状态反馈
- **错误处理**：完善的异常处理和用户提示

---

## 🔧 技术栈总结

### 核心技术
- **Flutter 3.x**：跨平台UI框架
- **Riverpod**：状态管理
- **SharedPreferences**：本地数据存储
- **Transform & Matrix4**：坐标变换
- **CustomPainter**：自定义图表绘制

### 设计模式
- **MVVM架构**：视图与逻辑分离
- **Provider模式**：状态管理
- **组件化设计**：可复用UI组件
- **响应式编程**：数据流管理

---

**文档维护**: 本文档记录核心功能的技术实现，新功能开发时应及时更新相应章节。
