import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// 底部导航栏隐藏功能演示
/// 
/// 展示修复后的导航逻辑：
/// 1. 商城背包页面正确隐藏底部导航栏
/// 2. 社区编辑文章页面正确隐藏底部导航栏
/// 3. 工资钱包页面正确隐藏底部导航栏
class NavigationBottomBarDemo extends StatefulWidget {
  const NavigationBottomBarDemo({super.key});

  @override
  State<NavigationBottomBarDemo> createState() => _NavigationBottomBarDemoState();
}

class _NavigationBottomBarDemoState extends State<NavigationBottomBarDemo> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('底部导航栏隐藏功能演示'),
        backgroundColor: const Color(0xFF2E7EED),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 修复说明
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFF7F6F3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFFE3E2DE)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '🔧 修复内容',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '修复了以下页面的底部导航栏显示问题：\n'
                    '• 商城页面 → 背包页面\n'
                    '• 社区页面 → 编辑文章页面\n'
                    '• 工资钱包页面（独立页面）',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF787774),
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 技术方案
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.04),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '⚙️ 技术方案',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildTechnicalPoint(
                    '路由重构',
                    '将页面从 MaterialPageRoute 改为 GoRouter 独立路由',
                    '确保页面不在 ShellRoute 内部，从而隐藏底部导航栏',
                  ),
                  const SizedBox(height: 12),
                  _buildTechnicalPoint(
                    '导航优化',
                    '使用 context.push() 替代 Navigator.push()',
                    '利用 GoRouter 的路由管理能力，提供更好的导航体验',
                  ),
                  const SizedBox(height: 12),
                  _buildTechnicalPoint(
                    '参数传递',
                    '通过 extra 参数传递复杂数据',
                    '保持页面间数据传递的灵活性和类型安全',
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 测试按钮
            const Text(
              '🧪 功能测试',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 12),
            
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 1.5,
                children: [
                  _buildTestButton(
                    '商城 → 背包',
                    '测试商城页面背包功能',
                    Icons.inventory,
                    const Color(0xFF2E7EED),
                    () => _testStoreToInventory(context),
                  ),
                  _buildTestButton(
                    '社区 → 编辑',
                    '测试社区编辑文章功能',
                    Icons.edit,
                    const Color(0xFF0F7B6C),
                    () => _testCommunityToEditor(context),
                  ),
                  _buildTestButton(
                    '工资钱包',
                    '测试工资钱包页面',
                    Icons.account_balance_wallet,
                    const Color(0xFFE03E3E),
                    () => _testWageWallet(context),
                  ),
                  _buildTestButton(
                    '路由配置',
                    '查看路由配置信息',
                    Icons.route,
                    const Color(0xFF9B9A97),
                    () => _showRouteInfo(context),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTechnicalPoint(String title, String description, String detail) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          description,
          style: const TextStyle(
            fontSize: 13,
            color: Color(0xFF787774),
          ),
        ),
        const SizedBox(height: 2),
        Text(
          detail,
          style: const TextStyle(
            fontSize: 12,
            color: Color(0xFF9B9A97),
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  Widget _buildTestButton(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2DE)),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: 32,
                  color: color,
                ),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 11,
                    color: Color(0xFF9B9A97),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _testStoreToInventory(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🛍️ 商城背包测试'),
        content: const Text(
          '测试步骤：\n'
          '1. 导航到商城页面（底部导航栏显示）\n'
          '2. 点击右上角背包图标\n'
          '3. 进入背包页面（底部导航栏隐藏）\n'
          '4. 点击返回按钮回到商城页面\n\n'
          '✅ 修复后：使用 context.push(\'/inventory\') 导航\n'
          '❌ 修复前：使用 MaterialPageRoute 导航',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('了解'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.go('/store');
            },
            child: const Text('前往测试'),
          ),
        ],
      ),
    );
  }

  void _testCommunityToEditor(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('✍️ 社区编辑测试'),
        content: const Text(
          '测试步骤：\n'
          '1. 导航到社区页面（底部导航栏显示）\n'
          '2. 点击右下角编辑按钮\n'
          '3. 进入编辑文章页面（底部导航栏隐藏）\n'
          '4. 点击关闭按钮回到社区页面\n\n'
          '✅ 修复后：使用 context.push(\'/community-post-editor\') 导航\n'
          '❌ 修复前：使用 MaterialPageRoute 导航',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('了解'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.go('/community');
            },
            child: const Text('前往测试'),
          ),
        ],
      ),
    );
  }

  void _testWageWallet(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('💰 工资钱包测试'),
        content: const Text(
          '测试步骤：\n'
          '1. 从任意主页面导航到工资钱包\n'
          '2. 工资钱包页面应该隐藏底部导航栏\n'
          '3. 点击返回按钮回到上一页面\n\n'
          '✅ 修复后：工资钱包作为独立路由\n'
          '❌ 修复前：可能在 ShellRoute 内显示',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('了解'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.push('/wage-wallet');
            },
            child: const Text('前往测试'),
          ),
        ],
      ),
    );
  }

  void _showRouteInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🗺️ 路由配置信息'),
        content: const SingleChildScrollView(
          child: Text(
            '路由分类：\n\n'
            '📱 ShellRoute（显示底部导航栏）：\n'
            '• /home - 首页\n'
            '• /calendar - 日历\n'
            '• /store - 商城\n'
            '• /community - 社区\n'
            '• /profile - 个人中心\n\n'
            '🔗 独立路由（隐藏底部导航栏）：\n'
            '• /inventory - 背包页面\n'
            '• /community-post-editor - 编辑文章\n'
            '• /wage-wallet - 工资钱包\n'
            '• /settings - 设置\n'
            '• /exercise - 具身记忆\n'
            '• /timebox - 时间盒子\n'
            '• /memory-palace - 知忆相册\n\n'
            '🔧 修复方案：\n'
            '将问题页面从 MaterialPageRoute 改为 GoRouter 独立路由，确保正确的导航层级。',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
