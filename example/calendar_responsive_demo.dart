import 'package:flutter/material.dart';

/// 优化日志日历响应式布局演示
/// 
/// 展示如何实现响应式日历视图，适配不同屏幕尺寸
class CalendarResponsiveDemo extends StatefulWidget {
  const CalendarResponsiveDemo({super.key});

  @override
  State<CalendarResponsiveDemo> createState() => _CalendarResponsiveDemoState();
}

class _CalendarResponsiveDemoState extends State<CalendarResponsiveDemo> {
  DateTime _selectedDate = DateTime.now();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('日历响应式布局演示'),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF37352F),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 屏幕信息显示
            _buildScreenInfo(),
            
            const SizedBox(height: 20),
            
            // 响应式日历演示
            _buildResponsiveCalendar(),
            
            const SizedBox(height: 20),
            
            // 参数说明
            _buildParameterInfo(),
          ],
        ),
      ),
    );
  }

  /// 构建屏幕信息显示
  Widget _buildScreenInfo() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF7F6F3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '当前屏幕信息',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 8),
          LayoutBuilder(
            builder: (context, constraints) {
              String deviceType = _getDeviceType(constraints.maxWidth);
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('宽度: ${constraints.maxWidth.toInt()}px'),
                  Text('设备类型: $deviceType'),
                  Text('布局模式: ${_getLayoutMode(constraints.maxWidth)}'),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  /// 构建响应式日历
  Widget _buildResponsiveCalendar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // 响应式布局参数
          double horizontalPadding = 16;
          double fontSize = 16;
          double iconSize = 24;
          double spacing = 12;
          
          // iPad 横屏适配
          if (constraints.maxWidth > 900) {
            horizontalPadding = 32;
            fontSize = 18;
            iconSize = 28;
            spacing = 16;
          } else if (constraints.maxWidth > 600) {
            horizontalPadding = 24;
            fontSize = 17;
            iconSize = 26;
            spacing = 14;
          } else if (constraints.maxWidth < 400) {
            horizontalPadding = 12;
            fontSize = 14;
            iconSize = 20;
            spacing = 8;
          }
          
          return Container(
            padding: EdgeInsets.symmetric(
              horizontal: horizontalPadding,
              vertical: 16,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 日历头部
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: Text(
                        '${_selectedDate.year}年${_selectedDate.month}月',
                        style: TextStyle(
                          fontSize: fontSize,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF37352F),
                        ),
                      ),
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _selectedDate = DateTime(
                                _selectedDate.year,
                                _selectedDate.month - 1,
                              );
                            });
                          },
                          icon: Icon(
                            Icons.chevron_left,
                            color: const Color(0xFF37352F),
                            size: iconSize,
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _selectedDate = DateTime(
                                _selectedDate.year,
                                _selectedDate.month + 1,
                              );
                            });
                          },
                          icon: Icon(
                            Icons.chevron_right,
                            color: const Color(0xFF37352F),
                            size: iconSize,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                
                SizedBox(height: spacing),
                
                // 响应式日历网格
                _buildCalendarGrid(constraints),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建日历网格
  Widget _buildCalendarGrid(BoxConstraints constraints) {
    // 响应式参数
    double aspectRatio = 1.0;
    double margin = 2;
    double borderRadius = 4;
    double fontSize = 12;
    
    // iPad 横屏适配
    if (constraints.maxWidth > 900) {
      aspectRatio = 1.2; // 增加高度，避免溢出
      margin = 3;
      borderRadius = 6;
      fontSize = 14;
    } else if (constraints.maxWidth > 600) {
      aspectRatio = 1.1;
      margin = 2.5;
      borderRadius = 5;
      fontSize = 13;
    } else if (constraints.maxWidth < 400) {
      aspectRatio = 0.9; // 窄屏时降低高度
      margin = 1.5;
      borderRadius = 3;
      fontSize = 10;
    }

    final now = DateTime.now();
    final firstDayOfMonth = DateTime(_selectedDate.year, _selectedDate.month, 1);
    final startDate = firstDayOfMonth.subtract(Duration(days: firstDayOfMonth.weekday - 1));
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: aspectRatio,
        crossAxisSpacing: margin,
        mainAxisSpacing: margin,
      ),
      itemCount: 42, // 6 weeks * 7 days
      itemBuilder: (context, index) {
        final date = startDate.add(Duration(days: index));
        final isCurrentMonth = date.month == _selectedDate.month;
        final isToday = date.year == now.year && 
                       date.month == now.month && 
                       date.day == now.day;
        final hasEntry = index % 7 == 0; // 模拟有日志的日期
        
        return GestureDetector(
          onTap: isCurrentMonth ? () {
            setState(() {
              _selectedDate = date;
            });
          } : null,
          child: Container(
            decoration: BoxDecoration(
              color: isToday 
                  ? const Color(0xFF2E7EED) 
                  : hasEntry 
                      ? const Color(0xFF2E7EED).withValues(alpha: 0.1)
                      : Colors.transparent,
              borderRadius: BorderRadius.circular(borderRadius),
            ),
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '${date.day}',
                    style: TextStyle(
                      fontSize: fontSize,
                      fontWeight: isToday ? FontWeight.w600 : FontWeight.w400,
                      color: isCurrentMonth
                          ? isToday 
                              ? Colors.white 
                              : const Color(0xFF37352F)
                          : const Color(0xFF9B9A97),
                    ),
                  ),
                  if (hasEntry && !isToday)
                    Container(
                      width: fontSize * 0.3,
                      height: fontSize * 0.3,
                      margin: EdgeInsets.only(top: fontSize * 0.15),
                      decoration: const BoxDecoration(
                        color: Color(0xFF2E7EED),
                        shape: BoxShape.circle,
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建参数说明
  Widget _buildParameterInfo() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF7F6F3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '响应式参数配置',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 12),
          _buildParameterRow('超宽屏 (>900px)', '32px边距, 18px字体, 1.2宽高比'),
          _buildParameterRow('中等屏幕 (600-900px)', '24px边距, 17px字体, 1.1宽高比'),
          _buildParameterRow('标准屏幕 (400-600px)', '16px边距, 16px字体, 1.0宽高比'),
          _buildParameterRow('窄屏 (<400px)', '12px边距, 14px字体, 0.9宽高比'),
        ],
      ),
    );
  }

  Widget _buildParameterRow(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 8,
            height: 8,
            margin: const EdgeInsets.only(top: 6, right: 8),
            decoration: const BoxDecoration(
              color: Color(0xFF2E7EED),
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF37352F),
                  ),
                ),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF9B9A97),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getDeviceType(double width) {
    if (width > 900) return 'iPad 横屏 / 大屏设备';
    if (width > 600) return 'iPad 竖屏 / 中等设备';
    if (width > 400) return 'iPhone 横屏 / 标准设备';
    return 'iPhone 竖屏 / 小屏设备';
  }

  String _getLayoutMode(double width) {
    if (width > 900) return '宽屏优化模式';
    if (width > 600) return '中屏适配模式';
    if (width > 400) return '标准布局模式';
    return '紧凑布局模式';
  }
}
