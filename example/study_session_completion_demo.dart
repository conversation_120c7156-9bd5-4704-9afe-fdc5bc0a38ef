import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/features/time_box/models/timebox_models.dart';
import 'package:oneday/services/study_session_completion_service.dart';
import 'package:oneday/services/providers/study_session_completion_provider.dart';
import 'package:oneday/features/time_box/widgets/study_session_completion_dialog.dart';

/// 学习会话完成数据同步演示页面
///
/// 用于测试和演示学习会话完成后的数据同步功能
class StudySessionCompletionDemo extends ConsumerStatefulWidget {
  const StudySessionCompletionDemo({super.key});

  @override
  ConsumerState<StudySessionCompletionDemo> createState() =>
      _StudySessionCompletionDemoState();
}

class _StudySessionCompletionDemoState
    extends ConsumerState<StudySessionCompletionDemo> {
  StudySessionCompletionResult? _lastResult;
  bool _isProcessing = false;

  @override
  Widget build(BuildContext context) {
    final todayStudySummaryAsync = ref.watch(todayStudySummaryProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('学习会话完成数据同步演示'),
        backgroundColor: const Color(0xFF2E7EED),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            _buildDemoButtons(),
            const SizedBox(height: 24),
            _buildTodaySummary(todayStudySummaryAsync),
            const SizedBox(height: 24),
            if (_lastResult != null) _buildLastResult(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.info_outline, color: Color(0xFF2E7EED)),
                SizedBox(width: 8),
                Text(
                  '数据同步演示',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '此页面用于演示学习会话完成后的数据同步功能。当时间盒子学习会话结束时，系统会自动更新以下数据：',
              style: TextStyle(fontSize: 14, color: Color(0xFF787774)),
            ),
            const SizedBox(height: 8),
            _buildFeatureList(),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureList() {
    final features = [
      '学习时间统计数据（今日、本周、连续天数）',
      '成就系统数据（经验值、等级、技能）',
      '每日计划完成状态',
      '数据持久化到本地存储',
      'UI界面实时更新',
    ];

    return Column(
      children: features.map((feature) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('• ', style: TextStyle(color: Color(0xFF2E7EED))),
              Expanded(
                child: Text(
                  feature,
                  style: const TextStyle(
                    fontSize: 13,
                    color: Color(0xFF787774),
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDemoButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '测试操作',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing
                        ? null
                        : () => _simulateShortSession(),
                    icon: const Icon(Icons.timer),
                    label: const Text('模拟短时学习\n(25分钟)'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0F7B6C),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isProcessing
                        ? null
                        : () => _simulateLongSession(),
                    icon: const Icon(Icons.schedule),
                    label: const Text('模拟长时学习\n(90分钟)'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2E7EED),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isProcessing ? null : () => _simulateErrorCase(),
                icon: const Icon(Icons.error_outline),
                label: const Text('模拟错误情况'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFE74C3C),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            if (_isProcessing)
              const Padding(
                padding: EdgeInsets.only(top: 16),
                child: Center(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      SizedBox(width: 8),
                      Text('正在处理数据同步...'),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTodaySummary(AsyncValue<Map<String, dynamic>> summaryAsync) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '今日学习统计',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 16),
            summaryAsync.when(
              data: (summary) => _buildSummaryGrid(summary),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Text(
                '加载失败: $error',
                style: const TextStyle(color: Color(0xFFE74C3C)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryGrid(Map<String, dynamic> summary) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 2.5,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      children: [
        _buildSummaryCard(
          '总学习时长',
          '${summary['totalStudyMinutes']}分钟',
          Icons.schedule,
          const Color(0xFF2E7EED),
        ),
        _buildSummaryCard(
          '完成任务',
          '${summary['completedTasks']}个',
          Icons.task_alt,
          const Color(0xFF0F7B6C),
        ),
        _buildSummaryCard(
          '连续天数',
          '${summary['studyStreak']}天',
          Icons.local_fire_department,
          const Color(0xFFE67E22),
        ),
        _buildSummaryCard(
          '专注度',
          summary['focusLevel'],
          Icons.psychology,
          const Color(0xFF9B59B6),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF787774),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLastResult() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _lastResult!.isSuccess ? Icons.check_circle : Icons.error,
                  color: _lastResult!.isSuccess
                      ? const Color(0xFF0F7B6C)
                      : const Color(0xFFE74C3C),
                ),
                const SizedBox(width: 8),
                Text(
                  '最后一次处理结果',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: _lastResult!.isSuccess
                        ? const Color(0xFF0F7B6C)
                        : const Color(0xFFE74C3C),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _lastResult!.message,
              style: const TextStyle(fontSize: 14, color: Color(0xFF37352F)),
            ),
            const SizedBox(height: 8),
            Text(
              _lastResult!.updateSummary,
              style: const TextStyle(fontSize: 13, color: Color(0xFF787774)),
            ),
            if (_lastResult!.hasErrors) ...[
              const SizedBox(height: 8),
              ...(_lastResult!.allErrors.map(
                (error) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Text(
                    '• $error',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFFE74C3C),
                    ),
                  ),
                ),
              )),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _simulateShortSession() async {
    await _processSession(25, '专注学习 - 算法练习');
  }

  Future<void> _simulateLongSession() async {
    await _processSession(90, '深度学习 - 机器学习项目');
  }

  Future<void> _simulateErrorCase() async {
    // 这里可以模拟各种错误情况
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('错误模拟功能待实现'),
        backgroundColor: Color(0xFFE67E22),
      ),
    );
  }

  Future<void> _processSession(int minutes, String title) async {
    setState(() {
      _isProcessing = true;
    });

    try {
      final completedTask = TimeBoxTask(
        id: 'demo-${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        description: '这是一个演示任务，用于测试数据同步功能',
        plannedMinutes: minutes,
        status: TaskStatus.completed,
        priority: TaskPriority.medium,
        category: '演示',
        createdAt: DateTime.now().subtract(Duration(minutes: minutes + 5)),
        startTime: DateTime.now().subtract(Duration(minutes: minutes)),
        endTime: DateTime.now(),
      );

      final service = ref.read(studySessionCompletionServiceProvider);
      final result = await service.handleSessionCompletion(completedTask);

      setState(() {
        _lastResult = result;
      });

      // 刷新今日统计
      ref.invalidate(todayStudySummaryProvider);

      // 显示完成对话框
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => StudySessionCompletionDialog(
            completedTask: completedTask,
            completionResult: result,
          ),
        );
      }

      // 显示成功提示
      if (mounted && result.isSuccess) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✅ 学习会话完成处理成功！学习时长：$minutes分钟'),
            backgroundColor: const Color(0xFF0F7B6C),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ 处理失败：$e'),
            backgroundColor: const Color(0xFFE74C3C),
          ),
        );
      }
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }
}
