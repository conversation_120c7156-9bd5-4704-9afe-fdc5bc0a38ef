# OneDay Flutter 应用 - 架构设计文档

本文档描述了 OneDay Flutter 应用的高层架构、设计原则和核心模块，帮助新成员快速理解项目的技术全貌。

---

## 目录
1. [项目概述](#1-项目概述)
2. [技术栈](#2-技术栈)
3. [架构设计](#3-架构设计)
4. [目录结构](#4-目录结构)
5. [核心模块](#5-核心模块)
6. [设计原则](#6-设计原则)
7. [状态管理](#7-状态管理)
8. [路由系统](#8-路由系统)
9. [数据层设计](#9-数据层设计)
10. [UI设计系统](#10-ui设计系统)

---

## 1. 项目概述

### 🎯 产品定位
OneDay 是一款融合游戏化激励、记忆科学与健康管理的跨平台学习应用，通过"时间盒子 + 记忆宫殿 + 虚拟工资"三大核心机制，帮助用户高效规划、执行并复盘每日学习与健康活动。

### 🎨 设计理念
- **Seize the Day（活在当下）**: 专注当下每一个学习时刻
- **One Day I Will...（终有一天我会...）**: 激发长远学习目标
- **极简主义**: 遵循Notion风格的"Less is More"设计哲学

### 👥 目标用户
- **主要用户**: 备考人群（考研、考公、四六级等）的大学生 (18-25岁)
- **次要用户**: 职场学习者、终身学习爱好者 (25-35岁)

---

## 2. 技术栈

### 🛠️ 核心技术
```yaml
框架: Flutter 3.x
语言: Dart 3.x
状态管理: Riverpod 2.x + StateNotifier
路由管理: GoRouter
本地存储: SharedPreferences + Isar NoSQL
图片处理: image_picker + cached_network_image
分享功能: share_plus
国际化: Flutter Intl (中英文切换)
```

### 📱 平台支持
- **移动端**: iOS 12+, Android API 21+
- **桌面端**: Windows, macOS, Linux
- **Web端**: 现代浏览器支持

---

## 3. 架构设计

### 🏗️ 整体架构
采用**面向功能 (Feature-Oriented)** 的分层架构，确保高内聚、低耦合：

```mermaid
graph TD
    subgraph "🚀 Presentation Layer (UI)"
        A[features/../*_page.dart] -- "Manages UI & State" --> B(shared/widgets)
        A -- "Consumes State" --> C{Riverpod Providers}
        A -- "Navigates" --> D[router/app_router.dart]
    end

    subgraph "💼 Domain Layer (Business Logic)"
        C -- "Exposes Logic" --> E[features/../notifiers]
        E -- "Uses" --> F[features/../models]
    end

    subgraph "💾 Data Layer"
        G[Repository] -- "Abstracts Data Source" --> H[API Service]
        G -- "Abstracts Data Source" --> I[Local Storage]
        E -- "Depends on" --> G
    end
```

### 🔄 数据流向
1. **UI层**: 用户交互触发事件
2. **业务逻辑层**: Notifier处理业务逻辑
3. **数据层**: Repository抽象数据源
4. **状态更新**: Provider通知UI更新

---

## 4. 目录结构

### 📁 项目结构
```
lib/
├── core/                    # 核心常量、工具类
│   ├── constants/          # 应用常量
│   ├── utils/              # 工具函数
│   └── data/               # 核心数据模型
├── features/               # 功能模块（按业务划分）
│   ├── auth/               # 认证模块
│   ├── home/               # 首页模块
│   ├── memory_palace/      # 记忆宫殿模块
│   ├── time_box/           # 时间盒子模块
│   ├── exercise/           # PAO动作库模块
│   ├── wage_system/        # 工资系统模块
│   ├── calendar/           # 日历模块
│   ├── reflection/         # 反思日志模块
│   ├── community/          # 社区模块
│   ├── settings/           # 设置模块
│   └── profile/            # 个人中心模块
├── router/                 # 路由配置
│   └── app_router.dart
├── shared/                 # 共享组件、模型
│   ├── widgets/            # 通用UI组件
│   ├── models/             # 数据模型
│   └── providers/          # 全局状态管理
└── main.dart               # 应用入口
```

### 📦 功能模块结构
每个功能模块遵循统一的结构：
```
features/[module_name]/
├── [module_name]_page.dart      # 页面UI
├── [module_name]_notifier.dart  # 状态管理
├── models/                      # 模块数据模型
├── widgets/                     # 模块专用组件
└── providers/                   # 模块状态提供者
```

---

## 5. 核心模块

### 🏠 主要功能模块

#### 5.1 记忆宫殿系统 (Memory Palace)
- **功能**: 地点桩管理、锚点标注、场景切换
- **技术**: InteractiveViewer、自定义手势检测、图片处理
- **特色**: 支持图片缩放、平移、标记点精确定位

#### 5.2 时间盒子系统 (Time Box)
- **功能**: 专注计时、任务管理、进度追踪
- **技术**: Timer、CircularProgressIndicator、本地通知
- **特色**: 番茄钟模式、防打扰功能、工资计算

#### 5.3 PAO动作库 (Exercise Library)
- **功能**: 162个动作数据、PAO记忆法、运动推荐
- **技术**: 自适应网格布局、实时搜索、分类筛选
- **特色**: 考研人群定制、身心结合学习

#### 5.4 工资系统 (Wage System)
- **功能**: 虚拟货币、道具商城、奖励机制
- **技术**: 本地状态管理、交易记录、背包系统
- **特色**: 游戏化激励、学习动力提升

#### 5.5 日历视图 (Calendar)
- **功能**: 周视图时间表、学科分类、数据导出
- **技术**: 自定义日历组件、CSV导出、时间块管理
- **特色**: 考研科目分类、工资统计

---

## 6. 设计原则

### 🎨 UI/UX设计原则

#### 6.1 Notion风格极简主义
```dart
// 主色调配置
const Color primaryBlue = Color(0xFF2E7EED);    // 柔和蓝
const Color darkText = Color(0xFF37352F);       // 深色文字
const Color lightGray = Color(0xFFF7F6F3);      // 浅灰背景
const Color borderGray = Color(0xFFE3E2E0);     // 边框灰
```

#### 6.2 白纸黑字原则
- 白色背景 + 深色文字确保最佳可读性
- 微妙边框和阴影提供层次感
- 避免过度装饰，专注内容本身

#### 6.3 响应式设计
```dart
// 自适应布局示例
LayoutBuilder(
  builder: (context, constraints) {
    final width = constraints.maxWidth;
    final columns = width < 600 ? 2 : width < 900 ? 3 : 4;
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        childAspectRatio: _calculateAspectRatio(width, columns),
      ),
      // ...
    );
  },
)
```

### 🔧 代码设计原则

#### 6.1 单一职责原则
每个类、方法只负责一个明确的功能

#### 6.2 依赖注入
使用Riverpod进行依赖管理，便于测试和维护

#### 6.3 错误处理
```dart
// 统一错误处理模式
AsyncValue<T> when<T>({
  required Widget Function(T data) data,
  required Widget Function(Object error, StackTrace stackTrace) error,
  required Widget Function() loading,
})
```

---

## 7. 状态管理

### 🔄 Riverpod架构

#### 7.1 Provider类型
```dart
// StateProvider - 简单状态
final counterProvider = StateProvider<int>((ref) => 0);

// StateNotifierProvider - 复杂状态
final timeBoxProvider = StateNotifierProvider<TimeBoxNotifier, TimeBoxState>(
  (ref) => TimeBoxNotifier(),
);

// FutureProvider - 异步数据
final userProvider = FutureProvider<User>((ref) async {
  return await UserRepository().getCurrentUser();
});
```

#### 7.2 状态管理模式
```dart
class TimeBoxNotifier extends StateNotifier<TimeBoxState> {
  TimeBoxNotifier() : super(TimeBoxState.initial());

  Future<void> createTimeBox(TimeBox timeBox) async {
    state = state.copyWith(isLoading: true);
    try {
      await _repository.createTimeBox(timeBox);
      state = state.copyWith(
        isLoading: false,
        timeBoxes: [...state.timeBoxes, timeBox],
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }
}
```

#### 7.3 状态监听
```dart
class HomePage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final timeBoxState = ref.watch(timeBoxProvider);
    
    return timeBoxState.when(
      data: (data) => _buildContent(data),
      loading: () => const CircularProgressIndicator(),
      error: (error, stack) => ErrorWidget(error),
    );
  }
}
```

---

## 8. 路由系统

### 🛣️ GoRouter配置

#### 8.1 路由结构
```dart
static final GoRouter router = GoRouter(
  initialLocation: '/splash',
  routes: [
    // 启动流程
    GoRoute(path: '/splash', builder: (context, state) => SplashPage()),
    GoRoute(path: '/onboarding', builder: (context, state) => OnboardingPage()),
    GoRoute(path: '/login', builder: (context, state) => LoginPage()),
    
    // 主应用 - Shell路由
    ShellRoute(
      builder: (context, state, child) => MainContainerPage(child: child),
      routes: [
        GoRoute(path: '/home', pageBuilder: (context, state) => 
          NoTransitionPage(child: HomePage())),
        GoRoute(path: '/calendar', pageBuilder: (context, state) => 
          NoTransitionPage(child: CalendarPage())),
        // ... 其他主页面
      ],
    ),
    
    // 独立页面
    GoRoute(path: '/settings', builder: (context, state) => SettingsPage()),
    GoRoute(path: '/exercise', builder: (context, state) => ExerciseLibraryPage()),
  ],
);
```

#### 8.2 导航模式
- **Shell路由**: 主应用页面共享底部导航栏
- **独立路由**: 设置、详情等页面独立导航
- **无过渡动画**: 底部导航切换使用NoTransitionPage

#### 8.3 路由守卫
```dart
// 认证检查
redirect: (context, state) {
  final isLoggedIn = ref.read(authProvider).isLoggedIn;
  if (!isLoggedIn && state.location != '/login') {
    return '/login';
  }
  return null;
}
```

---

## 9. 数据层设计

### 💾 数据架构

#### 9.1 数据模型
```dart
@freezed
class TimeBox with _$TimeBox {
  const factory TimeBox({
    required String id,
    required String title,
    String? description,
    required int plannedMinutes,
    DateTime? startTime,
    DateTime? endTime,
    required double earnedWage,
    required TaskStatus status,
    String? categoryId,
  }) = _TimeBox;

  factory TimeBox.fromJson(Map<String, dynamic> json) => 
    _$TimeBoxFromJson(json);
}
```

#### 9.2 Repository模式
```dart
abstract class TimeBoxRepository {
  Future<List<TimeBox>> getTimeBoxes();
  Future<TimeBox> createTimeBox(TimeBox timeBox);
  Future<void> updateTimeBox(TimeBox timeBox);
  Future<void> deleteTimeBox(String id);
}

class LocalTimeBoxRepository implements TimeBoxRepository {
  final SharedPreferences _prefs;
  
  @override
  Future<List<TimeBox>> getTimeBoxes() async {
    final jsonList = _prefs.getStringList('timeboxes') ?? [];
    return jsonList.map((json) => TimeBox.fromJson(jsonDecode(json))).toList();
  }
  
  // ... 其他实现
}
```

#### 9.3 数据持久化
- **本地存储**: SharedPreferences存储用户偏好和简单数据
- **复杂数据**: 预留Isar NoSQL数据库接口
- **缓存策略**: 内存缓存 + 本地持久化

---

## 10. UI设计系统

### 🎨 组件库

#### 10.1 基础组件
```dart
// 统一卡片组件
class NotionCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Color(0xFFE3E2E0)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }
}
```

#### 10.2 主题配置
```dart
ThemeData(
  useMaterial3: true,
  brightness: Brightness.light,
  primaryColor: Color(0xFF2E7EED),
  scaffoldBackgroundColor: Colors.white,
  
  // AppBar主题
  appBarTheme: AppBarTheme(
    backgroundColor: Colors.white,
    foregroundColor: Color(0xFF37352F),
    elevation: 0,
    centerTitle: true,
  ),
  
  // 卡片主题
  cardTheme: CardThemeData(
    color: Color(0xFFFAFAFA),
    elevation: 0,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(8),
      side: BorderSide(color: Color(0xFF37352F).withOpacity(0.08)),
    ),
  ),
)
```

#### 10.3 响应式设计
- **断点定义**: 600px (平板), 900px (桌面)
- **自适应网格**: 根据屏幕宽度动态调整列数
- **字体缩放**: 支持系统字体大小设置

---

**文档状态**: ✅ 已完成  
**更新日期**: 2025年1月  
**适用版本**: Flutter 3.x + Dart 3.x  
**维护团队**: OneDay开发团队
