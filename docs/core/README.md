# 核心文档

本目录包含 OneDay 项目的核心架构、设计理念和产品规划文档。

## 📋 文档列表

### 🏗️ [项目架构设计](ARCHITECTURE.md)
详细描述了 OneDay Flutter 应用的高层架构、设计原则和核心模块，包括：
- 技术栈选择
- 架构设计模式
- 目录结构规划
- 核心模块介绍
- 状态管理方案
- 路由系统设计

### 📄 [产品需求文档](PRD.md)
产品的核心需求、功能规划和业务逻辑说明，包括：
- 产品定位和目标用户
- 核心功能需求
- 用户体验设计
- 技术实现要求

### ⭐ [功能特性说明](FEATURES.md)
详细介绍应用的各项功能特性，包括：
- 时间盒子系统
- 记忆宫殿功能
- PAO 动作库
- 工资系统
- 社区功能
- 其他辅助功能

## 🔗 相关链接

- [开发文档](../development/) - 技术实现和功能开发
- [问题解决](../troubleshooting/) - 常见问题和解决方案
- [测试文档](../testing/) - 测试策略和指南
- [用户指南](../guides/) - 使用说明和操作指南

---

**维护说明**: 本目录下的文档是项目的核心文档，任何重大架构变更或产品规划调整都应该及时更新相关文档。
