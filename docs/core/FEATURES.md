# OneDay Flutter 应用 - 功能开发文档

本文档记录了 OneDay Flutter 应用中实现的核心功能、技术方案和开发过程，旨在为团队提供功能实现的参考和指导。

---

## 目录
1. [照片查看器垂直手势功能](#1-照片查看器垂直手势功能)
2. [开发者快速入口](#2-开发者快速入口)
3. [拖拽排序功能优化](#3-拖拽排序功能优化)
4. [浮动按钮自动隐藏](#4-浮动按钮自动隐藏)
5. [分类树拖拽功能](#5-分类树拖拽功能)
6. [Cover模式手势控制](#6-cover模式手势控制)
7. [位置跟随系统](#7-位置跟随系统)
8. [图标设置系统](#8-图标设置系统)
9. [分类树子分类添加功能](#9-分类树子分类添加功能)
10. [上下文感知相册创建](#10-上下文感知相册创建)
11. [三点菜单功能](#11-三点菜单功能)
12. [词汇管理系统](#12-词汇管理系统)

---

## 1. 照片查看器垂直手势功能

### 🎯 功能概述
在Flutter照片查看器中实现上滑分享和下滑预览的手势交互功能，确保与现有的左右滑动切换图片功能不产生冲突。

### 🔧 技术实现

#### 手势检测器集成
在 `scene_detail_page.dart` 中为现有的 `GestureDetector` 添加垂直手势检测：

```dart
GestureDetector(
  // 现有手势
  onTap: () => _handleTap(index),
  onLongPress: () => _enterEditMode(),
  onDoubleTap: () => _onDoubleTap(index),
  
  // 新增垂直手势检测
  onPanStart: (details) => _onVerticalPanStart(details, index),
  onPanUpdate: (details) => _onVerticalPanUpdate(details, index),
  onPanEnd: (details) => _onVerticalPanEnd(details, index),
  
  child: InteractiveViewer(...)
)
```

#### 智能方向识别
- 垂直分量必须是水平分量的1.5倍以上才被识别为垂直手势
- 初始移动距离需超过15像素才开始方向判断
- 避免与PageView水平滑动和InteractiveViewer缩放平移冲突

#### 功能实现
- **上滑分享功能**: 获取当前图片路径和标记点数据，构建分享内容，调用系统分享
- **下滑预览功能**: 切换底部预览栏的展开/收起状态

### 📦 依赖集成
```yaml
share_plus: ^7.2.2
```

### ✅ 测试验证
- 上滑分享：触发系统分享界面，内容包含场景和知识点信息
- 下滑预览：展开/收起底部预览栏
- 手势冲突：与现有手势功能无冲突

---

## 2. 开发者快速入口

### 🎯 功能概述
为方便开发测试，在引导页添加隐蔽的开发者快速入口，仅在Debug模式下启用，发布版本中自动隐藏。

### 🔧 技术实现

#### 激活机制
- 连续快速点击Logo区域5次激活
- 使用`kDebugMode`条件编译，确保仅在Debug模式下启用
- 提供视觉反馈（小圆点指示器）和触觉反馈

#### 开发者面板功能
- **跳转主页**: 直接进入应用主界面，跳过所有引导流程
- **跳转登录**: 跳转到登录页面
- **关闭面板**: 点击面板右上角的×号关闭

#### 安全性保证
```dart
if (kDebugMode) {
  // 开发者功能代码
  GestureDetector(
    onTap: _onLogoTap,
    child: Logo(),
  )
}
```

### ✅ 发布安全
- 使用`kDebugMode`标志，Release版本中完全不可见
- 需要连续点击5次才能激活，具有隐蔽性
- 可直接发布，无需手动清理代码

---

## 3. 拖拽排序功能优化

### 🎯 功能概述
优化记忆宫殿和相册创建中的图片拖拽排序功能，移除冗余的拖拽手柄，改用长按手势激活拖拽。

### 🔧 技术实现

#### 移除拖拽手柄
```dart
// 优化前：显示拖拽图标
ListTile(
  trailing: Icon(Icons.drag_handle),
  // ...
)

// 优化后：纯净界面
ListTile(
  // 移除trailing图标
  // ...
)
```

#### 长按拖拽交互
```dart
ReorderableListView(
  onReorder: (oldIndex, newIndex) {
    // 处理拖拽排序逻辑
  },
  children: items.map((item) => ListTile(
    key: ValueKey(item.id),
    // 长按自动激活拖拽
  )).toList(),
)
```

### 🎨 用户体验优化
- **界面简洁**: 移除视觉干扰元素，符合极简设计原则
- **直观操作**: 长按手势更符合用户习惯
- **空间节省**: 移除图标后界面更加宽敞

---

## 4. 浮动按钮自动隐藏

### 🎯 功能概述
实现浮动操作按钮在侧边栏/抽屉打开时自动隐藏，避免UI重叠，提升用户体验。

### 🔧 技术实现

#### 状态监听
```dart
class _PageState extends State<Page> {
  bool _isDrawerOpen = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: Drawer(
        // 抽屉内容
      ),
      onDrawerChanged: (isOpened) {
        setState(() {
          _isDrawerOpen = isOpened;
        });
      },
      floatingActionButton: _isDrawerOpen ? null : FloatingActionButton(
        // 按钮内容
      ),
    );
  }
}
```

#### 动画过渡
```dart
AnimatedSwitcher(
  duration: Duration(milliseconds: 200),
  child: _isDrawerOpen ? SizedBox.shrink() : FloatingActionButton(
    key: ValueKey('fab'),
    // 按钮内容
  ),
)
```

### ✅ 效果验证
- 抽屉打开时：浮动按钮平滑隐藏
- 抽屉关闭时：浮动按钮平滑显示
- 动画流畅：200ms过渡动画，用户体验良好

---

## 5. 分类树拖拽功能

### 🎯 功能概述
实现分类树节点的拖拽重排序和层级调整功能，支持可视化的拖拽反馈和精确的位置检测。

### 🔧 技术实现

#### 拖拽检测
```dart
GestureDetector(
  onPanStart: (details) {
    // 开始拖拽，创建浮动元素
    _startDrag(node, details.globalPosition);
  },
  onPanUpdate: (details) {
    // 更新拖拽位置，检测目标
    _updateDrag(details.globalPosition);
  },
  onPanEnd: (details) {
    // 结束拖拽，执行重排序
    _endDrag();
  },
  child: CategoryNode(),
)
```

#### 可视化反馈
```dart
OverlayEntry _createDragOverlay(CategoryNode node) {
  return OverlayEntry(
    builder: (context) => Positioned(
      left: _dragPosition.dx,
      top: _dragPosition.dy,
      child: Transform.scale(
        scale: 0.9,
        child: Opacity(
          opacity: 0.8,
          child: CategoryNodeWidget(node),
        ),
      ),
    ),
  );
}
```

#### 位置检测算法
```dart
CategoryNode? _findDropTarget(Offset position) {
  for (var node in visibleNodes) {
    final box = node.key.currentContext?.findRenderObject() as RenderBox?;
    if (box != null) {
      final nodeRect = box.localToGlobal(Offset.zero) & box.size;
      if (nodeRect.contains(position)) {
        // 检测是插入到节点上方、下方还是作为子节点
        final relativeY = position.dy - nodeRect.top;
        final threshold = nodeRect.height / 2;
        
        if (relativeY < threshold) {
          return InsertPosition.above(node);
        } else {
          return InsertPosition.below(node);
        }
      }
    }
  }
  return null;
}
```

### 🎨 交互设计
- **拖拽启动**: 长按节点启动拖拽
- **视觉反馈**: 半透明缩放的拖拽元素跟随手指
- **目标指示**: 拖拽到有效位置时显示插入指示线
- **层级调整**: 支持拖拽到不同层级，自动调整缩进

---

## 6. Cover模式手势控制

### 🎯 功能概述
在照片查看器的Cover模式（放大裁剪模式）下，实现精确的图片平移控制和边界检测，提供类似原生相册的手势体验。

### 🔧 技术实现

#### 模式检测
```dart
bool get isCoverMode => _currentImageMode == ImageMode.cover;

void _onDoubleTap(int index) {
  setState(() {
    _currentImageMode = isCoverMode ? ImageMode.contain : ImageMode.cover;
  });
}
```

#### 手势优先级控制
```dart
void _onVerticalPanUpdate(DragUpdateDetails details, int index) {
  if (isCoverMode) {
    // Cover模式：优先处理图片平移
    _handleImagePan(details);
  } else {
    // Contain模式：处理分享/预览手势
    _handleVerticalGesture(details);
  }
}
```

#### 边界检测与反弹
```dart
void _handleImagePan(DragUpdateDetails details) {
  final delta = details.delta;
  final newOffset = _imageOffset + delta;
  
  // 检测边界
  final bounds = _calculateImageBounds();
  final clampedOffset = Offset(
    newOffset.dx.clamp(bounds.left, bounds.right),
    newOffset.dy.clamp(bounds.top, bounds.bottom),
  );
  
  // iOS风格反弹效果
  if (newOffset != clampedOffset) {
    _triggerBounceEffect(newOffset - clampedOffset);
  }
  
  setState(() {
    _imageOffset = clampedOffset;
  });
}
```

### 🎨 用户体验
- **模式切换**: 双击图片在Contain和Cover模式间切换
- **精确控制**: Cover模式下支持像素级的图片平移
- **边界反馈**: 到达边界时提供iOS风格的反弹效果
- **手势分离**: 不同模式下手势功能完全分离，避免冲突

---

## 7. 位置跟随系统

### 🎯 功能概述
实现图片标记点的精确位置跟随系统，确保标记在图片缩放、平移、旋转时保持正确的相对位置。

### 🔧 技术实现

#### 坐标系转换
```dart
class PositionFollowingSystem {
  // 将屏幕坐标转换为图片相对坐标
  Offset screenToImageCoordinate(Offset screenPos, ImageTransform transform) {
    final relativeX = (screenPos.dx - transform.offset.dx) / transform.scale;
    final relativeY = (screenPos.dy - transform.offset.dy) / transform.scale;
    return Offset(relativeX / transform.imageSize.width, relativeY / transform.imageSize.height);
  }
  
  // 将图片相对坐标转换为屏幕坐标
  Offset imageToScreenCoordinate(Offset imagePos, ImageTransform transform) {
    final screenX = imagePos.dx * transform.imageSize.width * transform.scale + transform.offset.dx;
    final screenY = imagePos.dy * transform.imageSize.height * transform.scale + transform.offset.dy;
    return Offset(screenX, screenY);
  }
}
```

#### 实时位置更新
```dart
class AnchorPoint extends StatefulWidget {
  final Offset relativePosition; // 相对于图片的位置 (0.0-1.0)
  final ImageTransform transform;
  
  @override
  Widget build(BuildContext context) {
    final screenPosition = PositionFollowingSystem.imageToScreenCoordinate(
      relativePosition, 
      transform
    );
    
    return Positioned(
      left: screenPosition.dx,
      top: screenPosition.dy,
      child: AnchorWidget(),
    );
  }
}
```

#### 变换监听
```dart
class ImageViewer extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return InteractiveViewer(
      onInteractionUpdate: (details) {
        setState(() {
          _imageTransform = ImageTransform(
            scale: details.scale,
            offset: details.focalPoint,
            rotation: details.rotation,
          );
        });
      },
      child: Stack(
        children: [
          Image.asset(imagePath),
          ...anchors.map((anchor) => AnchorPoint(
            relativePosition: anchor.position,
            transform: _imageTransform,
          )),
        ],
      ),
    );
  }
}
```

### ✅ 精度保证
- **亚像素精度**: 使用double类型坐标，支持亚像素级定位
- **实时更新**: 变换过程中实时更新标记位置
- **边界处理**: 标记超出可视区域时自动隐藏
- **性能优化**: 使用高效的坐标转换算法，避免重复计算

---

## 8. 图标设置系统

### 🎯 功能概述
实现应用图标的自动化生成和配置系统，支持多平台图标适配和自定义图标设置。

### 🔧 技术实现

#### 配置文件设置
```yaml
# pubspec.yaml
dev_dependencies:
  flutter_launcher_icons: ^0.13.1

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/app_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icon/app_icon.png"
  windows:
    generate: true
    image_path: "assets/icon/app_icon.png"
  macos:
    generate: true
    image_path: "assets/icon/app_icon.png"
```

#### 自动化生成脚本
```bash
# 生成所有平台图标
flutter packages pub run flutter_launcher_icons:main

# 清理旧图标
rm -rf ios/Runner/Assets.xcassets/AppIcon.appiconset/*
rm -rf android/app/src/main/res/mipmap-*

# 重新生成
flutter packages pub run flutter_launcher_icons:main
```

#### 平台特定配置
```dart
// iOS配置
// ios/Runner/Info.plist
<key>CFBundleIcons</key>
<dict>
  <key>CFBundlePrimaryIcon</key>
  <dict>
    <key>CFBundleIconFiles</key>
    <array>
      <string>Icon-App-20x20@1x</string>
      <string>Icon-App-20x20@2x</string>
      <string>Icon-App-20x20@3x</string>
      // ... 其他尺寸
    </array>
  </dict>
</dict>
```

### 📱 多平台支持
- **iOS**: 支持所有必需的图标尺寸（20x20到1024x1024）
- **Android**: 支持自适应图标和传统图标
- **Web**: 生成favicon和PWA图标
- **Desktop**: 支持Windows、macOS、Linux图标

### ✅ 质量保证
- **自动化流程**: 一键生成所有平台图标
- **尺寸完整**: 覆盖所有平台要求的图标尺寸
- **格式优化**: 自动优化图标文件大小和质量
- **版本控制**: 图标文件纳入版本控制，确保团队同步

---

---

## 9. 分类树子分类添加功能

### 🎯 功能概述
OneDay应用的分类树导航侧边栏支持为任意分类节点添加子分类的功能，用户可以通过右键菜单快速在当前节点下创建新的子分类。

### 🔧 技术实现

#### 上下文菜单增强
```dart
itemBuilder: (popupContext) => [
  const PopupMenuItem(value: 'edit', child: Text('编辑')),
  const PopupMenuItem(value: 'add_child', child: Text('添加子分类')),
  const PopupMenuItem(value: 'delete', child: Text('删除')),
]
```

#### 智能子分类创建
```dart
void _addChildCategory(CategoryNode parentNode) {
  final newChild = CategoryNode(
    id: _generateId(),
    name: '新分类',
    level: parentNode.level + 1,
    parentId: parentNode.id,
    isEditing: true,
  );

  setState(() {
    parentNode.children.add(newChild);
    parentNode.isExpanded = true; // 自动展开父节点
  });
}
```

### 🎨 用户体验
- **智能层级**: 新子分类的层级自动设置为父分类层级+1
- **父节点展开**: 创建子分类后父节点自动展开以显示新内容
- **即时编辑**: 新创建的子分类自动进入编辑模式

---

## 10. 上下文感知相册创建

### 🎯 功能概述
实现上下文感知的相册创建功能，新相册自动分配到当前浏览的分类文件夹，无需手动选择分类。

### 🔧 技术实现

#### 当前分类检测
```dart
class AlbumCreationContext {
  static String? getCurrentCategoryId() {
    final currentRoute = GoRouter.of(context).location;
    if (currentRoute.contains('/category/')) {
      return _extractCategoryIdFromRoute(currentRoute);
    }
    return null;
  }
}
```

#### 自动分类分配
```dart
void createAlbum(AlbumData albumData) {
  final currentCategoryId = AlbumCreationContext.getCurrentCategoryId();

  final album = Album(
    id: _generateId(),
    title: albumData.title,
    categoryId: currentCategoryId ?? 'default',
    images: albumData.images,
    createdAt: DateTime.now(),
  );

  _albumRepository.save(album);
  _navigateToCurrentCategory(); // 立即显示在当前文件夹
}
```

### ✅ 用户体验优化
- **自动分类**: 相册自动分配到当前浏览的分类
- **即时显示**: 创建后立即在当前文件夹中显示
- **减少步骤**: 无需手动选择分类，简化创建流程

---

## 11. 三点菜单功能

### 🎯 功能概述
为OneDay应用的相册卡片添加三点菜单功能，提供编辑、删除、分享等操作选项。

### 🔧 技术实现

#### 菜单按钮设计
```dart
Positioned(
  bottom: 8,
  right: 8,
  child: PopupMenuButton<String>(
    icon: Icon(
      Icons.more_vert,
      color: Color(0xFF2F76DA),
      size: 20,
    ),
    itemBuilder: (context) => [
      PopupMenuItem(
        value: 'edit',
        child: Row(
          children: [
            Icon(Icons.edit, size: 16),
            SizedBox(width: 8),
            Text('编辑'),
          ],
        ),
      ),
      PopupMenuItem(
        value: 'share',
        child: Row(
          children: [
            Icon(Icons.share, size: 16),
            SizedBox(width: 8),
            Text('分享'),
          ],
        ),
      ),
      PopupMenuItem(
        value: 'delete',
        child: Row(
          children: [
            Icon(Icons.delete, size: 16, color: Colors.red),
            SizedBox(width: 8),
            Text('删除', style: TextStyle(color: Colors.red)),
          ],
        ),
      ),
    ],
  ),
)
```

### 🎨 设计规范
- **位置**: 卡片右下角，不干扰主要内容
- **颜色**: 使用主题蓝色 #2F76DA
- **图标**: 带图标的菜单项，提升可识别性
- **交互**: 点击触发，符合Material Design规范

---

## 12. 词汇管理系统

### 🎯 功能概述
实现完整的词汇管理系统，支持分类管理、学习进度跟踪和与现有学习系统的集成。

### 🔧 技术实现

#### 词汇数据模型
```dart
class Vocabulary {
  final String id;
  final String word;
  final String pronunciation;
  final String meaning;
  final String example;
  final VocabularyCategory category;
  final LearningProgress progress;
  final DateTime createdAt;
  final DateTime? lastReviewedAt;
}

enum VocabularyCategory {
  daily, business, academic, technical
}

enum LearningProgress {
  new_, learning, reviewing, mastered
}
```

#### 底部表单创建
```dart
void _showCreateVocabularySheet() {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    builder: (context) => Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: VocabularyCreationForm(),
    ),
  );
}
```

#### 学习系统集成
```dart
class VocabularyLearningIntegration {
  static void startLearningSession(List<Vocabulary> vocabularies) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LearningSessionPage(
          vocabularies: vocabularies,
          sessionType: SessionType.vocabulary,
        ),
      ),
    );
  }
}
```

### 🎨 用户界面
- **分类组织**: 按类别组织词汇，便于管理
- **进度指示**: 清晰的学习进度可视化
- **快速创建**: 底部表单快速添加新词汇
- **学习集成**: 与现有学习系统无缝集成

---

**文档状态**: ✅ 已完成
**更新日期**: 2025年1月
**涵盖功能**: 12个核心功能模块
**文档用途**: 功能开发指导和技术参考
