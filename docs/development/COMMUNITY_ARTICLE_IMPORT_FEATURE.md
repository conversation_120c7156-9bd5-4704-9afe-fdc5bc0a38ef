# 社区文章导入与词汇高亮功能

## 功能概述

为OneDay应用的社区模块新增了文章导入功能，支持英文文章的考研词汇高亮显示，帮助用户在阅读过程中学习和记忆考研大纲词汇。

## 主要功能

### 1. 文章导入
- **入口位置**: 社区页面的"+"浮动按钮 → 发布动态页面
- **支持格式**: .txt 和 .md 文件（暂时使用示例文本代替文件选择）
- **大小限制**: 最大5000行文本
- **处理方式**: 一次性读取到内存，实时处理词汇高亮

### 2. 词汇高亮
- **高亮范围**: 考研大纲词汇及其变体形式
- **高亮样式**: 红色文字 + 浅红色背景
- **难度分级**: 支持按难度级别过滤（beginner/intermediate/advanced/expert）
- **白名单模式**: 可选择只高亮expert级别词汇

### 3. 交互功能
- **词汇点击**: 点击高亮词汇显示详细信息
- **词汇详情**: 显示释义、难度级别、原始形式（如果是变体）
- **添加到背诵列表**: 一键将词汇添加到考研词库的已选单词中
- **预览模式**: 支持编辑/预览模式切换

### 4. 设置管理
- **词汇高亮开关**: 可在设置页面启用/禁用词汇高亮功能
- **专家级过滤**: 可选择只高亮expert级别的考研词汇
- **设置持久化**: 使用SharedPreferences保存用户偏好

## 技术实现

### 核心文件

1. **ArticleImportService** (`lib/features/community/article_import_service.dart`)
   - 文章内容处理和词汇高亮核心逻辑
   - 考研词库匹配（包括变体形式）
   - 设置管理和持久化

2. **CommunityPostEditorPage** (`lib/features/community/community_post_editor_page.dart`)
   - 发帖编辑器界面
   - 文章导入和预览功能
   - 词汇交互和详情显示

3. **设置页面扩展** (`lib/features/settings/settings_page.dart`)
   - 词汇高亮相关设置项
   - 白名单配置选项

### 数据模型

```dart
// 高亮设置
class HighlightSettings {
  final bool expertOnly;  // 是否只高亮expert级别词汇
  final bool enabled;     // 是否启用高亮功能
}

// 导入结果
class ImportResult {
  final String originalText;           // 原始文本
  final String highlightedText;        // 高亮后的HTML文本
  final List<HighlightedWord> highlightedWords;  // 高亮词汇列表
  final int totalWords;                // 总词数
  final int vocabularyWordsFound;      // 发现的考研词汇数
}

// 高亮词汇
class HighlightedWord {
  final String word;          // 词汇
  final String definition;    // 释义
  final String difficulty;    // 难度级别
  final int startIndex;       // 开始位置
  final int endIndex;         // 结束位置
  final bool isVariant;       // 是否为变体形式
  final String originalForm;  // 原始词汇形式
}
```

### 词汇匹配算法

1. **词库加载**: 从VocabularyService加载考研词汇库
2. **难度过滤**: 根据用户设置过滤词汇难度级别
3. **变体处理**: 包含主词汇和alternativeSpellings变体
4. **正则匹配**: 使用`\b[a-zA-Z]+\b`匹配英文单词
5. **位置记录**: 记录每个匹配词汇的位置信息
6. **HTML生成**: 生成带高亮标签的HTML文本

## 用户体验

### 操作流程

1. **进入发帖页面**: 点击社区页面的"+"按钮
2. **导入文章**: 点击"导入TXT"按钮（当前使用示例文本）
3. **查看高亮**: 系统自动高亮考研词汇并显示统计信息
4. **词汇学习**: 点击高亮词汇查看详细信息
5. **添加背诵**: 将感兴趣的词汇添加到背诵列表
6. **发布动态**: 编辑完成后发布到社区

### 设置配置

1. **打开设置页面**: 主页 → 设置
2. **词汇高亮设置**: 在"系统偏好"分组中找到相关选项
3. **启用/禁用**: 控制词汇高亮功能的开关
4. **专家级过滤**: 选择是否只高亮expert级别词汇

## 性能优化

- **内存处理**: 一次性加载文本到内存，避免频繁IO操作
- **词汇缓存**: 词汇库映射表缓存，提高匹配效率
- **异步处理**: 文章处理和词汇匹配使用异步操作
- **UI响应**: 导入过程显示加载状态，避免界面卡顿

## 扩展计划

### 短期优化
- [ ] 恢复文件选择器功能（解决file_picker兼容性问题）
- [ ] 添加在线词典API集成
- [ ] 优化词汇匹配算法性能
- [ ] 增加更多文件格式支持

### 长期规划
- [ ] 云同步功能
- [ ] 词汇学习进度跟踪
- [ ] 个性化词汇推荐
- [ ] 社区词汇分享功能

## 测试建议

1. **功能测试**:
   - 测试词汇高亮的准确性
   - 验证设置保存和加载
   - 检查词汇详情显示
   - 确认背诵列表添加功能

2. **性能测试**:
   - 测试大文本处理性能
   - 验证内存使用情况
   - 检查UI响应速度

3. **用户体验测试**:
   - 验证操作流程的直观性
   - 测试错误处理和提示信息
   - 检查界面布局和视觉效果

## 已知问题

1. **文件选择器**: 当前版本暂时禁用了file_picker，使用示例文本代替
2. **词库依赖**: 功能依赖现有的VocabularyService和考研词库数据
3. **HTML渲染**: 使用flutter_html渲染高亮文本，可能存在性能影响

## 总结

社区文章导入与词汇高亮功能已成功实现并集成到OneDay应用中。该功能为用户提供了便捷的英文学习工具，将阅读和词汇学习有机结合，符合OneDay应用的学习理念。通过持续优化和功能扩展，该模块将成为用户英语学习的重要助手。
