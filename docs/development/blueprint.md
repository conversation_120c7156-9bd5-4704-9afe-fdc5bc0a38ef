# 知识点气泡功能蓝图

## 1. 核心功能规格

### 1.1. 标记物外观
- 整体是一个白色的、带有圆角的“**对话气泡**” (Speech Bubble)。
- 气泡下方有一个小指针，指针末端有一个小圆点，用于**精确指向**图片上的标记位置。
- 指针和文本的尺寸比例应与参考图片保持一致。

### 1.2. 尺寸稳定性
- 无论用户如何缩放图片，所有标记气泡在屏幕上的**视觉尺寸必须保持恒定**。

### 1.3. 位置粘滞性
- 当用户平移或缩放图片时，标记气泡必须像“粘”在图片的对应位置上一样，**平滑地跟随移动**。

### 1.4. 创建新标记的流程
1. **触发**：用户在图片上点击，希望创建标记。
2. **临时气泡**：在点击处立即出现一个临时的、内容为空的标记气泡。
3. **输入界面**：同时，屏幕底部滑出一个文本输入栏，键盘自动弹出。
4. **输入与定位**：
    - 用户在输入栏打字时，临时气泡**无需**同步显示内容。
    - 用户滑动屏幕时，键盘应**自动收起**，以便全屏查看和调整标记位置。
5. **保存/取消**：
    - **保存**：输入栏和键盘收起，标记气泡显示最终文字，成为一个固定的锚点。
    - **取消**：临时气泡、输入栏和键盘全部消失。

### 1.5. 查看/编辑已有标记
- 点击一个已存在的标记气泡，应高亮显示它，并提供编辑或删除的选项。

---

## 2. 技术与质量要求

### 2.1. 高性能 (High Performance)
- **流畅度**：标记气泡跟随图片平移/缩放的过程必须达到设备的最大刷新率，如丝般顺滑，**严禁任何卡顿或掉帧**。
- **优化渲染**：
    - **隔离重绘范围**：监听 `TransformationController` 的变化时，**严禁**调用整个页面的 `setState()`。必须将重绘范围隔离在标记物本身。
    - **推荐方案**：优先使用 `ValueListenableBuilder` 或 `AnimationBuilder` 来包裹标记物。
- **绘制缓存**：建议使用 `RepaintBoundary` 包裹自定义的气泡组件，以缓存其绘制结果，提升图形性能。

### 2.2. 跨平台与视觉统一 (Cross-Platform & Visual Consistency)
- **统一 UI**：此功能的所有UI元素（对话气泡、输入栏、按钮等）在 **iOS 和 Android** 上必须看起来完全一样。

### 2.3. 自适应布局 (Adaptive Layout)
- **响应式设计**：整个交互界面需要能良好地适应不同尺寸和长宽比的设备屏幕（手机、平板等）。
- **避免硬编码**：布局中应避免使用写死的像素值。建议使用相对单位或根据屏幕尺寸动态计算，确保在任何设备上都表现协调。

---

## 3. 开发实施步骤

> **重要提示**: 为了确保代码质量和实现精度，我们将分步进行。请不要一次性生成所有功能的代码。我将按照以下步骤逐一实现，并向您反馈。

### 第一步：标记物UI组件设计
- **任务**:
    1. 创建对话气泡 `Widget`（白色圆角 + 指针 + 圆点）。
    2. 实现尺寸稳定性逻辑（在 `Transform.scale` 中应用反向缩放因子）。
    3. 添加 `RepaintBoundary` 进行性能优化。
- **预期成果**: 一个可独立显示的、尺寸恒定的静态气泡组件。

### 第二步：位置跟随系统
- **任务**:
    1. 实现核心的坐标转换逻辑（图片比例坐标 ↔ 屏幕像素坐标）。
    2. 使用 `ValueListenableBuilder` 监听 `TransformationController` 的变化。
    3. 确保气泡能根据图片变换（移动/缩放）平滑更新位置。
- **预期成果**: 气泡能够精确地“粘滞”在图片的指定位置上。

### 第三步：创建标记流程
- **任务**:
    1. 实现图片区域的点击检测，并在点击处显示临时气泡。
    2. 创建并集成底部的文本输入面板。
    3. 集成键盘规避功能，确保输入栏不被键盘遮挡。
    4. 实现保存/取消的完整逻辑流。
- **预期成果**: 用户可以完成一个从点击到保存/取消的完整标记创建流程。

### 第四步：编辑和管理功能
- **任务**:
    1. 实现气泡的点击选中状态（例如，高亮显示）。
    2. 添加编辑和删除功能入口及相应逻辑。
    3. 优化整体交互体验。
- **预期成果**: 用户可以方便地管理已创建的标记。

### 第五步：性能优化和适配
- **任务**:
    1. 在真实设备上进行性能分析和优化。
    2. 检查并微调跨平台的视觉一致性。
    3. 在多种设备上测试并完善响应式布局。
- **预期成果**: 一个功能完整、性能卓越、适配良好的生产就绪功能。