# Cursor AI 使用优化指南

本指南旨在帮助您在不超出Pro会员限制、不产生额外费用的前提下，最大化利用Cursor中Claude 3.5 Sonnet、Claude 3 Opus等大型语言模型的效能。

## 核心理念：管理上下文（Context）

模型的成本与上下文长度直接相关。我们对话中的每一句话、每一个文件、每一次代码修改，都会被添加到上下文中。对话越长，上下文就越大，单次请求消耗的资源就越多。

**优化策略的核心就是：在每次交互时，只给模型提供完成当前任务所必需的最小上下文。**

---

## 策略一：何时开启一个新Chat？

开启新Chat是清空上下文、降低消耗最直接有效的方法。以下是建议开启新Chat的最佳时机：

1.  **完成一个独立的功能模块后**
    *   **场景**: 您已经完成了"记忆宫殿管理页"的开发、测试和文档更新。
    *   **操作**: 在开始开发"时间盒子"功能前，果断关闭当前Chat，开启一个新窗口。
    *   **原因**: 防止"记忆宫殿"的上下文（代码、讨论、错误）干扰和消耗新任务的资源。

2.  **当任务焦点发生重大转移时**
    *   **场景**: 我们刚完成前端UI的重构，现在要转向后端的数据库模型设计。
    *   **操作**: 开启新Chat，并明确指出新任务："接下来，请帮我设计数据库，这是相关的`models`文件：@file1 @file2"。
    *   **原因**: UI和后端逻辑所需上下文完全不同，分离它们可以显著提升效率和准确性。

3.  **当AI开始"犯糊涂"或响应变慢时**
    *   **场景**: AI反复出现同样的错误、理解偏差，或者响应速度明显下降。
    *   **操作**: 开启新Chat，用一个清晰、简洁的初始问题重新开始。
    *   **原因**: 这通常是上下文过大或变得混乱的迹象。重置上下文（开新Chat）是最快的"修复"方法。

4.  **在开始一个复杂的调试任务前**
    *   **场景**: 您遇到了一个棘手的Bug，可能涉及多个文件和复杂的逻辑链。
    *   **操作**: 开启新Chat，精确描述问题，并使用`@`符号附上所有相关文件。例如："我遇到了一个Bug，点击首页的A按钮后，用户余额没有更新。这可能与 `@home_page.dart`、`@user_provider.dart` 和 `@api_service.dart` 有关。"
    *   **原因**: 为复杂问题创建一个"无菌"的上下文环境，可以帮助AI更专注于解决核心矛盾。

5.  **当上下文达到一定长度时（经验值）**
    *   **场景**: 当前对话已经进行了50+轮，或者包含了10+个大型文件。
    *   **操作**: 主动开启新Chat，即使任务还没完成。
    *   **原因**: 预防性措施，避免等到模型"卡顿"才切换。

---

## 策略二：如何解决新开窗口的缺点？

新开Chat窗口的最大缺点是**上下文丢失**，可能导致AI忘记之前的设计决策、编码风格、项目结构等。以下是避免这些问题的策略：

### 2.1 建立项目"记忆锚点"

**核心思想**: 在项目中创建关键文件，记录重要的设计决策和规范，让AI在新窗口中快速"回忆"起项目状态。

**具体做法**:
1.  **创建 `.cursorrules` 文件**（项目根目录）
    ```
    # OneDay Flutter项目开发规范
    
    ## UI设计风格
    - 严格遵循Notion风格极简设计
    - 背景色：#F7F6F3（浅灰）/#FFFFFF（纯白）
    - 主色调：#2E7EED（品牌蓝）/#7C3AED（辅助紫）
    - 文字色：#37352F（深灰主文字）/#787774（次要文字）
    - 输入框：必须使用纯白背景配深色文字（白纸黑字原则）
    
    ## 技术栈
    - Flutter 3.x + Dart 3.x
    - 状态管理：Riverpod 2.0
    - 路由：GoRouter
    - 数据持久化：SharedPreferences (临时) → Isar (计划)
    
    ## 代码规范
    - 遵循Effective Dart规范
    - 组件文件放在 lib/shared/widgets/
    - 页面文件放在 lib/features/[模块名]/
    - 所有新组件必须符合无障碍要求
    ```

2.  **维护 `DEVELOPMENT_STATUS.md` 文件**
    ```markdown
    # 开发状态总览
    
    ## 已完成模块
    - ✅ 启动页 (splash_page.dart) - Notion风格
    - ✅ 引导页 (onboarding_page.dart) - 5页滑动引导
    - ✅ 登录页 (login_page.dart) - 白纸黑字输入框
    - ✅ 首页 (home_page.dart) - 智能问候+统计面板
    - ✅ 记忆宫殿管理 (palace_manager_page.dart) - 网格/列表视图
    - ✅ 记忆场景详情 (scene_detail_page.dart) - Notion风格重构完成
    
    ## 当前任务
    - 🔄 [在这里更新当前正在进行的任务]
    
    ## 待开发模块
    - ⏳ 时间盒子管理 (time_box/)
    - ⏳ 工资系统 (wage_system/)
    - ⏳ 运动健康 (exercise/)
    ```

3.  **创建 `DESIGN_DECISIONS.md` 文件**
    ```markdown
    # 设计决策记录
    
    ## UI/UX决策
    - 2024-01-XX: 从暗色科技风改为Notion风格极简设计
    - 2024-01-XX: 记忆场景详情页从黑色沉浸式改为浅色背景
    - 2024-01-XX: 所有输入框统一为白色背景深色文字
    
    ## 技术决策
    - 2024-01-XX: 选择Riverpod作为状态管理方案
    - 2024-01-XX: 记忆宫殿支持多图片，按选择顺序排列
    - 2024-01-XX: 知识点使用比例坐标系统确保跨设备一致性
    ```

### 2.2 新Chat的"快速启动"模板

每次开启新Chat时，使用标准化的开场白，快速让AI进入项目状态：

**模板示例**:
```
我正在开发OneDay Flutter应用，这是一个学习管理平台。请先阅读以下关键文件了解项目状态：

@.cursorrules @README.md @DEVELOPMENT_STATUS.md

现在我需要[具体任务描述]。相关文件：@file1.dart @file2.dart

注意：
1. 严格遵循Notion风格UI设计规范
2. 所有输入框必须是白色背景配深色文字
3. 保持与现有页面的风格一致性
```

### 2.3 建立"风格检查清单"

在每个新Chat中完成关键任务后，使用这个清单确保一致性：

**UI风格检查清单**:
- [ ] 背景色是否为浅灰色(#F7F6F3)或纯白(#FFFFFF)？
- [ ] 输入框是否使用白色背景配深色文字？
- [ ] 主要按钮是否使用品牌蓝(#2E7EED)？
- [ ] 文字颜色是否符合层级规范？
- [ ] 圆角和间距是否遵循8px网格系统？
- [ ] 是否添加了合适的无障碍标签？

---

## 策略三：如何延长单个Chat的"续航"？

在单个Chat中，您也可以通过以下技巧来节省资源：

1.  **精确使用`@`符号**
    *   **多用**: `@file.dart`, `@MyWidget`, `@my_function`
    *   **少用**: 直接在聊天框中粘贴大段代码。
    *   **原因**: `@`符号能让AI精确地只加载相关代码片段，而不是整个文件或历史记录，这是节省资源的最重要技巧之一。

2.  **使用更小、更快的模型处理简单任务**
    *   **场景**: 您只需要写一段简单的函数、生成文档注释、或者解释一小段代码。
    *   **操作**: 切换到Claude 3 Haiku或GPT-4等较小模型来执行这些任务。
    *   **原因**: 小模型消耗的资源远少于大模型，将它们用于简单任务，可以把大模型的"火力"保留给最复杂的架构设计和逻辑推理。

3.  **主动"修剪"上下文**
    *   **场景**: 在一个长对话中，您要开始一个稍微不同的子任务。
    *   **操作**: 在提问前，先做个总结。例如："好了，UI部分完成了。现在我们来处理数据逻辑，请专注于`user_provider.dart`中的`updateUser`方法。"
    *   **原因**: 这句话像一个"锚点"，可以帮助AI忽略掉之前不相关的UI讨论，重新聚焦。

4.  **将大任务分解**
    *   **不要说**: "帮我开发整个电商App。"
    *   **应该说**:
        1.  "首先，帮我设计`Product`的数据模型。"
        2.  （完成后）"很好，现在基于这个模型，创建一个展示商品列表的页面。"
        3.  （再完成后）"接下来，实现商品详情页。"
    *   **原因**: 这种方式自然地将一个巨大的上下文分解成了多个小块，您甚至可以在每个步骤之间开启新Chat，实现最大化优化。

---

## 策略四：模型选择建议

不同的任务适合不同的模型，合理分配可以大幅节省资源：

| 任务类型 | 推荐模型 | 原因 |
|---------|---------|------|
| 复杂架构设计、重构 | Claude 3 Opus | 最强推理能力 |
| UI组件开发、页面布局 | Claude 3.5 Sonnet | 平衡性能和成本 |
| 简单函数、注释生成 | Claude 3 Haiku | 快速且便宜 |
| 代码解释、文档编写 | Claude 3.5 Sonnet | 文本处理优秀 |
| 调试、错误修复 | Claude 3 Opus | 复杂推理能力 |

---

## 实战技巧总结

1.  **预防性开启新Chat**: 不要等到模型"卡住"才换，主动管理上下文长度。
2.  **建立项目记忆系统**: 通过关键文件让AI快速"回忆"项目状态。
3.  **使用标准化开场白**: 让每个新Chat都能快速进入工作状态。
4.  **建立检查清单**: 确保跨Chat的一致性。
5.  **合理选择模型**: 小模型做小事，大模型做大事。

通过结合运用这些策略，您将能够在预算范围内最大化利用AI的能力，同时保持项目的一致性和质量。 


展示筛选后的“宫殿”卡片网格。
内容：这部分的视觉样式和功能保持不变，它只会根据左侧大纲的选择来更新显示内容。你漏掉了很多功能，导入的第一张图为知忆相册的封面图，知忆相册右下角应该有三个点，编辑可编辑功能。在进入知忆相册界面后，点击创建知忆相册，弹出的窗口具体要求如下：
在不改动其他功能的前提下，改动的只是点击右下角创建知忆相册后弹出的弹窗。
**核心功能：**
1. 点击选择图片使用第一张导入的图片作为相册的封面预览图
2. 点击"创建知忆相册"并选择图片后，显示照片预览区域，包含：
   - 图片以水平可滚动列表形式排列。只排列成一行，滑动展示位于后面的照片
   - 每张图片右下角显示拖拽手柄（长按拖拽图标），用于重新排序
   - 预览区域下方显示两个操作按钮："添加图片"和"清空全部"
弹窗最上方文字显示为“创建知忆相册”，下方编辑名称栏是相册名称。
**UI设计要求：**
遵循Flutter跨平台、自适应、高性能设计原则，保持视觉一致性：

**设计主题：** 极简主义 + 扁平化 + 排版优先的设计系统
- 整体视觉风格：简洁、现代、高端
- 组件特征：扁平化设计、结构化排版、克制的动画效果
- 参考Dribbble、Figma社区或Material3官方样式中的高评分Flutter UI设计

**配色方案：**
- 背景色：#FFFFFF（浅色模式）/ #1D1D1D（深色模式）
- 主要文本：#37352F（接近黑色，用于主要内容和标题）
- 次要文本：#6E6E6E 和 #9B9A97（用于注释、标签、描述）
- 边框/分割线：#E3E2E0（极浅灰色，用于内容分隔）
- 主要强调蓝色：#2F76DA 或 #0B57D0（按钮、链接、选中状态）
- 附加强调色：紫色 #9061F9、绿色 #4CAF50、红色 #EB5757（标签、状态指示器）

**技术要求：**
- 清晰的视觉层次结构
- 适配不同屏幕尺寸的响应式设计
- 流畅的拖拽交互实现图片重新排序功能
- 正确的手势处理，确保不与现有照片查看器功能产生冲突
- 使用适当的Flutter组件（如ReorderableListView、GestureDetector等）
- 实现长按拖拽的视觉反馈效果
- 确保拖拽操作的性能优化，避免卡顿
- 添加适当的动画过渡效果，提升用户体验

**具体实现细节：**
- 图片预览列表应使用合适的缩略图尺寸以优化性能
- 拖拽手柄应在用户长按时显示明显的视觉反馈
- "添加图片"按钮应打开系统图片选择器
- "清空全部"按钮应包含确认对话框以防误操作
- 相册封面应实时更新为列表中的第一张图片。