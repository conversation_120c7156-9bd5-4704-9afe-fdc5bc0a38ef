# TimeBox计时器小窗口功能

## 功能概述
为OneDay应用的TimeBox计时器添加了浮动小窗口显示功能，允许用户在使用其他功能时继续监控计时器状态。

## 功能特性

### 1. 小窗口显示按钮
- 在计时器运行时，内联计时器UI中会显示一个"小窗口显示"按钮（图标：picture_in_picture_alt）
- 按钮位置：暂停/停止按钮右侧
- 按钮颜色：灰色（#9B9A97）
- 支持Tooltip提示："小窗口显示"

### 2. 浮动小窗口特性
- **尺寸**：160x60像素，超紧凑设计
- **位置**：默认位置(20, 100)，支持拖拽移动
- **透明度**：60%透明度，最大化减少对用户视野的干扰
- **边框**：圆角8px，带有淡蓝色边框
- **阴影**：Material elevation 4

### 3. 小窗口内容（简化设计）
- **任务标题**：显示当前任务名称（支持文本截断）
- **剩余时间**：MM:SS格式，等宽字体，居中显示
- **关闭按钮**：右上角小型圆形关闭按钮

**已移除的元素**：
- 顶部进度条
- 状态指示器（绿色/灰色圆点）
- 状态文字（"进行中"/"已暂停"）
- 收入显示
- 控制按钮（暂停/恢复、停止）

### 4. 交互功能
- **拖拽移动**：支持拖拽移动窗口位置
- **边界检测**：确保窗口不会移出屏幕边界
- **时间同步**：与主计时器保持实时同步显示剩余时间
- **自动关闭**：停止计时器时自动关闭小窗口
- **简化操作**：仅支持查看功能，控制操作需返回主界面

## 技术实现

### 状态管理
```dart
// 小窗口状态变量
bool _isFloatingWindowVisible = false;
Offset _floatingWindowPosition = const Offset(20, 100);
```

### 核心方法
- `_toggleFloatingWindow()`: 切换小窗口显示状态
- `_closeFloatingWindow()`: 关闭小窗口
- `_updateFloatingWindowPosition()`: 更新窗口位置
- `_buildFloatingTimerWindow()`: 构建小窗口UI

### UI结构
- 主界面使用Stack布局支持浮动窗口
- 小窗口使用Positioned定位
- 支持GestureDetector处理拖拽手势
- 采用紧凑的双行布局：第一行显示标题和关闭按钮，第二行居中显示时间

## 使用方法

1. **启动计时器**：点击任务的"开始"按钮启动计时器
2. **显示小窗口**：在内联计时器中点击小窗口图标按钮
3. **移动窗口**：拖拽小窗口可以移动到合适位置
4. **控制计时器**：在小窗口中可以暂停/恢复/停止计时器
5. **关闭窗口**：点击右上角关闭按钮或停止计时器

## 设计原则

### 用户体验
- **非模态设计**：不阻塞用户操作其他界面
- **半透明效果**：避免遮挡重要内容
- **直观控制**：提供基本的计时器控制功能
- **状态同步**：确保与主计时器状态一致

### 视觉设计
- **OneDay风格**：遵循应用的设计语言和颜色主题
- **简洁布局**：信息层次清晰，避免冗余元素
- **响应式设计**：适配不同屏幕尺寸

### 性能优化
- **条件渲染**：仅在需要时渲染小窗口
- **状态管理**：高效的状态更新机制
- **内存管理**：适当的资源清理

## 后续优化建议

1. **记忆位置**：保存用户偏好的窗口位置
2. **尺寸调整**：支持窗口大小调整
3. **更多控制**：添加更多计时器控制选项
4. **主题适配**：支持深色模式
5. **动画效果**：添加窗口显示/隐藏动画
