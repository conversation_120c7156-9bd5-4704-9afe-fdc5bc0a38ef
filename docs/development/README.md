# 开发文档

本目录包含功能开发、技术实现和代码规范相关的文档。

## 📋 文档分类

### 🚀 功能开发文档

#### 计时器相关功能
- [浮动计时器功能](FLOATING_TIMER_FEATURE.md) - 浮动计时器的核心功能实现
- [系统级浮动计时器](FLOATING_TIMER_SYSTEM_LEVEL_IMPLEMENTATION.md) - 系统级浮动窗口实现
- [番茄钟计时器](POMODORO_TIMER_IMPLEMENTATION.md) - 番茄钟功能的技术实现

#### 社区和内容功能
- [社区文章导入功能](COMMUNITY_ARTICLE_IMPORT_FEATURE.md) - 社区文章导入和处理
- [自定义运动分类](CUSTOM_EXERCISE_CATEGORY_FEATURE.md) - 运动分类自定义功能

#### 相册和分享功能
- [相册编辑增强](EDIT_ALBUM_ENHANCEMENT.md) - 相册编辑功能的增强实现
- [分享功能简化](SHARE_FUNCTION_SIMPLIFICATION.md) - 分享功能的简化优化
- [分享UI恢复功能](SHARE_UI_RESTORE_FEATURE.md) - 分享后UI状态恢复

#### 词汇和学习功能
- [词汇滚动条增强](VOCABULARY_SCROLLBAR_ENHANCEMENT.md) - 词汇列表滚动条功能
- [单词显示小写更新](WORD_DISPLAY_LOWERCASE_UPDATE.md) - 单词显示格式优化

### 🎨 UI/UX 改进
- [Notion 风格更新](NOTION_STYLE_UPDATES.md) - UI 风格向 Notion 风格的调整
- [滑动手势演示](SWIPE_GESTURE_DEMO.md) - 手势交互的实现演示

### 🔧 技术实现
- [项目蓝图](blueprint.md) - 项目的技术蓝图和实现规划

### 📱 功能移除
- [相机功能移除](REMOVE_CAMERA_FEATURE.md) - 相机功能的移除和替代方案

## 🛠️ 开发规范

### 代码规范
- 遵循 Flutter 官方代码规范
- 使用 Effective Dart 编程指南
- 保持代码的可读性和可维护性

### 文档规范
- 新功能开发必须包含相应的技术文档
- 文档应包含功能描述、技术实现和使用说明
- 定期更新和维护技术文档

## 🔗 相关链接

- [核心文档](../core/) - 项目架构和产品规划
- [问题解决](../troubleshooting/) - 开发过程中的问题和解决方案
- [测试文档](../testing/) - 功能测试和质量保证
- [用户指南](../guides/) - 开发环境搭建和使用指南

---

**维护说明**: 本目录下的文档记录了各项功能的技术实现细节，开发新功能时应及时添加相应的技术文档。
