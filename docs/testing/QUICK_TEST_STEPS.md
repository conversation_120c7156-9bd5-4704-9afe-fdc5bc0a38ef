# TimeBox功能快速测试步骤

## 测试环境
- 设备：iPhone 16 Pro (模拟器)
- 应用状态：正在运行中

## 测试步骤

### 1. 测试休息界面自动返回功能 ✅

**步骤：**
1. 打开OneDay应用
2. 导航到TimeBox页面
3. 找到"【开发者测试】"按钮，点击"进入"
4. 直接进入PAO学习界面
5. 观察右上角的休息计时器（显示5:00开始倒计时）
6. 等待计时器倒计时到0:00
7. **预期结果**：计时器到0时自动返回TimeBox任务列表

**验证要点：**
- 休息计时器正常倒计时
- 计时器到0时自动返回，无需手动点击按钮
- 返回过程流畅，无卡顿

### 2. 测试计时器启动功能 🔄

**步骤：**
1. 在TimeBox任务列表中选择任意任务
2. 点击"开始学习"按钮
3. 观察计时器界面是否显示
4. 检查计时器是否开始倒计时
5. 观察剩余时间是否逐秒递减

**验证要点：**
- 计时器界面正常显示
- 倒计时正常工作
- 时间显示准确

**调试信息：**
查看控制台输出，应该看到类似信息：
```
🚀 启动高精度计时器 - 总时长: XXX 秒, 开始时间: ...
▶️ 计时器已启动
⏰ 计时器更新: 剩余 XXX 秒 (已过 X 秒)
```

### 3. 测试计时器控制功能

**步骤：**
1. 启动计时器后，测试暂停按钮
2. 测试恢复按钮
3. 测试停止按钮

**验证要点：**
- 暂停功能正常
- 恢复功能正常
- 停止功能正常

## 当前状态

### ✅ 已修复
- **休息界面自动返回**：完全正常工作
- **高精度计时器逻辑**：核心逻辑已实现
- **调试信息**：可以跟踪计时器状态

### ⚠️ 已知问题
- **Ticker生命周期管理**：开发环境下有dispose错误
- **热重载兼容性**：可能需要重启应用来清除状态

### 🔄 待验证
- **计时器启动**：需要实际测试确认
- **计时器精度**：需要与外部计时器对比

## 快速验证方法

### 最简单的测试
1. 点击"【开发者测试】" → "进入"
2. 观察休息计时器是否自动倒计时并返回
3. 如果正常，说明核心修复生效

### 完整功能测试
1. 创建一个1分钟的测试任务
2. 启动计时器，观察倒计时
3. 等待完成，进入休息界面
4. 观察休息计时器自动返回

## 故障排除

### 如果计时器不启动
1. 检查控制台是否有启动日志
2. 尝试重启应用（非热重载）
3. 检查任务时长设置是否正确

### 如果休息界面不自动返回
1. 检查控制台是否有"🏁 休息时间结束，自动返回"日志
2. 确认休息计时器是否正常倒计时
3. 检查onComplete回调是否正确

## 测试报告模板

**测试时间**：
**测试功能**：
**测试结果**：
- [ ] 休息界面自动返回
- [ ] 计时器启动
- [ ] 计时器精度
- [ ] 控制功能

**发现问题**：
**建议改进**：
