# OneDay Flutter 应用 - 综合测试指南

本文档整合了 OneDay Flutter 应用的所有功能测试指南，为团队提供统一的测试流程和验证标准。

---

## 目录
1. [照片查看器手势功能测试](#1-照片查看器手势功能测试)
2. [分类树功能测试](#2-分类树功能测试)
3. [Cover模式功能测试](#3-cover模式功能测试)
4. [拖拽功能测试](#4-拖拽功能测试)
5. [UI组件功能测试](#5-ui组件功能测试)
6. [故障排除指南](#6-故障排除指南)

---

## 1. 照片查看器手势功能测试

### 🎯 功能概述
照片查看器支持多种手势交互，包括垂直手势分享/预览、模式切换、图片平移等功能。

### 🧪 基础手势测试

#### 1.1 进入照片查看器
1. 启动OneDay应用
2. 导航到"记忆宫殿"页面
3. 选择任意一个宫殿进入详情页面
4. 确保进入了照片查看器界面（黑色背景，全屏图片显示）

#### 1.2 快速功能验证（推荐）
1. 在顶部工具栏找到**橙色的测试按钮**（🐛图标）
2. 点击测试按钮，应该直接触发分享功能
3. 查看控制台输出：`🧪 [TEST] 测试按钮被点击`

#### 1.3 上滑分享功能测试
**操作**：在当前显示的图片上，从下往上快速滑动（滑动距离需超过50像素）

**预期结果**：
- ✅ 系统分享界面弹出
- ✅ 分享内容包含场景标题和知识点列表
- ✅ 控制台显示：`📤 触发上滑分享手势`

#### 1.4 下滑预览功能测试
**操作**：在当前显示的图片上，从上往下快速滑动（滑动距离需超过50像素）

**预期结果**：
- ✅ 底部预览栏展开，显示相册中的其他图片缩略图
- ✅ 再次下滑应该收起预览栏
- ✅ 控制台显示：`📖 触发下滑预览手势`

### 🔄 模式切换测试

#### 1.5 Contain模式验证（默认状态）
**当前状态**：图片处于Contain模式（完整显示，可能有黑边）

**预期行为**：
- ✅ 顶部工具栏可见
- ✅ 底部预览栏可见
- ✅ 上滑触发分享功能
- ✅ 下滑展开/收起底部预览栏
- ✅ 控制台显示：`🖱️ [DEBUG] 当前图片显示模式: Contain模式（全局预览）`

#### 1.6 Cover模式切换测试
**操作**：在当前图片上**双击**

**预期变化**：
- 🎭 图片切换到Cover模式（填满屏幕，可能被裁剪）
- 🎭 顶部工具栏自动隐藏（向上滑出动画）
- 🎭 底部预览栏自动隐藏（向下滑出动画）
- 🎭 控制台显示：`🎭 模式切换: 进入Cover模式 - 隐藏UI`

#### 1.7 Cover模式手势验证
**当前状态**：图片处于Cover模式（全屏显示）

**预期行为**：
- ❌ 顶部工具栏隐藏（不可见）
- ❌ 底部预览栏隐藏（不可见）
- ✅ 上滑触发图片平移（不是分享功能）
- ❌ 下滑预览功能被禁用
- ✅ 知识点标记功能正常（点击添加气泡）
- ✅ 双击可切换回Contain模式

### 🚫 边界检测测试

#### 1.8 垂直边界检测
**适用模式**：仅在Cover模式下

**向上拖拽测试**：
- ✅ 图片向上移动，显示图片的下半部分
- ✅ 当图片底边到达屏幕底边时，图片立即停止移动
- ✅ 控制台显示：`🖱️ 🚫 Cover模式垂直平移: 到达底部边界 - 停止移动`

**向下拖拽测试**：
- ✅ 图片向下移动，显示图片的上半部分
- ✅ 当图片顶边到达屏幕顶边时，图片立即停止移动
- ✅ 控制台显示：`🖱️ 🚫 Cover模式垂直平移: 到达顶部边界 - 停止移动`

### ⚡ 手势冲突处理测试

#### 1.9 多手势兼容性验证
1. **水平滑动测试**：左右滑动切换图片，确保不会误触垂直手势
2. **缩放测试**：双指缩放图片，确保不会误触垂直手势
3. **平移测试**：单指拖拽移动图片，确保不会误触垂直手势
4. **斜向滑动测试**：斜向滑动，确保只有明显的垂直分量才会触发垂直手势

**手势识别逻辑**：
- 垂直分量必须是水平分量的1.5倍以上才被识别为垂直手势
- 初始移动距离需超过15像素才开始方向判断
- 垂直滑动距离需超过50像素才触发功能

---

## 2. 分类树功能测试

### 🎯 功能概述
分类树支持搜索、添加、编辑、删除、层级调整等完整的分类管理功能。

### 🧪 基础功能测试

#### 2.1 打开分类抽屉
**操作**：点击主页面左上角的菜单按钮（☰）

**预期行为**：
- ✅ 分类抽屉从左侧滑出
- ✅ 显示升级后的顶部操作栏
- ✅ 右侧显示搜索🔍和添加➕图标
- ✅ 分类树正常显示，保持原有层级结构

#### 2.2 搜索功能测试
**操作**：点击顶部的🔍搜索图标

**预期行为**：
- ✅ 搜索框展开显示
- ✅ 输入框自动获得焦点
- ✅ 显示"搜索分类..."提示文本
- ✅ 搜索图标变为关闭图标

**搜索过滤测试**：
- 输入"学"
- ✅ 实时过滤显示包含"学"的分类
- ✅ 非匹配项被隐藏
- ✅ 匹配项正常显示层级关系

#### 2.3 添加分类测试
**操作**：点击顶部的➕添加图标

**预期行为**：
- ✅ 在分类列表末尾创建新的编辑节点
- ✅ 节点显示为蓝色（新建标识）
- ✅ 输入框自动获得焦点
- ✅ 默认文本"新分类"被全选

**保存测试**：
- 输入"🎨 艺术"并按回车键
- ✅ 新分类保存成功
- ✅ 节点颜色变为正常黑色
- ✅ 自动退出编辑模式

### 🔄 层级调整测试

#### 2.4 增加缩进测试
**操作**：
1. 长按"小学"节点进入编辑模式
2. 点击➡️增加缩进按钮

**预期行为**：
- ✅ "小学"成为"学前"的子节点
- ✅ 缩进层级增加一级
- ✅ 层级变化立即生效
- ✅ 父节点自动展开显示新的子节点

#### 2.5 减少缩进测试
**操作**：
1. 长按刚才移动的"小学"节点
2. 点击⬅️减少缩进按钮

**预期行为**：
- ✅ "小学"重新成为"🏫 学校"的直接子节点
- ✅ 缩进层级减少一级
- ✅ 恢复到原有的层级结构

### 📝 子分类添加测试

#### 2.6 添加子分类功能
**操作**：右键点击任意分类节点，选择"添加子分类"

**预期行为**：
- ✅ 父节点自动展开
- ✅ 在父节点下创建新的子分类
- ✅ 新子分类自动进入编辑模式
- ✅ 层级自动设置为父分类层级+1

---

## 3. Cover模式功能测试

### 🎯 功能概述
Cover模式提供沉浸式的全屏体验，自动隐藏UI元素，专注于图片内容和标记功能。

### 🧪 沉浸式体验测试

#### 3.1 进入Cover模式
**操作**：在照片查看器中双击图片

**预期变化**：
- 🎭 图片切换到Cover模式（填满屏幕）
- 🎭 顶部工具栏自动隐藏
- 🎭 底部预览栏自动隐藏
- 🎭 控制台显示：`🎭 模式切换: 进入Cover模式 - 隐藏UI`

#### 3.2 Cover模式交互测试
**当前状态**：图片处于Cover模式

**交互验证**：
- ✅ 上滑触发图片平移（不是分享功能）
- ✅ 知识点标记功能正常（点击添加气泡）
- ✅ 双击可切换回Contain模式
- ❌ 下滑预览功能被禁用
- ❌ 顶部和底部工具栏隐藏

#### 3.3 退出Cover模式
**操作**：在Cover模式下双击图片

**预期变化**：
- 🎭 图片切换回Contain模式
- 🎭 顶部工具栏重新显示
- 🎭 底部预览栏重新显示
- 🎭 控制台显示：`🎭 模式切换: 退出Cover模式 - 显示UI`

---

## 4. 拖拽功能测试

### 🎯 功能概述
支持分类树节点的拖拽重排序、浮动按钮自动隐藏、拖拽手柄移除等优化功能。

### 🧪 拖拽排序测试

#### 4.1 长按拖拽启动
**操作**：长按任意分类节点

**预期行为**：
- ✅ 开始拖拽操作（不再进入编辑模式）
- ✅ 显示拖拽预览（蓝色背景的节点副本）
- ✅ 原位置显示半透明占位符
- ✅ 控制台显示：`🔄 开始拖拽节点: [节点名称]`

#### 4.2 拖拽位置检测
**操作**：拖拽节点到不同位置

**预期行为**：
- ✅ 合法放置位置显示Notion风格的浅色高亮
- ✅ 非法位置显示禁止放置的视觉提示
- ✅ 根据拖拽位置自动确定新的顺序和层级

#### 4.3 浮动按钮自动隐藏
**操作**：打开侧边栏/抽屉

**预期行为**：
- ✅ 浮动操作按钮自动隐藏
- ✅ 关闭侧边栏后浮动按钮重新显示
- ✅ 动画过渡流畅（200ms）

### 🎨 拖拽手柄移除测试

#### 4.4 界面简化验证
**检查项目**：
- ✅ 记忆宫殿拖拽排序：移除拖拽图标，使用长按手势
- ✅ 相册创建拖拽排序：移除拖拽图标，使用长按手势
- ✅ 界面更加简洁，符合极简设计原则

---

## 5. UI组件功能测试

### 🎯 功能概述
测试各种UI组件的交互功能，包括弹出菜单、三点菜单、词汇管理等。

### 🧪 弹出菜单测试

#### 5.1 三点菜单功能
**操作**：点击相册卡片右下角的三点菜单

**预期行为**：
- ✅ 弹出菜单显示
- ✅ 包含"编辑"、"分享"、"删除"选项
- ✅ 每个选项都有对应图标
- ✅ 删除选项显示为红色

#### 5.2 上下文菜单测试
**操作**：右键点击分类树节点

**预期行为**：
- ✅ 显示上下文菜单
- ✅ 包含"编辑"、"添加子分类"、"删除"等选项
- ✅ 菜单项按逻辑顺序排列

### 📚 词汇管理测试

#### 5.3 词汇创建测试
**操作**：点击词汇管理页面的"+"按钮

**预期行为**：
- ✅ 底部表单弹出
- ✅ 表单包含单词、发音、含义、例句等字段
- ✅ 支持分类选择
- ✅ 保存后集成到学习系统

---

## 6. 故障排除指南

### 🚨 常见问题诊断

#### 6.1 手势不工作
**检查步骤**：
1. **检查控制台日志**：
   - 如果没有看到 `🖱️ [DEBUG] 垂直手势开始被调用`，说明手势检测器没有被触发
   - 如果看到 `🖱️ [DEBUG] 忽略非当前页面的手势`，说明页面索引不匹配

2. **检查手势方向**：
   - 确保滑动是明显的垂直方向（垂直分量 > 水平分量 × 1.2）
   - 滑动距离需要超过10像素才开始方向判断

3. **检查触发阈值**：
   - 垂直滑动距离需要超过50像素才触发功能
   - 可以在日志中看到最终的deltaY值

4. **使用测试按钮**：
   - 点击顶部的橙色测试按钮直接测试分享功能
   - 这可以验证分享功能本身是否正常

#### 6.2 拖拽功能异常
**检查步骤**：
1. **确认长按时间**：长按时间需要超过500ms
2. **检查拖拽预览**：应该显示蓝色背景的节点副本
3. **验证目标检测**：合法位置应该有高亮提示

#### 6.3 模式切换问题
**检查步骤**：
1. **验证双击功能**：确保可以通过双击切换模式
2. **检查控制台日志**：查看模式切换相关日志
3. **确认UI状态**：验证工具栏的显示/隐藏状态

### 📊 性能指标

#### 6.4 性能基准
- **搜索响应时间**：< 100ms
- **层级调整响应时间**：< 50ms
- **数据保存时间**：< 200ms
- **UI动画流畅度**：60fps
- **手势识别延迟**：< 16ms

### 🔍 调试日志说明

#### 6.5 关键日志模式
```
🖱️ [DEBUG] 垂直手势开始被调用 - 手势检测器被触发
🖱️ ✅ 确认为垂直手势 - 确认为垂直方向
📤 触发上滑分享手势 - 上滑分享被触发
🎭 模式切换: 进入Cover模式 - 隐藏UI
🔄 开始拖拽节点: [节点名称] - 拖拽操作开始
💾 已保存分类树数据到本地存储 - 数据保存成功
```

---

**文档状态**: ✅ 已完成  
**更新日期**: 2025年1月  
**涵盖测试**: 6大功能模块的完整测试流程  
**文档用途**: 统一测试标准和故障排除指南
