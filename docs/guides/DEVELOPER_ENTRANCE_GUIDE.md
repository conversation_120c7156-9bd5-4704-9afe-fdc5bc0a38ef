# 开发者快速入口使用指南

## 🚀 功能介绍

为了方便开发测试，我们在引导页添加了一个隐蔽的开发者快速入口。这个功能仅在Debug模式下启用，发布版本中会自动隐藏。

## 🎯 使用方法

### 激活开发者模式
1. 在引导页中连续快速点击**Logo区域** 5次
2. 第3次点击后会出现提示："再点击2次激活开发者模式"
3. 第5次点击后会弹出开发者面板，并显示确认提示

### 开发者面板功能
- **跳转主页**：直接进入应用主界面，跳过所有引导流程
- **跳转登录**：跳转到登录页面
- **关闭面板**：点击面板右上角的×号关闭

## 🔧 技术实现

### 触发机制
- 使用`kDebugMode`条件编译，确保仅在Debug模式下启用
- 连续点击Logo区域5次激活（3秒内有效）
- 提供视觉反馈（小圆点指示器）和触觉反馈

### 安全性保证
- **仅Debug模式**：使用Flutter的`kDebugMode`标志
- **自动隐藏**：Release版本中完全不可见
- **隐蔽性**：需要连续点击5次才能激活

## 📦 发布前清理

### 方法一：保留功能（推荐）
由于使用了`kDebugMode`条件编译，可以直接发布，Release版本中会自动隐藏。

### 方法二：完全删除（可选）
如果需要完全删除代码，请删除以下部分：

#### onboarding_page.dart
```dart
// 删除这些变量
int _logoTapCount = 0;
bool _showDeveloperPanel = false;
DateTime? _lastTapTime;

// 删除这些方法
void _onLogoTap() { ... }
Widget _buildDeveloperPanel() { ... }

// 修改build方法中的Logo部分，移除GestureDetector
// 修改Stack为Column，移除开发者面板
```

#### notion_style_onboarding_page.dart
```dart
// 同样删除对应的变量、方法和UI组件
```

## ⚠️ 注意事项

1. **仅在Debug模式**：此功能只在开发调试时可用
2. **不影响生产**：Release版本不会包含此功能
3. **便于测试**：可快速跳过引导流程，提高开发效率
4. **易于清理**：如需删除，代码位置明确，便于清理

## 🎨 自定义配置

如需修改激活方式，可调整以下参数：

```dart
// 修改点击次数（当前为5次）
if (_logoTapCount >= 5) { ... }

// 修改重置时间（当前为3秒）
if (_lastTapTime != null && now.difference(_lastTapTime!).inSeconds > 3) { ... }

// 修改提示时机（当前为第3次点击）
} else if (_logoTapCount == 3) { ... }
```

---

**结论**：这个开发者入口设计兼顾了开发便利性和发布安全性，推荐保留代码并依赖条件编译自动隐藏。 