# TimeBox计时器小窗口使用指南

## 功能介绍
TimeBox计时器小窗口功能让您在使用OneDay应用的其他功能时，也能方便地监控和控制正在进行的学习任务。

## 如何使用

### 1. 启动计时器
1. 在时间盒子页面找到要学习的任务
2. 点击任务卡片右下角的"继续"按钮启动计时器
3. 计时器会在任务下方显示内联计时器界面

### 2. 打开小窗口
1. 在内联计时器界面中，找到右侧的小窗口图标按钮（📱图标）
2. 点击该按钮，小窗口会立即显示在屏幕上
3. 小窗口默认出现在屏幕左上角位置

### 3. 移动小窗口
1. 用手指按住小窗口的任意位置
2. 拖拽到您希望的位置
3. 松开手指，小窗口会固定在新位置
4. 系统会自动确保窗口不会移出屏幕边界

### 4. 使用小窗口监控计时器
小窗口提供以下功能：
- **查看任务信息**：显示当前任务标题
- **监控时间**：实时显示剩余时间（MM:SS格式）
- **纯监控设计**：专注于时间显示，不提供控制功能

**注意**：小窗口采用简化设计，如需控制计时器（暂停/恢复/停止），请返回主界面操作。

### 5. 关闭小窗口
有两种方式关闭小窗口：
1. **手动关闭**：点击小窗口右上角的圆形关闭按钮（×）
2. **自动关闭**：停止计时器时，小窗口会自动关闭

## 小窗口界面说明

### 第一行
- 左侧：当前任务标题（过长会自动截断）
- 右侧：小型关闭按钮

### 第二行
- 居中显示：剩余时间（MM:SS格式，等宽字体）

### 整体设计
- **尺寸**：160x60像素，超紧凑设计
- **透明度**：60%透明度，减少视野干扰
- **布局**：简洁的双行布局，信息一目了然

## 使用技巧

### 1. 最佳位置
- 建议将小窗口放在屏幕边缘，避免遮挡主要内容
- 可以根据当前使用的功能调整窗口位置

### 2. 多任务使用
- 小窗口允许您在学习时使用应用的其他功能
- 例如：查看其他任务、浏览统计数据、创建新任务等

### 3. 时间监控
- 实时显示剩余时间，方便随时查看学习进度
- 等宽字体确保数字显示稳定清晰

### 4. 简化操作
- 专注于时间显示功能，避免误操作
- 需要控制计时器时返回主界面操作

## 注意事项

1. **单任务限制**：同时只能有一个任务的计时器运行
2. **状态同步**：小窗口与主计时器完全同步，修改会实时反映
3. **自动关闭**：停止计时器时小窗口会自动关闭
4. **边界保护**：拖拽时系统会防止窗口移出屏幕
5. **透明设计**：小窗口采用半透明设计，减少对视野的影响

## 常见问题

**Q: 小窗口不显示怎么办？**
A: 确保已经启动了计时器，只有在计时器运行时才能显示小窗口。

**Q: 可以同时显示多个小窗口吗？**
A: 不可以，系统设计为同时只支持一个计时器和一个小窗口。

**Q: 小窗口会影响其他功能吗？**
A: 不会，小窗口采用非模态设计，不会阻塞其他操作。

**Q: 如何调整小窗口大小？**
A: 当前版本小窗口大小固定，后续版本可能会支持大小调整。

**Q: 小窗口位置会被记住吗？**
A: 当前版本不会保存位置，每次打开都会回到默认位置。

## 反馈建议
如果您在使用过程中遇到问题或有改进建议，欢迎反馈给开发团队。
