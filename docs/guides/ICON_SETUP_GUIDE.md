# OneDay 应用图标设置指南

## 📱 图标设计说明

您提供的图标设计非常棒！这是一个现代化的全球网络风格图标，中心有字母"D"，代表OneDay。

## 🎨 图标规格要求

### 设计规范
- **风格**: 简洁现代，线条清晰
- **颜色**: 建议使用 #2E7EED (品牌蓝色) 或深色 #37352F
- **背景**: 透明背景或白色背景
- **格式**: PNG（支持透明度）

### 尺寸要求

#### iOS 图标尺寸
```
20x20px (1x, 2x, 3x)    - 通知图标
29x29px (1x, 2x, 3x)    - 设置图标  
40x40px (1x, 2x, 3x)    - Spotlight搜索
60x60px (2x, 3x)        - 主屏幕图标(iPhone)
76x76px (1x, 2x)        - 主屏幕图标(iPad)
83.5x83.5px (2x)        - 主屏幕图标(iPad Pro)
1024x1024px (1x)        - App Store图标
```

#### Android 图标尺寸
```
48x48px   (mdpi)    - 基准尺寸
72x72px   (hdpi)    - 高密度
96x96px   (xhdpi)   - 超高密度
144x144px (xxhdpi)  - 超超高密度
192x192px (xxxhdpi) - 超超超高密度
```

#### 其他平台
```
16x16px    - Favicon
32x32px    - 小图标
64x64px    - 中等图标
128x128px  - 大图标
256x256px  - 高清图标
512x512px  - 超高清图标
```

## 📁 文件结构

请在项目根目录创建以下文件夹结构：

```
assets/
  icons/
    app_icon.png (512x512)
    app_icon_rounded.png (512x512, 圆角版本)
    icon_16.png
    icon_32.png
    icon_64.png
    icon_128.png
    icon_256.png
    icon_512.png
    icon_1024.png
    android/
      ic_launcher_48.png
      ic_launcher_72.png
      ic_launcher_96.png
      ic_launcher_144.png
      ic_launcher_192.png
    ios/
      <EMAIL> (20x20)
      <EMAIL> (40x40)
      <EMAIL> (60x60)
      <EMAIL> (29x29)
      <EMAIL> (58x58)
      <EMAIL> (87x87)
      <EMAIL> (40x40)
      <EMAIL> (80x80)
      <EMAIL> (120x120)
      <EMAIL> (120x120)
      <EMAIL> (180x180)
      <EMAIL> (76x76)
      <EMAIL> (152x152)
      <EMAIL> (167x167)
      <EMAIL> (1024x1024)
```

## 🛠️ 设置步骤

### 1. 创建图标文件
使用您提供的设计，创建上述所有尺寸的PNG文件。

### 2. 替换Android图标
将对应尺寸的图标文件复制到：
```
android/app/src/main/res/mipmap-mdpi/ic_launcher.png (48x48)
android/app/src/main/res/mipmap-hdpi/ic_launcher.png (72x72)  
android/app/src/main/res/mipmap-xhdpi/ic_launcher.png (96x96)
android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png (144x144)
android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png (192x192)
```

### 3. 替换iOS图标
将对应尺寸的图标文件复制到：
```
ios/Runner/Assets.xcassets/AppIcon.appiconset/
```

### 4. 更新iOS配置
编辑 `ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json`：

```json
{
  "images" : [
    {
      "idiom" : "iphone",
      "scale" : "2x",
      "size" : "20x20"
    },
    {
      "idiom" : "iphone", 
      "scale" : "3x",
      "size" : "20x20"
    },
    {
      "idiom" : "iphone",
      "scale" : "2x", 
      "size" : "29x29"
    },
    {
      "idiom" : "iphone",
      "scale" : "3x",
      "size" : "29x29" 
    },
    {
      "idiom" : "iphone",
      "scale" : "2x",
      "size" : "40x40"
    },
    {
      "idiom" : "iphone", 
      "scale" : "3x",
      "size" : "40x40"
    },
    {
      "idiom" : "iphone",
      "scale" : "2x",
      "size" : "60x60"
    },
    {
      "idiom" : "iphone",
      "scale" : "3x", 
      "size" : "60x60"
    },
    {
      "idiom" : "ipad",
      "scale" : "1x",
      "size" : "20x20"
    },
    {
      "idiom" : "ipad",
      "scale" : "2x",
      "size" : "20x20"
    },
    {
      "idiom" : "ipad",
      "scale" : "1x",
      "size" : "29x29"
    },
    {
      "idiom" : "ipad", 
      "scale" : "2x",
      "size" : "29x29"
    },
    {
      "idiom" : "ipad",
      "scale" : "1x",
      "size" : "40x40"
    },
    {
      "idiom" : "ipad",
      "scale" : "2x",
      "size" : "40x40"
    },
    {
      "idiom" : "ipad",
      "scale" : "1x", 
      "size" : "76x76"
    },
    {
      "idiom" : "ipad",
      "scale" : "2x",
      "size" : "76x76"
    },
    {
      "idiom" : "ipad",
      "scale" : "2x",
      "size" : "83.5x83.5"
    },
    {
      "idiom" : "ios-marketing",
      "scale" : "1x",
      "size" : "1024x1024"
    }
  ],
  "info" : {
    "author" : "xcode",
    "version" : 1
  }
}
```

## 🚀 自动化工具推荐

### flutter_launcher_icons 插件
可以使用这个插件自动生成所有尺寸的图标：

1. 在 `pubspec.yaml` 中添加：
```yaml
dev_dependencies:
  flutter_launcher_icons: ^0.13.1

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icons/app_icon.png"
  windows:
    generate: true
    image_path: "assets/icons/app_icon.png"
  macos:
    generate: true
    image_path: "assets/icons/app_icon.png"
```

2. 运行命令：
```bash
flutter pub get
flutter pub run flutter_launcher_icons:main
```

## ✅ 验证步骤

1. **Android**: 在模拟器或真机上安装应用，检查主屏幕图标
2. **iOS**: 在模拟器或真机上安装应用，检查主屏幕图标  
3. **应用内**: 确认启动页和引导页的Logo显示正确

## 🎨 设计建议

基于您提供的图标设计：

1. **保持简洁**: 图标在小尺寸下仍需清晰可辨
2. **对比度**: 确保在不同背景下都有良好的可视性
3. **一致性**: 所有尺寸保持设计一致性
4. **适配性**: 考虑iOS的圆角和Android的材料设计

您的图标设计非常符合现代应用的审美，全球网络的概念很好地体现了OneDay的跨平台学习理念！ 