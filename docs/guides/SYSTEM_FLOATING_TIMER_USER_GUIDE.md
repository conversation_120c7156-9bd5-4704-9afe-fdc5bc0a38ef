# OneDay系统级浮动计时器使用指南

## 功能介绍
OneDay应用的TimeBox计时器现在支持系统级浮动窗口功能，让您在使用其他功能或切换页面时也能持续监控学习时间。

## 新功能特性

### 1. 标题居中显示
- **视觉优化**：任务标题现在在小窗口中居中显示
- **更好的可读性**：标题文字在可用空间内完美居中
- **保持功能**：仍支持长标题的文本截断功能

### 2. 系统级浮动显示
- **跨页面显示**：在OneDay应用内切换页面时保持显示
- **持续监控**：提供不间断的时间监控体验
- **智能切换**：在页面内窗口和系统级窗口间自由切换

## 使用方法

### 基础操作

#### 1. 启动计时器
1. 在时间盒子页面选择要学习的任务
2. 点击任务的"继续"按钮启动计时器
3. 计时器会在任务下方显示内联界面

#### 2. 显示小窗口
1. 在内联计时器中找到小窗口图标按钮（📱）
2. 点击按钮，小窗口会显示在页面上
3. 此时显示的是"页面内小窗口"

#### 3. 切换到系统级窗口
1. 再次点击小窗口图标按钮
2. 页面内小窗口会消失
3. 系统级浮动窗口会出现在屏幕上
4. 现在您可以自由切换到其他页面，窗口会保持显示

### 高级功能

#### 窗口移动
- **拖拽移动**：按住窗口任意位置拖拽到合适位置
- **边界保护**：系统会防止窗口移出屏幕边界
- **位置记忆**：窗口位置会被记住

#### 模式切换
- **页面内模式**：窗口只在当前页面显示
- **系统级模式**：窗口在整个应用范围内显示
- **智能切换**：点击小窗口按钮在两种模式间切换

#### 状态同步
- **实时更新**：时间显示每秒更新
- **状态同步**：暂停/恢复状态实时反映
- **操作同步**：在主界面的操作会同步到浮动窗口

## 窗口界面说明

### 布局结构
- **第一行**：任务标题（居中显示）+ 关闭按钮（右上角）
- **第二行**：剩余时间（居中显示，MM:SS格式）

### 视觉特性
- **尺寸**：160×60像素，超紧凑设计
- **透明度**：60%透明度，减少视觉干扰
- **样式**：圆角设计，蓝色边框，符合OneDay风格

## 使用场景

### 1. 跨页面学习
- 启动计时器后切换到系统级窗口
- 在OneDay应用内浏览其他功能
- 随时查看学习进度，不错过任何时间

### 2. 多任务处理
- 在学习过程中查看其他任务
- 浏览统计数据和学习记录
- 创建新的学习计划

### 3. 专注学习
- 将窗口放在屏幕角落
- 最小化干扰的同时保持时间意识
- 专注于当前学习内容

## 操作技巧

### 1. 最佳位置
- **推荐位置**：屏幕右上角或左上角
- **避免遮挡**：不要放在重要内容区域
- **易于查看**：选择容易瞥见的位置

### 2. 模式选择
- **页面内模式**：适合在时间盒子页面内操作
- **系统级模式**：适合需要切换页面的场景
- **灵活切换**：根据使用需求随时切换

### 3. 时间管理
- **定期查看**：养成定期查看剩余时间的习惯
- **及时调整**：根据进度调整学习策略
- **专注提醒**：利用时间显示保持专注

## 权限说明

### Android设备
- **悬浮窗权限**：首次使用时可能需要授予悬浮窗权限
- **权限位置**：设置 → 应用管理 → OneDay → 权限管理 → 悬浮窗
- **重要性**：此权限是系统级显示的必要条件

### iOS设备
- **应用内有效**：iOS系统限制，浮动窗口仅在OneDay应用内有效
- **后台限制**：切换到其他应用时窗口会暂时隐藏
- **返回恢复**：返回OneDay应用时窗口会自动恢复

## 注意事项

### 1. 性能考虑
- **适度使用**：长时间显示可能影响设备性能
- **及时关闭**：学习结束后及时关闭浮动窗口
- **电池影响**：持续显示会增加电池消耗

### 2. 使用限制
- **单任务限制**：同时只能有一个计时器运行
- **权限依赖**：部分功能需要系统权限支持
- **平台差异**：iOS和Android功能略有不同

### 3. 最佳实践
- **合理放置**：选择不影响正常使用的位置
- **定期检查**：确保时间显示准确
- **及时反馈**：遇到问题及时反馈给开发团队

## 故障排除

### 常见问题

#### Q: 小窗口不显示怎么办？
A: 确保已启动计时器，检查是否授予了必要权限。

#### Q: 窗口无法拖拽移动？
A: 尝试重启应用，确保触摸功能正常。

#### Q: 时间显示不准确？
A: 检查系统时间设置，重启计时器。

#### Q: 切换页面后窗口消失？
A: 确保使用的是系统级模式，检查权限设置。

### 解决方案
1. **重启应用**：大多数问题可通过重启解决
2. **检查权限**：确保授予了必要的系统权限
3. **更新应用**：使用最新版本的OneDay应用
4. **联系支持**：持续问题请联系技术支持

## 反馈建议
如果您在使用过程中遇到问题或有改进建议，欢迎通过以下方式反馈：
- 应用内反馈功能
- 官方邮箱
- 用户社区

我们会持续优化这个功能，为您提供更好的时间管理体验！
