# OneDay自定义动作库使用指南

## 功能简介

OneDay应用的自定义动作库功能允许您创建个性化的PAO（Person-Action-Object）记忆动作库，用于具身记忆训练。您可以为A-Z每个字母设置专属的动作，让记忆训练更符合您的喜好和习惯。

## 快速开始

### 1. 创建您的第一个动作库

1. **进入动作库管理**
   - 打开OneDay应用
   - 进入"动作库"页面
   - 点击右上角的"更多选项"（三个点）
   - 选择"创建自定义动作库"

2. **填写基本信息**
   - 输入动作库名称（如"我的健身动作库"）
   - 添加描述（可选，如"专为宿舍健身设计的动作"）
   - 点击"创建"

3. **设置字母动作**
   - 创建成功后会自动进入编辑界面
   - 看到A-Z的字母网格
   - 点击任意字母开始设置动作

### 2. 编辑字母动作

1. **点击字母**
   - 在网格中点击要设置的字母
   - 弹出动作编辑对话框

2. **填写动作信息**
   - **动作中文名称**：如"俯卧撑"
   - **动作英文名称**：如"Push Up"
   - **动作分类**：选择合适的分类（健身、瑜伽等）
   - **适用场景**：选择"简单活动"或"集中训练"
   - **动作描述**：详细描述动作执行方法
   - **关键词**：用于PAO记忆的关键词（用逗号分隔）

3. **参考预设动作**
   - 如果有预设动作，会显示参考信息
   - 可以点击"复制"快速填入预设内容
   - 然后根据需要修改

4. **保存动作**
   - 填写完成后点击"保存"
   - 返回字母网格，可以看到该字母已设置

### 3. 批量操作

1. **从预设复制**
   - 在编辑页面点击"批量操作"
   - 选择"从预设动作库复制"
   - 一键复制所有A-Z的预设动作
   - 然后可以逐个修改

2. **清空所有动作**
   - 点击"批量操作" → "清空所有动作"
   - 重新开始设置

## 高级功能

### 1. 动作库管理

1. **查看所有动作库**
   - 动作库页面 → 更多选项 → "管理自定义动作库"
   - 查看所有已创建的动作库
   - 显示完成度和统计信息

2. **切换动作库**
   - 在管理界面点击动作库
   - 选择"选择使用"
   - 该动作库将成为当前使用的动作库

3. **编辑动作库**
   - 点击动作库进入编辑界面
   - 修改A-Z字母对应的动作

4. **复制动作库**
   - 在动作库菜单中选择"复制"
   - 创建一个副本进行修改

5. **删除动作库**
   - 在动作库菜单中选择"删除"
   - 确认后永久删除（无法恢复）

### 2. 导入导出功能

1. **导出动作库**
   - 在动作库菜单中选择"导出"
   - 动作库将保存为JSON文件到设备存储
   - 可以分享给其他用户

2. **导入动作库**
   - 动作库页面 → 更多选项 → "导入动作库"
   - 粘贴JSON数据
   - 点击"导入"完成

### 3. 在TimeBox训练中使用

1. **选择动作库**
   - 进入TimeBox具身记忆训练
   - 点击右上角的动作库图标
   - 选择要使用的动作库

2. **查看当前动作库**
   - 在训练界面可以看到当前使用的动作库名称
   - 显示为蓝色标签

3. **动作训练**
   - 系统会根据单词字母生成对应的动作序列
   - 使用您自定义的动作进行训练

## 使用技巧

### 1. 动作设计建议

- **简单活动**：适合在图书馆、宿舍等安静场所
- **集中训练**：适合在健身房、操场等运动场所
- **动作描述**：写得详细一些，方便记忆和执行
- **关键词**：选择与字母相关的英文单词

### 2. 分类建议

- **健身**：力量训练动作
- **瑜伽**：柔韧性和平衡动作
- **养生**：传统保健动作
- **拉伸**：办公室可做的简单动作
- **自定义**：其他类型的动作

### 3. 完成度管理

- 建议先设置常用字母的动作
- 逐步完善到26个字母全覆盖
- 可以先从简单动作开始

## 常见问题

### Q: 如何备份我的动作库？
A: 使用导出功能将动作库保存为JSON文件，妥善保存该文件。

### Q: 可以创建多少个动作库？
A: 没有数量限制，但建议根据实际需要创建，避免过多造成管理困难。

### Q: 动作库数据存储在哪里？
A: 数据存储在设备本地，不会上传到云端。

### Q: 如何分享动作库给朋友？
A: 导出动作库为JSON文件，通过微信、邮件等方式分享给朋友，朋友可以导入使用。

### Q: 删除动作库后能恢复吗？
A: 删除后无法恢复，建议删除前先导出备份。

### Q: 动作库不完整可以使用吗？
A: 可以使用，缺失的字母会使用系统预设动作。

## 最佳实践

1. **从模板开始**：先复制预设动作库，再根据需要修改
2. **分场景创建**：为不同场景（宿舍、健身房）创建不同动作库
3. **定期备份**：重要的动作库要定期导出备份
4. **逐步完善**：不必一次性设置完所有字母，可以逐步完善
5. **测试使用**：创建后在TimeBox训练中测试效果

## 技术支持

如果在使用过程中遇到问题，可以：
1. 查看应用内的帮助文档
2. 重启应用尝试解决
3. 检查设备存储空间是否充足
4. 确保应用版本为最新版本

---

享受您的个性化PAO记忆训练体验！🎯
