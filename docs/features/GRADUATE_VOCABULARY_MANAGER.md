# 考研词库管理功能

## 功能概述

考研词库管理功能为OneDay应用提供了完整的考研词汇选择和管理系统，允许用户从5530个考研核心词汇中选择需要学习的单词，并与TimeBox具身记忆训练功能无缝集成。

## 主要特性

### 1. 词汇分类管理
- **已选单词**：显示用户已选择学习的词汇
- **未选单词**：显示尚未选择的词汇
- **实时统计**：显示已选/未选单词数量和选择进度

### 2. 搜索功能
- 支持按单词拼写搜索
- 支持按中文释义搜索
- 实时过滤结果

### 3. 批量操作
- 批量选择单词
- 批量取消选择
- 全选当前标签页单词
- 选择模式切换

### 4. 单词详情展示
- 单词拼写
- 音标显示
- 中文释义
- 词性标注
- 选择状态指示

### 5. 统计信息
- 总单词数统计
- 已选单词数统计
- 未选单词数统计
- 选择进度百分比

## 技术实现

### 核心组件

#### 1. GraduateVocabularyManagerPage
主要的词库管理界面，提供：
- TabBar分类显示（已选/未选）
- 搜索栏
- 单词列表
- 批量操作工具栏

#### 2. VocabularyService扩展
新增的词汇管理方法：
- `getSelectedWordsFromProgress()` - 获取已选单词列表
- `getUnselectedWords()` - 获取未选单词列表
- `batchSelectWords()` - 批量选择单词
- `batchUnselectWords()` - 批量取消选择
- `getSelectionStatistics()` - 获取选择统计

#### 3. WordLearningProgress模型
扩展的学习进度模型，包含：
- `isSelected` - 单词选择状态
- `isLearned` - 学习完成状态
- `reviewCount` - 复习次数
- `masteryLevel` - 掌握程度

### 数据存储

使用SharedPreferences存储：
- 单词学习进度（包含选择状态）
- 本地缓存优化性能

### 与TimeBox集成

TimeBox具身记忆训练功能已集成词库管理：
- 自动使用用户选择的考研词汇
- 如果没有选择词汇，回退到默认词库
- 支持动态词汇更新

## 使用流程

### 1. 访问词库管理
1. 打开OneDay应用
2. 进入首页 → 词汇管理
3. 点击"考研词库管理"

### 2. 选择单词
1. 在"未选"标签页浏览单词
2. 点击单词右侧的"+"图标选择
3. 或使用批量选择功能

### 3. 管理已选单词
1. 切换到"已选"标签页
2. 查看已选择的单词
3. 点击"-"图标取消选择

### 4. 批量操作
1. 点击工具栏的批量选择图标
2. 选择多个单词
3. 使用底部工具栏进行批量操作

### 5. 查看统计
1. 点击统计图标
2. 查看详细的选择统计信息

## 界面设计

### 设计原则
- 遵循OneDay应用的Notion风格设计
- 简洁的黑白配色方案
- 清晰的视觉层次
- 直观的交互反馈

### 颜色方案
- 主色调：`#2F76DA`（蓝色）
- 文本色：`#37352F`（深灰）
- 边框色：`#E3E2E0`（浅灰）
- 背景色：`#F7F6F3`（米白）

### 交互反馈
- 选择状态的视觉指示
- 批量操作的进度提示
- 操作成功的SnackBar提示

## 性能优化

### 1. 数据缓存
- 词汇数据本地缓存
- 分批加载大数据集
- 搜索结果缓存

### 2. UI优化
- 虚拟滚动列表
- 延迟加载
- 状态管理优化

## 扩展性

### 未来功能
- 自定义词汇分类
- 学习计划制定
- 进度同步云端
- 社区词汇分享

### 技术扩展
- 支持更多词库
- 多语言支持
- 离线功能增强
- AI推荐算法

## 测试

### 单元测试
- 词汇服务功能测试
- 数据模型序列化测试
- 批量操作逻辑测试

### 集成测试
- 页面导航测试
- 数据持久化测试
- TimeBox集成测试

## 文件结构

```
lib/features/vocabulary/
├── graduate_vocabulary_manager_page.dart  # 主界面
├── vocabulary_service.dart                # 服务层（扩展）
├── word_model.dart                        # 数据模型
└── vocabulary_cache_manager.dart          # 缓存管理

test/
└── graduate_vocabulary_manager_test.dart  # 单元测试
```

## 依赖关系

- Flutter SDK
- shared_preferences（数据存储）
- flutter_riverpod（状态管理）
- go_router（路由导航）
