# 帮助与反馈功能文档

## 概述

帮助与反馈功能为OneDay应用用户提供了完整的支持和反馈渠道，包括常见问题解答、用户反馈提交、开发者联系方式、应用信息展示和用户指南入口。

## 功能特性

### 1. 常见问题 (FAQ)
- **可展开/折叠的问题列表**：用户可以点击问题查看详细答案
- **涵盖核心功能**：包括时间盒子、知忆相册、学习统计、动觉记忆训练、数据备份等
- **清晰的问答格式**：问题和答案分层显示，便于阅读

### 2. 用户反馈提交
- **反馈类型选择**：功能建议、问题反馈、使用体验、其他
- **详细描述输入**：多行文本输入框，支持详细描述
- **草稿自动保存**：输入内容自动保存到本地，防止意外丢失
- **Enter键提交**：支持键盘Enter键直接提交反馈
- **提交状态反馈**：显示提交进度和结果消息

### 3. 联系开发者
- **多平台社交媒体**：小红书、抖音、微博、B站、知乎
- **统一设计风格**：每个平台都有对应的图标和颜色
- **点击跳转**：点击后可跳转到对应的社交媒体平台（待实现）

### 4. 应用信息显示
- **应用名称**：OneDay
- **版本信息**：自动获取当前应用版本号
- **开发团队**：OneDay团队
- **应用简介**：让每一天都充满收获

### 5. 用户指南
- **快速入门**：基本功能和使用方法
- **功能详解**：核心功能深入介绍
- **学习方法**：高效学习技巧和记忆方法
- **常见问题**：更多问题和解决方案

## 设计规范

### UI设计
- **Notion风格**：采用Notion风格设计，简洁清晰
- **白色背景**：使用`Colors.white/#FFFFFF`作为主背景色
- **12px圆角**：所有卡片和容器使用12px圆角边框
- **分层卡片布局**：每个功能模块独立显示为卡片
- **统一颜色方案**：遵循OneDay应用的整体设计语言

### 交互设计
- **SnackBar反馈**：使用SnackBar显示操作反馈，持续1秒
- **自动保存**：反馈内容失去焦点时自动保存草稿
- **键盘支持**：支持Enter键直接提交反馈
- **响应式布局**：适配不同屏幕尺寸

## 技术实现

### 文件结构
```
oneday/lib/features/help_feedback/
├── help_feedback_page.dart          # 主页面实现
```

### 路由配置
- **路由路径**：`/help-feedback`
- **路由名称**：`help-feedback`
- **导航方式**：从个人中心页面的"帮助与反馈"菜单项进入

### 数据存储
- **SharedPreferences**：用于保存反馈草稿和反馈类型选择
- **本地存储键**：
  - `feedback_draft`：反馈内容草稿
  - `feedback_type`：选择的反馈类型

### 依赖包
- `shared_preferences`：本地数据存储
- `package_info_plus`：获取应用版本信息

## 使用流程

### 用户反馈流程
1. 用户进入"我的"页面
2. 点击"帮助与反馈"菜单项
3. 选择反馈类型（功能建议、问题反馈等）
4. 在文本框中输入详细描述
5. 点击"提交反馈"按钮或按Enter键
6. 系统显示提交成功消息
7. 反馈内容被清空，草稿被删除

### 常见问题查看流程
1. 进入帮助与反馈页面
2. 在"常见问题"部分找到相关问题
3. 点击问题展开查看详细答案
4. 可以展开多个问题同时查看

## 测试覆盖

### 单元测试
- 页面组件渲染测试
- 功能模块显示测试
- 用户交互测试
- 数据存储测试

### 测试文件
```
oneday/test/features/help_feedback/
├── help_feedback_page_test.dart     # 页面测试
```

## 未来扩展

### 待实现功能
1. **实际社交媒体跳转**：实现真实的社交媒体平台跳转
2. **用户指南页面**：创建详细的用户指南和教程页面
3. **反馈数据上传**：将用户反馈上传到服务器
4. **在线客服集成**：集成在线客服系统
5. **多语言支持**：支持中英文切换

### 优化方向
1. **搜索功能**：在常见问题中添加搜索功能
2. **反馈分类统计**：统计不同类型反馈的数量
3. **用户满意度调查**：添加满意度评分功能
4. **离线支持**：支持离线查看常见问题

## 维护说明

### 更新常见问题
在`help_feedback_page.dart`文件中的`_faqItems`列表中添加或修改问题和答案。

### 更新联系方式
在`_socialContacts`列表中修改社交媒体平台信息，包括名称、图标、颜色和描述。

### 版本信息更新
版本信息通过`package_info_plus`包自动获取，无需手动更新。

---

**最后更新**：2025-07-17
**维护者**：OneDay开发团队
