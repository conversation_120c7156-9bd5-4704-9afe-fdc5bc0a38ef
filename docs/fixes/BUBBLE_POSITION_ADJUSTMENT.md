# 知识气泡定位圆点位置调整

## 调整需求

用户要求将知识气泡的定位圆点位置上移一个指针连线的距离（8px）。

## 调整分析

### 气泡结构回顾
```
Column(
  children: [
    文本框,     // 高度：21px
    指针连线,   // 高度：8px  
    定位圆点,   // 高度：4px
  ]
)
总高度：33px
```

### 位置计算

**调整前**：
- 定位圆点中心对准基准位置
- 圆点中心距离Column顶部：21px + 8px + 2px = 31px
- 偏移比例：31px / 33px ≈ 0.94
- FractionalTranslation：`Offset(-0.5, -0.94)`

**调整后**：
- 连线顶部（文本框底部）对准基准位置
- 连线顶部距离Column顶部：21px
- 偏移比例：21px / 33px ≈ 0.64
- FractionalTranslation：`Offset(-0.5, -0.64)`

**实际效果**：
- 定位圆点向上移动了：(0.94 - 0.64) × 33px = 0.30 × 33px ≈ 10px
- 这个距离略大于连线高度8px，因为还包含了圆点半径的调整

## 代码修改

### 1. 锚点气泡定位调整
**文件位置**：`lib/features/memory_palace/scene_detail_page.dart`
**行数**：1710-1713

**修改前**：
```dart
translation: const Offset(
  -0.5, // 水平居中
  -0.94, // 让定位圆点中心对准锚点位置
),
```

**修改后**：
```dart
translation: const Offset(
  -0.5, // 水平居中
  -0.64, // 让连线顶部对准锚点位置（圆点上移8px）
),
```

### 2. 临时气泡定位调整
**文件位置**：`lib/features/memory_palace/scene_detail_page.dart`
**行数**：1760-1763

**修改前**：
```dart
translation: const Offset(
  -0.5, // 水平居中
  -0.94, // 让定位圆点中心对准点击位置
),
```

**修改后**：
```dart
translation: const Offset(
  -0.5, // 水平居中
  -0.64, // 让连线顶部对准点击位置（圆点上移8px）
),
```

### 3. 验证函数注释更新
**文件位置**：`lib/features/memory_palace/scene_detail_page.dart`
**行数**：2789-2791

**修改前**：
```dart
print('🎯 [位置验证] 注意：定位圆点通过FractionalTranslation(-0.5, -0.94)精确对准点击位置');
```

**修改后**：
```dart
print('🎯 [位置验证] 注意：定位圆点通过FractionalTranslation(-0.5, -0.64)上移8px，连线顶部对准点击位置');
```

## 调整效果

### 视觉变化
- **定位圆点**：从原来对准点击位置，变为在点击位置下方8px处
- **连线顶部**：现在精确对准点击位置
- **文本框底部**：也对准点击位置（与连线顶部重合）

### 用户体验
- 点击位置现在对应连线与文本框的交界处
- 定位圆点作为视觉指示器，位于点击位置下方
- 保持了临时气泡和最终锚点的位置一致性

## 技术细节

### 偏移值计算公式
```
新偏移值 = -(目标位置距Column顶部的距离) / Column总高度
```

对于连线顶部对准基准位置：
```
偏移值 = -21px / 33px = -0.636... ≈ -0.64
```

### 一致性保证
- 临时气泡和锚点气泡使用相同的偏移值
- 确保用户看到的临时位置就是最终锚点位置
- 维护了整体UI的一致性

## 总结

通过将FractionalTranslation的Y偏移值从-0.94调整为-0.64，成功实现了定位圆点上移8px的需求。现在连线顶部精确对准用户的点击位置，而定位圆点作为视觉指示器位于点击位置下方，提供了清晰的位置标识。

这个调整保持了临时气泡和锚点气泡的一致性，确保用户获得准确的视觉反馈。
