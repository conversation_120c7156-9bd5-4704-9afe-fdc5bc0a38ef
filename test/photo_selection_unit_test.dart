import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/photo_album/photo_album_creator_page.dart';
import 'package:oneday/utils/safe_area_helper.dart';

void main() {
  group('Photo Selection Unit Tests', () {
    testWidgets('PhotoAlbumCreatorPage should load correctly', (WidgetTester tester) async {
      // 创建测试用的PhotoAlbumCreatorPage
      await tester.pumpWidget(
        MaterialApp(
          home: PhotoAlbumCreatorPage(),
        ),
      );

      // 验证页面已加载
      expect(find.text('创建知忆相册'), findsOneWidget);

      // 验证照片选择按钮存在
      final photoSelectButton = find.byIcon(Icons.add_photo_alternate_outlined);
      expect(photoSelectButton, findsOneWidget);

      print('✅ PhotoAlbumCreatorPage loads correctly with photo selection button');
    });

    testWidgets('SafeAreaHelper should detect Dynamic Island correctly', (WidgetTester tester) async {
      // 创建一个测试widget来测试SafeAreaHelper
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // 模拟Dynamic Island设备的MediaQuery数据
              return MediaQuery(
                data: const MediaQueryData(
                  size: Size(430, 932), // iPhone 15 Pro Max size
                  padding: EdgeInsets.only(top: 59.0, bottom: 34.0), // Dynamic Island padding
                ),
                child: Scaffold(
                  body: Builder(
                    builder: (context) {
                      final deviceInfo = SafeAreaHelper.getDeviceInfo(context);
                      
                      // 验证Dynamic Island检测
                      expect(deviceInfo['hasDynamicIsland'], true);
                      expect(deviceInfo['topSafeArea'], 59.0);
                      
                      return const Text('Test');
                    },
                  ),
                ),
              );
            },
          ),
        ),
      );

      print('✅ SafeAreaHelper correctly detects Dynamic Island');
    });

    testWidgets('SafeAreaScaffold should handle Dynamic Island properly', (WidgetTester tester) async {
      // 测试SafeAreaScaffold组件
      await tester.pumpWidget(
        MaterialApp(
          home: MediaQuery(
            data: const MediaQueryData(
              size: Size(430, 932),
              padding: EdgeInsets.only(top: 59.0, bottom: 34.0),
            ),
            child: SafeAreaScaffold(
              title: 'Test Title',
              body: const Text('Test Body'),
            ),
          ),
        ),
      );

      // 验证SafeAreaScaffold正确渲染
      expect(find.text('Test Title'), findsOneWidget);
      expect(find.text('Test Body'), findsOneWidget);
      expect(find.byType(SafeArea), findsAtLeastNWidgets(1));

      print('✅ SafeAreaScaffold handles Dynamic Island correctly');
    });
  });
}
