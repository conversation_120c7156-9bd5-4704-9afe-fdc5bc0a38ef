/// 气泡定位问题分析和修复方案
void main() {
  analyzeBubbleStructure();
  calculateCorrectOffset();
  provideSolution();
}

/// 分析气泡组件的实际结构和尺寸
void analyzeBubbleStructure() {
  print('🔍 气泡组件结构分析');
  print('=' * 50);

  print('\n📐 临时气泡 (_TempPositionBubble) 结构：');
  print('1. 文本容器：');
  print('   - padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4)');
  print('   - 文本: "移动图层选择记忆锚点"');
  print('   - fontSize: 11, fontWeight: w500');
  print('   - 边框: 2px');
  print('   - 估计高度: 4(上padding) + 13(文字) + 4(下padding) + 4(边框) = 25px');

  print('\n2. 指针连线：');
  print('   - width: 1.5px, height: 8px');
  print('   - 高度: 8px');

  print('\n3. 定位圆点：');
  print('   - width: 4px, height: 4px');
  print('   - 圆心位置: 距离圆点顶部 2px');
  print('   - 高度: 4px');

  print('\n📏 总高度计算: 25 + 8 + 4 = 37px');

  print('\n📐 锚点气泡 (KnowledgePointBubble) 结构：');
  print('与临时气泡结构相同，但文本内容可能不同');
  print('总高度也约为 37px（取决于文本长度）');
}

/// 计算正确的偏移值
void calculateCorrectOffset() {
  print('\n🧮 偏移值计算');
  print('=' * 50);

  // 气泡组件尺寸
  const textBoxHeight = 25.0; // 文本框高度（包含padding和边框）
  const lineHeight = 8.0; // 连线高度
  const dotHeight = 4.0; // 圆点高度
  const dotRadius = 2.0; // 圆点半径

  const totalHeight = textBoxHeight + lineHeight + dotHeight; // 37px

  print('📊 组件尺寸：');
  print('- 文本框高度: ${textBoxHeight}px');
  print('- 连线高度: ${lineHeight}px');
  print('- 圆点高度: ${dotHeight}px');
  print('- 总高度: ${totalHeight}px');

  // 计算圆点中心位置
  final dotCenterFromTop = textBoxHeight + lineHeight + dotRadius; // 35px
  print('\n🎯 圆点中心位置：');
  print('- 距离Column顶部: ${dotCenterFromTop}px');

  // 计算FractionalTranslation偏移值
  final offsetRatio = dotCenterFromTop / totalHeight;
  print('\n📐 FractionalTranslation偏移值计算：');
  print(
    '- 偏移比例: ${dotCenterFromTop}px / ${totalHeight}px = ${offsetRatio.toStringAsFixed(4)}',
  );
  print('- Y偏移值: -${offsetRatio.toStringAsFixed(4)}');

  // 验证当前使用的-0.94是否正确
  final currentOffset = 0.94;
  final currentPosition = currentOffset * totalHeight;
  print('\n🔍 当前偏移值-0.94验证：');
  print(
    '- 当前定位位置: $currentOffset × ${totalHeight}px = ${currentPosition.toStringAsFixed(1)}px',
  );
  print('- 圆点中心应在: ${dotCenterFromTop}px');
  print(
    '- 位置差异: ${(currentPosition - dotCenterFromTop).abs().toStringAsFixed(1)}px',
  );

  if ((currentPosition - dotCenterFromTop).abs() > 1.0) {
    print('❌ 当前偏移值不准确，需要调整');
  } else {
    print('✅ 当前偏移值基本准确');
  }
}

/// 提供解决方案
void provideSolution() {
  print('\n💡 解决方案');
  print('=' * 50);

  const textBoxHeight = 25.0;
  const lineHeight = 8.0;
  const dotHeight = 4.0;
  const dotRadius = 2.0;
  const totalHeight = textBoxHeight + lineHeight + dotHeight;
  const dotCenterFromTop = textBoxHeight + lineHeight + dotRadius;

  final correctOffset = dotCenterFromTop / totalHeight;

  print('🎯 正确的偏移值应该是: -${correctOffset.toStringAsFixed(3)}');
  print('');
  print('📝 代码修改建议：');
  print('```dart');
  print('translation: const Offset(');
  print('  -0.5, // 水平居中');
  print('  -${correctOffset.toStringAsFixed(3)}, // 让定位圆点中心对准目标位置');
  print('),');
  print('```');

  print('\n🔍 问题诊断：');
  print('如果当前点击位置显示在连线与文本框交界处，说明：');
  print('1. 当前偏移值让文本框底部对准了点击位置');
  print('2. 需要进一步向上偏移 ${lineHeight + dotRadius}px');
  print(
    '3. 额外偏移比例: ${((lineHeight + dotRadius) / totalHeight).toStringAsFixed(3)}',
  );

  final currentWrongOffset = textBoxHeight / totalHeight;
  print('4. 如果文本框底部对准点击位置，当前偏移值约为: -${currentWrongOffset.toStringAsFixed(3)}');

  print('\n✅ 验证方法：');
  print('1. 在图片上点击一个明显的特征点');
  print('2. 观察临时气泡的定位圆点是否精确对准该特征点');
  print('3. 保存锚点后，确认最终锚点的圆点位置与临时位置一致');
}

/// 额外的调试建议
void debuggingSuggestions() {
  print('\n🛠️ 调试建议');
  print('=' * 50);

  print('1. 添加视觉调试标记：');
  print('   - 在点击位置绘制一个红色十字标记');
  print('   - 在气泡的各个组件周围添加边框');
  print('   - 显示实际的偏移值和计算结果');

  print('\n2. 运行时测量：');
  print('   - 使用GlobalKey获取气泡组件的实际渲染尺寸');
  print('   - 打印FractionalTranslation的实际偏移像素值');
  print('   - 验证圆点的最终屏幕坐标');

  print('\n3. 分步验证：');
  print('   - 先确保坐标转换逻辑正确');
  print('   - 再验证FractionalTranslation的偏移计算');
  print('   - 最后检查气泡组件的实际渲染尺寸');
}
