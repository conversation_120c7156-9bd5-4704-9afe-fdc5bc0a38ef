import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/memory_palace/palace_manager_page.dart';

void main() {
  group('知忆相册创建弹窗布局测试', () {
    testWidgets('应该按正确顺序显示UI组件', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(home: PalaceManagerPage()));

      // 打开对话框
      final fab = find.byType(FloatingActionButton);
      await tester.tap(fab);
      await tester.pumpAndSettle();

      // 验证对话框已打开
      expect(find.text('创建知忆相册'), findsAtLeastNWidgets(1));

      // 验证布局顺序：
      // 1. 标题栏应该在最顶部
      final titleWidget = find.text('创建知忆相册').first;
      expect(titleWidget, findsOneWidget);

      // 2. 选择图片按钮应该在内容区域的顶部
      final selectImageButton = find.text('选择图片');
      expect(selectImageButton, findsOneWidget);

      // 3. 相册信息区域应该存在
      expect(find.text('相册信息'), findsOneWidget);
      expect(find.text('相册标题'), findsOneWidget);
      expect(find.text('相册描述（可选）'), findsOneWidget);

      // 4. 关闭按钮应该在标题栏
      expect(find.byIcon(Icons.close), findsOneWidget);
    });

    testWidgets('选择图片按钮应该在最顶部', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(home: PalaceManagerPage()));

      // 打开对话框
      final fab = find.byType(FloatingActionButton);
      await tester.tap(fab);
      await tester.pumpAndSettle();

      // 查找选择图片按钮
      final selectImageButton = find.text('选择图片');
      expect(selectImageButton, findsOneWidget);

      // 查找相册信息区域
      final albumInfoSection = find.text('相册信息');
      expect(albumInfoSection, findsOneWidget);

      // 通过位置验证顺序（选择图片按钮应该在上方）
      final selectImageButtonPosition = tester.getTopLeft(selectImageButton);
      final albumInfoPosition = tester.getTopLeft(albumInfoSection);

      expect(selectImageButtonPosition.dy, lessThan(albumInfoPosition.dy));
    });

    testWidgets('应该有正确的按钮文本', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(home: PalaceManagerPage()));

      // 打开对话框
      final fab = find.byType(FloatingActionButton);
      await tester.tap(fab);
      await tester.pumpAndSettle();

      // 验证选择图片按钮文本
      expect(find.text('选择图片'), findsOneWidget);

      // 验证图片选择按钮的图标
      expect(find.byIcon(Icons.add_photo_alternate_outlined), findsOneWidget);
    });

    testWidgets('应该有正确的输入字段标签', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(home: PalaceManagerPage()));

      // 打开对话框
      final fab = find.byType(FloatingActionButton);
      await tester.tap(fab);
      await tester.pumpAndSettle();

      // 验证输入字段标签
      expect(find.text('相册标题'), findsOneWidget);
      expect(find.text('相册描述（可选）'), findsOneWidget);

      // 验证输入字段提示文本
      expect(find.text('请输入相册标题'), findsOneWidget);
      expect(find.text('请输入相册描述'), findsOneWidget);
    });

    testWidgets('应该有正确的配色方案', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(home: PalaceManagerPage()));

      // 打开对话框
      final fab = find.byType(FloatingActionButton);
      await tester.tap(fab);
      await tester.pumpAndSettle();

      // 验证主要按钮颜色
      final elevatedButtons = find.byType(ElevatedButton);
      expect(elevatedButtons, findsAtLeastNWidgets(1));

      // 验证浮动操作按钮的颜色
      final fabWidget = tester.widget<FloatingActionButton>(fab);
      expect(fabWidget.backgroundColor, const Color(0xFF2E7EED));
    });

    testWidgets('关闭按钮应该能正常工作', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(home: PalaceManagerPage()));

      // 打开对话框
      final fab = find.byType(FloatingActionButton);
      await tester.tap(fab);
      await tester.pumpAndSettle();

      // 验证对话框已打开
      expect(find.text('创建知忆相册'), findsAtLeastNWidgets(1));

      // 点击关闭按钮
      final closeButton = find.byIcon(Icons.close);
      expect(closeButton, findsOneWidget);
      await tester.tap(closeButton);
      await tester.pumpAndSettle();

      // 验证对话框已关闭（相册信息区域不再显示）
      expect(find.text('相册信息'), findsNothing);
    });
  });
}
