import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/memory_palace/palace_manager_page.dart';

void main() {
  group('CategorySidebar PopupMenu 交互测试', () {
    late CategoryManager categoryManager;

    setUp(() {
      categoryManager = CategoryManager();
      categoryManager.initializeDefaultCategories();
    });

    /// 创建测试用的CategorySidebar widget
    Widget createTestWidget({
      required CategoryManager manager,
      VoidCallback? onCategoriesChanged,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: CategorySidebar(
            isVisible: true,
            onClose: () {},
            onNodeSelected: (node) {},
            onEditingStateChanged: (isEditing) {},
            categoryManager: manager,
            onCategoriesChanged: onCategoriesChanged ?? () {},
          ),
        ),
      );
    }

    testWidgets('PopupMenuButton应该正确显示菜单项', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 点击第一个PopupMenuButton
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      // 验证所有菜单项都存在
      expect(find.text('编辑'), findsOneWidget);
      expect(find.text('添加子分类'), findsOneWidget);
      expect(find.text('设为公共'), findsOneWidget);
      expect(find.text('设为隐私'), findsOneWidget);
      expect(find.text('删除'), findsOneWidget);
    });

    testWidgets('编辑菜单项应该正确工作', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 点击PopupMenuButton并选择编辑
      await _selectPopupMenuItem(tester, 0, '编辑');

      // 验证进入编辑模式
      expect(find.byType(TextField), findsOneWidget);

      // 验证TextField包含原始文本
      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.controller?.text, '学校');
    });

    testWidgets('删除菜单项应该显示确认对话框', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 点击PopupMenuButton并选择删除
      await _selectPopupMenuItem(tester, 0, '删除');

      // 验证确认对话框出现
      expect(find.byType(AlertDialog), findsOneWidget);
      expect(find.text('确认删除'), findsOneWidget);
      expect(find.text('确定要删除分类"学校"吗？'), findsOneWidget);
      expect(find.text('取消'), findsOneWidget);
      expect(find.text('删除'), findsOneWidget);
    });

    testWidgets('确认删除应该移除节点', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 验证节点存在
      expect(categoryManager.findNodeById('school'), isNotNull);

      // 点击删除并确认
      await _selectPopupMenuItem(tester, 0, '删除');
      await tester.tap(find.text('删除').last); // 点击对话框中的删除按钮
      await tester.pumpAndSettle();

      // 验证节点已被删除
      expect(categoryManager.findNodeById('school'), isNull);
    });

    testWidgets('取消删除应该保留节点', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 验证节点存在
      expect(categoryManager.findNodeById('school'), isNotNull);

      // 点击删除但取消
      await _selectPopupMenuItem(tester, 0, '删除');
      await tester.tap(find.text('取消'));
      await tester.pumpAndSettle();

      // 验证节点仍然存在
      expect(categoryManager.findNodeById('school'), isNotNull);
    });

    testWidgets('设为公开应该更改隐私状态', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 验证初始状态为私有
      final schoolNode = categoryManager.findNodeById('school')!;
      expect(schoolNode.privacyStatus, PrivacyStatus.private);

      // 点击设为公开
      await _selectPopupMenuItem(tester, 0, '设为公开');

      // 验证状态已更改为公开
      expect(schoolNode.privacyStatus, PrivacyStatus.public);
    });

    testWidgets('公开节点应该显示设为私有选项', (WidgetTester tester) async {
      // 先设置节点为公开状态
      final schoolNode = categoryManager.findNodeById('school')!;
      schoolNode.privacyStatus = PrivacyStatus.public;

      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 点击PopupMenuButton
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      // 验证显示"设为私有"选项
      expect(find.text('设为私有'), findsOneWidget);
      expect(find.text('设为公开'), findsNothing);
    });

    testWidgets('设为私有应该更改隐私状态', (WidgetTester tester) async {
      // 先设置节点为公开状态
      final schoolNode = categoryManager.findNodeById('school')!;
      schoolNode.privacyStatus = PrivacyStatus.public;

      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 点击设为私有
      await _selectPopupMenuItem(tester, 0, '设为私有');

      // 验证状态已更改为私有
      expect(schoolNode.privacyStatus, PrivacyStatus.private);
    });

    testWidgets('PopupMenu应该在点击外部时关闭', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 打开PopupMenu
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      // 验证菜单已打开
      expect(find.text('编辑'), findsOneWidget);

      // 点击外部区域
      await tester.tapAt(const Offset(50, 50));
      await tester.pumpAndSettle();

      // 验证菜单已关闭
      expect(find.text('编辑'), findsNothing);
    });

    testWidgets('多个PopupMenu应该能够独立工作', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 测试第一个PopupMenu
      await _selectPopupMenuItem(tester, 0, '编辑');
      expect(find.byType(TextField), findsOneWidget);

      // 取消编辑
      await tester.tap(find.text('工作'));
      await tester.pumpAndSettle();

      // 测试第二个PopupMenu
      await _selectPopupMenuItem(tester, 1, '编辑');
      expect(find.byType(TextField), findsOneWidget);

      // 验证编辑的是工作节点
      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.controller?.text, '工作');
    });

    testWidgets('PopupMenu操作不应该导致widget错误', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 连续快速操作多个PopupMenu
      for (int i = 0; i < 4; i++) {
        // 打开菜单
        final popupMenuButtons = find.byType(PopupMenuButton<String>);
        await tester.tap(popupMenuButtons.at(i));
        await tester.pumpAndSettle();

        // 选择编辑
        await tester.tap(find.text('编辑'));
        await tester.pumpAndSettle();

        // 输入文本
        await tester.enterText(find.byType(TextField), '测试$i');
        await tester.pumpAndSettle();

        // 完成编辑
        await tester.testTextInput.receiveAction(TextInputAction.done);
        await tester.pumpAndSettle();
      }

      // 验证所有编辑都成功
      expect(categoryManager.findNodeById('school')?.title, '测试0');
      expect(categoryManager.findNodeById('work')?.title, '测试1');
      expect(categoryManager.findNodeById('residence')?.title, '测试2');
      expect(categoryManager.findNodeById('scenic')?.title, '测试3');
    });
  });
}

/// 辅助方法：选择PopupMenu菜单项
Future<void> _selectPopupMenuItem(
  WidgetTester tester,
  int buttonIndex,
  String menuItem,
) async {
  final popupMenuButtons = find.byType(PopupMenuButton<String>);
  await tester.tap(popupMenuButtons.at(buttonIndex));
  await tester.pumpAndSettle();

  await tester.tap(find.text(menuItem));
  await tester.pumpAndSettle();
}
