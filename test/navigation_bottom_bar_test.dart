import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:oneday/router/app_router.dart';

void main() {
  group('底部导航栏隐藏测试', () {
    testWidgets('测试商城背包页面隐藏底部导航栏', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(
            routerConfig: AppRouter.router,
          ),
        ),
      );

      // 等待应用启动
      await tester.pumpAndSettle();

      // 导航到商城页面
      final context = tester.element(find.byType(MaterialApp));
      context.go('/store');
      await tester.pumpAndSettle();

      // 验证在商城页面时底部导航栏存在
      expect(find.byType(BottomNavigationBar), findsOneWidget);

      // 模拟点击背包按钮（通过路由导航）
      context.push('/inventory', extra: {
        'inventory': <String, int>{},
        'allItems': [],
      });
      await tester.pumpAndSettle();

      // 验证在背包页面时底部导航栏不存在
      expect(find.byType(BottomNavigationBar), findsNothing);

      // 验证有返回按钮
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);
    });

    testWidgets('测试社区编辑文章页面隐藏底部导航栏', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(
            routerConfig: AppRouter.router,
          ),
        ),
      );

      // 等待应用启动
      await tester.pumpAndSettle();

      // 导航到社区页面
      final context = tester.element(find.byType(MaterialApp));
      context.go('/community');
      await tester.pumpAndSettle();

      // 验证在社区页面时底部导航栏存在
      expect(find.byType(BottomNavigationBar), findsOneWidget);

      // 导航到编辑文章页面
      context.push('/community-post-editor');
      await tester.pumpAndSettle();

      // 验证在编辑文章页面时底部导航栏不存在
      expect(find.byType(BottomNavigationBar), findsNothing);

      // 验证有关闭按钮
      expect(find.byIcon(Icons.close), findsOneWidget);
    });

    testWidgets('测试工资钱包页面隐藏底部导航栏', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(
            routerConfig: AppRouter.router,
          ),
        ),
      );

      // 等待应用启动
      await tester.pumpAndSettle();

      // 导航到工资钱包页面
      final context = tester.element(find.byType(MaterialApp));
      context.push('/wage-wallet');
      await tester.pumpAndSettle();

      // 验证在工资钱包页面时底部导航栏不存在
      expect(find.byType(BottomNavigationBar), findsNothing);

      // 验证有返回按钮
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);
    });

    testWidgets('测试从独立页面返回主页面时底部导航栏重新显示', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(
            routerConfig: AppRouter.router,
          ),
        ),
      );

      // 等待应用启动
      await tester.pumpAndSettle();

      // 导航到商城页面
      final context = tester.element(find.byType(MaterialApp));
      context.go('/store');
      await tester.pumpAndSettle();

      // 验证底部导航栏存在
      expect(find.byType(BottomNavigationBar), findsOneWidget);

      // 导航到背包页面
      context.push('/inventory', extra: {
        'inventory': <String, int>{},
        'allItems': [],
      });
      await tester.pumpAndSettle();

      // 验证底部导航栏不存在
      expect(find.byType(BottomNavigationBar), findsNothing);

      // 点击返回按钮
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // 验证返回商城页面后底部导航栏重新显示
      expect(find.byType(BottomNavigationBar), findsOneWidget);
    });

    testWidgets('测试路由配置正确性', (WidgetTester tester) async {
      // 验证路由常量定义
      expect(AppRouter.inventory, '/inventory');
      expect(AppRouter.communityPostEditor, '/community-post-editor');
      expect(AppRouter.wageWallet, '/wage-wallet');

      // 验证路由配置
      final router = AppRouter.router;
      expect(router.configuration.routes.length, greaterThan(0));

      // 查找独立路由（非 ShellRoute 子路由）
      final independentRoutes = router.configuration.routes
          .where((route) => route is GoRoute && route.path.startsWith('/'))
          .cast<GoRoute>()
          .toList();

      // 验证关键的独立路由存在
      final routePaths = independentRoutes.map((route) => route.path).toList();
      expect(routePaths, contains('/inventory'));
      expect(routePaths, contains('/community-post-editor'));
      expect(routePaths, contains('/wage-wallet'));
    });

    test('测试路由导航逻辑', () {
      // 测试 ShellRoute 内的页面（应该显示底部导航栏）
      const shellRoutes = ['/home', '/calendar', '/store', '/community', '/profile'];
      
      // 测试独立页面（应该隐藏底部导航栏）
      const independentRoutes = [
        '/inventory',
        '/community-post-editor',
        '/wage-wallet',
        '/settings',
        '/exercise',
        '/timebox',
        '/memory-palace',
      ];

      // 验证路由分类正确
      for (final route in shellRoutes) {
        expect(route.startsWith('/'), true, reason: '$route 应该是绝对路径');
      }

      for (final route in independentRoutes) {
        expect(route.startsWith('/'), true, reason: '$route 应该是绝对路径');
      }
    });
  });

  group('导航用户体验测试', () {
    testWidgets('测试页面切换动画流畅性', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(
            routerConfig: AppRouter.router,
          ),
        ),
      );

      // 等待应用启动
      await tester.pumpAndSettle();

      final context = tester.element(find.byType(MaterialApp));

      // 测试从主页面到独立页面的切换
      context.go('/store');
      await tester.pumpAndSettle();

      context.push('/inventory', extra: {
        'inventory': <String, int>{},
        'allItems': [],
      });
      
      // 验证动画过程中没有异常
      await tester.pump();
      expect(tester.takeException(), isNull);
      
      await tester.pumpAndSettle();
      expect(tester.takeException(), isNull);
    });

    testWidgets('测试AppBar配置正确性', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(
            routerConfig: AppRouter.router,
          ),
        ),
      );

      await tester.pumpAndSettle();

      final context = tester.element(find.byType(MaterialApp));

      // 测试背包页面的AppBar
      context.push('/inventory', extra: {
        'inventory': <String, int>{},
        'allItems': [],
      });
      await tester.pumpAndSettle();

      // 验证AppBar存在且有正确的返回按钮
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);

      // 测试编辑文章页面的AppBar
      context.go('/community-post-editor');
      await tester.pumpAndSettle();

      // 验证AppBar存在且有正确的关闭按钮
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.byIcon(Icons.close), findsOneWidget);
    });
  });
}
