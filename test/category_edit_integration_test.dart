import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/time_box/pages/task_category_management_page.dart';

void main() {
  group('分类编辑集成测试', () {
    setUp(() async {
      // 清理SharedPreferences
      SharedPreferences.setMockInitialValues({});
    });

    testWidgets('完整的默认分类编辑流程测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const TaskCategoryManagementPage(),
        ),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 验证页面标题
      expect(find.text('任务分类管理'), findsOneWidget);
      
      // 验证默认分类存在并显示新标签
      expect(find.text('计算机科学'), findsOneWidget);
      expect(find.text('系统分类（可编辑）'), findsWidgets);
      
      // 验证所有分类都有PopupMenuButton
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      expect(popupMenuButtons, findsWidgets);
      
      // 点击第一个PopupMenuButton（默认分类）
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      // 验证只有编辑选项，没有删除选项
      expect(find.text('编辑'), findsOneWidget);
      expect(find.text('删除'), findsNothing);
      
      // 点击编辑选项
      await tester.tap(find.text('编辑'));
      await tester.pumpAndSettle();

      // 验证编辑对话框打开
      expect(find.text('编辑分类'), findsOneWidget);
    });

    testWidgets('自定义分类应该有删除选项', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const TaskCategoryManagementPage(),
        ),
      );

      await tester.pumpAndSettle();

      // 先添加一个自定义分类
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // 输入分类名称
      await tester.enterText(find.byType(TextFormField), '测试分类');
      await tester.pumpAndSettle();

      // 点击保存按钮
      await tester.tap(find.text('保存'));
      await tester.pumpAndSettle();

      // 验证分类添加成功
      expect(find.text('测试分类'), findsOneWidget);
      expect(find.text('自定义分类'), findsOneWidget);

      // 找到自定义分类的PopupMenuButton
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      
      // 点击最后一个PopupMenuButton（自定义分类）
      await tester.tap(popupMenuButtons.last);
      await tester.pumpAndSettle();

      // 验证有编辑和删除选项
      expect(find.text('编辑'), findsOneWidget);
      expect(find.text('删除'), findsOneWidget);
    });

    testWidgets('UI显示验证测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const TaskCategoryManagementPage(),
        ),
      );

      await tester.pumpAndSettle();

      // 验证所有默认分类都显示新的标签
      final systemCategoryLabels = find.text('系统分类（可编辑）');
      expect(systemCategoryLabels, findsWidgets);
      
      // 验证默认分类数量（应该有6个默认分类）
      expect(systemCategoryLabels.evaluate().length, equals(6));
      
      // 验证PopupMenuButton数量等于分类数量
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      final categoryCards = find.byType(Card);
      expect(popupMenuButtons.evaluate().length, 
             equals(categoryCards.evaluate().length));
    });

    testWidgets('颜色和图标选择功能测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const TaskCategoryManagementPage(),
        ),
      );

      await tester.pumpAndSettle();

      // 点击第一个默认分类的编辑按钮
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      await tester.tap(find.text('编辑'));
      await tester.pumpAndSettle();

      // 验证颜色选择器存在
      expect(find.text('选择颜色'), findsOneWidget);
      
      // 验证图标选择器存在
      expect(find.text('选择图标'), findsOneWidget);
      
      // 查找颜色圆圈
      final colorContainers = find.byType(Container).evaluate()
          .where((element) {
            final widget = element.widget as Container;
            final decoration = widget.decoration as BoxDecoration?;
            return decoration?.shape == BoxShape.circle;
          });

      // 验证有颜色选项
      expect(colorContainers.length, greaterThan(3));
      
      // 查找图标
      final icons = find.byType(Icon);
      expect(icons, findsWidgets);
    });

    testWidgets('取消编辑功能测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const TaskCategoryManagementPage(),
        ),
      );

      await tester.pumpAndSettle();

      // 打开编辑对话框
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      await tester.tap(find.text('编辑'));
      await tester.pumpAndSettle();

      // 验证对话框打开
      expect(find.text('编辑分类'), findsOneWidget);

      // 点击取消按钮
      await tester.tap(find.text('取消'));
      await tester.pumpAndSettle();

      // 验证对话框关闭
      expect(find.text('编辑分类'), findsNothing);
    });
  });
}
