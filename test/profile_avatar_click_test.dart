import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:oneday/features/profile/profile_page.dart';

void main() {
  group('个人主页头像点击测试', () {
    late GoRouter router;
    String? lastNavigatedRoute;

    setUp(() {
      lastNavigatedRoute = null;
      router = GoRouter(
        initialLocation: '/profile',
        routes: [
          GoRoute(
            path: '/profile',
            builder: (context, state) => const ProfilePage(),
          ),
          GoRoute(
            path: '/login',
            builder: (context, state) {
              lastNavigatedRoute = '/login';
              return const Scaffold(
                body: Center(child: Text('登录页面')),
              );
            },
          ),
          GoRoute(
            path: '/settings',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('设置页面')),
            ),
          ),
        ],
      );
    });

    testWidgets('单次点击头像应该跳转到登录页面', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp.router(
          routerConfig: router,
          theme: ThemeData(
            useMaterial3: true,
            primaryColor: const Color(0xFF2E7EED),
          ),
        ),
      );

      // 等待页面完全加载
      await tester.pumpAndSettle();

      // 验证个人主页加载成功
      expect(find.text('我的'), findsOneWidget);
      expect(find.text('学习者'), findsOneWidget);

      // 查找头像区域（用户信息卡片）
      final userCardFinder = find.byType(GestureDetector).first;
      expect(userCardFinder, findsOneWidget);

      // 点击头像区域
      await tester.tap(userCardFinder);
      await tester.pumpAndSettle();

      // 验证跳转到登录页面
      expect(lastNavigatedRoute, equals('/login'));
      expect(find.text('登录页面'), findsOneWidget);

      print('✅ 单次点击头像成功跳转到登录页面');
    });

    testWidgets('头像UI设计应该保持不变', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp.router(
          routerConfig: router,
          theme: ThemeData(
            useMaterial3: true,
            primaryColor: const Color(0xFF2E7EED),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证头像容器存在
      final avatarContainer = find.byWidgetPredicate(
        (widget) => widget is Container && 
                   widget.decoration is BoxDecoration &&
                   (widget.decoration as BoxDecoration).borderRadius == BorderRadius.circular(30),
      );
      expect(avatarContainer, findsOneWidget);

      // 验证头像图标存在
      expect(find.byIcon(Icons.person), findsOneWidget);

      // 验证用户信息文本存在
      expect(find.text('学习者'), findsOneWidget);
      expect(find.text('让每一天都充满收获'), findsOneWidget);

      // 验证编辑按钮存在
      expect(find.byIcon(Icons.edit_outlined), findsOneWidget);

      print('✅ 头像UI设计保持不变');
    });

    testWidgets('用户信息卡片应该是可点击的', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp.router(
          routerConfig: router,
          theme: ThemeData(
            useMaterial3: true,
            primaryColor: const Color(0xFF2E7EED),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找用户信息卡片的GestureDetector
      final gestureDetector = find.byWidgetPredicate(
        (widget) => widget is GestureDetector && widget.onTap != null,
      );
      
      // 应该至少有一个可点击的GestureDetector（用户信息卡片）
      expect(gestureDetector, findsWidgets);

      print('✅ 用户信息卡片保持可点击状态');
    });

    testWidgets('其他功能按钮应该不受影响', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp.router(
          routerConfig: router,
          theme: ThemeData(
            useMaterial3: true,
            primaryColor: const Color(0xFF2E7EED),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证设置按钮存在且可点击
      final settingsButton = find.byIcon(Icons.settings_outlined);
      expect(settingsButton, findsOneWidget);

      // 验证编辑按钮存在且可点击
      final editButton = find.byIcon(Icons.edit_outlined);
      expect(editButton, findsOneWidget);

      // 验证功能菜单项存在
      expect(find.text('优化日志'), findsOneWidget);
      expect(find.text('道具商城'), findsOneWidget);

      print('✅ 其他功能按钮不受影响');
    });

    testWidgets('页面布局应该保持完整', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp.router(
          routerConfig: router,
          theme: ThemeData(
            useMaterial3: true,
            primaryColor: const Color(0xFF2E7EED),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证主要区域存在
      expect(find.text('学习概览'), findsOneWidget);
      expect(find.text('功能菜单'), findsOneWidget);
      expect(find.text('其他'), findsOneWidget);

      // 验证统计卡片存在
      expect(find.text('今日学习'), findsOneWidget);
      expect(find.text('累计工资'), findsOneWidget);
      expect(find.text('连续天数'), findsOneWidget);
      expect(find.text('记忆宫殿'), findsOneWidget);

      print('✅ 页面布局保持完整');
    });
  });
}
