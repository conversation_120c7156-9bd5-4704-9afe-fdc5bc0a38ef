import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/time_box/pages/task_category_management_page.dart';

void main() {
  group('TaskCategoryManagementPage Integration Tests', () {
    setUp(() async {
      // 清理SharedPreferences
      SharedPreferences.setMockInitialValues({});
    });

    testWidgets('完整的添加分类流程测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(home: const TaskCategoryManagementPage()),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 验证页面标题
      expect(find.text('任务分类管理'), findsOneWidget);

      // 验证默认分类存在
      expect(find.text('计算机科学'), findsOneWidget);
      expect(find.text('数学'), findsOneWidget);

      // 点击添加按钮
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // 验证对话框打开
      expect(find.text('添加分类'), findsOneWidget);

      // 输入分类名称
      await tester.enterText(find.byType(TextFormField), '测试分类');
      await tester.pumpAndSettle();

      // 验证输入成功
      expect(find.text('测试分类'), findsOneWidget);

      // 点击保存按钮
      await tester.tap(find.text('保存'));
      await tester.pumpAndSettle();

      // 验证对话框关闭
      expect(find.text('添加分类'), findsNothing);
    });

    testWidgets('颜色选择功能测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(home: const TaskCategoryManagementPage()),
      );

      await tester.pumpAndSettle();

      // 打开添加对话框
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // 验证颜色选择器存在
      expect(find.text('选择颜色'), findsOneWidget);

      // 查找颜色圆圈
      final colorContainers = find.byType(Container).evaluate().where((
        element,
      ) {
        final widget = element.widget as Container;
        final decoration = widget.decoration as BoxDecoration?;
        return decoration?.shape == BoxShape.circle;
      });

      // 验证有颜色选项
      expect(colorContainers.length, greaterThan(3));
    });

    testWidgets('图标选择功能测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(home: const TaskCategoryManagementPage()),
      );

      await tester.pumpAndSettle();

      // 打开添加对话框
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // 验证图标选择器存在
      expect(find.text('选择图标'), findsOneWidget);

      // 查找图标
      final icons = find.byType(Icon);
      expect(icons, findsWidgets);
    });

    testWidgets('表单验证测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(home: const TaskCategoryManagementPage()),
      );

      await tester.pumpAndSettle();

      // 打开添加对话框
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // 不输入名称直接点击保存
      await tester.tap(find.text('保存'));
      await tester.pumpAndSettle();

      // 验证验证错误信息
      expect(find.text('请输入分类名称'), findsOneWidget);
    });

    testWidgets('取消按钮功能测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(home: const TaskCategoryManagementPage()),
      );

      await tester.pumpAndSettle();

      // 打开添加对话框
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // 验证对话框打开
      expect(find.text('添加分类'), findsOneWidget);

      // 点击取消按钮
      await tester.tap(find.text('取消'));
      await tester.pumpAndSettle();

      // 验证对话框关闭
      expect(find.text('添加分类'), findsNothing);
    });

    testWidgets('默认分类不显示编辑菜单测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(home: const TaskCategoryManagementPage()),
      );

      await tester.pumpAndSettle();

      // 查找默认分类
      expect(find.text('默认分类'), findsWidgets);

      // 默认分类应该没有PopupMenuButton
      final cards = find.byType(Card);

      // 验证卡片存在但PopupMenu数量有限（只有自定义分类才有）
      expect(cards, findsWidgets);
    });
  });
}
