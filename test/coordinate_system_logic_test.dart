import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/memory_palace/utils/anchor_data_migration.dart';

void main() {
  group('标准化坐标系统逻辑测试', () {
    group('基础坐标转换逻辑', () {
      test('缩放比例计算应该正确', () {
        // 模拟图片尺寸变化
        const originalSize = Size(2000, 1500);
        const compressedSize800 = Size(800, 600);
        const compressedSize1000 = Size(1000, 750);

        // 计算缩放比例
        final scale800X = compressedSize800.width / originalSize.width;
        final scale800Y = compressedSize800.height / originalSize.height;
        final scale1000X = compressedSize1000.width / originalSize.width;
        final scale1000Y = compressedSize1000.height / originalSize.height;

        expect(scale800X, 0.4);
        expect(scale800Y, 0.4);
        expect(scale1000X, 0.5);
        expect(scale1000Y, 0.5);
      });

      test('锚点坐标在不同压缩配置下应该保持一致', () {
        // 不同压缩配置的尺寸
        const size800 = <PERSON>ze(800, 600);
        const size1000 = Size(1000, 750);

        // 锚点在原始图片中的比例位置
        const anchorXRatio = 0.5; // 中心位置
        const anchorYRatio = 0.4;

        // 计算在不同压缩配置下的像素位置
        final pos800X = anchorXRatio * size800.width;
        final pos800Y = anchorYRatio * size800.height;
        final pos1000X = anchorXRatio * size1000.width;
        final pos1000Y = anchorYRatio * size1000.height;

        // 验证比例位置保持一致
        expect(pos800X / size800.width, closeTo(anchorXRatio, 0.001));
        expect(pos800Y / size800.height, closeTo(anchorYRatio, 0.001));
        expect(pos1000X / size1000.width, closeTo(anchorXRatio, 0.001));
        expect(pos1000Y / size1000.height, closeTo(anchorYRatio, 0.001));
      });

      test('坐标校准逻辑应该正确', () {
        // 模拟从800px配置迁移到1000px配置
        const originalSize = Size(2000, 1500);
        const oldCompressedSize = Size(800, 600);
        const newCompressedSize = Size(1000, 750);

        // 基于旧压缩尺寸的锚点比例坐标
        const oldXRatio = 0.5;
        const oldYRatio = 0.5;

        // 1. 从旧比例坐标恢复为旧压缩图片的像素坐标
        final oldImageX = oldXRatio * oldCompressedSize.width; // 400
        final oldImageY = oldYRatio * oldCompressedSize.height; // 300

        // 2. 转换为原始图片的像素坐标
        final scaleFactorX =
            originalSize.width / oldCompressedSize.width; // 2.5
        final scaleFactorY =
            originalSize.height / oldCompressedSize.height; // 2.5

        final originalImageX = oldImageX * scaleFactorX; // 1000
        final originalImageY = oldImageY * scaleFactorY; // 750

        // 3. 转换为基于原始图片尺寸的新比例坐标
        final newXRatio = originalImageX / originalSize.width; // 0.5
        final newYRatio = originalImageY / originalSize.height; // 0.5

        // 验证校准结果
        expect(newXRatio, closeTo(0.5, 0.001));
        expect(newYRatio, closeTo(0.5, 0.001));

        // 4. 验证在新压缩配置下的显示位置
        final newImageX = newXRatio * newCompressedSize.width; // 500
        final newImageY = newYRatio * newCompressedSize.height; // 375

        // 验证比例位置保持一致
        expect(newImageX / newCompressedSize.width, closeTo(0.5, 0.001));
        expect(newImageY / newCompressedSize.height, closeTo(0.5, 0.001));
      });
    });

    group('屏幕坐标转换逻辑', () {
      test('屏幕坐标到图片坐标转换应该正确', () {
        // 模拟变换矩阵参数
        const scale = 0.8;
        const translationX = 100.0;
        const translationY = 50.0;

        // 屏幕点击位置
        const screenX = 300.0;
        const screenY = 200.0;

        // 转换为图片内坐标
        final imageX = (screenX - translationX) / scale; // (300-100)/0.8 = 250
        final imageY = (screenY - translationY) / scale; // (200-50)/0.8 = 187.5

        expect(imageX, closeTo(250, 0.1));
        expect(imageY, closeTo(187.5, 0.1));
      });

      test('往返坐标转换应该保持一致性', () {
        // 原始屏幕坐标
        const originalScreenX = 300.0;
        const originalScreenY = 200.0;

        // 变换参数
        const scale = 0.8;
        const translationX = 100.0;
        const translationY = 50.0;

        // 屏幕坐标 -> 图片坐标
        final imageX = (originalScreenX - translationX) / scale;
        final imageY = (originalScreenY - translationY) / scale;

        // 图片坐标 -> 屏幕坐标
        final backToScreenX = imageX * scale + translationX;
        final backToScreenY = imageY * scale + translationY;

        // 验证往返转换的一致性
        expect(backToScreenX, closeTo(originalScreenX, 0.1));
        expect(backToScreenY, closeTo(originalScreenY, 0.1));
      });
    });

    group('边界情况测试', () {
      test('应该正确处理边界坐标', () {
        const imageSize = Size(800, 600);

        // 测试左上角 (0, 0)
        const topLeftRatio = Offset(0.0, 0.0);
        final topLeftPixel = Offset(
          topLeftRatio.dx * imageSize.width,
          topLeftRatio.dy * imageSize.height,
        );
        expect(topLeftPixel.dx, 0);
        expect(topLeftPixel.dy, 0);

        // 测试右下角 (1, 1)
        const bottomRightRatio = Offset(1.0, 1.0);
        final bottomRightPixel = Offset(
          bottomRightRatio.dx * imageSize.width,
          bottomRightRatio.dy * imageSize.height,
        );
        expect(bottomRightPixel.dx, imageSize.width);
        expect(bottomRightPixel.dy, imageSize.height);
      });

      test('应该正确处理零尺寸情况', () {
        const zeroSize = Size(0, 0);

        // 零尺寸的缩放比例应该是特殊值
        final scaleX = 100.0 / zeroSize.width;
        final scaleY = 100.0 / zeroSize.height;

        expect(scaleX.isInfinite, true);
        expect(scaleY.isInfinite, true);
      });
    });

    group('数据迁移统计测试', () {
      test('MigrationStats 应该正确计算进度', () {
        const stats = MigrationStats(
          totalScenes: 10,
          migratedScenes: 7,
          totalAnchors: 50,
          migratedAnchors: 35,
        );

        expect(stats.progress, closeTo(0.7, 0.01));
        expect(stats.isComplete, false);
      });

      test('完成状态应该正确判断', () {
        const completeStats = MigrationStats(
          totalScenes: 5,
          migratedScenes: 5,
          totalAnchors: 25,
          migratedAnchors: 25,
        );

        expect(completeStats.isComplete, true);
        expect(completeStats.progress, 1.0);
      });

      test('空数据应该正确处理', () {
        const emptyStats = MigrationStats(
          totalScenes: 0,
          migratedScenes: 0,
          totalAnchors: 0,
          migratedAnchors: 0,
        );

        expect(emptyStats.progress, 1.0); // 空数据视为完成
        expect(emptyStats.isComplete, false); // 但不是真正完成
      });
    });

    group('实际场景模拟测试', () {
      test('模拟压缩配置从800px改为1000px的影响', () {
        // 原始图片：2778x1940
        const originalSize = Size(2778, 1940);

        // 旧配置：800px压缩
        const oldMaxDimension = 800.0;
        final oldScale = oldMaxDimension / originalSize.width; // 0.288
        final oldCompressedSize = Size(
          originalSize.width * oldScale,
          originalSize.height * oldScale,
        ); // 800x559

        // 新配置：1000px压缩
        const newMaxDimension = 1000.0;
        final newScale = newMaxDimension / originalSize.width; // 0.36
        final newCompressedSize = Size(
          originalSize.width * newScale,
          originalSize.height * newScale,
        ); // 1000x699

        // 锚点在旧配置下的位置（中心点）
        final oldAnchorX = oldCompressedSize.width * 0.5; // 400
        final oldAnchorY = oldCompressedSize.height * 0.5; // 279.5

        // 转换为基于原始图片的标准化坐标
        final standardizedX = oldAnchorX / oldScale; // 1389
        final standardizedY = oldAnchorY / oldScale; // 970

        // 在新配置下的位置
        final newAnchorX = standardizedX * newScale; // 500
        final newAnchorY = standardizedY * newScale; // 349.2

        // 验证比例位置保持一致
        final oldRatioX = oldAnchorX / oldCompressedSize.width;
        final oldRatioY = oldAnchorY / oldCompressedSize.height;
        final newRatioX = newAnchorX / newCompressedSize.width;
        final newRatioY = newAnchorY / newCompressedSize.height;

        expect(newRatioX, closeTo(oldRatioX, 0.001));
        expect(newRatioY, closeTo(oldRatioY, 0.001));
        expect(newRatioX, closeTo(0.5, 0.001));
        expect(newRatioY, closeTo(0.5, 0.001));
      });
    });
  });
}
