import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/memory_palace/palace_manager_page.dart';

void main() {
  group('CategorySidebar 编辑功能测试', () {
    late CategoryManager categoryManager;

    setUp(() {
      categoryManager = CategoryManager();
      categoryManager.initializeDefaultCategories();
    });

    /// 创建测试用的CategorySidebar widget
    Widget createTestWidget({
      required CategoryManager manager,
      VoidCallback? onCategoriesChanged,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: CategorySidebar(
            isVisible: true,
            onClose: () {},
            onNodeSelected: (node) {},
            onEditingStateChanged: (isEditing) {},
            categoryManager: manager,
            onCategoriesChanged: onCategoriesChanged ?? () {},
          ),
        ),
      );
    }

    testWidgets('应该正确显示分类树结构', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 验证根节点存在
      expect(find.text('学校'), findsOneWidget);
      expect(find.text('工作'), findsOneWidget);
      expect(find.text('居所'), findsOneWidget);
      expect(find.text('景点'), findsOneWidget);
    });

    testWidgets('应该能够点击PopupMenuButton显示菜单', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 查找第一个PopupMenuButton（学校节点的菜单）
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      expect(popupMenuButtons, findsWidgets);

      // 点击第一个PopupMenuButton
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      // 验证菜单项存在
      expect(find.text('编辑'), findsOneWidget);
      expect(find.text('删除'), findsOneWidget);
      expect(find.text('设为公开'), findsOneWidget);
    });

    testWidgets('应该能够进入编辑模式', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 点击第一个PopupMenuButton
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      // 点击编辑菜单项
      await tester.tap(find.text('编辑'));
      await tester.pumpAndSettle();

      // 验证进入编辑模式 - 应该出现TextField
      expect(find.byType(TextField), findsOneWidget);

      // 验证TextField中的文本是原始标题
      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.controller?.text, '学校');
    });

    testWidgets('应该能够保存编辑后的文本', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(manager: categoryManager, onCategoriesChanged: () {}),
      );
      await tester.pumpAndSettle();

      // 进入编辑模式
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      await tester.tap(find.text('编辑'));
      await tester.pumpAndSettle();

      // 修改文本
      await tester.enterText(find.byType(TextField), '修改后的学校');
      await tester.pumpAndSettle();

      // 提交编辑（按回车键）
      await tester.testTextInput.receiveAction(TextInputAction.done);
      await tester.pumpAndSettle();

      // 验证文本已更新
      final schoolNode = categoryManager.findNodeById('school');
      expect(schoolNode?.title, '修改后的学校');
    });

    testWidgets('应该能够取消编辑', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 进入编辑模式
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      await tester.tap(find.text('编辑'));
      await tester.pumpAndSettle();

      // 修改文本但不保存
      await tester.enterText(find.byType(TextField), '临时修改');
      await tester.pumpAndSettle();

      // 点击其他地方取消编辑
      await tester.tap(find.text('工作'));
      await tester.pumpAndSettle();

      // 验证原始文本未改变
      final schoolNode = categoryManager.findNodeById('school');
      expect(schoolNode?.title, '学校');
    });

    testWidgets('应该能够处理空文本输入', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 进入编辑模式
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      await tester.tap(find.text('编辑'));
      await tester.pumpAndSettle();

      // 清空文本
      await tester.enterText(find.byType(TextField), '');
      await tester.pumpAndSettle();

      // 提交编辑
      await tester.testTextInput.receiveAction(TextInputAction.done);
      await tester.pumpAndSettle();

      // 验证恢复原始标题（对于现有节点）
      final schoolNode = categoryManager.findNodeById('school');
      expect(schoolNode?.title, '学校');
    });

    testWidgets('应该能够设置隐私状态', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 点击PopupMenuButton
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      // 点击设为公开
      await tester.tap(find.text('设为公开'));
      await tester.pumpAndSettle();

      // 验证隐私状态已更改
      final schoolNode = categoryManager.findNodeById('school');
      expect(schoolNode?.privacyStatus, PrivacyStatus.public);
    });

    testWidgets('应该能够连续多次编辑不同节点', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 第一次编辑：学校节点
      await _editNodeByIndex(tester, 0, '新学校名称');

      // 验证第一次编辑成功
      expect(categoryManager.findNodeById('school')?.title, '新学校名称');

      // 第二次编辑：工作节点
      await _editNodeByIndex(tester, 1, '新工作名称');

      // 验证第二次编辑成功
      expect(categoryManager.findNodeById('work')?.title, '新工作名称');

      // 第三次编辑：居所节点
      await _editNodeByIndex(tester, 2, '新居所名称');

      // 验证第三次编辑成功
      expect(categoryManager.findNodeById('residence')?.title, '新居所名称');

      // 第四次编辑：景点节点
      await _editNodeByIndex(tester, 3, '新景点名称');

      // 验证第四次编辑成功
      expect(categoryManager.findNodeById('scenic')?.title, '新景点名称');
    });

    testWidgets('应该能够连续编辑同一个节点多次', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 第一次编辑
      await _editNodeByIndex(tester, 0, '第一次修改');
      expect(categoryManager.findNodeById('school')?.title, '第一次修改');

      // 第二次编辑同一个节点
      await _editNodeByIndex(tester, 0, '第二次修改');
      expect(categoryManager.findNodeById('school')?.title, '第二次修改');

      // 第三次编辑同一个节点
      await _editNodeByIndex(tester, 0, '第三次修改');
      expect(categoryManager.findNodeById('school')?.title, '第三次修改');

      // 第四次编辑同一个节点
      await _editNodeByIndex(tester, 0, '第四次修改');
      expect(categoryManager.findNodeById('school')?.title, '第四次修改');

      // 第五次编辑同一个节点
      await _editNodeByIndex(tester, 0, '第五次修改');
      expect(categoryManager.findNodeById('school')?.title, '第五次修改');
    });

    testWidgets('应该能够快速连续编辑多个节点', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 快速连续编辑多个节点
      final editTasks = [
        ('school', '快速编辑1'),
        ('work', '快速编辑2'),
        ('residence', '快速编辑3'),
        ('scenic', '快速编辑4'),
      ];

      for (int i = 0; i < editTasks.length; i++) {
        await _editNodeByIndex(tester, i, editTasks[i].$2);
        // 验证每次编辑都成功
        expect(
          categoryManager.findNodeById(editTasks[i].$1)?.title,
          editTasks[i].$2,
        );
      }
    });
  });
}

/// 辅助方法：通过索引编辑节点
Future<void> _editNodeByIndex(
  WidgetTester tester,
  int index,
  String newText,
) async {
  // 查找所有PopupMenuButton
  final popupMenuButtons = find.byType(PopupMenuButton<String>);
  expect(popupMenuButtons, findsWidgets);

  // 点击指定索引的PopupMenuButton
  await tester.tap(popupMenuButtons.at(index));
  await tester.pumpAndSettle();

  // 点击编辑菜单项
  await tester.tap(find.text('编辑'));
  await tester.pumpAndSettle();

  // 输入新文本
  await tester.enterText(find.byType(TextField), newText);
  await tester.pumpAndSettle();

  // 提交编辑
  await tester.testTextInput.receiveAction(TextInputAction.done);
  await tester.pumpAndSettle();
}
