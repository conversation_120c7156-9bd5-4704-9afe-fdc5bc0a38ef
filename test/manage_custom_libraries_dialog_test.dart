import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/exercise/manage_custom_libraries_dialog.dart';
import 'package:oneday/features/exercise/custom_action_library_service.dart';

void main() {
  group('ManageCustomLibrariesDialog UI Tests', () {
    late CustomActionLibraryService mockService;

    setUp(() {
      mockService = CustomActionLibraryService();
    });

    testWidgets('底部按钮区域应该使用Wrap布局防止溢出', (WidgetTester tester) async {
      // 创建一个小屏幕尺寸来测试溢出情况
      await tester.binding.setSurfaceSize(const Size(300, 600));

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ManageCustomLibrariesDialog(
              customLibraryService: mockService,
              onLibrariesChanged: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找Wrap组件，确保按钮使用了Wrap布局
      expect(find.byType(Wrap), findsAtLeastNWidgets(1));

      // 查找底部按钮
      expect(find.text('创建动作库'), findsOneWidget);
      expect(find.text('导入'), findsOneWidget);
      expect(find.text('关闭'), findsOneWidget);

      // 确保没有溢出错误
      expect(tester.takeException(), isNull);
    });

    testWidgets('统计信息文本应该有溢出处理', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ManageCustomLibrariesDialog(
              customLibraryService: mockService,
              onLibrariesChanged: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找统计信息文本（底部的统计信息）
      expect(find.text('共 0 个动作库'), findsOneWidget);

      // 查找Flexible组件，确保文本有溢出处理
      expect(find.byType(Flexible), findsAtLeastNWidgets(1));
    });

    testWidgets('对话框应该在不同屏幕尺寸下正常显示', (WidgetTester tester) async {
      // 测试小屏幕
      await tester.binding.setSurfaceSize(const Size(320, 568));

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ManageCustomLibrariesDialog(
              customLibraryService: mockService,
              onLibrariesChanged: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(tester.takeException(), isNull);

      // 测试大屏幕
      await tester.binding.setSurfaceSize(const Size(800, 1200));
      await tester.pumpAndSettle();
      expect(tester.takeException(), isNull);

      // 重置屏幕尺寸
      await tester.binding.setSurfaceSize(null);
    });
  });
}
