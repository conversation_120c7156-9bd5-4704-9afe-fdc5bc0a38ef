import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/memory_palace/palace_manager_page.dart';

void main() {
  group('CategorySidebar 编辑状态管理测试', () {
    late CategoryManager categoryManager;

    setUp(() {
      categoryManager = CategoryManager();
      categoryManager.initializeDefaultCategories();
    });

    /// 创建测试用的CategorySidebar widget
    Widget createTestWidget({
      required CategoryManager manager,
      VoidCallback? onCategoriesChanged,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: CategorySidebar(
            isVisible: true,
            onClose: () {},
            onNodeSelected: (node) {},
            onEditingStateChanged: (isEditing) {},
            categoryManager: manager,
            onCategoriesChanged: onCategoriesChanged ?? () {},
          ),
        ),
      );
    }

    testWidgets('编辑状态应该正确初始化', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 验证初始状态下没有节点在编辑
      expect(find.byType(TextField), findsNothing);

      // 验证所有节点都显示为文本
      expect(find.text('学校'), findsOneWidget);
      expect(find.text('工作'), findsOneWidget);
      expect(find.text('居所'), findsOneWidget);
      expect(find.text('景点'), findsOneWidget);
    });

    testWidgets('应该能够正确进入编辑状态', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 进入编辑模式
      await _startEditing(tester, 0);

      // 验证编辑状态
      expect(find.byType(TextField), findsOneWidget);
      expect(find.text('学校'), findsNothing); // 原文本应该被TextField替换
    });

    testWidgets('应该能够正确退出编辑状态', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 进入编辑模式
      await _startEditing(tester, 0);
      expect(find.byType(TextField), findsOneWidget);

      // 完成编辑
      await tester.testTextInput.receiveAction(TextInputAction.done);
      await tester.pumpAndSettle();

      // 验证退出编辑状态
      expect(find.byType(TextField), findsNothing);
      expect(find.text('学校'), findsOneWidget);
    });

    testWidgets('应该能够处理编辑状态切换', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 编辑第一个节点
      await _startEditing(tester, 0);
      expect(find.byType(TextField), findsOneWidget);

      // 切换到编辑第二个节点
      await _startEditing(tester, 1);

      // 应该只有一个TextField（新的编辑状态）
      expect(find.byType(TextField), findsOneWidget);

      // 验证编辑的是工作节点
      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.controller?.text, '工作');
    });

    testWidgets('应该能够处理失去焦点时的自动保存', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 进入编辑模式
      await _startEditing(tester, 0);

      // 修改文本
      await tester.enterText(find.byType(TextField), '修改后的学校');
      await tester.pumpAndSettle();

      // 点击其他地方使TextField失去焦点
      await tester.tap(find.text('工作'));
      await tester.pumpAndSettle();

      // 验证自动保存
      expect(categoryManager.findNodeById('school')?.title, '修改后的学校');
      expect(find.byType(TextField), findsNothing);
    });

    testWidgets('应该能够处理空文本的情况', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 进入编辑模式
      await _startEditing(tester, 0);

      // 清空文本
      await tester.enterText(find.byType(TextField), '');
      await tester.pumpAndSettle();

      // 完成编辑
      await tester.testTextInput.receiveAction(TextInputAction.done);
      await tester.pumpAndSettle();

      // 验证恢复原始标题
      expect(categoryManager.findNodeById('school')?.title, '学校');
    });

    testWidgets('应该能够处理新节点的编辑', (WidgetTester tester) async {
      // 添加一个新节点
      categoryManager.addNode('新分类');
      final newNode = categoryManager.categories.last;
      newNode.isNew = true;

      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 查找新节点的PopupMenuButton（应该是最后一个）
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      final lastIndex = tester.widgetList(popupMenuButtons).length - 1;

      await _startEditing(tester, lastIndex);

      // 清空文本（模拟用户删除默认文本）
      await tester.enterText(find.byType(TextField), '');
      await tester.pumpAndSettle();

      // 完成编辑
      await tester.testTextInput.receiveAction(TextInputAction.done);
      await tester.pumpAndSettle();

      // 验证新节点被删除（因为空文本）
      expect(
        categoryManager.categories.any((node) => node.title == '新分类'),
        false,
      );
    });

    testWidgets('应该能够处理编辑过程中的错误', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 进入编辑模式
      await _startEditing(tester, 0);

      // 输入非常长的文本（测试边界情况）
      final longText = 'A' * 1000;
      await tester.enterText(find.byType(TextField), longText);
      await tester.pumpAndSettle();

      // 完成编辑
      await tester.testTextInput.receiveAction(TextInputAction.done);
      await tester.pumpAndSettle();

      // 验证文本被正确保存（即使很长）
      expect(categoryManager.findNodeById('school')?.title, longText);
    });

    testWidgets('应该能够处理快速连续的编辑操作', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 快速连续编辑操作
      for (int i = 0; i < 5; i++) {
        await _startEditing(tester, 0);
        await tester.enterText(find.byType(TextField), '快速编辑$i');
        await tester.testTextInput.receiveAction(TextInputAction.done);
        await tester.pumpAndSettle();
      }

      // 验证最后一次编辑生效
      expect(categoryManager.findNodeById('school')?.title, '快速编辑4');
    });

    testWidgets('应该能够处理编辑状态的资源清理', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 进入编辑模式
      await _startEditing(tester, 0);
      expect(find.byType(TextField), findsOneWidget);

      // 销毁widget（模拟页面关闭）
      await tester.pumpWidget(Container());
      await tester.pumpAndSettle();

      // 验证没有内存泄漏或错误（这个测试主要是确保dispose方法正确调用）
      // 如果有资源泄漏，测试框架会报告错误
    });

    testWidgets('应该能够处理多个节点同时编辑的冲突', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 尝试同时编辑多个节点（应该只允许一个）
      await _startEditing(tester, 0);
      expect(find.byType(TextField), findsOneWidget);

      // 尝试编辑另一个节点
      await _startEditing(tester, 1);

      // 应该只有一个TextField（新的编辑会取消之前的编辑）
      expect(find.byType(TextField), findsOneWidget);

      // 验证编辑的是第二个节点
      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.controller?.text, '工作');
    });
  });
}

/// 辅助方法：开始编辑指定索引的节点
Future<void> _startEditing(WidgetTester tester, int index) async {
  final popupMenuButtons = find.byType(PopupMenuButton<String>);
  await tester.tap(popupMenuButtons.at(index));
  await tester.pumpAndSettle();

  await tester.tap(find.text('编辑'));
  await tester.pumpAndSettle();
}
