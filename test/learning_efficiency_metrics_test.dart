import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/study_time/models/study_time_models.dart';

void main() {
  group('学习效率指标测试', () {
    test('学习ROI计算测试', () {
      final sessions = [
        StudySession(
          sessionId: '1',
          title: '算法学习',
          category: '计算机科学',
          startTime: DateTime.now(),
          plannedMinutes: 60,
          actualMinutes: 60,
          wage: 50.0,
        ),
        StudySession(
          sessionId: '2',
          title: '英语学习',
          category: '英语',
          startTime: DateTime.now(),
          plannedMinutes: 30,
          actualMinutes: 30,
          wage: 25.0,
        ),
      ];

      final statistics = StudyTimeStatistics(
        date: '2024-01-15',
        totalStudyMinutes: 90,
        completedTasks: 2,
        totalPlannedMinutes: 90,
        totalWage: 75.0,
        sessions: sessions,
        lastUpdated: DateTime.now(),
      );

      // 测试学习ROI计算
      // 效果得分 = 完成任务数 * 10 + 效率百分比 = 2 * 10 + 100 = 120
      // 时间投入 = 90分钟 / 60 = 1.5小时
      // ROI = 120 / 1.5 = 80
      expect(statistics.learningROI, equals(80.0));
    });

    test('并行时间统计测试', () {
      final sessions = [
        StudySession(
          sessionId: '1',
          title: '动觉记忆背单词',
          category: '休息',
          startTime: DateTime.now(),
          plannedMinutes: 30,
          actualMinutes: 30,
          wage: 0.0,
        ),
        StudySession(
          sessionId: '2',
          title: '普通休息',
          category: '休息',
          startTime: DateTime.now(),
          plannedMinutes: 15,
          actualMinutes: 15,
          wage: 0.0,
        ),
      ];

      final statistics = StudyTimeStatistics(
        date: '2024-01-15',
        totalStudyMinutes: 45,
        completedTasks: 2,
        totalPlannedMinutes: 45,
        totalWage: 0.0,
        sessions: sessions,
        lastUpdated: DateTime.now(),
      );

      // 测试并行时间计算 - 只有动觉记忆时间算作并行时间
      expect(statistics.parallelTimeMinutes, equals(30));
    });

    test('专注信噪比计算测试', () {
      final sessions = [
        StudySession(
          sessionId: '1',
          title: '算法学习',
          category: '计算机科学',
          startTime: DateTime.now(),
          plannedMinutes: 60,
          actualMinutes: 60,
          wage: 50.0,
        ),
        StudySession(
          sessionId: '2',
          title: '动觉记忆背单词',
          category: '休息',
          startTime: DateTime.now(),
          plannedMinutes: 20,
          actualMinutes: 20,
          wage: 0.0,
        ),
        StudySession(
          sessionId: '3',
          title: '普通休息',
          category: '休息',
          startTime: DateTime.now(),
          plannedMinutes: 10,
          actualMinutes: 10,
          wage: 0.0,
        ),
      ];

      final statistics = StudyTimeStatistics(
        date: '2024-01-15',
        totalStudyMinutes: 90,
        completedTasks: 3,
        totalPlannedMinutes: 90,
        totalWage: 50.0,
        sessions: sessions,
        lastUpdated: DateTime.now(),
      );

      // 专注时间 = 60分钟（计算机科学）
      // 噪音时间 = 10分钟（普通休息，动觉记忆不算噪音）
      // 信噪比 = 60 / 10 = 6.0
      expect(statistics.focusSignalToNoiseRatio, equals(6.0));
      expect(statistics.focusRatingLevel, equals('需改进')); // 6.0 < 50，所以是需改进
    });

    test('信噪比等级评价测试', () {
      // 测试优秀等级
      final excellentSessions = [
        StudySession(
          sessionId: '1',
          title: '学习',
          category: '计算机科学',
          startTime: DateTime.now(),
          plannedMinutes: 240, // 4小时专注学习
          actualMinutes: 240,
          wage: 200.0,
        ),
        StudySession(
          sessionId: '2',
          title: '休息',
          category: '休息',
          startTime: DateTime.now(),
          plannedMinutes: 3, // 3分钟休息
          actualMinutes: 3,
          wage: 0.0,
        ),
      ];

      final excellentStats = StudyTimeStatistics(
        date: '2024-01-15',
        totalStudyMinutes: 243,
        completedTasks: 2,
        totalPlannedMinutes: 243,
        totalWage: 200.0,
        sessions: excellentSessions,
        lastUpdated: DateTime.now(),
      );

      // 信噪比 = 240 / 3 = 80，应该是优秀
      expect(excellentStats.focusSignalToNoiseRatio, equals(80.0));
      expect(excellentStats.focusRatingLevel, equals('优秀'));
    });
  });
}
