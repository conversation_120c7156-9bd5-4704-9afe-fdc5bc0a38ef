import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/features/reflection/reflection_log_page.dart';

void main() {
  group('优化日志日历响应式布局测试', () {
    testWidgets('iPad 横屏模式 - 日历视图不溢出', (WidgetTester tester) async {
      // 设置 iPad 横屏尺寸
      await tester.binding.setSurfaceSize(const Size(1366, 1024));
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ReflectionLogPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证没有溢出错误
      expect(tester.takeException(), isNull);
      
      // 查找日历切换按钮并点击
      final calendarButton = find.byIcon(Icons.calendar_month_outlined);
      expect(calendarButton, findsOneWidget);
      
      await tester.tap(calendarButton);
      await tester.pumpAndSettle();
      
      // 验证日历视图显示且没有溢出
      expect(tester.takeException(), isNull);
      
      // 验证日历网格存在
      expect(find.byType(GridView), findsOneWidget);
    });

    testWidgets('iPad 竖屏模式 - 日历视图正常显示', (WidgetTester tester) async {
      // 设置 iPad 竖屏尺寸
      await tester.binding.setSurfaceSize(const Size(1024, 1366));
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ReflectionLogPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证没有溢出错误
      expect(tester.takeException(), isNull);
      
      // 点击日历按钮
      final calendarButton = find.byIcon(Icons.calendar_month_outlined);
      await tester.tap(calendarButton);
      await tester.pumpAndSettle();
      
      // 验证日历视图正常显示
      expect(tester.takeException(), isNull);
      expect(find.byType(GridView), findsOneWidget);
    });

    testWidgets('iPhone 模式 - 日历视图正常显示', (WidgetTester tester) async {
      // 设置 iPhone 尺寸
      await tester.binding.setSurfaceSize(const Size(414, 896));
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ReflectionLogPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证没有溢出错误
      expect(tester.takeException(), isNull);
      
      // 点击日历按钮
      final calendarButton = find.byIcon(Icons.calendar_month_outlined);
      await tester.tap(calendarButton);
      await tester.pumpAndSettle();
      
      // 验证日历视图正常显示
      expect(tester.takeException(), isNull);
      expect(find.byType(GridView), findsOneWidget);
    });

    testWidgets('窄屏模式 - 日历视图适配', (WidgetTester tester) async {
      // 设置窄屏尺寸
      await tester.binding.setSurfaceSize(const Size(320, 568));
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ReflectionLogPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证没有溢出错误
      expect(tester.takeException(), isNull);
      
      // 点击日历按钮
      final calendarButton = find.byIcon(Icons.calendar_month_outlined);
      await tester.tap(calendarButton);
      await tester.pumpAndSettle();
      
      // 验证日历视图在窄屏下正常显示
      expect(tester.takeException(), isNull);
      expect(find.byType(GridView), findsOneWidget);
    });

    testWidgets('超宽屏模式 - 日历视图适配', (WidgetTester tester) async {
      // 设置超宽屏尺寸（模拟外接显示器）
      await tester.binding.setSurfaceSize(const Size(1920, 1080));
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ReflectionLogPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证没有溢出错误
      expect(tester.takeException(), isNull);
      
      // 点击日历按钮
      final calendarButton = find.byIcon(Icons.calendar_month_outlined);
      await tester.tap(calendarButton);
      await tester.pumpAndSettle();
      
      // 验证日历视图在超宽屏下正常显示
      expect(tester.takeException(), isNull);
      expect(find.byType(GridView), findsOneWidget);
    });
  });

  group('日历交互功能测试', () {
    testWidgets('月份切换功能正常', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(1366, 1024));
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ReflectionLogPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();
      
      // 打开日历视图
      final calendarButton = find.byIcon(Icons.calendar_month_outlined);
      await tester.tap(calendarButton);
      await tester.pumpAndSettle();
      
      // 测试月份切换按钮
      final prevButton = find.byIcon(Icons.chevron_left);
      final nextButton = find.byIcon(Icons.chevron_right);
      
      expect(prevButton, findsOneWidget);
      expect(nextButton, findsOneWidget);
      
      // 点击下一月
      await tester.tap(nextButton);
      await tester.pumpAndSettle();
      
      // 验证没有错误
      expect(tester.takeException(), isNull);
      
      // 点击上一月
      await tester.tap(prevButton);
      await tester.pumpAndSettle();
      
      // 验证没有错误
      expect(tester.takeException(), isNull);
    });

    testWidgets('日期选择功能正常', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(1366, 1024));
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ReflectionLogPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();
      
      // 打开日历视图
      final calendarButton = find.byIcon(Icons.calendar_month_outlined);
      await tester.tap(calendarButton);
      await tester.pumpAndSettle();
      
      // 查找日期文本（假设当前月有15号）
      final dateText = find.text('15');
      if (dateText.evaluate().isNotEmpty) {
        await tester.tap(dateText.first);
        await tester.pumpAndSettle();
        
        // 验证没有错误
        expect(tester.takeException(), isNull);
      }
    });
  });
}
