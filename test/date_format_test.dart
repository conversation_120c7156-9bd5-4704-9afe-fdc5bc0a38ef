import 'package:flutter_test/flutter_test.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';

void main() {
  group('DateFormat 本地化测试', () {
    setUpAll(() async {
      // 初始化中文本地化数据
      await initializeDateFormatting('zh_CN', null);
    });

    test('中文日期格式化应该正常工作', () {
      final now = DateTime(2024, 7, 15, 14, 30, 0);
      
      // 测试月日格式
      final monthDayFormat = DateFormat('MM月dd日', 'zh_CN');
      expect(monthDayFormat.format(now), '07月15日');
      
      // 测试星期格式
      final weekdayFormat = DateFormat('EEEE', 'zh_CN');
      expect(weekdayFormat.format(now), isNotEmpty);
      
      // 测试完整格式（优化日志页面使用的格式）
      final fullFormat = DateFormat('MM月dd日 EEEE', 'zh_CN');
      final result = fullFormat.format(now);
      expect(result, contains('07月15日'));
      expect(result, isNotEmpty);
      
      print('格式化结果: $result');
    });

    test('默认标题格式应该正常工作', () {
      final now = DateTime(2024, 7, 15);
      
      // 测试优化日志默认标题格式
      final titleFormat = DateFormat('MM月dd日 学习反思');
      final result = titleFormat.format(now);
      expect(result, '07月15日 学习反思');
      
      print('默认标题: $result');
    });

    test('不同日期的格式化应该正确', () {
      final dates = [
        DateTime(2024, 1, 1),
        DateTime(2024, 12, 31),
        DateTime(2024, 6, 15),
      ];
      
      final format = DateFormat('MM月dd日 EEEE', 'zh_CN');
      
      for (final date in dates) {
        final result = format.format(date);
        expect(result, isNotEmpty);
        expect(result, contains('月'));
        expect(result, contains('日'));
        print('日期 ${date.toString().substring(0, 10)} 格式化为: $result');
      }
    });
  });
}
