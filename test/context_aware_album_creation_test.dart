import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/memory_palace/palace_manager_page.dart';
import 'package:oneday/features/photo_album/photo_album_creator_page.dart';

void main() {
  group('基于上下文的相册创建功能测试', () {
    late CategoryManager categoryManager;
    
    setUp(() {
      categoryManager = CategoryManager();
      categoryManager.initializeDefaultCategories();
    });

    testWidgets('PhotoAlbum模型应该包含category字段', (WidgetTester tester) async {
      final album = PhotoAlbum(
        id: '1',
        title: '测试相册',
        imagePaths: ['/path/to/image1.jpg'],
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
        category: '工作',
      );

      expect(album.category, '工作');
    });

    testWidgets('PhotoAlbum copyWith应该正确更新category字段', (WidgetTester tester) async {
      final originalAlbum = PhotoAlbum(
        id: '1',
        title: '原始相册',
        imagePaths: ['/path/to/image1.jpg'],
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
        category: '学校',
      );

      final updatedAlbum = originalAlbum.copyWith(
        category: '工作',
      );

      expect(updatedAlbum.category, '工作');
      expect(updatedAlbum.title, originalAlbum.title); // 其他字段应保持不变
    });

    testWidgets('相册创建应该基于上下文自动分类', (WidgetTester tester) async {
      // 这个测试验证相册创建的逻辑，而不是UI组件
      const testCategory = '工作';

      // 模拟创建相册的过程
      final album = PhotoAlbum(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: '测试相册',
        imagePaths: ['/path/to/image1.jpg'],
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
        category: testCategory, // 基于当前分类上下文设置
      );

      // 验证相册的分类设置正确
      expect(album.category, testCategory);
    });

    testWidgets('分类过滤应该正确工作', (WidgetTester tester) async {
      // 创建测试数据
      final palaces = [
        MemoryPalace(
          id: '1',
          title: '工作相册',
          imagePaths: [],
          anchorCount: 0,
          tags: [],
          category: '工作',
          createdAt: DateTime.now(),
          lastUsed: DateTime.now(),
        ),
        MemoryPalace(
          id: '2',
          title: '学校相册',
          imagePaths: [],
          anchorCount: 0,
          tags: [],
          category: '学校',
          createdAt: DateTime.now(),
          lastUsed: DateTime.now(),
        ),
      ];

      // 测试过滤逻辑
      final workPalaces = palaces.where((palace) => palace.category == '工作').toList();
      expect(workPalaces.length, 1);
      expect(workPalaces.first.title, '工作相册');

      final schoolPalaces = palaces.where((palace) => palace.category == '学校').toList();
      expect(schoolPalaces.length, 1);
      expect(schoolPalaces.first.title, '学校相册');
    });

    test('CategoryManager应该正确初始化默认分类', () {
      final manager = CategoryManager();
      manager.initializeDefaultCategories();

      expect(manager.categories.isNotEmpty, true);
      
      // 验证默认分类包含预期的项目
      final categoryTitles = manager.categories.map((c) => c.title).toList();
      expect(categoryTitles.contains('学校'), true);
      expect(categoryTitles.contains('工作'), true);
    });

    test('分类节点应该有正确的层级结构', () {
      final manager = CategoryManager();
      manager.initializeDefaultCategories();

      final schoolCategory = manager.categories.firstWhere((c) => c.title == '学校');
      expect(schoolCategory.children.isNotEmpty, true);
      expect(schoolCategory.level, 0);

      final primarySchool = schoolCategory.children.firstWhere((c) => c.title == '小学');
      expect(primarySchool.level, 1);
    });
  });
}
