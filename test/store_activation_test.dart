import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/wage_system/services/premium_article_service.dart';

void main() {
  group('Store Activation Functions Tests', () {
    late PremiumArticleService premiumService;

    setUp(() async {
      // 设置测试环境
      SharedPreferences.setMockInitialValues({});
      premiumService = PremiumArticleService.instance;
      await premiumService.initialize();
      await premiumService.resetPremiumAccess();
    });

    test('优质文章阅读券激活功能（7天临时权限）', () async {
      // 测试7天临时权限激活
      final success = await premiumService.activatePremiumAccess(
        activationType: PremiumActivationType.purchase,
        duration: const Duration(days: 7),
        itemId: '12',
        metadata: {
          'purchaseTime': DateTime.now().toIso8601String(),
          'price': 50.0,
          'type': 'temporary_access',
        },
      );

      expect(success, true);
      expect(premiumService.hasPremiumAccess, true);

      final activeRecord = premiumService.getCurrentActiveRecord();
      expect(activeRecord, isNotNull);
      expect(activeRecord!.isPermanent, false);
      expect(activeRecord.activationType, PremiumActivationType.purchase);
      expect(activeRecord.itemId, '12');
      
      // 检查剩余时间应该接近7天
      final remainingTime = activeRecord.getRemainingTime();
      expect(remainingTime, isNotNull);
      expect(remainingTime!.inDays, 6); // 应该是6天多（因为有一些时间差）
    });

    test('优质文章访问权限激活功能（永久权限）', () async {
      // 测试永久权限激活
      final success = await premiumService.activatePremiumAccess(
        activationType: PremiumActivationType.purchase,
        itemId: '14',
        metadata: {
          'purchaseTime': DateTime.now().toIso8601String(),
          'price': 300.0,
        },
      );

      expect(success, true);
      expect(premiumService.hasPremiumAccess, true);

      final activeRecord = premiumService.getCurrentActiveRecord();
      expect(activeRecord, isNotNull);
      expect(activeRecord!.isPermanent, true);
      expect(activeRecord.activationType, PremiumActivationType.purchase);
      expect(activeRecord.itemId, '14');
      expect(activeRecord.getRemainingTime(), isNull); // 永久权限没有剩余时间
    });

    test('AI学习助手激活状态保存', () async {
      // 模拟AI助手激活
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('ai_assistant_activated', true);
      await prefs.setString('ai_assistant_activation_time', DateTime.now().toIso8601String());

      // 验证激活状态
      final isActivated = prefs.getBool('ai_assistant_activated') ?? false;
      final activationTime = prefs.getString('ai_assistant_activation_time');

      expect(isActivated, true);
      expect(activationTime, isNotNull);
      
      // 验证激活时间格式正确
      final parsedTime = DateTime.tryParse(activationTime!);
      expect(parsedTime, isNotNull);
      expect(parsedTime!.isBefore(DateTime.now().add(const Duration(seconds: 1))), true);
    });

    test('多种权限类型共存测试', () async {
      // 先激活临时权限
      await premiumService.activatePremiumAccess(
        activationType: PremiumActivationType.trial,
        duration: const Duration(hours: 1),
        itemId: '12',
      );

      // 再激活永久权限
      await premiumService.activatePremiumAccess(
        activationType: PremiumActivationType.purchase,
        itemId: '14',
      );

      // 验证当前生效的是永久权限（最新激活的）
      final activeRecord = premiumService.getCurrentActiveRecord();
      expect(activeRecord, isNotNull);
      expect(activeRecord!.isPermanent, true);
      expect(activeRecord.itemId, '14');

      // 验证激活历史包含两条记录
      final history = premiumService.activationHistory;
      expect(history.length, 2);
      expect(history.first.itemId, '14'); // 最新的在前
      expect(history.last.itemId, '12');
    });

    test('权限过期检查功能', () async {
      // 激活一个很短的临时权限（1毫秒）
      await premiumService.activatePremiumAccess(
        activationType: PremiumActivationType.trial,
        duration: const Duration(milliseconds: 1),
      );

      expect(premiumService.hasPremiumAccess, true);

      // 等待权限过期
      await Future.delayed(const Duration(milliseconds: 10));

      // 检查权限状态更新
      await premiumService.checkAndUpdatePermissionStatus();

      expect(premiumService.hasPremiumAccess, false);
    });
  });
}
