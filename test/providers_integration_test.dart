import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// 导入所有Provider
import 'package:oneday/features/learning_report/providers/learning_report_providers.dart';
import 'package:oneday/features/vocabulary/providers/vocabulary_providers.dart';
import 'package:oneday/features/exercise/providers/exercise_providers.dart';
import 'package:oneday/features/ability_radar/providers/ability_radar_providers.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('Provider Integration Tests - Basic Initialization', () {
    test('Learning Report Providers should be defined', () {
      // 只测试Provider是否正确定义，不触发实际的服务初始化
      expect(learningReportServiceProvider, isNotNull);
      expect(learningReportExportServiceProvider, isNotNull);
      expect(currentLearningReportProvider, isNotNull);
      expect(learningReportQuickAccessProvider, isNotNull);
    });

    test('Vocabulary Providers should be defined', () {
      // 只测试Provider是否正确定义
      expect(vocabularyServiceProvider, isNotNull);
      expect(fsrsServiceProvider, isNotNull);
      expect(vocabularyLearningServiceProvider, isNotNull);
      expect(vocabularyProgressProvider, isNotNull);
      expect(vocabularyDataProvider, isNotNull);
      expect(memoryVocabularyProvider, isNotNull);
      expect(vocabularySearchProvider, isNotNull);
    });

    test('Exercise Providers should be defined', () {
      // 只测试Provider是否正确定义
      expect(customActionLibraryServiceProvider, isNotNull);
      expect(paoIntegrationServiceProvider, isNotNull);
      expect(customLibrariesProvider, isNotNull);
      expect(exerciseSessionProvider, isNotNull);
      expect(currentPAOExerciseMapProvider, isNotNull);
      expect(exerciseLibraryStatsProvider, isNotNull);
    });

    test('Ability Radar Provider should be defined', () {
      // 只测试Provider是否正确定义
      expect(abilityRadarServiceProvider, isNotNull);
    });

    test('Provider types should be correct', () {
      // 验证Provider的类型是否正确
      expect(learningReportServiceProvider, isA<Provider>());
      expect(vocabularyServiceProvider, isA<Provider>());
      expect(customActionLibraryServiceProvider, isA<Provider>());
      expect(abilityRadarServiceProvider, isA<Provider>());
      
      expect(currentLearningReportProvider, isA<StateNotifierProvider>());
      expect(vocabularyProgressProvider, isA<StateNotifierProvider>());
      expect(customLibrariesProvider, isA<StateNotifierProvider>());
      expect(exerciseSessionProvider, isA<StateNotifierProvider>());
      expect(vocabularySearchProvider, isA<StateNotifierProvider>());
      
      expect(vocabularyDataProvider, isA<FutureProvider>());
      expect(memoryVocabularyProvider, isA<FutureProvider>());
    });

    test('Provider dependencies should be correctly wired (basic check)', () {
      // 基本的依赖检查，不触发实际的服务初始化
      late ProviderContainer container;
      
      container = ProviderContainer();
      
      try {
        // 检查Provider是否能被正确读取（不执行实际逻辑）
        expect(() => container.read(learningReportServiceProvider), returnsNormally);
        expect(() => container.read(vocabularyServiceProvider), returnsNormally);
        expect(() => container.read(customActionLibraryServiceProvider), returnsNormally);
        expect(() => container.read(paoIntegrationServiceProvider), returnsNormally);
        expect(() => container.read(abilityRadarServiceProvider), returnsNormally);
      } finally {
        container.dispose();
      }
    });

    test('StateNotifier providers should have correct initial states', () {
      late ProviderContainer container;
      
      container = ProviderContainer();
      
      try {
        // 测试StateNotifier的初始状态
        final reportState = container.read(currentLearningReportProvider);
        expect(reportState.isLoading, false);
        expect(reportState.reportData, isNull);
        expect(reportState.error, isNull);

        final searchState = container.read(vocabularySearchProvider);
        expect(searchState.query, isEmpty);
        expect(searchState.results, isEmpty);
        expect(searchState.isLoading, false);

        final sessionState = container.read(exerciseSessionProvider);
        expect(sessionState.exercises, isEmpty);
        expect(sessionState.isActive, false);
        expect(sessionState.completedExercises, 0);
      } finally {
        container.dispose();
      }
    });
  });
}
