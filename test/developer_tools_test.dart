import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:oneday/features/profile/profile_page.dart';

void main() {
  group('开发者工具测试', () {
    late GoRouter router;

    setUp(() {
      router = GoRouter(
        initialLocation: '/profile',
        routes: [
          GoRoute(
            path: '/profile',
            builder: (context, state) => const ProfilePage(),
          ),
          GoRoute(
            path: '/login',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('登录页面')),
            ),
          ),
          GoRoute(
            path: '/settings',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('设置页面')),
            ),
          ),
        ],
      );
    });

    testWidgets('头像单次点击应该直接跳转到登录页面', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp.router(
          routerConfig: router,
          theme: ThemeData(
            useMaterial3: true,
            primaryColor: const Color(0xFF2E7EED),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证个人主页加载成功
      expect(find.text('我的'), findsOneWidget);

      // 查找并点击头像区域
      final userCardFinder = find.byType(GestureDetector).first;
      await tester.tap(userCardFinder);
      await tester.pumpAndSettle();

      // 验证跳转到登录页面
      expect(find.text('登录页面'), findsOneWidget);

      print('✅ 头像单次点击成功跳转到登录页面');
    });

    testWidgets('开发者工具按钮在调试模式下应该显示', (WidgetTester tester) async {
      // 这个测试只在调试模式下有效
      if (!kDebugMode) {
        print('⚠️ 跳过测试：当前不在调试模式');
        return;
      }

      await tester.pumpWidget(
        MaterialApp.router(
          routerConfig: router,
          theme: ThemeData(
            useMaterial3: true,
            primaryColor: const Color(0xFF2E7EED),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 滚动到底部找到开发者工具按钮
      await tester.dragUntilVisible(
        find.text('开发者工具'),
        find.byType(SingleChildScrollView),
        const Offset(0, -200),
      );
      await tester.pumpAndSettle();

      // 验证开发者工具按钮存在
      expect(find.text('开发者工具'), findsOneWidget);
      expect(find.text('测试功能和开发者选项'), findsOneWidget);
      expect(find.byIcon(Icons.developer_mode), findsOneWidget);

      print('✅ 开发者工具按钮在调试模式下正常显示');
    });

    testWidgets('点击开发者工具按钮应该打开开发者对话框', (WidgetTester tester) async {
      if (!kDebugMode) {
        print('⚠️ 跳过测试：当前不在调试模式');
        return;
      }

      await tester.pumpWidget(
        MaterialApp.router(
          routerConfig: router,
          theme: ThemeData(
            useMaterial3: true,
            primaryColor: const Color(0xFF2E7EED),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 滚动到底部找到开发者工具按钮
      await tester.dragUntilVisible(
        find.text('开发者工具'),
        find.byType(SingleChildScrollView),
        const Offset(0, -200),
      );
      await tester.pumpAndSettle();

      // 点击开发者工具按钮
      await tester.tap(find.text('开发者工具'));
      await tester.pumpAndSettle();

      // 验证开发者对话框打开
      expect(find.byType(AlertDialog), findsOneWidget);
      expect(find.text('选择要执行的开发者操作：'), findsOneWidget);

      // 验证开发者选项存在
      expect(find.text('重置引导页'), findsOneWidget);
      expect(find.text('清除所有数据'), findsOneWidget);
      expect(find.text('调试信息'), findsOneWidget);

      print('✅ 开发者工具对话框正常打开，包含所有选项');
    });

    testWidgets('开发者工具基本功能测试', (WidgetTester tester) async {
      if (!kDebugMode) {
        print('⚠️ 跳过测试：当前不在调试模式');
        return;
      }

      await tester.pumpWidget(
        MaterialApp.router(
          routerConfig: router,
          theme: ThemeData(
            useMaterial3: true,
            primaryColor: const Color(0xFF2E7EED),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 滚动到底部找到开发者工具按钮
      await tester.dragUntilVisible(
        find.text('开发者工具'),
        find.byType(SingleChildScrollView),
        const Offset(0, -200),
      );
      await tester.pumpAndSettle();

      // 验证开发者工具按钮存在
      expect(find.text('开发者工具'), findsOneWidget);
      expect(find.byIcon(Icons.developer_mode), findsOneWidget);

      print('✅ 开发者工具基本功能测试通过');
    });
  });
}
