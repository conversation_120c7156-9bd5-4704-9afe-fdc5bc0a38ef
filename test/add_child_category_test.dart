import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/memory_palace/palace_manager_page.dart';

void main() {
  group('添加子分类功能测试', () {
    late CategoryManager categoryManager;

    setUp(() {
      categoryManager = CategoryManager();
      categoryManager.initializeDefaultCategories();
    });

    /// 创建测试用的CategorySidebar widget
    Widget createTestWidget({
      required CategoryManager manager,
      VoidCallback? onCategoriesChanged,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: CategorySidebar(
            isVisible: true,
            onClose: () {},
            onNodeSelected: (node) {},
            onEditingStateChanged: (isEditing) {},
            categoryManager: manager,
            onCategoriesChanged: onCategoriesChanged ?? () {},
          ),
        ),
      );
    }

    test('CategoryManager.addNode应该正确创建子分类', () {
      final parentNode = categoryManager.findNodeById('school')!;
      final originalChildrenCount = parentNode.children.length;

      // 添加子分类
      categoryManager.addNode('新子分类', parent: parentNode);

      // 验证子分类被正确添加
      expect(parentNode.children.length, originalChildrenCount + 1);

      final newChild = parentNode.children.last;
      expect(newChild.title, '新子分类');
      expect(newChild.level, parentNode.level + 1);
      expect(newChild.isNew, true);
      expect(parentNode.isExpanded, true); // 父节点应该自动展开
    });

    test('子分类应该有正确的层级结构', () {
      final schoolNode = categoryManager.findNodeById('school')!;
      final originalLevel = schoolNode.level;

      // 添加子分类
      categoryManager.addNode('体育', parent: schoolNode);
      final sportsNode = schoolNode.children.last;

      // 验证层级正确
      expect(sportsNode.level, originalLevel + 1);

      // 再添加子分类的子分类
      categoryManager.addNode('足球', parent: sportsNode);
      final footballNode = sportsNode.children.last;

      // 验证三级层级
      expect(footballNode.level, originalLevel + 2);
      expect(sportsNode.isExpanded, true);
    });

    testWidgets('PopupMenu应该包含添加子分类选项', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 查找第一个PopupMenuButton（学校节点的菜单）
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      expect(popupMenuButtons, findsWidgets);

      // 点击第一个PopupMenuButton
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      // 验证菜单项存在
      expect(find.text('编辑'), findsOneWidget);
      expect(find.text('添加子分类'), findsOneWidget);
      expect(find.text('删除'), findsOneWidget);
    });

    testWidgets('点击添加子分类应该创建新的子节点', (WidgetTester tester) async {
      bool categoriesChanged = false;

      await tester.pumpWidget(
        createTestWidget(
          manager: categoryManager,
          onCategoriesChanged: () {
            categoriesChanged = true;
          },
        ),
      );
      await tester.pumpAndSettle();

      final schoolNode = categoryManager.findNodeById('school')!;
      final originalChildrenCount = schoolNode.children.length;

      // 点击PopupMenuButton
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      // 点击添加子分类
      await tester.tap(find.text('添加子分类'));
      await tester.pumpAndSettle();

      // 验证子分类被添加
      expect(schoolNode.children.length, originalChildrenCount + 1);
      expect(schoolNode.isExpanded, true);
      expect(categoriesChanged, true);

      final newChild = schoolNode.children.last;
      expect(newChild.title, '新子分类');
      expect(newChild.level, schoolNode.level + 1);
      expect(newChild.isNew, true);
    });

    testWidgets('新创建的子分类应该自动进入编辑模式', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 点击PopupMenuButton
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      // 点击添加子分类
      await tester.tap(find.text('添加子分类'));
      await tester.pumpAndSettle();

      // 验证进入编辑模式 - 应该出现TextField
      expect(find.byType(TextField), findsOneWidget);

      // 验证TextField中的文本是新子分类的标题
      final textField = tester.widget<TextField>(find.byType(TextField));
      expect(textField.controller?.text, '新子分类');
    });

    testWidgets('应该能够为不同层级的节点添加子分类', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(manager: categoryManager));
      await tester.pumpAndSettle();

      // 先为学校节点添加子分类
      final schoolNode = categoryManager.findNodeById('school')!;
      categoryManager.addNode('体育', parent: schoolNode);

      await tester.pumpAndSettle();

      // 验证体育节点被正确添加
      final sportsNode = schoolNode.children.last;
      expect(sportsNode.title, '体育');
      expect(sportsNode.level, schoolNode.level + 1);

      // 再为体育节点添加子分类
      categoryManager.addNode('足球', parent: sportsNode);

      // 验证足球节点被正确添加
      final footballNode = sportsNode.children.last;
      expect(footballNode.title, '足球');
      expect(footballNode.level, sportsNode.level + 1);
      expect(footballNode.level, schoolNode.level + 2);
    });

    test('添加子分类后应该更新搜索结果', () {
      final schoolNode = categoryManager.findNodeById('school')!;

      // 添加子分类
      categoryManager.addNode('音乐', parent: schoolNode);

      // 搜索新添加的子分类
      categoryManager.searchCategories('音乐');

      // 验证搜索结果包含新子分类
      final searchResults = categoryManager.searchResults;
      expect(searchResults.any((node) => node.title == '音乐'), true);
    });

    test('应该能够为根节点和子节点都添加子分类', () {
      // 为根节点添加子分类
      final schoolNode = categoryManager.findNodeById('school')!;
      categoryManager.addNode('艺术', parent: schoolNode);

      final artNode = schoolNode.children.last;
      expect(artNode.title, '艺术');
      expect(artNode.level, 1);

      // 为子节点添加子分类
      categoryManager.addNode('绘画', parent: artNode);

      final paintingNode = artNode.children.last;
      expect(paintingNode.title, '绘画');
      expect(paintingNode.level, 2);

      // 验证层级结构正确
      expect(schoolNode.level, 0);
      expect(artNode.level, 1);
      expect(paintingNode.level, 2);
    });

    test('边界情况：为空子节点列表的节点添加子分类', () {
      // 创建一个没有子节点的新节点
      final newNode = CategoryNode(title: '测试节点', level: 0);
      categoryManager.categories.add(newNode);

      expect(newNode.children.isEmpty, true);

      // 添加子分类
      categoryManager.addNode('第一个子分类', parent: newNode);

      // 验证子分类被正确添加
      expect(newNode.children.length, 1);
      expect(newNode.children.first.title, '第一个子分类');
      expect(newNode.children.first.level, 1);
      expect(newNode.isExpanded, true);
    });
  });
}
