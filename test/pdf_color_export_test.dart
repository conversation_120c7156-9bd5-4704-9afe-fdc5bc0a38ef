import 'package:flutter_test/flutter_test.dart';
import 'package:pdf/pdf.dart';
import 'package:oneday/features/learning_report/services/learning_report_export_service.dart';

void main() {
  group('PDF颜色导出测试', () {
    test('验证OneDay颜色方案定义', () {
      // 验证主色调
      expect(
        LearningReportExportService.oneDayColors['primary'],
        equals(const PdfColor.fromInt(0xFF2E7EED)),
      );

      // 验证学科分类颜色
      expect(
        LearningReportExportService.oneDayColors['computerScience'],
        equals(const PdfColor.fromInt(0xFFE03E3E)),
      );
      expect(
        LearningReportExportService.oneDayColors['mathematics'],
        equals(const PdfColor.fromInt(0xFFFFD700)),
      );
      expect(
        LearningReportExportService.oneDayColors['english'],
        equals(const PdfColor.fromInt(0xFF0F7B6C)),
      );
      expect(
        LearningReportExportService.oneDayColors['politics'],
        equals(const PdfColor.fromInt(0xFF2E7EED)),
      );

      // 验证功能色
      expect(
        LearningReportExportService.oneDayColors['success'],
        equals(const PdfColor.fromInt(0xFF0F7B6C)),
      );
      expect(
        LearningReportExportService.oneDayColors['warning'],
        equals(const PdfColor.fromInt(0xFFD9730D)),
      );
      expect(
        LearningReportExportService.oneDayColors['error'],
        equals(const PdfColor.fromInt(0xFFE03E3E)),
      );

      // 验证中性色
      expect(
        LearningReportExportService.oneDayColors['textPrimary'],
        equals(const PdfColor.fromInt(0xFF37352F)),
      );
      expect(
        LearningReportExportService.oneDayColors['background'],
        equals(PdfColors.white),
      );
    });

    test('验证颜色常量完整性', () {
      // 验证所有必需的颜色都已定义
      final colors = LearningReportExportService.oneDayColors;

      // 主色调
      expect(colors.containsKey('primary'), isTrue);
      expect(colors.containsKey('primaryDark'), isTrue);

      // 学科分类颜色
      expect(colors.containsKey('computerScience'), isTrue);
      expect(colors.containsKey('mathematics'), isTrue);
      expect(colors.containsKey('english'), isTrue);
      expect(colors.containsKey('politics'), isTrue);
      expect(colors.containsKey('rest'), isTrue);

      // 功能色
      expect(colors.containsKey('success'), isTrue);
      expect(colors.containsKey('warning'), isTrue);
      expect(colors.containsKey('error'), isTrue);
      expect(colors.containsKey('info'), isTrue);

      // 中性色
      expect(colors.containsKey('textPrimary'), isTrue);
      expect(colors.containsKey('textSecondary'), isTrue);
      expect(colors.containsKey('textHint'), isTrue);
      expect(colors.containsKey('border'), isTrue);
      expect(colors.containsKey('background'), isTrue);

      // 图表颜色
      expect(colors.containsKey('chart1'), isTrue);
      expect(colors.containsKey('chart2'), isTrue);
      expect(colors.containsKey('chart3'), isTrue);
      expect(colors.containsKey('chart4'), isTrue);
      expect(colors.containsKey('chart5'), isTrue);
    });

    test('验证颜色值正确性', () {
      final colors = LearningReportExportService.oneDayColors;

      // 验证颜色值不为null且有效
      colors.forEach((key, color) {
        expect(color, isNotNull, reason: '颜色 $key 不应为null');
        expect(color, isA<PdfColor>(), reason: '颜色 $key 应为PdfColor类型');
      });

      // 验证特定颜色对象
      expect(colors['primary'], equals(const PdfColor.fromInt(0xFF2E7EED)));
      expect(
        colors['computerScience'],
        equals(const PdfColor.fromInt(0xFFE03E3E)),
      );
      expect(colors['mathematics'], equals(const PdfColor.fromInt(0xFFFFD700)));
      expect(colors['english'], equals(const PdfColor.fromInt(0xFF0F7B6C)));
    });
  });
}
