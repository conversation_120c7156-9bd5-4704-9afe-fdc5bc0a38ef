import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/achievement/models/achievement.dart';

void main() {
  group('成就解锁界面导航修复测试', () {
    test('成就模型应该正确创建', () {
      // 创建测试成就
      final testAchievement = Achievement(
        id: 'test_001',
        name: '测试成就',
        description: '这是一个测试成就',
        icon: '🏆',
        type: AchievementType.memory,
        rarity: AchievementRarity.common,
        unlockCondition: '测试条件',
        maxProgress: 1,
        experienceReward: 100,
        wageReward: 10,
        createdAt: DateTime.now(),
      );

      // 验证成就属性
      expect(testAchievement.id, 'test_001');
      expect(testAchievement.name, '测试成就');
      expect(testAchievement.description, '这是一个测试成就');
      expect(testAchievement.icon, '🏆');
      expect(testAchievement.type, AchievementType.memory);
      expect(testAchievement.rarity, AchievementRarity.common);
      expect(testAchievement.experienceReward, 100);
      expect(testAchievement.wageReward, 10);
      expect(testAchievement.maxProgress, 1);
    });

    test('成就应该有正确的类型和稀有度', () {
      // 测试不同类型的成就
      final memoryAchievement = Achievement(
        id: 'memory_001',
        name: '记忆大师',
        description: '创建第一个记忆宫殿',
        icon: '🏰',
        type: AchievementType.memory,
        rarity: AchievementRarity.common,
        unlockCondition: '创建1个记忆宫殿',
        maxProgress: 1,
        experienceReward: 50,
        wageReward: 5,
        createdAt: DateTime.now(),
      );

      final learningAchievement = Achievement(
        id: 'learning_001',
        name: '学习达人',
        description: '完成第一次学习',
        icon: '📚',
        type: AchievementType.learning,
        rarity: AchievementRarity.rare,
        unlockCondition: '完成1次学习',
        maxProgress: 1,
        experienceReward: 100,
        wageReward: 10,
        createdAt: DateTime.now(),
      );

      // 验证类型
      expect(memoryAchievement.type, AchievementType.memory);
      expect(learningAchievement.type, AchievementType.learning);

      // 验证稀有度
      expect(memoryAchievement.rarity, AchievementRarity.common);
      expect(learningAchievement.rarity, AchievementRarity.rare);

      // 验证类型名称
      expect(memoryAchievement.typeName, '记忆成就');
      expect(learningAchievement.typeName, '学习成就');

      // 验证稀有度名称
      expect(memoryAchievement.rarityName, '普通');
      expect(learningAchievement.rarityName, '稀有');
    });
  });

  group('防重复创建测试', () {
    test('应该有防重复创建的逻辑', () {
      // 这个测试验证防重复创建的概念
      bool isCreating = false;

      // 模拟第一次创建
      expect(isCreating, false);
      isCreating = true;
      expect(isCreating, true);

      // 模拟重复创建请求 - 应该被阻止
      if (isCreating) {
        // 这里应该阻止重复创建
        expect(isCreating, true, reason: '应该检测到正在创建中的状态');
      }

      // 创建完成后重置标志
      isCreating = false;
      expect(isCreating, false);
    });
  });
}
