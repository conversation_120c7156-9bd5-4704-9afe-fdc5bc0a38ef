import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/features/vocabulary/vocabulary_category_page.dart';
import 'package:oneday/features/vocabulary/word_model.dart';

void main() {
  group('VocabularyCategoryPage Scrollbar Tests', () {
    late VocabularyCategory testCategory;

    setUp(() {
      testCategory = VocabularyCategory(
        id: 'test_category',
        name: '测试词库',
        description: '用于测试的词库',
        totalWords: 100,
        selectedWords: 10,
        createdAt: DateTime.now(),
      );
    });

    testWidgets('应该显示Scrollbar组件', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: VocabularyCategoryPage(category: testCategory),
          ),
        ),
      );

      // 等待页面加载
      await tester.pumpAndSettle();

      // 查找Scrollbar组件
      expect(find.byType(Scrollbar), findsOneWidget);
    });

    testWidgets('Scrollbar应该有正确的配置', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: VocabularyCategoryPage(category: testCategory),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找Scrollbar组件
      final scrollbarFinder = find.byType(Scrollbar);
      expect(scrollbarFinder, findsOneWidget);

      final scrollbar = tester.widget<Scrollbar>(scrollbarFinder);
      
      // 验证Scrollbar配置
      expect(scrollbar.thumbVisibility, isTrue);
      expect(scrollbar.trackVisibility, isTrue);
      expect(scrollbar.interactive, isTrue);
      expect(scrollbar.thickness, equals(6.0));
      expect(scrollbar.radius, equals(const Radius.circular(3.0)));
      expect(scrollbar.scrollbarOrientation, equals(ScrollbarOrientation.right));
    });

    testWidgets('应该显示ScrollbarTheme组件', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: VocabularyCategoryPage(category: testCategory),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找ScrollbarTheme组件
      expect(find.byType(ScrollbarTheme), findsOneWidget);
    });

    testWidgets('ListView应该有ScrollController', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: VocabularyCategoryPage(category: testCategory),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找ListView组件
      final listViewFinder = find.byType(ListView);
      if (listViewFinder.evaluate().isNotEmpty) {
        final listView = tester.widget<ListView>(listViewFinder);
        expect(listView.controller, isNotNull);
      }
    });

    testWidgets('滚动条主题应该使用OneDay设计颜色', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: VocabularyCategoryPage(category: testCategory),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找ScrollbarTheme组件
      final scrollbarThemeFinder = find.byType(ScrollbarTheme);
      expect(scrollbarThemeFinder, findsOneWidget);

      final scrollbarTheme = tester.widget<ScrollbarTheme>(scrollbarThemeFinder);
      final themeData = scrollbarTheme.data;
      
      // 验证主题配置
      expect(themeData.thumbVisibility?.resolve({}), isTrue);
      expect(themeData.trackVisibility?.resolve({}), isTrue);
      expect(themeData.interactive, isTrue);
      expect(themeData.thickness?.resolve({}), equals(6.0));
      expect(themeData.radius, equals(const Radius.circular(3.0)));
      expect(themeData.minThumbLength, equals(48.0));
      expect(themeData.crossAxisMargin, equals(2.0));
      expect(themeData.mainAxisMargin, equals(4.0));
    });
  });
}
