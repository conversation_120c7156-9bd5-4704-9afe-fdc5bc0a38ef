import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/time_box/models/timebox_models.dart';
import 'package:oneday/features/time_box/managers/task_category_manager.dart';

void main() {
  group('TaskCategory Tests', () {
    late TaskCategoryManager categoryManager;

    setUp(() async {
      // 清理SharedPreferences
      SharedPreferences.setMockInitialValues({});
      categoryManager = TaskCategoryManager();
    });

    test('应该创建默认分类', () async {
      await categoryManager.loadFromStorage();

      expect(categoryManager.categories.length, greaterThan(0));

      // 检查默认分类是否存在
      final defaultCategories = categoryManager.getDefaultCategories();
      expect(defaultCategories.length, equals(4));

      // 检查特定默认分类
      final computerScience = categoryManager.findCategoryByName('计算机科学');
      expect(computerScience, isNotNull);
      expect(computerScience!.isDefault, isTrue);
      expect(computerScience.color, equals(const Color(0xFFE03E3E)));
    });

    test('应该能够添加自定义分类', () async {
      await categoryManager.loadFromStorage();

      final initialCount = categoryManager.categories.length;

      // 添加自定义分类
      final success = await categoryManager.addCategory(
        '自定义分类',
        Colors.purple,
        'work',
      );

      expect(success, isTrue);
      expect(categoryManager.categories.length, equals(initialCount + 1));

      final customCategory = categoryManager.findCategoryByName('自定义分类');
      expect(customCategory, isNotNull);
      expect(customCategory!.isDefault, isFalse);
      expect(customCategory.iconName, equals('work'));
    });

    test('应该防止重复的分类名称', () async {
      await categoryManager.loadFromStorage();

      // 尝试添加与默认分类同名的分类
      final success = await categoryManager.addCategory(
        '计算机科学',
        Colors.blue,
        'computer',
      );

      expect(success, isFalse);
    });

    test('应该能够更新自定义分类', () async {
      await categoryManager.loadFromStorage();

      // 先添加一个自定义分类
      await categoryManager.addCategory('测试分类', Colors.red, 'test');

      final category = categoryManager.findCategoryByName('测试分类');
      expect(category, isNotNull);

      // 更新分类
      final success = await categoryManager.updateCategory(
        category!.id,
        '更新后的分类',
        Colors.green,
        'updated',
      );

      expect(success, isTrue);

      final updatedCategory = categoryManager.findCategoryById(category.id);
      expect(updatedCategory, isNotNull);
      expect(updatedCategory!.name, equals('更新后的分类'));
      expect(updatedCategory.color.toARGB32(), equals(Colors.green.toARGB32()));
      expect(updatedCategory.iconName, equals('updated'));
    });

    test('应该能够删除自定义分类', () async {
      await categoryManager.loadFromStorage();

      // 先添加一个自定义分类
      await categoryManager.addCategory('待删除分类', Colors.orange, 'delete');

      final category = categoryManager.findCategoryByName('待删除分类');
      expect(category, isNotNull);

      final initialCount = categoryManager.categories.length;

      // 删除分类
      final success = await categoryManager.deleteCategory(category!.id);

      expect(success, isTrue);
      expect(categoryManager.categories.length, equals(initialCount - 1));
      expect(categoryManager.findCategoryByName('待删除分类'), isNull);
    });

    test('不应该能够删除默认分类', () async {
      await categoryManager.loadFromStorage();

      final defaultCategory = categoryManager.findCategoryByName('计算机科学');
      expect(defaultCategory, isNotNull);

      final initialCount = categoryManager.categories.length;

      // 尝试删除默认分类
      final success = await categoryManager.deleteCategory(defaultCategory!.id);

      expect(success, isFalse);
      expect(categoryManager.categories.length, equals(initialCount));
    });

    test('应该能够获取分类颜色', () async {
      await categoryManager.loadFromStorage();

      // 测试默认分类颜色
      final computerScienceColor = categoryManager.getCategoryColor('计算机科学');
      expect(computerScienceColor, equals(const Color(0xFFE03E3E)));

      // 测试不存在的分类（应该回退到默认颜色）
      final unknownColor = categoryManager.getCategoryColor('不存在的分类');
      expect(unknownColor, isNotNull);
    });

    test('应该正确处理迁移逻辑', () async {
      // 模拟现有任务数据
      SharedPreferences.setMockInitialValues({
        'timebox_tasks_v1': '''[
          {
            "id": "1",
            "title": "测试任务1",
            "description": "描述",
            "plannedMinutes": 60,
            "status": "pending",
            "priority": "medium",
            "category": "自定义学科",
            "createdAt": "2024-01-01T00:00:00.000Z"
          },
          {
            "id": "2",
            "title": "测试任务2",
            "description": "描述",
            "plannedMinutes": 30,
            "status": "pending",
            "priority": "high",
            "category": "计算机科学",
            "createdAt": "2024-01-01T00:00:00.000Z"
          }
        ]''',
      });

      await categoryManager.loadFromStorage();

      // 应该包含默认分类和迁移的自定义分类
      expect(categoryManager.categories.length, greaterThan(6));

      // 检查迁移的分类
      final migratedCategory = categoryManager.findCategoryByName('自定义学科');
      expect(migratedCategory, isNotNull);
      expect(migratedCategory!.isDefault, isFalse);
    });
  });

  group('TaskCategory Model Tests', () {
    test('应该正确创建TaskCategory实例', () {
      final now = DateTime.now();
      final category = TaskCategory(
        id: 'test_id',
        name: '测试分类',
        colorValue: Colors.blue.toARGB32(),
        iconName: 'test_icon',
        isDefault: false,
        createdAt: now,
        lastModified: now,
      );

      expect(category.id, equals('test_id'));
      expect(category.name, equals('测试分类'));
      expect(category.color.toARGB32(), equals(Colors.blue.toARGB32()));
      expect(category.iconName, equals('test_icon'));
      expect(category.isDefault, isFalse);
      expect(category.createdAt, equals(now));
      expect(category.lastModified, equals(now));
    });

    test('应该正确进行JSON序列化和反序列化', () {
      final now = DateTime.now();
      final originalCategory = TaskCategory(
        id: 'test_id',
        name: '测试分类',
        colorValue: Colors.red.toARGB32(),
        iconName: 'test_icon',
        isDefault: true,
        createdAt: now,
        lastModified: now,
      );

      // 序列化
      final json = originalCategory.toJson();

      // 反序列化
      final deserializedCategory = TaskCategory.fromJson(json);

      expect(deserializedCategory.id, equals(originalCategory.id));
      expect(deserializedCategory.name, equals(originalCategory.name));
      expect(
        deserializedCategory.color.toARGB32(),
        equals(originalCategory.color.toARGB32()),
      );
      expect(deserializedCategory.iconName, equals(originalCategory.iconName));
      expect(
        deserializedCategory.isDefault,
        equals(originalCategory.isDefault),
      );
      expect(
        deserializedCategory.createdAt,
        equals(originalCategory.createdAt),
      );
      expect(
        deserializedCategory.lastModified,
        equals(originalCategory.lastModified),
      );
    });

    test('应该正确使用copyWith方法', () {
      final now = DateTime.now();
      final originalCategory = TaskCategory(
        id: 'test_id',
        name: '原始分类',
        colorValue: Colors.blue.toARGB32(),
        iconName: 'original_icon',
        isDefault: false,
        createdAt: now,
        lastModified: now,
      );

      final updatedCategory = originalCategory.copyWith(
        name: '更新后的分类',
        color: Colors.green,
        iconName: 'updated_icon',
      );

      expect(updatedCategory.id, equals(originalCategory.id));
      expect(updatedCategory.name, equals('更新后的分类'));
      expect(updatedCategory.color.toARGB32(), equals(Colors.green.toARGB32()));
      expect(updatedCategory.iconName, equals('updated_icon'));
      expect(updatedCategory.isDefault, equals(originalCategory.isDefault));
      expect(updatedCategory.createdAt, equals(originalCategory.createdAt));
    });
  });
}
