/// 气泡偏移值验证脚本
/// 验证修复后的偏移值计算是否正确
library;

void main() {
  print('🔍 气泡偏移值验证');
  print('=' * 50);

  verifyBubbleStructure();
  verifyOffsetCalculation();
  verifyPositionAccuracy();
}

/// 验证气泡结构尺寸
void verifyBubbleStructure() {
  print('\n📐 气泡结构验证');
  print('-' * 20);

  // 统一后的气泡组件尺寸
  const textBoxPadding = 8.0; // vertical padding: 4 + 4
  const textHeight = 11.0; // fontSize: 11
  const borderWidth = 1.0; // 统一边框宽度
  const lineHeight = 8.0; // 指针连线高度
  const dotHeight = 4.0; // 圆点高度
  const dotRadius = 2.0; // 圆点半径

  final textBoxHeight = textBoxPadding + textHeight + borderWidth;
  final totalHeight = textBoxHeight + lineHeight + dotHeight;
  final dotCenterFromTop = textBoxHeight + lineHeight + dotRadius;

  print('📊 组件尺寸：');
  print(
    '- 文本框高度: ${textBoxHeight}px (padding: ${textBoxPadding}px + 文字: ${textHeight}px + 边框: ${borderWidth}px)',
  );
  print('- 连线高度: ${lineHeight}px');
  print('- 圆点高度: ${dotHeight}px');
  print('- 总高度: ${totalHeight}px');
  print('- 圆点中心位置: ${dotCenterFromTop}px');

  // 验证计算是否正确
  assert(textBoxHeight == 20.0, '文本框高度计算错误');
  assert(totalHeight == 32.0, '总高度计算错误');
  assert(dotCenterFromTop == 30.0, '圆点中心位置计算错误');

  print('✅ 气泡结构尺寸验证通过');
}

/// 验证偏移值计算
void verifyOffsetCalculation() {
  print('\n🧮 偏移值计算验证');
  print('-' * 20);

  const totalHeight = 32.0;
  const dotCenterFromTop = 30.0;

  // 计算FractionalTranslation偏移值
  final offsetRatio = dotCenterFromTop / totalHeight;
  final yOffset = -offsetRatio;

  print('📐 偏移值计算：');
  print('- 圆点中心位置: ${dotCenterFromTop}px');
  print('- 总高度: ${totalHeight}px');
  print('- 偏移比例: ${dotCenterFromTop}px / ${totalHeight}px = $offsetRatio');
  print('- Y偏移值: $yOffset');

  // 验证偏移值是否正确
  const expectedOffset = -0.9375;
  final difference = (yOffset - expectedOffset).abs();

  print('\n🔍 精度验证：');
  print('- 期望偏移值: $expectedOffset');
  print('- 计算偏移值: $yOffset');
  print('- 差异: $difference');

  assert(difference < 0.0001, '偏移值计算精度不足');
  print('✅ 偏移值计算验证通过');
}

/// 验证定位精度
void verifyPositionAccuracy() {
  print('\n🎯 定位精度验证');
  print('-' * 20);

  const totalHeight = 32.0;
  const yOffset = -0.9375;

  // 模拟FractionalTranslation的效果
  final actualOffset = yOffset * totalHeight; // 实际向上偏移的像素
  final dotPositionFromTop = -actualOffset; // 圆点距离基准位置的距离

  print('📍 定位效果：');
  print(
    '- FractionalTranslation偏移: $yOffset × ${totalHeight}px = ${actualOffset}px',
  );
  print('- 圆点距离基准位置: ${dotPositionFromTop}px');

  // 验证圆点是否精确对准基准位置
  const expectedDotPosition = 30.0; // 圆点中心应该在30px处
  final positionError = (dotPositionFromTop - expectedDotPosition).abs();

  print('- 期望圆点位置: ${expectedDotPosition}px');
  print('- 实际圆点位置: ${dotPositionFromTop}px');
  print('- 位置误差: ${positionError}px');

  assert(positionError < 0.1, '定位精度不足');
  print('✅ 定位精度验证通过');
}
