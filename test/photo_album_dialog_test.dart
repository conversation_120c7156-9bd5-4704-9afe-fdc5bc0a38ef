import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/memory_palace/palace_manager_page.dart';

void main() {
  group('创建知忆相册弹窗测试', () {
    testWidgets('应该显示创建知忆相册对话框', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 查找浮动操作按钮
      final fab = find.byType(FloatingActionButton);
      expect(fab, findsOneWidget);

      // 点击浮动操作按钮
      await tester.tap(fab);
      await tester.pumpAndSettle();

      // 验证对话框是否显示
      expect(find.text('创建知忆相册'), findsAtLeastNWidgets(1));
      expect(find.text('相册信息'), findsOneWidget);
      expect(find.text('相册标题'), findsOneWidget);
      expect(find.text('相册描述（可选）'), findsOneWidget);
    });

    testWidgets('应该显示选择图片按钮', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 打开对话框
      final fab = find.byType(FloatingActionButton);
      await tester.tap(fab);
      await tester.pumpAndSettle();

      // 验证选择图片按钮
      expect(find.text('选择图片'), findsOneWidget);
      expect(find.byIcon(Icons.add_photo_alternate_outlined), findsOneWidget);
    });

    testWidgets('应该使用正确的配色方案', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 打开对话框
      final fab = find.byType(FloatingActionButton);
      await tester.tap(fab);
      await tester.pumpAndSettle();

      // 检查对话框的背景色
      final dialog = tester.widget<Dialog>(find.byType(Dialog));
      expect(dialog.backgroundColor, Colors.transparent);

      // 检查浮动操作按钮的颜色
      final fabWidget = tester.widget<FloatingActionButton>(fab);
      expect(fabWidget.backgroundColor, const Color(0xFF2E7EED));
    });

    testWidgets('应该有关闭按钮', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 打开对话框
      final fab = find.byType(FloatingActionButton);
      await tester.tap(fab);
      await tester.pumpAndSettle();

      // 验证关闭按钮
      expect(find.byIcon(Icons.close), findsOneWidget);

      // 点击关闭按钮
      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();

      // 验证对话框已关闭
      expect(find.text('相册信息'), findsNothing);
    });

    testWidgets('应该验证输入字段', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 打开对话框
      final fab = find.byType(FloatingActionButton);
      await tester.tap(fab);
      await tester.pumpAndSettle();

      // 查找标题输入框
      final titleField = find.widgetWithText(TextField, '请输入相册标题');
      expect(titleField, findsOneWidget);

      // 查找描述输入框
      final descField = find.widgetWithText(TextField, '请输入相册描述');
      expect(descField, findsOneWidget);

      // 输入标题
      await tester.enterText(titleField, '测试相册');
      await tester.pump();

      // 验证输入的文本
      expect(find.text('测试相册'), findsOneWidget);
    });

    testWidgets('应该有正确的字符限制', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 打开对话框
      final fab = find.byType(FloatingActionButton);
      await tester.tap(fab);
      await tester.pumpAndSettle();

      // 查找标题输入框并验证最大长度
      final titleFields = find.byType(TextField);
      expect(titleFields, findsAtLeastNWidgets(2));

      // 获取第一个TextField（标题）
      final titleField = tester.widget<TextField>(titleFields.first);
      expect(titleField.maxLength, 50);

      // 获取第二个TextField（描述）
      final descField = tester.widget<TextField>(titleFields.last);
      expect(descField.maxLength, 200);
      expect(descField.maxLines, 3);
    });
  });
}
