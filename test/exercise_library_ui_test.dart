import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/features/exercise/exercise_library_page.dart';

void main() {
  group('动作库管理页面UI优化测试', () {
    testWidgets('验证网格布局优化 - 响应式列数和间距', (WidgetTester tester) async {
      // 测试不同屏幕尺寸下的布局
      await tester.binding.setSurfaceSize(const Size(375, 667)); // iPhone标准尺寸
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ExerciseLibraryPage(),
          ),
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 验证页面正常渲染
      expect(find.byType(ExerciseLibraryPage), findsOneWidget);
      expect(find.text('动作库管理'), findsOneWidget);
      
      // 验证网格布局存在
      expect(find.byType(GridView), findsOneWidget);
    });

    testWidgets('验证字体层级优化 - 动作卡片文字清晰度', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(375, 667));
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ExerciseLibraryPage(),
          ),
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 查找动作卡片
      final cardFinder = find.byType(Card);
      expect(cardFinder, findsAtLeastNWidgets(1));
      
      // 验证文字组件存在且有适当的样式
      final textWidgets = tester.widgetList<Text>(find.byType(Text));
      
      // 验证主要文字都有合适的字体大小设置
      for (final textWidget in textWidgets) {
        if (textWidget.style?.fontSize != null) {
          expect(textWidget.style!.fontSize! >= 6.5, true, 
            reason: '字体大小应该足够清晰可读');
          expect(textWidget.style!.fontSize! <= 20, true,
            reason: '字体大小应该适中，不过大');
        }
      }
    });

    testWidgets('验证空间利用率优化 - 紧凑布局', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(375, 667));
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ExerciseLibraryPage(),
          ),
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 验证编辑提示区域存在且紧凑
      expect(find.textContaining('长按动作卡片'), findsOneWidget);
      
      // 验证网格视图的padding设置
      final gridView = tester.widget<GridView>(find.byType(GridView));
      expect(gridView, isNotNull);
    });

    testWidgets('验证侧边栏优化 - 字体和间距', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(375, 667));

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ExerciseLibraryPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 打开侧边栏
      final menuButton = find.byIcon(Icons.menu);
      expect(menuButton, findsOneWidget);

      await tester.tap(menuButton);
      await tester.pumpAndSettle();

      // 验证侧边栏标题
      expect(find.text('动作库'), findsOneWidget);

      // 验证分类列表项
      expect(find.text('全部'), findsAtLeastNWidgets(1));
    });

    testWidgets('验证侧边栏功能按钮优化 - 添加自定义动作按钮', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(375, 667));

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ExerciseLibraryPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 打开侧边栏
      final menuButton = find.byIcon(Icons.menu);
      await tester.tap(menuButton);
      await tester.pumpAndSettle();

      // 验证添加自定义动作按钮存在
      expect(find.byIcon(Icons.add_circle_outline), findsOneWidget);

      // 验证搜索按钮仍然存在
      expect(find.byIcon(Icons.search), findsOneWidget);

      // 验证主页面AppBar中的帮助按钮仍然存在（应该只有一个，在主页面）
      expect(find.byIcon(Icons.help_outline), findsOneWidget);

      // 测试点击添加按钮
      await tester.tap(find.byIcon(Icons.add_circle_outline));
      await tester.pumpAndSettle();

      // 验证对话框出现
      expect(find.text('添加自定义分类'), findsOneWidget);
      expect(find.text('分类名称 *'), findsOneWidget);
      expect(find.text('选择图标'), findsOneWidget);

      // 关闭对话框
      await tester.tap(find.text('取消'));
      await tester.pumpAndSettle();
    });

    testWidgets('验证自定义分类创建功能', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(375, 667));

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ExerciseLibraryPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 打开侧边栏
      final menuButton = find.byIcon(Icons.menu);
      await tester.tap(menuButton);
      await tester.pumpAndSettle();

      // 点击添加按钮
      await tester.tap(find.byIcon(Icons.add_circle_outline));
      await tester.pumpAndSettle();

      // 验证对话框内容
      expect(find.text('添加自定义分类'), findsOneWidget);
      expect(find.text('分类名称 *'), findsOneWidget);
      expect(find.text('分类描述'), findsOneWidget);
      expect(find.text('选择图标'), findsOneWidget);

      // 验证输入框存在
      expect(find.byType(TextFormField), findsAtLeastNWidgets(2));

      // 验证图标网格存在
      expect(find.byType(GridView), findsOneWidget);

      // 验证按钮存在
      expect(find.text('取消'), findsOneWidget);
      expect(find.text('创建'), findsOneWidget);

      // 关闭对话框
      await tester.tap(find.text('取消'));
      await tester.pumpAndSettle();
    });

    testWidgets('验证响应式适配 - 不同屏幕尺寸', (WidgetTester tester) async {
      // 测试平板尺寸
      await tester.binding.setSurfaceSize(const Size(768, 1024));
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ExerciseLibraryPage(),
          ),
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 验证页面在大屏幕下正常显示
      expect(find.byType(ExerciseLibraryPage), findsOneWidget);
      expect(find.byType(GridView), findsOneWidget);
      
      // 测试桌面尺寸
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      await tester.pumpAndSettle();
      
      // 验证页面在桌面尺寸下正常显示
      expect(find.byType(ExerciseLibraryPage), findsOneWidget);
      expect(find.byType(GridView), findsOneWidget);
    });

    testWidgets('验证UI元素无溢出 - 布局完整性', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(320, 568)); // 小屏设备
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ExerciseLibraryPage(),
          ),
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 验证没有渲染溢出错误
      expect(tester.takeException(), isNull);
      
      // 验证基本UI元素存在
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.byType(FloatingActionButton), findsOneWidget);
    });
  });
}
