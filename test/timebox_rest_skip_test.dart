import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/features/time_box/models/timebox_models.dart';
import 'package:oneday/services/global_timer_service.dart';

void main() {
  group('TimeBox 休息时间提前结束测试', () {
    late GlobalTimerService globalTimerService;

    setUp(() {
      globalTimerService = GlobalTimerService();
    });

    testWidgets('测试休息时间提前结束的导航逻辑', (WidgetTester tester) async {
      // 创建测试任务
      final testTask = TimeBoxTask(
        id: 'test-task-1',
        title: '测试任务',
        description: '测试任务描述',
        plannedMinutes: 25,
        status: TaskStatus.pending,
        priority: TaskPriority.medium,
        category: '学习',
        createdAt: DateTime.now(),
      );

      bool restCompletedCallbackCalled = false;

      // 设置休息完成回调
      globalTimerService.setRestCompletedCallback(() {
        restCompletedCallbackCalled = true;
        print('🏁 休息完成回调被调用');
      });

      // 1. 启动工作计时器
      await globalTimerService.startTimer(testTask);
      expect(globalTimerService.pomodoroState, PomodoroTimerState.work);
      expect(globalTimerService.currentTask?.id, testTask.id);

      // 2. 模拟进入休息时间
      // 由于我们无法直接设置内部状态，我们直接测试 skipRestTime 方法
      // 首先启动休息时间
      await globalTimerService.startRestTimer();
      expect(globalTimerService.pomodoroState, PomodoroTimerState.rest);

      // 3. 测试提前结束休息时间
      await globalTimerService.skipRestTime();

      // 4. 验证状态变化
      expect(globalTimerService.pomodoroState, PomodoroTimerState.stopped);
      expect(globalTimerService.isTimerRunning, false);
      expect(globalTimerService.currentTask, null);

      // 5. 验证休息完成回调被调用
      expect(restCompletedCallbackCalled, true);

      print('✅ 休息时间提前结束测试通过');
    });

    testWidgets('测试非休息状态下调用 skipRestTime', (WidgetTester tester) async {
      // 确保不在休息状态
      expect(globalTimerService.pomodoroState, PomodoroTimerState.stopped);

      bool restCompletedCallbackCalled = false;
      globalTimerService.setRestCompletedCallback(() {
        restCompletedCallbackCalled = true;
      });

      // 尝试跳过休息时间
      await globalTimerService.skipRestTime();

      // 验证回调没有被调用
      expect(restCompletedCallbackCalled, false);
      expect(globalTimerService.pomodoroState, PomodoroTimerState.stopped);

      print('✅ 非休息状态下调用 skipRestTime 测试通过');
    });

    test('测试最后完成任务的保存和获取', () {
      final testTask = TimeBoxTask(
        id: 'test-task-2',
        title: '测试任务2',
        description: '测试任务描述2',
        plannedMinutes: 30,
        status: TaskStatus.pending,
        priority: TaskPriority.high,
        category: '工作',
        createdAt: DateTime.now(),
      );

      // 初始状态下没有最后完成的任务
      expect(globalTimerService.lastCompletedTask, null);

      // 启动任务
      globalTimerService.startTimer(testTask);

      // 模拟任务完成
      testTask.copyWith(status: TaskStatus.completed, endTime: DateTime.now());

      // 这里需要一个公共方法来设置最后完成的任务
      // 在实际实现中，这会在工作时间完成时自动设置

      print('✅ 最后完成任务保存测试通过');
    });
  });
}

/// 测试用的简化版 TimeBox 页面组件
class TestTimeBoxPage extends ConsumerStatefulWidget {
  final VoidCallback? onRestCompleted;

  const TestTimeBoxPage({super.key, this.onRestCompleted});

  @override
  ConsumerState<TestTimeBoxPage> createState() => _TestTimeBoxPageState();
}

class _TestTimeBoxPageState extends ConsumerState<TestTimeBoxPage> {
  final GlobalTimerService _globalTimerService = GlobalTimerService();
  bool _isKinestheticLearningShowing = false;

  @override
  void initState() {
    super.initState();

    // 设置休息完成回调
    _globalTimerService.setRestCompletedCallback(() {
      _onRestCompleted();
    });
  }

  void _onRestCompleted() {
    if (mounted) {
      print('🏁 休息时间完成，显示任务完成对话框');
      widget.onRestCompleted?.call();
      _showTaskCompletionDialog();
    }
  }

  void _showTaskCompletionDialog() {
    final task =
        _globalTimerService.currentTask ??
        _globalTimerService.lastCompletedTask;
    if (task == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🎉 任务完成'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('任务："${task.title}"'),
            const SizedBox(height: 8),
            Text('学习时长：${task.plannedMinutes} 分钟'),
            Text('获得工资：¥${task.calculateWage().toStringAsFixed(1)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('完成'),
          ),
        ],
      ),
    );
  }

  void _showKinestheticLearningInterface() {
    if (_isKinestheticLearningShowing) return;

    _isKinestheticLearningShowing = true;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TestKinestheticLearningPage(
          onComplete: () {
            _isKinestheticLearningShowing = false;
            Navigator.of(context).pop();
          },
          onSkipRest: () {
            _isKinestheticLearningShowing = false;
            _globalTimerService.skipRestTime();
            Navigator.of(context).pop();
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('测试 TimeBox')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('当前状态: ${_globalTimerService.pomodoroState}'),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _showKinestheticLearningInterface,
              child: const Text('显示动觉记忆法界面'),
            ),
          ],
        ),
      ),
    );
  }
}

/// 测试用的简化版动觉记忆法学习页面
class TestKinestheticLearningPage extends StatelessWidget {
  final VoidCallback onComplete;
  final VoidCallback? onSkipRest;

  const TestKinestheticLearningPage({
    super.key,
    required this.onComplete,
    this.onSkipRest,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('动觉记忆法学习')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('休息时间学习页面'),
            const SizedBox(height: 20),
            if (onSkipRest != null)
              ElevatedButton(
                onPressed: onSkipRest,
                child: const Text('提前结束休息，立即工作'),
              ),
            const SizedBox(height: 10),
            ElevatedButton(onPressed: onComplete, child: const Text('完成休息')),
          ],
        ),
      ),
    );
  }
}
