import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/features/time_box/providers/timebox_provider.dart';
import 'package:oneday/features/time_box/models/timebox_models.dart';

/// 测试时间盒子数据一致性
void main() {
  group('时间盒子数据一致性测试', () {
    late ProviderContainer container;

    setUp(() async {
      // 清除SharedPreferences
      SharedPreferences.setMockInitialValues({});
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('初始化时应该包含默认任务', () async {
      // 等待数据加载完成
      await Future.delayed(const Duration(milliseconds: 100));

      final state = container.read(timeBoxProvider);

      // 验证默认任务存在
      expect(state.tasks.isNotEmpty, true);
      expect(state.tasks.length, 5); // 应该有5个默认任务

      // 验证默认任务ID
      final taskIds = state.tasks.map((task) => task.id).toList();
      expect(taskIds.contains('1'), true);
      expect(taskIds.contains('2'), true);
      expect(taskIds.contains('3'), true);
      expect(taskIds.contains('4'), true);
      expect(taskIds.contains('5'), true);

      print('✅ 默认任务验证通过：${state.tasks.length}个任务');
    });

    test('添加任务后数据应该一致', () async {
      // 等待初始化完成
      await Future.delayed(const Duration(milliseconds: 100));

      final notifier = container.read(timeBoxProvider.notifier);
      final initialState = container.read(timeBoxProvider);
      final initialCount = initialState.tasks.length;

      // 添加新任务
      final newTask = TimeBoxTask(
        id: 'test-task-1',
        title: '测试任务',
        description: '这是一个测试任务',
        plannedMinutes: 30,
        status: TaskStatus.pending,
        priority: TaskPriority.medium,
        category: '测试',
        createdAt: DateTime.now(),
      );

      await notifier.addTask(newTask);

      // 验证任务数量增加
      final updatedState = container.read(timeBoxProvider);
      expect(updatedState.tasks.length, initialCount + 1);

      // 验证新任务存在
      final addedTask = updatedState.tasks.firstWhere(
        (task) => task.id == 'test-task-1',
      );
      expect(addedTask.title, '测试任务');

      print('✅ 添加任务验证通过：${updatedState.tasks.length}个任务');
    });

    test('更新任务状态后数据应该一致', () async {
      // 等待初始化完成
      await Future.delayed(const Duration(milliseconds: 100));

      final notifier = container.read(timeBoxProvider.notifier);
      final initialState = container.read(timeBoxProvider);

      // 获取第一个任务并更新状态
      final firstTask = initialState.tasks.first;
      final updatedTask = firstTask.copyWith(status: TaskStatus.completed);

      await notifier.updateTask(updatedTask);

      // 验证任务状态更新
      final updatedState = container.read(timeBoxProvider);
      final taskAfterUpdate = updatedState.tasks.firstWhere(
        (task) => task.id == firstTask.id,
      );
      expect(taskAfterUpdate.status, TaskStatus.completed);

      print('✅ 更新任务验证通过：任务状态已更新为${taskAfterUpdate.status.displayName}');
    });

    test('删除任务后数据应该一致', () async {
      // 等待初始化完成
      await Future.delayed(const Duration(milliseconds: 100));

      final notifier = container.read(timeBoxProvider.notifier);
      final initialState = container.read(timeBoxProvider);
      final initialCount = initialState.tasks.length;

      // 删除第一个任务
      final taskToDelete = initialState.tasks.first;
      await notifier.deleteTask(taskToDelete.id);

      // 验证任务数量减少
      final updatedState = container.read(timeBoxProvider);
      expect(updatedState.tasks.length, initialCount - 1);

      // 验证任务已删除
      final taskExists = updatedState.tasks.any(
        (task) => task.id == taskToDelete.id,
      );
      expect(taskExists, false);

      print('✅ 删除任务验证通过：${updatedState.tasks.length}个任务');
    });

    test('过滤功能应该正确计算任务数量', () async {
      // 等待初始化完成
      await Future.delayed(const Duration(milliseconds: 100));

      final state = container.read(timeBoxProvider);
      final today = DateTime.now();

      // 计算今日任务
      final todayTasks = state.tasks.where((task) {
        if (task.startTime != null) {
          final taskDate = DateTime(
            task.startTime!.year,
            task.startTime!.month,
            task.startTime!.day,
          );
          final todayDate = DateTime(today.year, today.month, today.day);
          return taskDate == todayDate;
        }
        return false;
      }).toList();

      // 计算已完成任务
      final completedTasks = state.tasks
          .where((task) => task.status == TaskStatus.completed)
          .toList();

      print('✅ 过滤功能验证通过：');
      print('   总任务数: ${state.tasks.length}');
      print('   今日任务数: ${todayTasks.length}');
      print('   已完成任务数: ${completedTasks.length}');

      expect(state.tasks.length, greaterThan(0));
    });

    test('数据持久化应该正常工作', () async {
      // 等待初始化完成
      await Future.delayed(const Duration(milliseconds: 100));

      final notifier = container.read(timeBoxProvider.notifier);

      // 添加测试任务
      final testTask = TimeBoxTask(
        id: 'persistence-test',
        title: '持久化测试任务',
        description: '测试数据持久化',
        plannedMinutes: 45,
        status: TaskStatus.pending,
        priority: TaskPriority.high,
        category: '测试',
        createdAt: DateTime.now(),
      );

      await notifier.addTask(testTask);

      // 创建新的容器来模拟应用重启
      final newContainer = ProviderContainer();
      await Future.delayed(const Duration(milliseconds: 100));

      // 验证数据是否持久化
      final newState = newContainer.read(timeBoxProvider);
      final persistedTask = newState.tasks.firstWhere(
        (task) => task.id == 'persistence-test',
        orElse: () => throw Exception('任务未找到'),
      );

      expect(persistedTask.title, '持久化测试任务');

      print('✅ 数据持久化验证通过：任务已正确保存和加载');

      newContainer.dispose();
    });
  });
}

/// 运行测试的辅助函数
void runTimeBoxConsistencyTests() {
  print('🧪 开始运行时间盒子数据一致性测试...');

  // 这里可以添加更多的集成测试逻辑
  // 例如模拟用户操作序列

  print('📊 测试完成！请查看上述输出验证修复效果。');
}
