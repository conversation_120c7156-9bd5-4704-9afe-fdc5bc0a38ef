import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/time_box/pages/task_category_management_page.dart';
import 'package:oneday/features/time_box/managers/task_category_manager.dart';

void main() {
  group('默认分类编辑功能测试', () {
    setUp(() async {
      // 清理SharedPreferences
      SharedPreferences.setMockInitialValues({});
    });

    testWidgets('默认分类应该显示编辑菜单', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const TaskCategoryManagementPage(),
        ),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 验证默认分类存在
      expect(find.text('计算机科学'), findsOneWidget);
      expect(find.text('数学'), findsOneWidget);
      
      // 验证默认分类显示新的标签
      expect(find.text('系统分类（可编辑）'), findsWidgets);
      
      // 验证所有分类都有PopupMenuButton
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      expect(popupMenuButtons, findsWidgets);
      
      // 验证PopupMenuButton数量应该等于分类数量
      final categoryCards = find.byType(Card);
      expect(popupMenuButtons.evaluate().length, 
             equals(categoryCards.evaluate().length));
    });

    testWidgets('默认分类的菜单应该只有编辑选项', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const TaskCategoryManagementPage(),
        ),
      );

      await tester.pumpAndSettle();

      // 找到第一个默认分类的PopupMenuButton
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      expect(popupMenuButtons, findsWidgets);

      // 点击第一个PopupMenuButton（应该是默认分类）
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      // 验证只有编辑选项，没有删除选项
      expect(find.text('编辑'), findsOneWidget);
      expect(find.text('删除'), findsNothing);
    });

    testWidgets('自定义分类的菜单应该有编辑和删除选项', (WidgetTester tester) async {
      // 预设一个自定义分类
      SharedPreferences.setMockInitialValues({
        'task_categories_v1': '''[
          {
            "id": "custom_1",
            "name": "自定义分类",
            "colorValue": 4294901760,
            "iconName": "work",
            "isDefault": false,
            "createdAt": "2024-01-01T00:00:00.000Z",
            "lastModified": "2024-01-01T00:00:00.000Z"
          }
        ]'''
      });

      await tester.pumpWidget(
        MaterialApp(
          home: const TaskCategoryManagementPage(),
        ),
      );

      await tester.pumpAndSettle();

      // 查找自定义分类
      expect(find.text('自定义分类'), findsOneWidget);

      // 找到自定义分类的PopupMenuButton
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      
      // 点击最后一个PopupMenuButton（应该是自定义分类）
      await tester.tap(popupMenuButtons.last);
      await tester.pumpAndSettle();

      // 验证有编辑和删除选项
      expect(find.text('编辑'), findsOneWidget);
      expect(find.text('删除'), findsOneWidget);
    });

    testWidgets('应该能够编辑默认分类', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const TaskCategoryManagementPage(),
        ),
      );

      await tester.pumpAndSettle();

      // 点击第一个默认分类的PopupMenuButton
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      // 点击编辑选项
      await tester.tap(find.text('编辑'));
      await tester.pumpAndSettle();

      // 验证编辑对话框打开
      expect(find.text('编辑分类'), findsOneWidget);
      expect(find.text('分类名称'), findsOneWidget);
      expect(find.text('选择颜色'), findsOneWidget);
      expect(find.text('选择图标'), findsOneWidget);
    });

    testWidgets('编辑默认分类应该能够保存', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const TaskCategoryManagementPage(),
        ),
      );

      await tester.pumpAndSettle();

      // 点击第一个默认分类的PopupMenuButton
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      // 点击编辑选项
      await tester.tap(find.text('编辑'));
      await tester.pumpAndSettle();

      // 修改分类名称
      final nameField = find.byType(TextFormField);
      await tester.enterText(nameField, '编程技术');
      await tester.pumpAndSettle();

      // 点击保存按钮
      await tester.tap(find.text('保存'));
      await tester.pumpAndSettle();

      // 验证对话框关闭
      expect(find.text('编辑分类'), findsNothing);
      
      // 验证成功提示
      expect(find.text('分类更新成功'), findsOneWidget);
    });

    testWidgets('TaskCategoryManager应该能够更新默认分类', (WidgetTester tester) async {
      final manager = TaskCategoryManager();
      await manager.loadFromStorage();

      // 获取第一个默认分类
      final defaultCategory = manager.categories.firstWhere((cat) => cat.isDefault);
      
      // 更新默认分类
      final success = await manager.updateCategory(
        defaultCategory.id,
        '新的分类名称',
        Colors.purple,
        'school',
      );

      // 验证更新成功
      expect(success, isTrue);
      
      // 验证分类已更新
      final updatedCategory = manager.findCategoryById(defaultCategory.id);
      expect(updatedCategory?.name, equals('新的分类名称'));
      expect(updatedCategory?.color, equals(Colors.purple));
      expect(updatedCategory?.iconName, equals('school'));
      expect(updatedCategory?.isDefault, isTrue); // 仍然是默认分类
    });

    testWidgets('默认分类仍然不能被删除', (WidgetTester tester) async {
      final manager = TaskCategoryManager();
      await manager.loadFromStorage();

      // 获取第一个默认分类
      final defaultCategory = manager.categories.firstWhere((cat) => cat.isDefault);
      
      // 尝试删除默认分类
      final success = await manager.deleteCategory(defaultCategory.id);

      // 验证删除失败
      expect(success, isFalse);
      
      // 验证分类仍然存在
      final category = manager.findCategoryById(defaultCategory.id);
      expect(category, isNotNull);
    });
  });
}
