import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/memory_palace/palace_manager_page.dart';

void main() {
  group('知忆相册弹窗位置修复测试', () {
    testWidgets('弹窗应该使用正确的对齐方式和安全边距', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 查找并点击浮动操作按钮
      final fab = find.byType(FloatingActionButton);
      expect(fab, findsOneWidget);

      await tester.tap(fab);
      await tester.pumpAndSettle();

      // 验证对话框是否显示
      expect(find.text('创建知忆相册'), findsAtLeastNWidgets(1));

      // 查找Dialog组件
      final dialogFinder = find.byType(Dialog);
      expect(dialogFinder, findsOneWidget);

      // 获取Dialog的位置信息
      final dialogWidget = tester.widget<Dialog>(dialogFinder);
      
      // 验证Dialog使用了正确的对齐方式（顶部对齐）
      expect(dialogWidget.alignment, equals(Alignment.topCenter));

      // 验证Dialog使用了动态的insetPadding而不是固定值
      expect(dialogWidget.insetPadding, isA<EdgeInsets>());
      final insetPadding = dialogWidget.insetPadding as EdgeInsets;
      
      // 验证不是默认的固定值
      expect(insetPadding, isNot(equals(const EdgeInsets.all(16))));

      print('✅ 弹窗位置修复验证通过：');
      print('   - 对齐方式: ${dialogWidget.alignment}');
      print('   - 顶部边距: ${insetPadding.top}');
      print('   - 底部边距: ${insetPadding.bottom}');
      print('   - 左侧边距: ${insetPadding.left}');
      print('   - 右侧边距: ${insetPadding.right}');
    });
  });
}
