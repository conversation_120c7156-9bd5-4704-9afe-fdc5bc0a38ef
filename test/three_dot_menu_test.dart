import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/memory_palace/palace_manager_page.dart';

void main() {
  group('三点菜单功能测试', () {
    testWidgets('相册卡片应该显示三点菜单按钮', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 查找三点菜单按钮
      final moreOptionsButton = find.byIcon(Icons.more_horiz);
      
      // 验证三点菜单按钮存在
      expect(moreOptionsButton, findsAtLeastNWidgets(1));
    });

    testWidgets('点击三点菜单按钮应该显示菜单选项', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 查找并点击第一个三点菜单按钮
      final moreOptionsButton = find.byIcon(Icons.more_horiz).first;
      await tester.tap(moreOptionsButton);
      await tester.pumpAndSettle();

      // 验证菜单选项是否显示
      expect(find.text('编辑'), findsOneWidget);
      expect(find.text('复制图片'), findsOneWidget);
      expect(find.text('更改分类'), findsOneWidget);
      expect(find.text('导出知识点'), findsOneWidget);
      expect(find.text('删除'), findsOneWidget);
    });

    testWidgets('菜单选项应该有正确的图标', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 点击三点菜单按钮
      final moreOptionsButton = find.byIcon(Icons.more_horiz).first;
      await tester.tap(moreOptionsButton);
      await tester.pumpAndSettle();

      // 验证菜单选项的图标
      expect(find.byIcon(Icons.edit_outlined), findsOneWidget);
      expect(find.byIcon(Icons.content_copy_outlined), findsOneWidget);
      expect(find.byIcon(Icons.folder_outlined), findsOneWidget);
      expect(find.byIcon(Icons.share_outlined), findsOneWidget);
      expect(find.byIcon(Icons.delete_outline), findsOneWidget);
    });

    testWidgets('点击编辑选项应该打开编辑对话框', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 点击三点菜单按钮
      final moreOptionsButton = find.byIcon(Icons.more_horiz).first;
      await tester.tap(moreOptionsButton);
      await tester.pumpAndSettle();

      // 点击编辑选项
      await tester.tap(find.text('编辑'));
      await tester.pumpAndSettle();

      // 验证编辑对话框是否打开
      expect(find.text('编辑相册'), findsOneWidget);
      expect(find.text('相册标题'), findsOneWidget);
      expect(find.text('保存更改'), findsOneWidget);
    });

    testWidgets('点击复制图片选项应该显示确认对话框', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 点击三点菜单按钮
      final moreOptionsButton = find.byIcon(Icons.more_horiz).first;
      await tester.tap(moreOptionsButton);
      await tester.pumpAndSettle();

      // 点击复制图片选项
      await tester.tap(find.text('复制图片'));
      await tester.pumpAndSettle();

      // 验证确认对话框是否显示
      expect(find.text('复制图片'), findsAtLeastNWidgets(1));
      expect(find.text('确定要复制相册'), findsOneWidget);
    });

    testWidgets('点击删除选项应该显示确认对话框', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 点击三点菜单按钮
      final moreOptionsButton = find.byIcon(Icons.more_horiz).first;
      await tester.tap(moreOptionsButton);
      await tester.pumpAndSettle();

      // 点击删除选项
      await tester.tap(find.text('删除'));
      await tester.pumpAndSettle();

      // 验证删除确认对话框是否显示
      expect(find.text('确认删除'), findsOneWidget);
      expect(find.text('确定要删除相册'), findsOneWidget);
    });

    testWidgets('三点菜单按钮不应该与卡片点击事件冲突', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 点击卡片的其他区域（不是三点菜单按钮）
      final cardWidget = find.byType(Card).first;
      await tester.tap(cardWidget, warnIfMissed: false);
      await tester.pumpAndSettle();

      // 验证没有打开菜单（应该进入详情页或其他操作）
      expect(find.text('编辑'), findsNothing);
      expect(find.text('复制图片'), findsNothing);
    });
  });
}
