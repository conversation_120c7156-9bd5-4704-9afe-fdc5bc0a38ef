import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/exercise/custom_action_library.dart';
import 'package:oneday/features/exercise/custom_action_library_service.dart';

void main() {
  group('CustomActionLibrary 编辑保存修复验证', () {
    late CustomActionLibraryService service;

    setUp(() async {
      // 设置测试环境
      SharedPreferences.setMockInitialValues({});
      service = CustomActionLibraryService();
      await service.loadFromStorage();
    });

    test('测试编辑动作后的保存机制', () async {
      // 1. 创建一个测试动作库
      final library = await service.createLibrary(
        name: '测试动作库',
        description: '用于测试编辑保存功能',
        category: '测试分类',
      );

      // 2. 添加一个动作
      final originalAction = CustomPAOAction(
        letter: 'A',
        nameEn: 'Original Action',
        nameCn: '原始动作',
        description: '这是原始动作描述',
        category: '健身',
        scene: '简单活动',
        keywords: ['original', 'test'],
      );

      await service.updateActionInLibrary(library.id, 'A', originalAction);

      // 3. 验证动作已保存
      final savedLibrary = service.findLibraryById(library.id);
      expect(savedLibrary, isNotNull);
      expect(savedLibrary!.getActionForLetter('A'), isNotNull);
      expect(savedLibrary.getActionForLetter('A')!.nameCn, equals('原始动作'));

      // 4. 模拟编辑页面的操作 - 获取库对象
      final editingLibrary = service.findLibraryById(library.id)!;

      // 5. 编辑动作（模拟用户在编辑对话框中的操作）
      final editedAction = CustomPAOAction(
        letter: 'A',
        nameEn: 'Edited Action',
        nameCn: '编辑后的动作',
        description: '这是编辑后的动作描述',
        category: '瑜伽',
        scene: '集中训练',
        keywords: ['edited', 'modified'],
      );

      // 6. 模拟编辑页面的保存操作
      editingLibrary.setActionForLetter('A', editedAction);
      await service.updateLibrary(editingLibrary);

      // 7. 验证编辑后的数据是否正确保存
      final updatedLibrary = service.findLibraryById(library.id);
      expect(updatedLibrary, isNotNull);

      final updatedAction = updatedLibrary!.getActionForLetter('A');
      expect(updatedAction, isNotNull);
      expect(updatedAction!.nameCn, equals('编辑后的动作'));
      expect(updatedAction.description, equals('这是编辑后的动作描述'));
      expect(updatedAction.category, equals('瑜伽'));
      expect(updatedAction.scene, equals('集中训练'));
      expect(updatedAction.keywords, equals(['edited', 'modified']));

      print('✅ 编辑保存测试通过');
    });

    test('测试预设动作编辑后的保存', () async {
      // 1. 创建动作库
      final library = await service.createLibrary(
        name: '预设动作测试库',
        description: '测试预设动作编辑',
        category: '测试分类',
      );

      // 2. 从预设动作模板获取动作
      final defaultAction = DefaultActionTemplates.getDefaultActionForLetter(
        'A',
      );
      expect(defaultAction, isNotNull);

      // 3. 保存预设动作
      await service.updateActionInLibrary(library.id, 'A', defaultAction!);

      // 4. 验证预设动作已保存
      final savedLibrary = service.findLibraryById(library.id);
      expect(savedLibrary!.getActionForLetter('A'), isNotNull);

      // 5. 编辑预设动作
      final editingLibrary = service.findLibraryById(library.id)!;
      final originalAction = editingLibrary.getActionForLetter('A')!;

      final editedAction = originalAction.copyWith(
        nameCn: '编辑后的预设动作',
        description: '这是编辑后的预设动作描述',
        keywords: ['edited', 'preset'],
      );

      // 6. 保存编辑后的动作
      editingLibrary.setActionForLetter('A', editedAction);
      await service.updateLibrary(editingLibrary);

      // 7. 验证编辑结果
      final updatedLibrary = service.findLibraryById(library.id);
      final updatedAction = updatedLibrary!.getActionForLetter('A');

      expect(updatedAction!.nameCn, equals('编辑后的预设动作'));
      expect(updatedAction.description, equals('这是编辑后的预设动作描述'));
      expect(updatedAction.keywords, equals(['edited', 'preset']));

      print('✅ 预设动作编辑保存测试通过');
    });

    test('测试对象引用一致性', () async {
      // 1. 创建动作库
      final library = await service.createLibrary(
        name: '引用测试库',
        description: '测试对象引用一致性',
        category: '测试分类',
      );

      // 2. 获取库对象的两个引用
      final ref1 = service.findLibraryById(library.id);
      final ref2 = service.findLibraryById(library.id);

      // 3. 验证是否是同一个对象引用
      expect(identical(ref1, ref2), isTrue, reason: '应该返回同一个对象引用');

      // 4. 通过一个引用修改数据
      final testAction = CustomPAOAction(
        letter: 'B',
        nameEn: 'Test Action',
        nameCn: '测试动作',
        description: '测试描述',
        category: '测试',
        scene: '简单活动',
        keywords: ['test'],
      );

      ref1!.setActionForLetter('B', testAction);

      // 5. 验证另一个引用是否能看到变化
      expect(ref2!.getActionForLetter('B'), isNotNull);
      expect(ref2.getActionForLetter('B')!.nameCn, equals('测试动作'));

      print('✅ 对象引用一致性测试通过');
    });

    test('测试修复后的编辑保存流程', () async {
      // 模拟修复后的编辑保存流程

      // 1. 创建动作库
      final library = await service.createLibrary(
        name: '修复测试库',
        description: '测试修复后的编辑保存功能',
        category: '测试分类',
      );

      // 2. 模拟页面获取库对象（类似 CustomLibraryEditorPage 的初始化）
      var pageLibrary = service.findLibraryById(library.id)!;

      // 3. 添加一个初始动作
      final initialAction = CustomPAOAction(
        letter: 'A',
        nameEn: 'Initial Action',
        nameCn: '初始动作',
        description: '初始描述',
        category: '健身',
        scene: '简单活动',
        keywords: ['initial'],
      );

      // 模拟编辑页面的保存操作
      pageLibrary.setActionForLetter('A', initialAction);
      await service.updateLibrary(pageLibrary);

      // 模拟修复后的逻辑：重新获取更新后的库对象
      pageLibrary = service.findLibraryById(library.id)!;

      // 4. 验证初始动作已保存
      expect(pageLibrary.getActionForLetter('A'), isNotNull);
      expect(pageLibrary.getActionForLetter('A')!.nameCn, equals('初始动作'));

      // 5. 模拟编辑现有动作
      final editedAction = CustomPAOAction(
        letter: 'A',
        nameEn: 'Edited Action',
        nameCn: '编辑后的动作',
        description: '编辑后的描述',
        category: '瑜伽',
        scene: '集中训练',
        keywords: ['edited', 'modified'],
      );

      // 模拟编辑页面的保存操作
      pageLibrary.setActionForLetter('A', editedAction);
      await service.updateLibrary(pageLibrary);

      // 模拟修复后的逻辑：重新获取更新后的库对象
      pageLibrary = service.findLibraryById(library.id)!;

      // 6. 验证编辑后的动作正确保存并在UI中可见
      final savedAction = pageLibrary.getActionForLetter('A');
      expect(savedAction, isNotNull);
      expect(savedAction!.nameCn, equals('编辑后的动作'));
      expect(savedAction.description, equals('编辑后的描述'));
      expect(savedAction.category, equals('瑜伽'));
      expect(savedAction.scene, equals('集中训练'));
      expect(savedAction.keywords, equals(['edited', 'modified']));

      // 7. 验证服务层的数据也是一致的
      final serviceLibrary = service.findLibraryById(library.id)!;
      final serviceAction = serviceLibrary.getActionForLetter('A');
      expect(serviceAction!.nameCn, equals('编辑后的动作'));
      expect(serviceAction.description, equals('编辑后的描述'));

      print('✅ 修复后的编辑保存流程测试通过');
    });
  });
}
