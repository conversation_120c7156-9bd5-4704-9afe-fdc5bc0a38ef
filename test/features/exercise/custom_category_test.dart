import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/exercise/custom_exercise_category.dart';

void main() {
  group('CustomExerciseCategoryManager Tests', () {
    late CustomExerciseCategoryManager manager;

    setUp(() async {
      // 设置测试环境
      SharedPreferences.setMockInitialValues({});
      manager = CustomExerciseCategoryManager();
    });

    test('应该能够添加新分类', () async {
      // 创建测试分类
      final testCategory = CustomExerciseCategory(
        id: 'test_id',
        name: '测试分类',
        icon: '🏃‍♂️',
        description: '这是一个测试分类',
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      // 添加分类
      await manager.addCategory(testCategory);

      // 验证分类已添加
      expect(manager.categories.length, 1);
      expect(manager.categories.first.name, '测试分类');
      expect(manager.categories.first.icon, '🏃‍♂️');
    });

    test('应该能够检查分类名称是否存在', () async {
      // 添加测试分类
      final testCategory = CustomExerciseCategory(
        id: 'test_id',
        name: '测试分类',
        icon: '🏃‍♂️',
        description: '这是一个测试分类',
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );
      await manager.addCategory(testCategory);

      // 检查名称是否存在
      expect(manager.isCategoryNameExists('测试分类'), true);
      expect(manager.isCategoryNameExists('不存在的分类'), false);
    });

    test('应该能够保存和加载分类数据', () async {
      // 添加测试分类
      final testCategory = CustomExerciseCategory(
        id: 'test_id',
        name: '测试分类',
        icon: '🏃‍♂️',
        description: '这是一个测试分类',
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );
      await manager.addCategory(testCategory);

      // 创建新的管理器实例并加载数据
      final newManager = CustomExerciseCategoryManager();
      await newManager.loadFromStorage();

      // 验证数据已正确加载
      expect(newManager.categories.length, 1);
      expect(newManager.categories.first.name, '测试分类');
      expect(newManager.categories.first.icon, '🏃‍♂️');
    });

    test('应该能够删除分类', () async {
      // 添加测试分类
      final testCategory = CustomExerciseCategory(
        id: 'test_id',
        name: '测试分类',
        icon: '🏃‍♂️',
        description: '这是一个测试分类',
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );
      await manager.addCategory(testCategory);

      // 验证分类已添加
      expect(manager.categories.length, 1);

      // 删除分类
      await manager.deleteCategory('test_id');

      // 验证分类已删除
      expect(manager.categories.length, 0);
    });

    test('应该能够更新分类', () async {
      // 添加测试分类
      final testCategory = CustomExerciseCategory(
        id: 'test_id',
        name: '测试分类',
        icon: '🏃‍♂️',
        description: '这是一个测试分类',
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );
      await manager.addCategory(testCategory);

      // 更新分类
      final updatedCategory = CustomExerciseCategory(
        id: 'test_id',
        name: '更新后的分类',
        icon: '🤸‍♂️',
        description: '这是更新后的分类',
        createdAt: testCategory.createdAt,
        lastModified: DateTime.now(),
      );
      await manager.updateCategory(updatedCategory);

      // 验证分类已更新
      expect(manager.categories.length, 1);
      expect(manager.categories.first.name, '更新后的分类');
      expect(manager.categories.first.icon, '🤸‍♂️');
    });

    test('应该能够处理空数据加载', () async {
      // 创建新的管理器实例（没有预先保存的数据）
      final newManager = CustomExerciseCategoryManager();
      await newManager.loadFromStorage();

      // 验证空列表
      expect(newManager.categories.length, 0);
    });

    test('分类序列化和反序列化应该正常工作', () {
      final testCategory = CustomExerciseCategory(
        id: 'test_id',
        name: '测试分类',
        icon: '🏃‍♂️',
        description: '这是一个测试分类',
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      // 序列化
      final json = testCategory.toJson();

      // 反序列化
      final deserializedCategory = CustomExerciseCategory.fromJson(json);

      // 验证数据一致性
      expect(deserializedCategory.id, testCategory.id);
      expect(deserializedCategory.name, testCategory.name);
      expect(deserializedCategory.icon, testCategory.icon);
      expect(deserializedCategory.description, testCategory.description);
    });
  });
}
