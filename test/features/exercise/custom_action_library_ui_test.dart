import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/exercise/exercise_library_page.dart';
import 'package:oneday/features/exercise/create_custom_library_dialog.dart';
import 'package:oneday/features/exercise/custom_library_editor_page.dart';
import 'package:oneday/features/exercise/custom_action_library.dart';
import 'package:oneday/features/exercise/custom_action_library_service.dart';

void main() {
  group('自定义动作库UI测试', () {
    testWidgets('动作库管理页面应该显示创建动作库按钮', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(home: ExerciseLibraryPage()));

      // 验证浮动按钮存在
      expect(find.byType(FloatingActionButton), findsOneWidget);

      // 验证按钮文字
      expect(find.text('创建动作库'), findsOneWidget);

      // 验证按钮图标
      expect(find.byIcon(Icons.library_add), findsOneWidget);
    });

    testWidgets('创建动作库对话框应该正确显示', (WidgetTester tester) async {
      final customLibraryService = CustomActionLibraryService();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => CreateCustomLibraryDialog(
                      customLibraryService: customLibraryService,
                      onLibraryCreated: (library) {},
                    ),
                  );
                },
                child: const Text('显示对话框'),
              ),
            ),
          ),
        ),
      );

      // 点击按钮显示对话框
      await tester.tap(find.text('显示对话框'));
      await tester.pumpAndSettle();

      // 验证对话框标题
      expect(find.text('创建自定义动作库'), findsOneWidget);

      // 验证输入字段
      expect(find.text('动作库名称'), findsOneWidget);
      expect(find.text('动作库描述'), findsOneWidget);

      // 验证按钮
      expect(find.text('取消'), findsOneWidget);
      expect(find.text('创建'), findsOneWidget);
    });

    testWidgets('创建动作库表单验证应该正常工作', (WidgetTester tester) async {
      final customLibraryService = CustomActionLibraryService();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => CreateCustomLibraryDialog(
                      customLibraryService: customLibraryService,
                      onLibraryCreated: (library) {},
                    ),
                  );
                },
                child: const Text('显示对话框'),
              ),
            ),
          ),
        ),
      );

      // 显示对话框
      await tester.tap(find.text('显示对话框'));
      await tester.pumpAndSettle();

      // 尝试创建空名称的动作库
      await tester.tap(find.text('创建'));
      await tester.pumpAndSettle();

      // 应该显示验证错误
      expect(find.text('请输入动作库名称'), findsOneWidget);
    });

    testWidgets('字母动作编辑界面应该正确显示网格', (WidgetTester tester) async {
      final customLibraryService = CustomActionLibraryService();
      final testLibrary = CustomActionLibrary(
        id: 'test_id',
        name: '测试动作库',
        description: '测试描述',
        category: '测试分类',
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: CustomLibraryEditorPage(
            library: testLibrary,
            customLibraryService: customLibraryService,
          ),
        ),
      );

      // 验证页面标题
      expect(find.text('测试动作库'), findsOneWidget);

      // 验证进度显示
      expect(find.text('0/26 动作已设置'), findsOneWidget);

      // 验证字母网格存在
      expect(find.byType(GridView), findsOneWidget);

      // 验证26个字母都存在
      for (int i = 0; i < 26; i++) {
        final letter = String.fromCharCode(65 + i); // A-Z
        expect(find.text(letter), findsOneWidget);
      }
    });

    testWidgets('字母网格应该响应式适配屏幕尺寸', (WidgetTester tester) async {
      final customLibraryService = CustomActionLibraryService();
      final testLibrary = CustomActionLibrary(
        id: 'test_id',
        name: '测试动作库',
        description: '测试描述',
        category: '测试分类',
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      // 测试小屏幕（手机）
      await tester.binding.setSurfaceSize(const Size(400, 800));
      await tester.pumpWidget(
        MaterialApp(
          home: CustomLibraryEditorPage(
            library: testLibrary,
            customLibraryService: customLibraryService,
          ),
        ),
      );

      // 验证网格存在
      expect(find.byType(GridView), findsOneWidget);

      // 测试大屏幕（平板）
      await tester.binding.setSurfaceSize(const Size(800, 600));
      await tester.pumpWidget(
        MaterialApp(
          home: CustomLibraryEditorPage(
            library: testLibrary,
            customLibraryService: customLibraryService,
          ),
        ),
      );

      // 验证网格仍然存在
      expect(find.byType(GridView), findsOneWidget);

      // 重置屏幕尺寸
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('创建动作库对话框应该防止内容溢出', (WidgetTester tester) async {
      final customLibraryService = CustomActionLibraryService();

      // 设置小屏幕尺寸
      await tester.binding.setSurfaceSize(const Size(300, 500));

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => CreateCustomLibraryDialog(
                      customLibraryService: customLibraryService,
                      onLibraryCreated: (library) {},
                    ),
                  );
                },
                child: const Text('显示对话框'),
              ),
            ),
          ),
        ),
      );

      // 显示对话框
      await tester.tap(find.text('显示对话框'));
      await tester.pumpAndSettle();

      // 验证对话框能正常显示，没有溢出错误
      expect(find.byType(SingleChildScrollView), findsOneWidget);
      expect(find.text('创建自定义动作库'), findsOneWidget);

      // 重置屏幕尺寸
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('浮动按钮应该在侧边栏打开时隐藏', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(home: ExerciseLibraryPage()));

      // 验证浮动按钮初始可见
      expect(find.byType(FloatingActionButton), findsOneWidget);

      // 查找并点击侧边栏按钮（通常是汉堡菜单图标）
      final sidebarButton = find.byIcon(Icons.menu);
      if (sidebarButton.evaluate().isNotEmpty) {
        await tester.tap(sidebarButton);
        await tester.pumpAndSettle();

        // 验证浮动按钮动画效果（透明度变化）
        final fab = tester.widget<AnimatedOpacity>(
          find.descendant(
            of: find.byType(FloatingActionButton),
            matching: find.byType(AnimatedOpacity),
          ),
        );

        // 侧边栏打开时，按钮应该变透明
        expect(fab.opacity, equals(0.0));
      }
    });
  });
}
