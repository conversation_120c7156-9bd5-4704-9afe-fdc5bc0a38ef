import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/help_feedback/help_feedback_page.dart';

void main() {
  group('HelpFeedbackPage Tests', () {
    setUp(() {
      // 设置SharedPreferences的模拟数据
      SharedPreferences.setMockInitialValues({});
    });

    testWidgets('应该显示所有主要功能模块', (WidgetTester tester) async {
      // 构建页面
      await tester.pumpWidget(MaterialApp(home: const HelpFeedbackPage()));

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 验证页面标题
      expect(find.text('帮助与反馈'), findsOneWidget);

      // 验证各个功能模块的标题（使用findsAtLeastNWidgets因为"常见问题"可能出现多次）
      expect(find.text('常见问题'), findsAtLeastNWidgets(1));
      expect(find.text('用户反馈'), findsOneWidget);
      expect(find.text('联系开发者'), findsOneWidget);
      expect(find.text('应用信息'), findsOneWidget);
      expect(find.text('用户指南'), findsOneWidget);
    });

    testWidgets('应该显示常见问题列表', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(home: const HelpFeedbackPage()));

      await tester.pumpAndSettle();

      // 验证常见问题项目
      expect(find.text('如何创建时间盒子任务？'), findsOneWidget);
      expect(find.text('知忆相册如何使用？'), findsOneWidget);
      expect(find.text('如何查看学习统计？'), findsOneWidget);
      expect(find.text('动觉记忆训练是什么？'), findsOneWidget);
      expect(find.text('如何备份我的学习数据？'), findsOneWidget);
    });

    testWidgets('应该显示反馈类型选择器', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(home: const HelpFeedbackPage()));

      await tester.pumpAndSettle();

      // 验证反馈类型选项
      expect(find.text('功能建议'), findsOneWidget);
      expect(find.text('问题反馈'), findsOneWidget);
      expect(find.text('使用体验'), findsOneWidget);
      expect(find.text('其他'), findsOneWidget);
    });

    testWidgets('应该显示联系开发者的社交媒体选项', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(home: const HelpFeedbackPage()));

      await tester.pumpAndSettle();

      // 验证社交媒体选项
      expect(find.text('小红书'), findsOneWidget);
      expect(find.text('抖音'), findsOneWidget);
      expect(find.text('微博'), findsOneWidget);
      expect(find.text('B站'), findsOneWidget);
      expect(find.text('知乎'), findsOneWidget);
    });

    testWidgets('应该显示应用信息', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(home: const HelpFeedbackPage()));

      await tester.pumpAndSettle();

      // 验证应用信息
      expect(find.text('应用名称'), findsOneWidget);
      expect(find.text('OneDay'), findsOneWidget);
      expect(find.text('当前版本'), findsOneWidget);
      expect(find.text('开发团队'), findsOneWidget);
      expect(find.text('OneDay团队'), findsOneWidget);
      expect(find.text('应用简介'), findsOneWidget);
      expect(find.text('让每一天都充满收获'), findsOneWidget);
    });

    testWidgets('应该显示用户指南选项', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(home: const HelpFeedbackPage()));

      await tester.pumpAndSettle();

      // 验证用户指南选项
      expect(find.text('快速入门'), findsOneWidget);
      expect(find.text('功能详解'), findsOneWidget);
      expect(find.text('学习方法'), findsOneWidget);
      expect(find.text('常见问题'), findsAtLeastNWidgets(2)); // 在两个地方出现
    });

    testWidgets('点击反馈类型应该能够选择', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(home: const HelpFeedbackPage()));

      await tester.pumpAndSettle();

      // 点击"问题反馈"选项
      await tester.tap(find.text('问题反馈'));
      await tester.pumpAndSettle();

      // 验证选择状态（这里需要根据实际的UI状态来验证）
      // 由于FilterChip的选中状态可能不容易直接验证，我们可以验证点击没有报错
      expect(tester.takeException(), isNull);
    });

    testWidgets('应该能找到反馈输入框和提交按钮', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(home: const HelpFeedbackPage()));

      await tester.pumpAndSettle();

      // 验证反馈输入框存在
      expect(find.byType(TextField), findsOneWidget);

      // 验证提交按钮存在
      expect(find.text('提交反馈'), findsOneWidget);

      // 验证反馈类型选择器存在
      expect(find.text('反馈类型'), findsOneWidget);
    });

    testWidgets('应该显示正确的应用版本信息', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(home: const HelpFeedbackPage()));

      await tester.pumpAndSettle();

      // 验证应用信息部分存在
      expect(find.text('应用名称'), findsOneWidget);
      expect(find.text('OneDay'), findsOneWidget);
      expect(find.text('开发团队'), findsOneWidget);
      expect(find.text('OneDay团队'), findsOneWidget);
    });
  });
}
