import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DropdownButton纯白色背景测试', () {
    testWidgets('验证Material 2.0模式下的DropdownButton纯白色背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            useMaterial3: true, // 应用级别使用Material 3.0
            brightness: Brightness.light,
            primaryColor: const Color(0xFF2E7EED),
          ),
          home: Scaffold(
            body: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // 模拟知忆相册的分类选择DropdownButton（使用Material 2.0）
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      border: Border.all(color: const Color(0xFFE3E2E0)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: Material(
                        color: Colors.transparent,
                        child: Theme(
                          data: ThemeData(
                            useMaterial3: false, // 局部使用Material 2.0避免surface tinting
                            brightness: Brightness.light,
                          ),
                          child: DropdownButton<String>(
                            value: null,
                            hint: const Text(
                              '请选择分类',
                              style: TextStyle(color: Color(0xFF6E6E6E)),
                            ),
                            isExpanded: true,
                            dropdownColor: const Color(0xFFFFFFFF), // 明确的纯白色
                            elevation: 8, // 增加阴影确保弹出菜单在最上层
                            items: const [
                              DropdownMenuItem(value: '景点', child: Text('景点')),
                              DropdownMenuItem(value: '人文', child: Text('人文')),
                              DropdownMenuItem(value: '自然', child: Text('自然')),
                              DropdownMenuItem(value: '新分类', child: Text('新分类')),
                              DropdownMenuItem(value: '学校', child: Text('学校')),
                            ],
                            onChanged: (String? newValue) {
                              // 处理选择变化
                            },
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证DropdownButton正确显示
      expect(find.text('请选择分类'), findsOneWidget);
      expect(find.byType(DropdownButton<String>), findsOneWidget);
    });

    testWidgets('对比Material 2.0和Material 3.0的DropdownButton背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            useMaterial3: true,
            brightness: Brightness.light,
            primaryColor: const Color(0xFF2E7EED),
          ),
          home: Scaffold(
            body: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Material 3.0版本（可能有surface tinting）
                  const Text('Material 3.0版本:'),
                  DropdownButton<String>(
                    value: null,
                    hint: const Text('Material 3.0样式'),
                    dropdownColor: const Color(0xFFFFFFFF),
                    items: const [
                      DropdownMenuItem(value: '选项1', child: Text('选项1')),
                    ],
                    onChanged: (value) {},
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // Material 2.0版本（纯白色背景）
                  const Text('Material 2.0版本:'),
                  Theme(
                    data: ThemeData(
                      useMaterial3: false, // 使用Material 2.0
                      brightness: Brightness.light,
                    ),
                    child: DropdownButton<String>(
                      value: null,
                      hint: const Text('Material 2.0样式'),
                      dropdownColor: const Color(0xFFFFFFFF),
                      elevation: 8,
                      items: const [
                        DropdownMenuItem(value: '选项1', child: Text('选项1')),
                      ],
                      onChanged: (value) {},
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证两种样式都正确显示
      expect(find.text('Material 3.0样式'), findsOneWidget);
      expect(find.text('Material 2.0样式'), findsOneWidget);
      expect(find.byType(DropdownButton<String>), findsNWidgets(2));
    });

    testWidgets('验证知忆相册分类选择的完整实现', (WidgetTester tester) async {
      // 模拟完整的知忆相册分类选择场景
      String? selectedCategory;
      
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            useMaterial3: true, // OneDay应用使用Material 3.0
            brightness: Brightness.light,
            primaryColor: const Color(0xFF2E7EED),
            scaffoldBackgroundColor: Colors.white,
          ),
          home: StatefulBuilder(
            builder: (context, setState) {
              return Scaffold(
                backgroundColor: Colors.white,
                body: Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '分类',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF37352F),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                        decoration: BoxDecoration(
                          border: Border.all(color: const Color(0xFFE3E2E0)),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: Material(
                            color: Colors.transparent,
                            child: Theme(
                              data: Theme.of(context).copyWith(
                                 // 使用Material 2.0避免surface tinting
                              ),
                              child: DropdownButton<String>(
                                value: selectedCategory,
                                hint: const Text(
                                  '请选择分类',
                                  style: TextStyle(color: Color(0xFF6E6E6E)),
                                ),
                                isExpanded: true,
                                dropdownColor: const Color(0xFFFFFFFF), // 明确的纯白色
                                elevation: 8, // 增加阴影确保弹出菜单在最上层
                                items: const [
                                  DropdownMenuItem(value: '景点', child: Text('景点')),
                                  DropdownMenuItem(value: '人文', child: Text('人文')),
                                  DropdownMenuItem(value: '自然', child: Text('自然')),
                                  DropdownMenuItem(value: '新分类', child: Text('新分类')),
                                  DropdownMenuItem(value: '学校', child: Text('学校')),
                                  DropdownMenuItem(value: '学前', child: Text('学前')),
                                  DropdownMenuItem(value: '小学', child: Text('小学')),
                                  DropdownMenuItem(value: '中学', child: Text('中学')),
                                  DropdownMenuItem(value: '高中', child: Text('高中')),
                                ],
                                onChanged: (String? newValue) {
                                  setState(() {
                                    selectedCategory = newValue;
                                  });
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始状态
      expect(find.text('请选择分类'), findsOneWidget);
      expect(find.text('分类'), findsOneWidget);
      
      // 点击DropdownButton打开菜单
      await tester.tap(find.byType(DropdownButton<String>));
      await tester.pumpAndSettle();
      
      // 验证菜单项显示
      expect(find.text('景点'), findsOneWidget);
      expect(find.text('人文'), findsOneWidget);
      expect(find.text('自然'), findsOneWidget);
      expect(find.text('新分类'), findsOneWidget);
      expect(find.text('学校'), findsOneWidget);
      expect(find.text('学前'), findsOneWidget);
      expect(find.text('小学'), findsOneWidget);
      expect(find.text('中学'), findsOneWidget);
      expect(find.text('高中'), findsOneWidget);
      
      // 选择一个选项
      await tester.tap(find.text('景点').last);
      await tester.pumpAndSettle();
      
      // 验证选择结果
      expect(find.text('景点'), findsOneWidget);
      expect(find.text('请选择分类'), findsNothing);
    });

    testWidgets('验证Color(0xFFFFFFFF)的纯白色效果', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            useMaterial3: false, // 使用Material 2.0确保纯净效果
            brightness: Brightness.light,
          ),
          home: Scaffold(
            body: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // 纯白色背景的Container作为对比
                  Container(
                    width: 200,
                    height: 50,
                    color: const Color(0xFFFFFFFF),
                    child: const Center(
                      child: Text(
                        '纯白色背景',
                        style: TextStyle(color: Color(0xFF37352F)),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // 使用相同颜色的DropdownButton
                  DropdownButton<String>(
                    value: null,
                    hint: const Text('纯白色DropdownButton'),
                    dropdownColor: const Color(0xFFFFFFFF), // 相同的纯白色
                    elevation: 8,
                    items: const [
                      DropdownMenuItem(value: '选项1', child: Text('选项1')),
                    ],
                    onChanged: (value) {},
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      
      expect(find.text('纯白色背景'), findsOneWidget);
      expect(find.text('纯白色DropdownButton'), findsOneWidget);
    });
  });
}
