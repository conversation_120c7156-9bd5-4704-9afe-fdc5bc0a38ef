import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/features/memory_palace/palace_manager_page.dart';
import 'package:oneday/features/home/<USER>';
import 'package:oneday/features/wage_system/store_page.dart';

void main() {
  group('弹窗背景色统一性测试', () {
    testWidgets('全局Dialog主题应该使用白色背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            dialogTheme: const DialogThemeData(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(16)),
              ),
            ),
          ),
          home: const Scaffold(
            body: Center(child: Text('Test')),
          ),
        ),
      );

      // 验证主题设置
      final theme = Theme.of(tester.element(find.text('Test')));
      expect(theme.dialogTheme.backgroundColor, Colors.white);
    });

    testWidgets('知忆相册页面的弹窗应该使用白色背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 等待页面加载
      await tester.pumpAndSettle();

      // 验证页面加载成功
      expect(find.byType(PalaceManagerPage), findsOneWidget);
    });

    testWidgets('首页的BottomSheet应该使用白色背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: const MaterialApp(
            home: HomePage(),
          ),
        ),
      );

      // 等待页面加载
      await tester.pumpAndSettle();

      // 验证页面加载成功
      expect(find.byType(HomePage), findsOneWidget);
    });

    testWidgets('工资商店页面的FilterChip应该使用白色背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: StorePage(),
        ),
      );

      // 等待页面加载
      await tester.pumpAndSettle();

      // 查找FilterChip组件
      final filterChips = find.byType(FilterChip);
      if (filterChips.evaluate().isNotEmpty) {
        final filterChip = tester.widget<FilterChip>(filterChips.first);
        
        // 验证未选中状态的背景色为白色
        expect(filterChip.backgroundColor, Colors.white);
      }
    });

    testWidgets('AlertDialog应该继承全局主题的白色背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            dialogTheme: const DialogThemeData(
              backgroundColor: Colors.white,
            ),
          ),
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => const AlertDialog(
                      title: Text('测试对话框'),
                      content: Text('这是一个测试对话框'),
                    ),
                  );
                },
                child: const Text('显示对话框'),
              ),
            ),
          ),
        ),
      );

      // 点击按钮显示对话框
      await tester.tap(find.text('显示对话框'));
      await tester.pumpAndSettle();

      // 验证对话框显示
      expect(find.text('测试对话框'), findsOneWidget);
      expect(find.text('这是一个测试对话框'), findsOneWidget);
    });

    testWidgets('ModalBottomSheet应该使用白色背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showModalBottomSheet(
                    context: context,
                    backgroundColor: Colors.white,
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                    ),
                    builder: (context) => Container(
                      height: 200,
                      padding: const EdgeInsets.all(16),
                      child: const Text('底部弹窗内容'),
                    ),
                  );
                },
                child: const Text('显示底部弹窗'),
              ),
            ),
          ),
        ),
      );

      // 点击按钮显示底部弹窗
      await tester.tap(find.text('显示底部弹窗'));
      await tester.pumpAndSettle();

      // 验证底部弹窗显示
      expect(find.text('底部弹窗内容'), findsOneWidget);
    });

    testWidgets('自定义Dialog应该使用白色背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => Dialog(
                      backgroundColor: Colors.transparent,
                      child: Container(
                        width: 300,
                        height: 200,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: const Color(0xFFE3E2E0), width: 1),
                        ),
                        child: const Center(
                          child: Text('自定义对话框'),
                        ),
                      ),
                    ),
                  );
                },
                child: const Text('显示自定义对话框'),
              ),
            ),
          ),
        ),
      );

      // 点击按钮显示自定义对话框
      await tester.tap(find.text('显示自定义对话框'));
      await tester.pumpAndSettle();

      // 验证自定义对话框显示
      expect(find.text('自定义对话框'), findsOneWidget);
    });

    testWidgets('PopupMenuButton应该使用白色背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: AppBar(
              actions: [
                PopupMenuButton<String>(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: const BorderSide(color: Color(0xFFE3E2E0), width: 1),
                  ),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'option1',
                      child: Text('选项1'),
                    ),
                    const PopupMenuItem(
                      value: 'option2',
                      child: Text('选项2'),
                    ),
                  ],
                ),
              ],
            ),
            body: const Center(child: Text('测试页面')),
          ),
        ),
      );

      // 查找PopupMenuButton
      final popupButton = find.byType(PopupMenuButton<String>);
      expect(popupButton, findsOneWidget);

      // 点击PopupMenuButton
      await tester.tap(popupButton);
      await tester.pumpAndSettle();

      // 验证菜单项显示
      expect(find.text('选项1'), findsOneWidget);
      expect(find.text('选项2'), findsOneWidget);
    });

    testWidgets('Notion风格设计一致性验证', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            // Notion风格主题配置
            dialogTheme: const DialogThemeData(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(16)),
              ),
              titleTextStyle: TextStyle(
                color: Color(0xFF37352F),
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
              contentTextStyle: TextStyle(
                color: Color(0xFF787774),
                fontSize: 16,
                height: 1.5,
              ),
            ),
          ),
          home: const Scaffold(
            body: Center(child: Text('Notion风格测试')),
          ),
        ),
      );

      // 验证主题配置
      final theme = Theme.of(tester.element(find.text('Notion风格测试')));
      
      // 验证对话框背景色
      expect(theme.dialogTheme.backgroundColor, Colors.white);
      
      // 验证圆角设计
      expect(theme.dialogTheme.shape, isA<RoundedRectangleBorder>());
      
      // 验证标题样式
      expect(theme.dialogTheme.titleTextStyle?.color, const Color(0xFF37352F));
      expect(theme.dialogTheme.titleTextStyle?.fontWeight, FontWeight.w600);
      
      // 验证内容样式
      expect(theme.dialogTheme.contentTextStyle?.color, const Color(0xFF787774));
    });
  });
}
