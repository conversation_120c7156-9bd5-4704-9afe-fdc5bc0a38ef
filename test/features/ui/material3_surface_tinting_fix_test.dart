import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Material 3.0 Surface Tinting修复测试', () {
    testWidgets('验证知忆相册DropdownButton禁用surface tinting', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            useMaterial3: true, // 启用Material 3.0
            brightness: Brightness.light,
            primaryColor: const Color(0xFF2E7EED),
          ),
          home: Scaffold(
            body: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // 模拟知忆相册的分类选择DropdownButton
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: const Color(0xFFE3E2E0)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: Theme(
                        data: ThemeData(
                          useMaterial3: true,
                          popupMenuTheme: const PopupMenuThemeData(
                            surfaceTintColor:
                                Colors.transparent, // 禁用surface tinting
                          ),
                        ),
                        child: DropdownButton<String>(
                          value: null,
                          hint: const Text(
                            '请选择分类',
                            style: TextStyle(color: Color(0xFF6E6E6E)),
                          ),
                          isExpanded: true,
                          dropdownColor: const Color(0xFFFFFFFF), // 明确的纯白色
                          items: const [
                            DropdownMenuItem(value: '景点', child: Text('景点')),
                            DropdownMenuItem(value: '人文', child: Text('人文')),
                            DropdownMenuItem(value: '自然', child: Text('自然')),
                          ],
                          onChanged: (String? newValue) {
                            // 处理选择变化
                          },
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证DropdownButton正确显示
      expect(find.text('请选择分类'), findsOneWidget);
      expect(find.byType(DropdownButton<String>), findsOneWidget);
    });

    testWidgets('验证时间盒子DropdownButtonFormField禁用surface tinting', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            useMaterial3: true, // 启用Material 3.0
            brightness: Brightness.light,
            primaryColor: const Color(0xFF2E7EED),
          ),
          home: Scaffold(
            body: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // 模拟时间盒子的学科分类选择器
                  Theme(
                    data: ThemeData(
                      useMaterial3: true,
                      popupMenuTheme: const PopupMenuThemeData(
                        surfaceTintColor:
                            Colors.transparent, // 禁用surface tinting
                      ),
                    ),
                    child: DropdownButtonFormField<String>(
                      value: null,
                      decoration: const InputDecoration(
                        labelText: '学科分类',
                        border: OutlineInputBorder(),
                      ),
                      dropdownColor: const Color(0xFFFFFFFF), // 明确的纯白色
                      items: ['计算机科学', '数学', '英语', '政治', '休息', '其他'].map((
                        category,
                      ) {
                        return DropdownMenuItem(
                          value: category,
                          child: Text(category),
                        );
                      }).toList(),
                      onChanged: (value) {
                        // 处理选择变化
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证DropdownButtonFormField正确显示
      expect(find.text('学科分类'), findsOneWidget);
      expect(find.byType(DropdownButtonFormField<String>), findsOneWidget);
    });

    testWidgets('对比测试：有无surface tinting的视觉差异', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            useMaterial3: true, // 启用Material 3.0
            brightness: Brightness.light,
            primaryColor: const Color(0xFF2E7EED),
          ),
          home: Scaffold(
            body: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // 未禁用surface tinting的DropdownButton
                  const Text('未禁用surface tinting:'),
                  DropdownButton<String>(
                    value: null,
                    hint: const Text('默认Material 3.0样式'),
                    dropdownColor: Colors.white, // 仅设置Colors.white
                    items: const [
                      DropdownMenuItem(value: '选项1', child: Text('选项1')),
                    ],
                    onChanged: (value) {},
                  ),

                  const SizedBox(height: 20),

                  // 禁用surface tinting的DropdownButton
                  const Text('已禁用surface tinting:'),
                  Theme(
                    data: ThemeData(
                      useMaterial3: true,
                      popupMenuTheme: const PopupMenuThemeData(
                        surfaceTintColor:
                            Colors.transparent, // 禁用surface tinting
                      ),
                    ),
                    child: DropdownButton<String>(
                      value: null,
                      hint: const Text('纯白色背景样式'),
                      dropdownColor: const Color(0xFFFFFFFF), // 明确的纯白色
                      items: const [
                        DropdownMenuItem(value: '选项1', child: Text('选项1')),
                      ],
                      onChanged: (value) {},
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证两种样式都正确显示
      expect(find.text('默认Material 3.0样式'), findsOneWidget);
      expect(find.text('纯白色背景样式'), findsOneWidget);
      expect(find.byType(DropdownButton<String>), findsNWidgets(2));
    });

    testWidgets('验证Color(0xFFFFFFFF)与Colors.white的等价性', (
      WidgetTester tester,
    ) async {
      // 验证两种白色定义在Material 3.0中的表现
      const explicitWhite = Color(0xFFFFFFFF);
      const materialWhite = Colors.white;

      // 验证色值相同
      expect(explicitWhite.toARGB32(), equals(materialWhite.toARGB32()));
      expect(explicitWhite.r, equals(materialWhite.r));
      expect(explicitWhite.g, equals(materialWhite.g));
      expect(explicitWhite.b, equals(materialWhite.b));
      expect(explicitWhite.a, equals(materialWhite.a));

      // 验证在Material 3.0主题下的表现
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(useMaterial3: true, brightness: Brightness.light),
          home: Scaffold(
            body: Column(
              children: [
                Container(
                  width: 100,
                  height: 50,
                  color: explicitWhite,
                  child: const Text('明确白色'),
                ),
                Container(
                  width: 100,
                  height: 50,
                  color: materialWhite,
                  child: const Text('Material白色'),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('明确白色'), findsOneWidget);
      expect(find.text('Material白色'), findsOneWidget);
    });

    testWidgets('验证OneDay应用的Material 3.0兼容性', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            useMaterial3: true, // OneDay应用使用Material 3.0
            brightness: Brightness.light,
            primaryColor: const Color(0xFF2E7EED), // OneDay主色调
            scaffoldBackgroundColor: Colors.white,

            // OneDay的Dialog主题
            dialogTheme: const DialogThemeData(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(16)),
              ),
            ),

            // 禁用全局surface tinting
            popupMenuTheme: const PopupMenuThemeData(
              surfaceTintColor: Colors.transparent,
            ),
          ),
          home: Scaffold(
            body: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // OneDay风格的白色背景组件
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: const Color(0xFFE3E2E0)),
                    ),
                    child: const Text(
                      'OneDay风格白色背景',
                      style: TextStyle(color: Color(0xFF37352F), fontSize: 16),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      expect(find.text('OneDay风格白色背景'), findsOneWidget);

      // 验证主题配置
      final theme = Theme.of(tester.element(find.text('OneDay风格白色背景')));
      expect(theme.useMaterial3, isTrue);
      expect(theme.dialogTheme.backgroundColor, equals(Colors.white));
      expect(theme.popupMenuTheme.surfaceTintColor, equals(Colors.transparent));
    });
  });
}
