import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/exercise/pao_integration_service.dart';
import 'package:oneday/features/exercise/custom_action_library_service.dart';

/// Test widget that simulates the library selector modal
class TestLibrarySelectorWidget extends StatefulWidget {
  final PAOIntegrationService paoService;

  const TestLibrarySelectorWidget({super.key, required this.paoService});

  @override
  State<TestLibrarySelectorWidget> createState() =>
      _TestLibrarySelectorWidgetState();
}

class _TestLibrarySelectorWidgetState extends State<TestLibrarySelectorWidget> {
  void _showLibrarySelector() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Fixed header
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: const Color(0xFFE3E2E0),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    '选择动作库',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                ],
              ),
            ),
            // Scrollable library list
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    ...(widget.paoService.getAvailableLibraryOptions()).map((
                      option,
                    ) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 0,
                            vertical: 4,
                          ),
                          leading: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: option['isSelected']
                                  ? const Color(
                                      0xFF2E7EED,
                                    ).withValues(alpha: 0.1)
                                  : const Color(0xFFF7F6F3),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: option['isSelected']
                                    ? const Color(0xFF2E7EED)
                                    : const Color(0xFFE3E2E0),
                              ),
                            ),
                            child: Icon(
                              option['type'] == 'preset'
                                  ? Icons.fitness_center
                                  : Icons.folder,
                              color: option['isSelected']
                                  ? const Color(0xFF2E7EED)
                                  : const Color(0xFF9B9A97),
                              size: 20,
                            ),
                          ),
                          title: Text(
                            option['name'],
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFF37352F),
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                option['description'],
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFF9B9A97),
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '${option['completedActions']}/26 动作',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Color(0xFF0F7B6C),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          trailing: option['isSelected']
                              ? const Icon(
                                  Icons.check_circle,
                                  color: Color(0xFF2E7EED),
                                )
                              : null,
                          onTap: () => Navigator.pop(context),
                        ),
                      );
                    }),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Library Selector'),
        actions: [
          IconButton(
            icon: const Icon(Icons.library_books),
            onPressed: _showLibrarySelector,
          ),
        ],
      ),
      body: const Center(
        child: Text('Tap the library icon to test the selector'),
      ),
    );
  }
}

void main() {
  group('Action Library Selector Overflow Tests', () {
    late CustomActionLibraryService customLibraryService;
    late PAOIntegrationService paoService;

    setUp(() {
      customLibraryService = CustomActionLibraryService();
      paoService = PAOIntegrationService(customLibraryService);
    });

    testWidgets(
      'Modal bottom sheet should handle many action libraries without overflow',
      (WidgetTester tester) async {
        // Create multiple custom action libraries to test overflow scenario
        for (int i = 1; i <= 10; i++) {
          await customLibraryService.createLibrary(
            name: '自定义动作库 $i',
            description:
                '这是第 $i 个自定义动作库，用于测试长列表的显示效果和溢出处理。这个描述故意写得很长来测试文本溢出处理。',
          );
        }

        // Test on a small screen size (iPhone SE)
        await tester.binding.setSurfaceSize(const Size(375, 667));

        await tester.pumpWidget(
          MaterialApp(home: TestLibrarySelectorWidget(paoService: paoService)),
        );

        await tester.pumpAndSettle();

        // Find and tap the library selector button
        final libraryButton = find.byIcon(Icons.library_books);
        expect(libraryButton, findsOneWidget);

        await tester.tap(libraryButton);
        await tester.pumpAndSettle();

        // Verify the modal bottom sheet is displayed
        expect(find.text('选择动作库'), findsOneWidget);

        // Verify that all libraries are displayed (preset + 10 custom)
        expect(find.text('系统预设动作库'), findsOneWidget);
        for (int i = 1; i <= 10; i++) {
          expect(find.text('自定义动作库 $i'), findsOneWidget);
        }

        // Verify no overflow errors occurred
        expect(tester.takeException(), isNull);

        // Verify the modal is scrollable by attempting to scroll
        final scrollable = find.byType(SingleChildScrollView);
        expect(scrollable, findsOneWidget);

        // Test scrolling functionality
        await tester.drag(scrollable, const Offset(0, -200));
        await tester.pumpAndSettle();

        // Verify no overflow after scrolling
        expect(tester.takeException(), isNull);
      },
    );

    testWidgets('Modal bottom sheet should handle long text without overflow', (
      WidgetTester tester,
    ) async {
      // Create a library with very long name and description
      await customLibraryService.createLibrary(
        name: '这是一个非常非常长的动作库名称，用来测试文本溢出处理是否正确工作',
        description:
            '这是一个超级长的描述文本，包含了很多很多的字符，目的是测试在小屏幕设备上是否会出现文本溢出的问题。这个描述应该被正确地截断并显示省略号。',
      );

      // Test on a very small screen
      await tester.binding.setSurfaceSize(const Size(320, 568));

      await tester.pumpWidget(
        MaterialApp(home: TestLibrarySelectorWidget(paoService: paoService)),
      );

      await tester.pumpAndSettle();

      // Open the library selector
      final libraryButton = find.byIcon(Icons.library_books);
      await tester.tap(libraryButton);
      await tester.pumpAndSettle();

      // Verify no overflow errors with long text
      expect(tester.takeException(), isNull);

      // Verify the long text is displayed (even if truncated)
      expect(find.textContaining('这是一个非常非常长的动作库名称'), findsOneWidget);
      expect(find.textContaining('这是一个超级长的描述文本'), findsOneWidget);
    });

    testWidgets('Modal bottom sheet should respect height constraints', (
      WidgetTester tester,
    ) async {
      // Create many libraries to test height constraints
      for (int i = 1; i <= 15; i++) {
        await customLibraryService.createLibrary(
          name: '动作库 $i',
          description: '描述 $i',
        );
      }

      // Test on different screen sizes
      final testSizes = [
        const Size(375, 667), // iPhone 8
        const Size(414, 896), // iPhone 11
        const Size(768, 1024), // iPad
      ];

      for (final size in testSizes) {
        await tester.binding.setSurfaceSize(size);

        await tester.pumpWidget(
          MaterialApp(home: TestLibrarySelectorWidget(paoService: paoService)),
        );

        await tester.pumpAndSettle();

        // Open the library selector
        final libraryButton = find.byIcon(Icons.library_books);
        await tester.tap(libraryButton);
        await tester.pumpAndSettle();

        // Verify the modal doesn't exceed 80% of screen height
        final modalContainer = tester.widget<Container>(
          find
              .descendant(
                of: find.byType(BottomSheet),
                matching: find.byType(Container),
              )
              .first,
        );

        final constraints = modalContainer.constraints;
        expect(constraints?.maxHeight, equals(size.height * 0.8));

        // Verify no overflow
        expect(tester.takeException(), isNull);

        // Close the modal for next iteration
        await tester.tapAt(Offset(size.width / 2, 50));
        await tester.pumpAndSettle();
      }
    });

    testWidgets('Modal bottom sheet should handle empty library list', (
      WidgetTester tester,
    ) async {
      // Test with no custom libraries (only preset)
      await tester.binding.setSurfaceSize(const Size(375, 667));

      await tester.pumpWidget(
        MaterialApp(home: TestLibrarySelectorWidget(paoService: paoService)),
      );

      await tester.pumpAndSettle();

      // Open the library selector
      final libraryButton = find.byIcon(Icons.library_books);
      await tester.tap(libraryButton);
      await tester.pumpAndSettle();

      // Verify only the preset library is shown
      expect(find.text('选择动作库'), findsOneWidget);
      expect(find.text('系统预设动作库'), findsOneWidget);

      // Verify no overflow with minimal content
      expect(tester.takeException(), isNull);
    });

    testWidgets('Modal bottom sheet text truncation should work correctly', (
      WidgetTester tester,
    ) async {
      await tester.binding.setSurfaceSize(const Size(375, 667));

      await tester.pumpWidget(
        MaterialApp(home: TestLibrarySelectorWidget(paoService: paoService)),
      );

      await tester.pumpAndSettle();

      // Open the library selector
      final libraryButton = find.byIcon(Icons.library_books);
      await tester.tap(libraryButton);
      await tester.pumpAndSettle();

      // Find all Text widgets in the modal
      final textWidgets = tester.widgetList<Text>(
        find.descendant(
          of: find.byType(BottomSheet),
          matching: find.byType(Text),
        ),
      );

      // Verify that text widgets with maxLines also have overflow handling
      for (final textWidget in textWidgets) {
        if (textWidget.maxLines != null && textWidget.maxLines! > 0) {
          expect(textWidget.overflow, equals(TextOverflow.ellipsis));
        }
      }
    });
  });
}
