import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/features/auth/register_page.dart';

void main() {
  group('RegisterPage Tests', () {
    Widget createTestWidget() {
      return ProviderScope(child: MaterialApp(home: const RegisterPage()));
    }

    testWidgets('注册页面应该显示基本UI元素', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // 验证页面标题
      expect(find.text('创建您的OneDay账户'), findsOneWidget);
      expect(find.text('开始您的个性化学习之旅'), findsOneWidget);

      // 验证输入框
      expect(find.byType(TextFormField), findsNWidgets(4)); // 姓名、邮箱、密码、确认密码

      // 验证标签
      expect(find.text('姓名'), findsOneWidget);
      expect(find.text('邮箱'), findsOneWidget);
      expect(find.text('密码'), findsOneWidget);
      expect(find.text('确认密码'), findsOneWidget);

      // 验证服务条款复选框
      expect(find.byType(Checkbox), findsOneWidget);

      // 验证社交登录按钮
      expect(find.text('使用 Apple 注册'), findsOneWidget);
      expect(find.text('使用微信登录'), findsOneWidget);

      // 验证底部链接
      expect(find.text('已有账户？'), findsOneWidget);
      expect(find.text('立即登录'), findsOneWidget);
    });

    testWidgets('密码可见性切换应该正常工作', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // 找到密码输入框的可见性切换按钮
      final visibilityButtons = find.byIcon(Icons.visibility);
      expect(visibilityButtons, findsNWidgets(2)); // 密码和确认密码

      // 点击第一个密码字段的可见性按钮
      await tester.tap(visibilityButtons.first);
      await tester.pump();

      // 验证图标变为 visibility_off
      expect(find.byIcon(Icons.visibility_off), findsAtLeastNWidgets(1));
    });

    testWidgets('返回按钮应该存在', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // 验证返回按钮存在
      expect(find.byIcon(Icons.arrow_back_ios), findsOneWidget);
    });
  });
}
