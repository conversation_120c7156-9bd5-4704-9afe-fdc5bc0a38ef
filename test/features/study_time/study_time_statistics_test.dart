import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/study_time/services/study_time_statistics_service.dart';
import 'package:oneday/features/study_time/models/study_time_models.dart';
import 'package:oneday/features/time_box/models/timebox_models.dart';

void main() {
  group('StudyTimeStatisticsService', () {
    late StudyTimeStatisticsService service;

    setUp(() async {
      // 设置测试环境的SharedPreferences
      SharedPreferences.setMockInitialValues({});
      service = StudyTimeStatisticsService();
      await service.initialize();
    });

    tearDown(() {
      service.dispose();
    });

    group('Data Aggregation', () {
      test('should calculate correct daily statistics from completed tasks', () async {
        // 创建测试任务
        final now = DateTime.now();
        final tasks = [
          TimeBoxTask(
            id: '1',
            title: '数学学习',
            description: '线性代数',
            plannedMinutes: 60,
            status: TaskStatus.completed,
            priority: TaskPriority.high,
            category: '数学',
            createdAt: now,
            startTime: now.subtract(const Duration(hours: 1)),
            endTime: now.subtract(const Duration(minutes: 30)),
          ),
          TimeBoxTask(
            id: '2',
            title: '英语学习',
            description: '词汇背诵',
            plannedMinutes: 30,
            status: TaskStatus.completed,
            priority: TaskPriority.medium,
            category: '英语',
            createdAt: now,
            startTime: now.subtract(const Duration(minutes: 30)),
            endTime: now,
          ),
        ];

        // 更新统计数据
        await service.updateFromTimeBoxTasks(tasks);

        // 验证聚合数据
        final aggregation = service.currentAggregation;
        expect(aggregation, isNotNull);
        expect(aggregation!.todayMinutes, equals(60)); // 30 + 30 分钟实际时长
        expect(aggregation.todayCompletedTasks, equals(2));
        expect(aggregation.todayWage, greaterThan(0));
      });

      test('should handle tasks from different days correctly', () async {
        final now = DateTime.now();
        final yesterday = now.subtract(const Duration(days: 1));
        
        final tasks = [
          // 今天的任务
          TimeBoxTask(
            id: '1',
            title: '今日任务',
            description: '今日学习',
            plannedMinutes: 60,
            status: TaskStatus.completed,
            priority: TaskPriority.high,
            category: '数学',
            createdAt: now,
            startTime: now.subtract(const Duration(hours: 1)),
            endTime: now.subtract(const Duration(minutes: 30)),
          ),
          // 昨天的任务
          TimeBoxTask(
            id: '2',
            title: '昨日任务',
            description: '昨日学习',
            plannedMinutes: 45,
            status: TaskStatus.completed,
            priority: TaskPriority.medium,
            category: '英语',
            createdAt: yesterday,
            startTime: yesterday.subtract(const Duration(hours: 1)),
            endTime: yesterday.subtract(const Duration(minutes: 15)),
          ),
        ];

        await service.updateFromTimeBoxTasks(tasks);

        // 验证今日数据
        final todayStats = service.todayStatistics;
        expect(todayStats, isNotNull);
        expect(todayStats!.totalStudyMinutes, equals(30)); // 只有今天的任务

        // 验证昨日数据
        final yesterdayStats = service.getDailyStatistics(yesterday);
        expect(yesterdayStats, isNotNull);
        expect(yesterdayStats!.totalStudyMinutes, equals(45));
      });

      test('should ignore incomplete tasks', () async {
        final now = DateTime.now();
        final tasks = [
          TimeBoxTask(
            id: '1',
            title: '完成任务',
            description: '已完成',
            plannedMinutes: 60,
            status: TaskStatus.completed,
            priority: TaskPriority.high,
            category: '数学',
            createdAt: now,
            startTime: now.subtract(const Duration(hours: 1)),
            endTime: now.subtract(const Duration(minutes: 30)),
          ),
          TimeBoxTask(
            id: '2',
            title: '未完成任务',
            description: '进行中',
            plannedMinutes: 30,
            status: TaskStatus.inProgress,
            priority: TaskPriority.medium,
            category: '英语',
            createdAt: now,
            startTime: now.subtract(const Duration(minutes: 15)),
          ),
        ];

        await service.updateFromTimeBoxTasks(tasks);

        final aggregation = service.currentAggregation;
        expect(aggregation!.todayCompletedTasks, equals(1)); // 只计算完成的任务
        expect(aggregation.todayMinutes, equals(30)); // 只计算完成任务的时长
      });
    });

    group('Study Session Creation', () {
      test('should create study session from time box task correctly', () {
        final now = DateTime.now();
        final task = TimeBoxTask(
          id: 'test-id',
          title: '测试任务',
          description: '测试描述',
          plannedMinutes: 60,
          status: TaskStatus.completed,
          priority: TaskPriority.high,
          category: '数学',
          createdAt: now,
          startTime: now.subtract(const Duration(hours: 1)),
          endTime: now.subtract(const Duration(minutes: 30)),
        );

        final session = StudySession.fromTimeBoxTask(task);

        expect(session.sessionId, equals('test-id'));
        expect(session.title, equals('测试任务'));
        expect(session.category, equals('数学'));
        expect(session.plannedMinutes, equals(60));
        expect(session.actualMinutes, equals(30));
        expect(session.isCompleted, isTrue);
        expect(session.wage, greaterThan(0));
      });
    });

    group('Time Formatting', () {
      test('should format study time correctly', () {
        final stats = StudyTimeStatistics(
          date: '2024-01-01',
          totalStudyMinutes: 125, // 2小时5分钟
          completedTasks: 3,
          totalPlannedMinutes: 180,
          totalWage: 416.67,
          sessions: [],
          lastUpdated: DateTime.now(),
        );

        expect(stats.formattedStudyTime, equals('2h 5m'));

        final shortStats = StudyTimeStatistics(
          date: '2024-01-01',
          totalStudyMinutes: 45, // 45分钟
          completedTasks: 1,
          totalPlannedMinutes: 60,
          totalWage: 150.0,
          sessions: [],
          lastUpdated: DateTime.now(),
        );

        expect(shortStats.formattedStudyTime, equals('45m'));
      });

      test('should calculate efficiency percentage correctly', () {
        final stats = StudyTimeStatistics(
          date: '2024-01-01',
          totalStudyMinutes: 90, // 实际90分钟
          completedTasks: 2,
          totalPlannedMinutes: 120, // 计划120分钟
          totalWage: 300.0,
          sessions: [],
          lastUpdated: DateTime.now(),
        );

        expect(stats.efficiencyPercentage, equals(75.0)); // 90/120 * 100 = 75%
      });
    });

    group('Streak Calculation', () {
      test('should calculate study streak correctly', () async {
        final now = DateTime.now();
        final tasks = <TimeBoxTask>[];

        // 创建连续3天的学习记录
        for (int i = 0; i < 3; i++) {
          final date = now.subtract(Duration(days: i));
          tasks.add(
            TimeBoxTask(
              id: 'task-$i',
              title: '学习任务 $i',
              description: '连续学习',
              plannedMinutes: 60,
              status: TaskStatus.completed,
              priority: TaskPriority.medium,
              category: '数学',
              createdAt: date,
              startTime: date.subtract(const Duration(hours: 1)),
              endTime: date.subtract(const Duration(minutes: 30)),
            ),
          );
        }

        await service.updateFromTimeBoxTasks(tasks);

        final aggregation = service.currentAggregation;
        expect(aggregation!.streakDays, equals(3));
      });

      test('should handle broken streak correctly', () async {
        final now = DateTime.now();
        final tasks = <TimeBoxTask>[];

        // 今天和前天有学习，昨天没有（中断连续性）
        final dates = [now, now.subtract(const Duration(days: 2))];
        
        for (int i = 0; i < dates.length; i++) {
          final date = dates[i];
          tasks.add(
            TimeBoxTask(
              id: 'task-$i',
              title: '学习任务 $i',
              description: '非连续学习',
              plannedMinutes: 60,
              status: TaskStatus.completed,
              priority: TaskPriority.medium,
              category: '数学',
              createdAt: date,
              startTime: date.subtract(const Duration(hours: 1)),
              endTime: date.subtract(const Duration(minutes: 30)),
            ),
          );
        }

        await service.updateFromTimeBoxTasks(tasks);

        final aggregation = service.currentAggregation;
        expect(aggregation!.streakDays, equals(1)); // 只有今天连续
      });
    });

    group('Data Persistence', () {
      test('should save and load statistics correctly', () async {
        final now = DateTime.now();
        final tasks = [
          TimeBoxTask(
            id: '1',
            title: '测试任务',
            description: '持久化测试',
            plannedMinutes: 60,
            status: TaskStatus.completed,
            priority: TaskPriority.high,
            category: '数学',
            createdAt: now,
            startTime: now.subtract(const Duration(hours: 1)),
            endTime: now.subtract(const Duration(minutes: 30)),
          ),
        ];

        // 更新并保存数据
        await service.updateFromTimeBoxTasks(tasks);
        final originalAggregation = service.currentAggregation;

        // 创建新的服务实例来测试加载
        final newService = StudyTimeStatisticsService();
        await newService.initialize();

        // 验证数据是否正确加载
        final loadedAggregation = newService.currentAggregation;
        expect(loadedAggregation, isNotNull);
        expect(loadedAggregation!.todayMinutes, equals(originalAggregation!.todayMinutes));
        expect(loadedAggregation.todayCompletedTasks, equals(originalAggregation.todayCompletedTasks));

        newService.dispose();
      });
    });
  });
}
