import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/memory_palace/palace_manager_page.dart';

void main() {
  group('知忆相册数据持久化测试', () {
    setUp(() {
      // 每个测试前清理SharedPreferences
      SharedPreferences.setMockInitialValues({});
    });

    testWidgets('MemoryPalace JSON序列化和反序列化应该正常工作', (WidgetTester tester) async {
      final originalPalace = MemoryPalace(
        id: 'test_id',
        title: '测试相册',
        imagePaths: ['/path/to/image1.jpg', '/path/to/image2.jpg'],
        anchorCount: 5,
        tags: ['测试', '相册'],
        category: '测试分类',
        createdAt: DateTime(2024, 1, 1, 12, 0, 0),
        lastUsed: DateTime(2024, 1, 2, 15, 30, 0),
        isFromGallery: true,
      );

      // 测试序列化
      final json = originalPalace.toJson();
      expect(json['id'], 'test_id');
      expect(json['title'], '测试相册');
      expect(json['imagePaths'], ['/path/to/image1.jpg', '/path/to/image2.jpg']);
      expect(json['anchorCount'], 5);
      expect(json['tags'], ['测试', '相册']);
      expect(json['category'], '测试分类');
      expect(json['createdAt'], DateTime(2024, 1, 1, 12, 0, 0).millisecondsSinceEpoch);
      expect(json['lastUsed'], DateTime(2024, 1, 2, 15, 30, 0).millisecondsSinceEpoch);
      expect(json['isFromGallery'], true);

      // 测试反序列化
      final deserializedPalace = MemoryPalace.fromJson(json);
      expect(deserializedPalace.id, originalPalace.id);
      expect(deserializedPalace.title, originalPalace.title);
      expect(deserializedPalace.imagePaths, originalPalace.imagePaths);
      expect(deserializedPalace.anchorCount, originalPalace.anchorCount);
      expect(deserializedPalace.tags, originalPalace.tags);
      expect(deserializedPalace.category, originalPalace.category);
      expect(deserializedPalace.createdAt, originalPalace.createdAt);
      expect(deserializedPalace.lastUsed, originalPalace.lastUsed);
      expect(deserializedPalace.isFromGallery, originalPalace.isFromGallery);
    });

    testWidgets('imagePath getter应该返回第一张图片路径', (WidgetTester tester) async {
      final palace = MemoryPalace(
        id: 'test',
        title: '测试',
        imagePaths: ['/first.jpg', '/second.jpg'],
        anchorCount: 0,
        tags: [],
        category: '测试',
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
      );

      expect(palace.imagePath, '/first.jpg');
    });

    testWidgets('imagePath getter应该处理空列表', (WidgetTester tester) async {
      final palace = MemoryPalace(
        id: 'test',
        title: '测试',
        imagePaths: [],
        anchorCount: 0,
        tags: [],
        category: '测试',
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
      );

      expect(palace.imagePath, '');
    });

    testWidgets('应该正确处理isFromGallery默认值', (WidgetTester tester) async {
      // 测试不包含isFromGallery字段的JSON
      final json = {
        'id': 'test',
        'title': '测试',
        'imagePaths': <String>[],
        'anchorCount': 0,
        'tags': <String>[],
        'category': '测试',
        'createdAt': DateTime.now().millisecondsSinceEpoch,
        'lastUsed': DateTime.now().millisecondsSinceEpoch,
        // 注意：没有isFromGallery字段
      };

      final palace = MemoryPalace.fromJson(json);
      expect(palace.isFromGallery, false); // 应该使用默认值false
    });

    testWidgets('PalaceManagerPage应该正确加载数据', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 等待异步加载完成
      await tester.pumpAndSettle();

      // 验证页面加载成功
      expect(find.byType(PalaceManagerPage), findsOneWidget);
    });

    testWidgets('应该有正确的存储键', (WidgetTester tester) async {
      // 这个测试验证存储键的命名约定
      const expectedKey = 'oneday_memory_palaces';
      
      // 验证键名符合项目命名规范
      expect(expectedKey.startsWith('oneday_'), true);
      expect(expectedKey.contains('memory_palaces'), true);
    });

    testWidgets('JSON编码解码应该保持数据完整性', (WidgetTester tester) async {
      final palaces = [
        MemoryPalace(
          id: '1',
          title: '相册1',
          imagePaths: ['/path1.jpg'],
          anchorCount: 1,
          tags: ['tag1'],
          category: 'cat1',
          createdAt: DateTime(2024, 1, 1),
          lastUsed: DateTime(2024, 1, 2),
          isFromGallery: true,
        ),
        MemoryPalace(
          id: '2',
          title: '相册2',
          imagePaths: ['/path2.jpg', '/path3.jpg'],
          anchorCount: 2,
          tags: ['tag2', 'tag3'],
          category: 'cat2',
          createdAt: DateTime(2024, 2, 1),
          lastUsed: DateTime(2024, 2, 2),
          isFromGallery: false,
        ),
      ];

      // 编码
      final jsonList = palaces.map((palace) => palace.toJson()).toList();
      final jsonString = json.encode(jsonList);

      // 解码
      final decodedJsonList = json.decode(jsonString) as List<dynamic>;
      final decodedPalaces = decodedJsonList
          .map((json) => MemoryPalace.fromJson(json))
          .toList();

      // 验证数据完整性
      expect(decodedPalaces.length, palaces.length);
      
      for (int i = 0; i < palaces.length; i++) {
        final original = palaces[i];
        final decoded = decodedPalaces[i];
        
        expect(decoded.id, original.id);
        expect(decoded.title, original.title);
        expect(decoded.imagePaths, original.imagePaths);
        expect(decoded.anchorCount, original.anchorCount);
        expect(decoded.tags, original.tags);
        expect(decoded.category, original.category);
        expect(decoded.createdAt, original.createdAt);
        expect(decoded.lastUsed, original.lastUsed);
        expect(decoded.isFromGallery, original.isFromGallery);
      }
    });

    testWidgets('应该正确处理特殊字符和中文', (WidgetTester tester) async {
      final palace = MemoryPalace(
        id: 'test_中文_123',
        title: '测试相册 🎨 特殊字符 "引号" \'单引号\'',
        imagePaths: ['/路径/中文文件名.jpg'],
        anchorCount: 0,
        tags: ['中文标签', 'English Tag', '特殊字符@#\$%'],
        category: '中文分类',
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
      );

      // 序列化和反序列化
      final json = palace.toJson();
      final jsonString = jsonEncode(json);
      final decodedJson = jsonDecode(jsonString);
      final decodedPalace = MemoryPalace.fromJson(decodedJson);

      // 验证特殊字符和中文处理正确
      expect(decodedPalace.id, palace.id);
      expect(decodedPalace.title, palace.title);
      expect(decodedPalace.imagePaths, palace.imagePaths);
      expect(decodedPalace.tags, palace.tags);
      expect(decodedPalace.category, palace.category);
    });
  });
}
