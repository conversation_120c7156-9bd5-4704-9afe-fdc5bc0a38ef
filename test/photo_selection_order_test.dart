import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/photo_album/photo_album_creator_page.dart';

void main() {
  group('PhotoSelectionOrderManager Tests', () {
    late PhotoSelectionOrderManager manager;

    setUp(() {
      manager = PhotoSelectionOrderManager();
    });

    test('初始状态应该为空', () {
      expect(manager.selectedCount, 0);
      expect(manager.selectedIds, isEmpty);
      expect(manager.selectedIdsSet, isEmpty);
    });

    test('选择照片应该按顺序添加', () {
      // 选择第一张照片
      bool result1 = manager.toggleSelection('photo1');
      expect(result1, true); // 返回true表示被选中
      expect(manager.selectedCount, 1);
      expect(manager.getSelectionOrder('photo1'), 1);
      expect(manager.isSelected('photo1'), true);

      // 选择第二张照片
      bool result2 = manager.toggleSelection('photo2');
      expect(result2, true);
      expect(manager.selectedCount, 2);
      expect(manager.getSelectionOrder('photo2'), 2);
      expect(manager.isSelected('photo2'), true);

      // 选择第三张照片
      bool result3 = manager.toggleSelection('photo3');
      expect(result3, true);
      expect(manager.selectedCount, 3);
      expect(manager.getSelectionOrder('photo3'), 3);
      expect(manager.isSelected('photo3'), true);

      // 验证选择顺序
      expect(manager.selectedIds, ['photo1', 'photo2', 'photo3']);
    });

    test('取消选择应该移除照片', () {
      // 先选择三张照片
      manager.toggleSelection('photo1');
      manager.toggleSelection('photo2');
      manager.toggleSelection('photo3');

      // 取消选择第二张照片
      bool result = manager.toggleSelection('photo2');
      expect(result, false); // 返回false表示被取消选择
      expect(manager.selectedCount, 2);
      expect(manager.isSelected('photo2'), false);
      expect(manager.getSelectionOrder('photo2'), null);

      // 验证剩余照片的顺序
      expect(manager.selectedIds, ['photo1', 'photo3']);
      expect(manager.getSelectionOrder('photo1'), 1);
      expect(manager.getSelectionOrder('photo3'), 2);
    });

    test('重新选择已取消的照片应该添加到末尾', () {
      // 选择三张照片
      manager.toggleSelection('photo1');
      manager.toggleSelection('photo2');
      manager.toggleSelection('photo3');

      // 取消选择第二张照片
      manager.toggleSelection('photo2');

      // 重新选择第二张照片
      manager.toggleSelection('photo2');

      // 验证photo2现在在末尾
      expect(manager.selectedIds, ['photo1', 'photo3', 'photo2']);
      expect(manager.getSelectionOrder('photo1'), 1);
      expect(manager.getSelectionOrder('photo3'), 2);
      expect(manager.getSelectionOrder('photo2'), 3);
    });

    test('清空所有选择', () {
      // 选择几张照片
      manager.toggleSelection('photo1');
      manager.toggleSelection('photo2');
      manager.toggleSelection('photo3');

      // 清空所有选择
      manager.clearAll();

      expect(manager.selectedCount, 0);
      expect(manager.selectedIds, isEmpty);
      expect(manager.selectedIdsSet, isEmpty);
      expect(manager.isSelected('photo1'), false);
      expect(manager.getSelectionOrder('photo1'), null);
    });

    test('selectedIdsSet应该与selectedIds保持一致', () {
      manager.toggleSelection('photo1');
      manager.toggleSelection('photo2');
      manager.toggleSelection('photo3');

      Set<String> expectedSet = {'photo1', 'photo2', 'photo3'};
      expect(manager.selectedIdsSet, expectedSet);

      // 取消选择一张照片
      manager.toggleSelection('photo2');
      expectedSet.remove('photo2');
      expect(manager.selectedIdsSet, expectedSet);
    });
  });
}
