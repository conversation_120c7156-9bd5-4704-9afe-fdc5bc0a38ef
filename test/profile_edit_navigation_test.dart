import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/router/app_router.dart';

void main() {
  group('个人资料编辑页面导航测试', () {
    testWidgets('测试个人资料编辑页面隐藏底部导航栏', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(routerConfig: AppRouter.router),
        ),
      );

      // 等待应用启动
      await tester.pumpAndSettle();

      // 导航到个人中心页面
      final router = AppRouter.router;
      router.go('/profile');
      await tester.pumpAndSettle();

      // 验证在个人中心页面时底部导航栏存在
      expect(find.byType(BottomNavigationBar), findsOneWidget);

      // 查找编辑按钮并点击
      final editButton = find.byIcon(Icons.edit_outlined);
      expect(editButton, findsOneWidget);

      await tester.tap(editButton);
      await tester.pumpAndSettle();

      // 验证在个人资料编辑页面时底部导航栏不存在
      expect(find.byType(BottomNavigationBar), findsNothing);

      // 验证有返回按钮
      expect(find.byIcon(Icons.arrow_back_ios), findsOneWidget);

      // 验证页面标题
      expect(find.text('编辑资料'), findsOneWidget);
    });

    testWidgets('测试从编辑页面返回时底部导航栏重新显示', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(routerConfig: AppRouter.router),
        ),
      );

      // 等待应用启动
      await tester.pumpAndSettle();

      // 直接导航到个人资料编辑页面
      final router = AppRouter.router;
      router.push('/profile-edit');
      await tester.pumpAndSettle();

      // 验证在编辑页面时底部导航栏不存在
      expect(find.byType(BottomNavigationBar), findsNothing);

      // 点击返回按钮
      final backButton = find.byIcon(Icons.arrow_back_ios);
      expect(backButton, findsOneWidget);

      await tester.tap(backButton);
      await tester.pumpAndSettle();

      // 验证返回后底部导航栏重新显示
      expect(find.byType(BottomNavigationBar), findsOneWidget);
    });

    testWidgets('测试路由配置正确性', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp.router(routerConfig: AppRouter.router),
        ),
      );

      // 等待应用启动
      await tester.pumpAndSettle();

      // 测试直接导航到编辑页面
      final router = AppRouter.router;
      router.go('/profile-edit');
      await tester.pumpAndSettle();

      // 验证页面正确加载
      expect(find.text('编辑资料'), findsOneWidget);
      expect(find.byType(BottomNavigationBar), findsNothing);

      // 验证有昵称输入框
      expect(find.text('昵称'), findsOneWidget);

      // 验证有个人简介输入框
      expect(find.text('个人简介'), findsOneWidget);
    });
  });
}
