import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/wage_system/store_page.dart';

void main() {
  group('商城新商品测试', () {
    testWidgets('商城页面应该显示新添加的商品', (WidgetTester tester) async {
      // 构建商城页面
      await tester.pumpWidget(
        MaterialApp(
          home: const StorePage(),
          theme: ThemeData(
            useMaterial3: true,
            primaryColor: const Color(0xFF2E7EED),
          ),
        ),
      );

      // 等待页面完全加载
      await tester.pumpAndSettle();

      // 验证页面标题存在
      expect(find.text('道具商城'), findsOneWidget);

      // 验证新的分类标签存在
      expect(find.text('内容服务'), findsOneWidget);
      expect(find.text('AI功能'), findsOneWidget);

      print('✅ 商城页面加载成功，新分类标签显示正常');
    });

    testWidgets('应该能找到优质文章阅读券商品', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const StorePage(),
          theme: ThemeData(
            useMaterial3: true,
            primaryColor: const Color(0xFF2E7EED),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 点击内容服务分类
      await tester.tap(find.text('内容服务'));
      await tester.pumpAndSettle();

      // 验证优质文章阅读券存在
      expect(find.text('优质文章阅读券'), findsOneWidget);

      print('✅ 优质文章阅读券商品显示正常');
    });

    testWidgets('应该能找到AI学习伙伴商品', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const StorePage(),
          theme: ThemeData(
            useMaterial3: true,
            primaryColor: const Color(0xFF2E7EED),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 点击AI功能分类
      await tester.tap(find.text('AI功能'));
      await tester.pumpAndSettle();

      // 验证AI学习伙伴存在
      expect(find.text('AI学习伙伴'), findsOneWidget);

      // 验证AI商品的特殊标签
      expect(find.text('高级'), findsOneWidget);

      print('✅ AI学习伙伴商品显示正常，包含高级标签');
    });

    testWidgets('AI商品应该有特殊的视觉效果', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const StorePage(),
          theme: ThemeData(
            useMaterial3: true,
            primaryColor: const Color(0xFF2E7EED),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 点击AI功能分类
      await tester.tap(find.text('AI功能'));
      await tester.pumpAndSettle();

      // 验证AI商品卡片存在
      final aiItemCard = find.ancestor(
        of: find.text('AI学习伙伴'),
        matching: find.byType(ShopItemCard),
      );
      expect(aiItemCard, findsOneWidget);

      print('✅ AI商品卡片渲染正常');
    });

    testWidgets('商品价格应该正确显示', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const StorePage(),
          theme: ThemeData(
            useMaterial3: true,
            primaryColor: const Color(0xFF2E7EED),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证优质文章阅读券价格
      await tester.tap(find.text('内容服务'));
      await tester.pumpAndSettle();
      expect(find.text('¥75'), findsOneWidget);

      // 验证AI学习伙伴价格
      await tester.tap(find.text('AI功能'));
      await tester.pumpAndSettle();
      expect(find.text('¥350'), findsOneWidget);

      print('✅ 新商品价格显示正确');
    });
  });
}
