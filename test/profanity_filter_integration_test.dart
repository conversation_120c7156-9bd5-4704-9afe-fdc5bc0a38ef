import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/community/article_import_service.dart';
import 'package:oneday/features/community/profanity_filter_service.dart';
import 'package:oneday/features/community/community_storage_service.dart';
import 'package:oneday/features/community/community_feed_page.dart';

void main() {
  // 初始化Flutter绑定
  TestWidgetsFlutterBinding.ensureInitialized();
  group('敏感词过滤集成测试', () {
    late ArticleImportService importService;
    late ProfanityFilterService filterService;
    late CommunityStorageService storageService;

    setUp(() async {
      // 设置测试环境
      SharedPreferences.setMockInitialValues({});

      importService = ArticleImportService();
      filterService = ProfanityFilterService();
      storageService = CommunityStorageService.instance;

      // 初始化服务
      await filterService.initialize();
      await storageService.initialize();
    });

    test('应该正确检测和过滤敏感词', () async {
      const testText = '这是一个包含傻逼的测试文本';

      // 测试过滤服务
      final filterResult = await filterService.filterText(testText);

      expect(filterResult.hasViolations, true);
      expect(filterResult.detectedWords.length, 1);
      expect(filterResult.filteredText, '这是一个包含***的测试文本');
    });

    test('应该在文章处理中正确过滤敏感词', () async {
      const testTitle = '测试标题傻逼';
      const testContent = '这是测试内容操你妈';

      // 测试文章导入服务
      final titleResult = await importService.processArticleContent(testTitle);
      final contentResult = await importService.processArticleContent(
        testContent,
      );

      expect(titleResult.hasProfanityViolations, true);
      expect(titleResult.profanityWordsFiltered, 1);
      expect(titleResult.highlightedText.contains('***'), true);
      expect(titleResult.highlightedText.contains('傻逼'), false);

      expect(contentResult.hasProfanityViolations, true);
      expect(contentResult.profanityWordsFiltered, greaterThan(0));
      expect(contentResult.highlightedText.contains('***'), true);
    });

    test('应该保存过滤后的内容到存储', () async {
      const originalTitle = '测试标题傻逼';
      const originalContent = '这是测试内容操';

      // 模拟发布流程
      final titleResult = await importService.processArticleContent(
        originalTitle,
      );
      final contentResult = await importService.processArticleContent(
        originalContent,
      );

      // 移除HTML标签
      String filteredTitle = _removeHtmlTags(titleResult.highlightedText);
      String filteredContent = _removeHtmlTags(contentResult.highlightedText);

      // 创建帖子
      final testPost = CommunityPost(
        id: 9999,
        author: UserInfo(
          id: 1,
          username: '测试用户',
          avatar: 'test_avatar.jpg',
          isVerified: false,
        ),
        content: '${filteredTitle.trim()}\n\n${filteredContent.trim()}',
        type: PostType.study,
        tags: ['测试'],
        images: [],
        likeCount: 0,
        commentCount: 0,
        shareCount: 0,
        createdAt: DateTime.now(),
        isLiked: false,
      );

      // 保存帖子
      final saveResult = await storageService.addPost(testPost);
      expect(saveResult, true);

      // 验证保存的内容是过滤后的
      final savedPosts = await storageService.getAllPosts();
      final savedPost = savedPosts.firstWhere((p) => p.id == 9999);

      expect(savedPost.content.contains('傻逼'), false);
      expect(savedPost.content.contains('操'), false);
      expect(savedPost.content.contains('***'), true);

      print('✅ 保存的帖子内容: "${savedPost.content}"');
    });

    test('应该正确处理无敏感词的内容', () async {
      const cleanText = '这是一个干净的测试文本';

      final result = await importService.processArticleContent(cleanText);

      expect(result.hasProfanityViolations, false);
      expect(result.profanityWordsFiltered, 0);
      expect(result.highlightedText, cleanText);
    });
  });
}

/// 移除HTML标签的辅助函数
String _removeHtmlTags(String htmlString) {
  if (htmlString.isEmpty) return htmlString;

  final RegExp htmlTagRegExp = RegExp(r'<[^>]*>');
  return htmlString.replaceAll(htmlTagRegExp, '');
}
