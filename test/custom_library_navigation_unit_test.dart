import 'package:flutter_test/flutter_test.dart';

void main() {
  group('自定义动作库导航逻辑测试', () {
    test('编辑完成后应该调用正确的回调函数', () {
      // 模拟导航逻辑
      String? selectedCategory;
      bool sidebarVisible = true;

      // 模拟回调函数
      void onCategorySelected(String category) {
        selectedCategory = category;
        sidebarVisible = false;
      }

      // 模拟编辑完成并返回 true（表示有保存更改）
      final result = true;
      final libraryName = 'qd';

      // 执行导航逻辑（这是我们在 _openCustomLibrary 中添加的逻辑）
      if (result == true) {
        onCategorySelected(libraryName);
      }

      // 验证结果
      expect(selectedCategory, equals('qd'));
      expect(sidebarVisible, isFalse);
    });

    test('编辑取消后不应该改变选中分类', () {
      // 模拟导航逻辑
      String? selectedCategory = '全部';
      bool sidebarVisible = true;

      // 模拟回调函数
      void onCategorySelected(String category) {
        selectedCategory = category;
        sidebarVisible = false;
      }

      // 模拟编辑取消（返回 null 或 false）
      final result = null;
      final libraryName = 'qd';

      // 执行导航逻辑
      if (result == true) {
        onCategorySelected(libraryName);
      }

      // 验证结果 - 选中分类不应该改变
      expect(selectedCategory, equals('全部'));
      expect(sidebarVisible, isTrue);
    });

    test('编辑失败后不应该改变选中分类', () {
      // 模拟导航逻辑
      String? selectedCategory = '健身';
      bool sidebarVisible = true;

      // 模拟回调函数
      void onCategorySelected(String category) {
        selectedCategory = category;
        sidebarVisible = false;
      }

      // 模拟编辑失败（返回 false）
      final result = false;
      final libraryName = 'qd';

      // 执行导航逻辑
      if (result == true) {
        onCategorySelected(libraryName);
      }

      // 验证结果 - 选中分类不应该改变
      expect(selectedCategory, equals('健身'));
      expect(sidebarVisible, isTrue);
    });
  });
}
