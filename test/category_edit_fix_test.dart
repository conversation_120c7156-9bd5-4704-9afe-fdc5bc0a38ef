import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/memory_palace/palace_manager_page.dart';

void main() {
  group('分类编辑修复验证', () {
    late CategoryManager categoryManager;

    setUp(() {
      categoryManager = CategoryManager();
      // 添加测试数据
      categoryManager.addNode('学校');
      categoryManager.addNode('工作');
    });

    testWidgets('CategorySidebar应该能够正常渲染而不出现布局错误', (WidgetTester tester) async {
      // 创建一个简单的测试环境
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Stack(
              children: [
                Container(color: Colors.grey[200]),
                CategorySidebar(
                  isVisible: true,
                  onClose: () {},
                  onNodeSelected: (node) {},
                  onEditingStateChanged: (isEditing) {},
                  categoryManager: categoryManager,
                  onCategoriesChanged: () {},
                ),
              ],
            ),
          ),
        ),
      );

      // 等待动画完成
      await tester.pumpAndSettle();

      // 验证CategorySidebar正常渲染
      expect(find.byType(CategorySidebar), findsOneWidget);

      // 验证分类节点存在
      expect(find.text('学校'), findsOneWidget);
      expect(find.text('工作'), findsOneWidget);

      // 验证没有布局错误（如果有错误，测试会失败）
      expect(tester.takeException(), isNull);
    });

    testWidgets('PopupMenuButton应该有正确的Notion风格', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Stack(
              children: [
                Container(color: Colors.grey[200]),
                CategorySidebar(
                  isVisible: true,
                  onClose: () {},
                  onNodeSelected: (node) {},
                  onEditingStateChanged: (isEditing) {},
                  categoryManager: categoryManager,
                  onCategoriesChanged: () {},
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找PopupMenuButton
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      expect(popupMenuButtons, findsWidgets);

      // 点击第一个PopupMenuButton
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      // 验证菜单项存在
      expect(find.text('编辑'), findsOneWidget);
      expect(find.text('添加子分类'), findsOneWidget);
      expect(find.text('设为公共'), findsOneWidget);
      expect(find.text('设为隐私'), findsOneWidget);
      expect(find.text('删除'), findsOneWidget);

      // 验证图标存在
      expect(find.byIcon(Icons.edit_outlined), findsOneWidget);
      expect(find.byIcon(Icons.create_new_folder_outlined), findsOneWidget);
      expect(find.byIcon(Icons.public_outlined), findsOneWidget);
      expect(find.byIcon(Icons.lock_outline), findsOneWidget);
      expect(find.byIcon(Icons.delete_outline), findsOneWidget);
    });

    testWidgets('编辑功能应该能够正常启动', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Stack(
              children: [
                Container(color: Colors.grey[200]),
                CategorySidebar(
                  isVisible: true,
                  onClose: () {},
                  onNodeSelected: (node) {},
                  onEditingStateChanged: (isEditing) {},
                  categoryManager: categoryManager,
                  onCategoriesChanged: () {},
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找PopupMenuButton
      final popupMenuButtons = find.byType(PopupMenuButton<String>);

      // 点击第一个PopupMenuButton
      await tester.tap(popupMenuButtons.first);
      await tester.pumpAndSettle();

      // 点击编辑选项
      await tester.tap(find.text('编辑'));
      await tester.pumpAndSettle();

      // 验证编辑模式已启动（应该有TextField出现）
      expect(find.byType(TextField), findsOneWidget);

      // 验证没有异常
      expect(tester.takeException(), isNull);
    });
  });
}
