import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/time_box/pages/task_category_management_page.dart';

void main() {
  group('TaskCategoryManagementPage Tests', () {
    setUp(() async {
      // 清理SharedPreferences
      SharedPreferences.setMockInitialValues({});
    });

    testWidgets('应该显示任务分类管理页面', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(home: const TaskCategoryManagementPage()),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 验证页面标题
      expect(find.text('任务分类管理'), findsOneWidget);

      // 验证添加按钮存在
      expect(find.byIcon(Icons.add), findsOneWidget);
    });

    testWidgets('应该显示默认分类列表', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(home: const TaskCategoryManagementPage()),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 验证默认分类是否显示
      expect(find.text('计算机科学'), findsOneWidget);
      expect(find.text('数学'), findsOneWidget);
      expect(find.text('英语'), findsOneWidget);
      expect(find.text('政治'), findsOneWidget);

      // 验证系统分类标签
      expect(find.text('系统分类（可编辑）'), findsWidgets);
    });

    testWidgets('点击添加按钮应该打开添加分类对话框', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(home: const TaskCategoryManagementPage()),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 点击添加按钮
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // 验证对话框是否打开
      expect(find.text('添加分类'), findsOneWidget);
      expect(find.text('分类名称'), findsOneWidget);
      expect(find.text('选择颜色'), findsOneWidget);
      expect(find.text('选择图标'), findsOneWidget);
    });

    testWidgets('应该能够在对话框中输入分类名称', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(home: const TaskCategoryManagementPage()),
      );

      await tester.pumpAndSettle();

      // 打开添加对话框
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // 输入分类名称
      await tester.enterText(find.byType(TextFormField), '测试分类');
      await tester.pumpAndSettle();

      // 验证输入的文本
      expect(find.text('测试分类'), findsOneWidget);
    });

    testWidgets('应该能够选择颜色', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(home: const TaskCategoryManagementPage()),
      );

      await tester.pumpAndSettle();

      // 打开添加对话框
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // 查找颜色选择器中的颜色圆圈
      final colorCircles = find.byType(Container).evaluate().where((element) {
        final widget = element.widget as Container;
        final decoration = widget.decoration as BoxDecoration?;
        return decoration?.shape == BoxShape.circle;
      });

      // 验证至少有一些颜色选项
      expect(colorCircles.length, greaterThan(5));
    });

    testWidgets('应该能够选择图标', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(home: const TaskCategoryManagementPage()),
      );

      await tester.pumpAndSettle();

      // 打开添加对话框
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // 查找图标选择器中的图标
      final iconGridView = find.byType(GridView);
      expect(iconGridView, findsOneWidget);

      // 验证图标网格中有图标
      final icons = find.descendant(
        of: iconGridView,
        matching: find.byType(Icon),
      );
      expect(icons, findsWidgets);
    });

    testWidgets('自定义分类应该显示编辑菜单', (WidgetTester tester) async {
      // 预设一个自定义分类
      SharedPreferences.setMockInitialValues({
        'task_categories_v1': '''[
          {
            "id": "custom_1",
            "name": "自定义分类",
            "colorValue": 4294901760,
            "iconName": "work",
            "isDefault": false,
            "createdAt": "2024-01-01T00:00:00.000Z",
            "lastModified": "2024-01-01T00:00:00.000Z"
          }
        ]''',
      });

      await tester.pumpWidget(
        MaterialApp(home: const TaskCategoryManagementPage()),
      );

      await tester.pumpAndSettle();

      // 查找自定义分类
      expect(find.text('自定义分类'), findsOneWidget);
      expect(find.text('自定义分类'), findsOneWidget);

      // 查找三点菜单按钮
      final popupMenuButtons = find.byType(PopupMenuButton<String>);
      expect(popupMenuButtons, findsWidgets);
    });

    testWidgets('默认分类不应该显示编辑菜单', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(home: const TaskCategoryManagementPage()),
      );

      await tester.pumpAndSettle();

      // 查找系统分类的卡片
      final systemCategoryCards = find.text('系统分类（可编辑）');
      expect(systemCategoryCards, findsWidgets);

      // 默认分类的数量应该是4个，现在所有分类都有PopupMenuButton
      final allCards = find.byType(Card);
      final popupMenuButtons = find.byType(PopupMenuButton<String>);

      // 所有分类都应该有PopupMenuButton
      expect(allCards.evaluate().length, greaterThanOrEqualTo(4));
      expect(
        popupMenuButtons.evaluate().length,
        equals(allCards.evaluate().length),
      );
    });
  });
}
