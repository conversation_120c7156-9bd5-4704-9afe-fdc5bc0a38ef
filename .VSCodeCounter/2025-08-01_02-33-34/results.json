{"file:///Users/<USER>/development/flutter_apps/oneday/assets/data/Action.html": {"language": "HTML", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/data/suffixes.json": {"language": "JSON", "code": 99, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/data/profanity_words.json": {"language": "JSON", "code": 259, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/data/prefixes.json": {"language": "JSON", "code": 88, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/icons/README.md": {"language": "<PERSON><PERSON>", "code": 33, "comment": 0, "blank": 13}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/data/word_roots.json": {"language": "JSON", "code": 158, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/PROBLEM_SOLUTIONS.md": {"language": "<PERSON><PERSON>", "code": 476, "comment": 0, "blank": 131}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/data/%E5%8A%A8%E4%BD%9C.html": {"language": "HTML", "code": 693, "comment": 23, "blank": 53}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/flutter/CMakeLists.txt": {"language": "CMake", "code": 98, "comment": 0, "blank": 12}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugins.cmake": {"language": "CMake", "code": 22, "comment": 0, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugin_registrant.h": {"language": "C++", "code": 5, "comment": 5, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugin_registrant.cc": {"language": "C++", "code": 15, "comment": 4, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/oneday/pubspec.yaml": {"language": "YAML", "code": 73, "comment": 58, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/FEATURES.md": {"language": "<PERSON><PERSON>", "code": 395, "comment": 0, "blank": 105}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/CMakeLists.txt": {"language": "CMake", "code": 89, "comment": 0, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/analysis_options.yaml": {"language": "YAML", "code": 4, "comment": 21, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/scripts/transform_vocabulary.py": {"language": "Python", "code": 152, "comment": 26, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/scripts/clear_wage_statistics.dart": {"language": "Dart", "code": 25, "comment": 4, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/router/app_router.dart": {"language": "Dart", "code": 392, "comment": 40, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/scripts/verify_navigation_fix.dart": {"language": "Dart", "code": 174, "comment": 20, "blank": 44}, "file:///Users/<USER>/development/flutter_apps/oneday/README.md": {"language": "<PERSON><PERSON>", "code": 1182, "comment": 0, "blank": 261}, "file:///Users/<USER>/development/flutter_apps/oneday/android/gradle.properties": {"language": "Java Properties", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/android/gradle/wrapper/gradle-wrapper.properties": {"language": "Java Properties", "code": 5, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/flutter_window.h": {"language": "C++", "code": 20, "comment": 5, "blank": 9}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/resource.h": {"language": "C++", "code": 9, "comment": 6, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/scripts/verify_radar_fix.dart": {"language": "Dart", "code": 111, "comment": 8, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/OneDayWidget/OneDayWidgetBundle.swift": {"language": "Swift", "code": 8, "comment": 6, "blank": 3}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/win32_window.cpp": {"language": "C++", "code": 210, "comment": 24, "blank": 55}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/main.cpp": {"language": "C++", "code": 30, "comment": 4, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/OneDayWidget/AppIntent.swift": {"language": "Swift", "code": 8, "comment": 7, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/win32_window.h": {"language": "C++", "code": 48, "comment": 31, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/utils.h": {"language": "C++", "code": 8, "comment": 6, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/scripts/clean_category_data.dart": {"language": "Dart", "code": 35, "comment": 8, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Podfile": {"language": "<PERSON>", "code": 32, "comment": 2, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugin_registrant.h": {"language": "C++", "code": 5, "comment": 5, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/utils.cpp": {"language": "C++", "code": 54, "comment": 2, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugins.cmake": {"language": "CMake", "code": 20, "comment": 0, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/flutter/CMakeLists.txt": {"language": "CMake", "code": 79, "comment": 0, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugin_registrant.cc": {"language": "C++", "code": 11, "comment": 4, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/runner/my_application.cc": {"language": "C++", "code": 83, "comment": 21, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/runner/CMakeLists.txt": {"language": "CMake", "code": 21, "comment": 0, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/flutter_window.cpp": {"language": "C++", "code": 49, "comment": 7, "blank": 16}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/runner/main.cc": {"language": "C++", "code": 5, "comment": 0, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/CMakeLists.txt": {"language": "CMake", "code": 34, "comment": 0, "blank": 7}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/runner/my_application.h": {"language": "C++", "code": 7, "comment": 7, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Base.lproj/Main.storyboard": {"language": "XML", "code": 25, "comment": 1, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Base.lproj/LaunchScreen.storyboard": {"language": "XML", "code": 36, "comment": 1, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/OneDayWidget/OneDayWidgetControl.swift": {"language": "Swift", "code": 56, "comment": 7, "blank": 15}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Runner-Bridging-Header.h": {"language": "C++", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json": {"language": "JSON", "code": 23, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/AppDelegate.swift": {"language": "Swift", "code": 12, "comment": 0, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md": {"language": "<PERSON><PERSON>", "code": 3, "comment": 0, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/CMakeLists.txt": {"language": "CMake", "code": 104, "comment": 0, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/RunnerTests/RunnerTests.swift": {"language": "Swift", "code": 7, "comment": 2, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/debug/radar_test_page.dart": {"language": "Dart", "code": 69, "comment": 2, "blank": 7}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/debug/route_debug_page.dart": {"language": "Dart", "code": 268, "comment": 10, "blank": 15}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/debug/image_picker_debug_page.dart": {"language": "Dart", "code": 264, "comment": 14, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/debug/profanity_filter_manual_test_page.dart": {"language": "Dart", "code": 291, "comment": 10, "blank": 31}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/debug/profanity_filter_test_page.dart": {"language": "Dart", "code": 189, "comment": 2, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/debug/profile_save_test_page.dart": {"language": "Dart", "code": 276, "comment": 20, "blank": 34}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/main.dart": {"language": "Dart", "code": 254, "comment": 25, "blank": 13}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/profile/AndroidManifest.xml": {"language": "XML", "code": 3, "comment": 4, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/debug/AndroidManifest.xml": {"language": "XML", "code": 3, "comment": 4, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/debug/image_cropper_test_page.dart": {"language": "Dart", "code": 228, "comment": 11, "blank": 22}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/utils/category_cleanup_helper.dart": {"language": "Dart", "code": 96, "comment": 10, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/system_overlay_permission.dart": {"language": "Dart", "code": 256, "comment": 26, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/widgets/oneday_logo.dart": {"language": "Dart", "code": 204, "comment": 17, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/services/enhanced_share_service.dart": {"language": "Dart", "code": 160, "comment": 27, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/core/data/pao_exercises_data.dart": {"language": "Dart", "code": 1587, "comment": 28, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/providers/navigation_provider.dart": {"language": "Dart", "code": 51, "comment": 6, "blank": 11}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/locale_service.dart": {"language": "Dart", "code": 84, "comment": 23, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/silent_refresh_utils.dart": {"language": "Dart", "code": 104, "comment": 35, "blank": 21}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/ui_utils.dart": {"language": "Dart", "code": 77, "comment": 19, "blank": 9}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/image_watermark_utils.dart": {"language": "Dart", "code": 285, "comment": 62, "blank": 59}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/simple_image_test.dart": {"language": "Dart", "code": 82, "comment": 14, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/widgets/icons/wechat_icon.dart": {"language": "Dart", "code": 62, "comment": 8, "blank": 14}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/image_compression_utils.dart": {"language": "Dart", "code": 213, "comment": 41, "blank": 47}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_model.dart": {"language": "Dart", "code": 590, "comment": 21, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/onboarding_page.dart": {"language": "Dart", "code": 770, "comment": 59, "blank": 76}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/notion_style_onboarding_page.dart": {"language": "Dart", "code": 246, "comment": 18, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_category_page.dart": {"language": "Dart", "code": 1358, "comment": 71, "blank": 97}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_statistics_page.dart": {"language": "Dart", "code": 845, "comment": 29, "blank": 83}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_manager_page.dart": {"language": "Dart", "code": 1002, "comment": 42, "blank": 75}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_root_service.dart": {"language": "Dart", "code": 188, "comment": 28, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_meaning_service.dart": {"language": "Dart", "code": 161, "comment": 17, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/custom_vocabulary_manager_page.dart": {"language": "Dart", "code": 518, "comment": 30, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/main/main_container_page.dart": {"language": "Dart", "code": 105, "comment": 6, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/community/profanity_filter_settings_page.dart": {"language": "Dart", "code": 722, "comment": 16, "blank": 30}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_feed_page.dart": {"language": "Dart", "code": 1291, "comment": 73, "blank": 89}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/community/content_report_service.dart": {"language": "Dart", "code": 347, "comment": 33, "blank": 57}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/community/article_import_service.dart": {"language": "Dart", "code": 244, "comment": 29, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_storage_service.dart": {"language": "Dart", "code": 121, "comment": 11, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_post_editor_page.dart": {"language": "Dart", "code": 905, "comment": 52, "blank": 79}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/community/profanity_filter_service.dart": {"language": "Dart", "code": 368, "comment": 34, "blank": 60}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/fsrs_service.dart": {"language": "Dart", "code": 316, "comment": 115, "blank": 70}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/community/content_report_dialog.dart": {"language": "Dart", "code": 364, "comment": 7, "blank": 18}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/services/radar_share_service.dart": {"language": "Dart", "code": 344, "comment": 59, "blank": 71}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/palace_manager_page.dart": {"language": "Dart", "code": 5078, "comment": 381, "blank": 452}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/widgets/radar_share_panel.dart": {"language": "Dart", "code": 259, "comment": 26, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json": {"language": "JSON", "code": 116, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/AndroidManifest.xml": {"language": "XML", "code": 46, "comment": 14, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/widgets/ability_radar_chart.dart": {"language": "Dart", "code": 337, "comment": 31, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/services/ability_radar_service.dart": {"language": "Dart", "code": 461, "comment": 45, "blank": 106}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/widgets/game_style_rank_widget.dart": {"language": "Dart", "code": 242, "comment": 20, "blank": 31}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/widgets/radar_community_share_dialog.dart": {"language": "Dart", "code": 411, "comment": 24, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/build.yaml": {"language": "YAML", "code": 11, "comment": 6, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/floating_timer_service.dart": {"language": "Dart", "code": 487, "comment": 77, "blank": 79}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page_copy.dart": {"language": "Dart", "code": 1596, "comment": 167, "blank": 199}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/learning_report_page.dart": {"language": "Dart", "code": 795, "comment": 40, "blank": 58}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/reflection/reflection_log_page.dart": {"language": "Dart", "code": 1327, "comment": 66, "blank": 136}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/utils/image_coordinate_system.dart": {"language": "Dart", "code": 195, "comment": 30, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/providers/memory_palace_provider.dart": {"language": "Dart", "code": 191, "comment": 18, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/utils/anchor_data_migration.dart": {"language": "Dart", "code": 214, "comment": 30, "blank": 51}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/fsrs_algorithm.dart": {"language": "Dart", "code": 223, "comment": 44, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_service.dart": {"language": "Dart", "code": 636, "comment": 83, "blank": 129}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_page.dart": {"language": "Dart", "code": 188, "comment": 2, "blank": 13}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/create_vocabulary_page.dart": {"language": "Dart", "code": 452, "comment": 11, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_learning_service.dart": {"language": "Dart", "code": 232, "comment": 28, "blank": 47}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/graduate_vocabulary_manager_page.dart": {"language": "Dart", "code": 598, "comment": 37, "blank": 49}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_cache_manager.dart": {"language": "Dart", "code": 248, "comment": 33, "blank": 64}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/drawable-v21/launch_background.xml": {"language": "XML", "code": 4, "comment": 7, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/models/ability_radar_models.dart": {"language": "Dart", "code": 222, "comment": 44, "blank": 65}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/pages/ability_radar_page.dart": {"language": "Dart", "code": 635, "comment": 52, "blank": 58}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/core/constants/app_icons.dart": {"language": "Dart", "code": 35, "comment": 5, "blank": 3}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/providers/learning_report_providers.dart": {"language": "Dart", "code": 148, "comment": 32, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/providers/vocabulary_providers.dart": {"language": "Dart", "code": 263, "comment": 27, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/photo_album/photo_album_creator_page.dart": {"language": "Dart", "code": 792, "comment": 48, "blank": 78}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/models/ability_radar_models.g.dart": {"language": "Dart", "code": 199, "comment": 32, "blank": 18}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/services/learning_report_export_service.dart": {"language": "Dart", "code": 556, "comment": 116, "blank": 57}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/profile/profile_page.dart": {"language": "Dart", "code": 825, "comment": 44, "blank": 70}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/models/learning_report_models.g.dart": {"language": "Dart", "code": 301, "comment": 51, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/models/learning_report_models.dart": {"language": "Dart", "code": 203, "comment": 61, "blank": 77}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/services/learning_report_service.dart": {"language": "Dart", "code": 474, "comment": 61, "blank": 100}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/auth/forgot_password_page.dart": {"language": "Dart", "code": 266, "comment": 17, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/auth/login_page.dart": {"language": "Dart", "code": 710, "comment": 42, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/auth/phone_login_page.dart": {"language": "Dart", "code": 394, "comment": 20, "blank": 30}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/widgets/chart_widgets.dart": {"language": "Dart", "code": 858, "comment": 36, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/profile/providers/user_profile_provider.dart": {"language": "Dart", "code": 189, "comment": 20, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/auth/reset_password_page.dart": {"language": "Dart", "code": 316, "comment": 18, "blank": 22}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/profile/services/user_profile_service.dart": {"language": "Dart", "code": 183, "comment": 23, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/test_data_sync_page.dart": {"language": "Dart", "code": 253, "comment": 11, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/services/study_time_statistics_service.dart": {"language": "Dart", "code": 348, "comment": 65, "blank": 86}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/auth/register_page.dart": {"language": "Dart", "code": 481, "comment": 28, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/auth/services/sms_service.dart": {"language": "Dart", "code": 174, "comment": 31, "blank": 46}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/timebox_list_page.dart": {"language": "Dart", "code": 3495, "comment": 261, "blank": 245}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/auth/services/wechat_auth_service.dart": {"language": "Dart", "code": 85, "comment": 120, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/profile/pages/profile_edit_page.dart": {"language": "Dart", "code": 851, "comment": 45, "blank": 74}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/providers/ability_radar_mock_providers.dart": {"language": "Dart", "code": 140, "comment": 5, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/providers/ability_radar_providers.dart": {"language": "Dart", "code": 303, "comment": 21, "blank": 57}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/profile/models/user_profile.dart": {"language": "Dart", "code": 84, "comment": 14, "blank": 21}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/pages/task_category_management_page.dart": {"language": "Dart", "code": 489, "comment": 14, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/focused_training_page.dart": {"language": "Dart", "code": 461, "comment": 18, "blank": 41}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/profile/models/user_profile.g.dart": {"language": "Dart", "code": 31, "comment": 11, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_action_editor_dialog.dart": {"language": "Dart", "code": 535, "comment": 19, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/manage_custom_libraries_dialog.dart": {"language": "Dart", "code": 2005, "comment": 103, "blank": 122}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_exercise_category.dart": {"language": "Dart", "code": 299, "comment": 37, "blank": 50}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/create_custom_library_dialog.dart": {"language": "Dart", "code": 297, "comment": 7, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/pao_integration_service.dart": {"language": "Dart", "code": 206, "comment": 32, "blank": 45}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/action_library_category_manager.dart": {"language": "Dart", "code": 312, "comment": 24, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_action_library.dart": {"language": "Dart", "code": 207, "comment": 25, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_session_page.dart": {"language": "Dart", "code": 998, "comment": 71, "blank": 83}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_library_page.dart": {"language": "Dart", "code": 4769, "comment": 304, "blank": 311}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/create_action_library_dialog.dart": {"language": "Dart", "code": 345, "comment": 7, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_library_editor_page.dart": {"language": "Dart", "code": 714, "comment": 42, "blank": 54}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/models/timebox_models.dart": {"language": "Dart", "code": 355, "comment": 58, "blank": 72}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_action_library_service.dart": {"language": "Dart", "code": 258, "comment": 33, "blank": 49}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/models/timebox_models.g.dart": {"language": "Dart", "code": 71, "comment": 15, "blank": 8}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/add_to_memory_dialog.dart": {"language": "Dart", "code": 182, "comment": 10, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/providers/timebox_provider.dart": {"language": "Dart", "code": 261, "comment": 27, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page.dart": {"language": "Dart", "code": 3901, "comment": 620, "blank": 643}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/auth/providers/phone_login_provider.dart": {"language": "Dart", "code": 228, "comment": 25, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/providers/exercise_providers.dart": {"language": "Dart", "code": 322, "comment": 32, "blank": 45}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/providers/study_time_providers.dart": {"language": "Dart", "code": 310, "comment": 26, "blank": 51}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/models/study_time_models.dart": {"language": "Dart", "code": 264, "comment": 58, "blank": 68}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/auth/providers/wechat_login_provider.dart": {"language": "Dart", "code": 137, "comment": 13, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/notifiers/daily_plan_notifier.dart": {"language": "Dart", "code": 204, "comment": 31, "blank": 44}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/models/daily_plan.g.dart": {"language": "Dart", "code": 49, "comment": 13, "blank": 7}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/managers/task_category_manager.dart": {"language": "Dart", "code": 311, "comment": 47, "blank": 54}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/models/study_time_models.g.dart": {"language": "Dart", "code": 175, "comment": 34, "blank": 12}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/models/daily_plan.dart": {"language": "Dart", "code": 151, "comment": 29, "blank": 34}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/widgets/achievement_grid.dart": {"language": "Dart", "code": 380, "comment": 19, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/widgets/achievement_unlock_notification.dart": {"language": "Dart", "code": 330, "comment": 21, "blank": 47}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/widgets/user_level_card.dart": {"language": "Dart", "code": 326, "comment": 7, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/widgets/skill_levels_card.dart": {"language": "Dart", "code": 323, "comment": 15, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/pages/achievement_page.dart": {"language": "Dart", "code": 485, "comment": 21, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/data/achievements_data.dart": {"language": "Dart", "code": 444, "comment": 18, "blank": 12}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/pages/leaderboard_page.dart": {"language": "Dart", "code": 336, "comment": 13, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/widgets/study_session_completion_dialog.dart": {"language": "Dart", "code": 405, "comment": 6, "blank": 21}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/services/achievement_trigger_service.dart": {"language": "Dart", "code": 113, "comment": 61, "blank": 63}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/services/daily_plan_storage_service.dart": {"language": "Dart", "code": 216, "comment": 33, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/README.md": {"language": "<PERSON><PERSON>", "code": 132, "comment": 0, "blank": 41}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/services/achievement_service.dart": {"language": "Dart", "code": 332, "comment": 38, "blank": 66}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/widget/models/widget_models.dart": {"language": "Dart", "code": 152, "comment": 15, "blank": 22}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/widget/utils/error_handler.dart": {"language": "Dart", "code": 360, "comment": 15, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/achievement.dart": {"language": "Dart", "code": 169, "comment": 29, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/user_level.g.dart": {"language": "Dart", "code": 91, "comment": 21, "blank": 11}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/achievement.g.dart": {"language": "Dart", "code": 125, "comment": 24, "blank": 11}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/badge.g.dart": {"language": "Dart", "code": 184, "comment": 40, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/user_level.dart": {"language": "Dart", "code": 266, "comment": 41, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/providers/achievement_provider.dart": {"language": "Dart", "code": 224, "comment": 38, "blank": 44}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/badge.dart": {"language": "Dart", "code": 230, "comment": 50, "blank": 69}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/help_feedback/user_guide_page.dart": {"language": "Dart", "code": 352, "comment": 11, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/values/styles.xml": {"language": "XML", "code": 9, "comment": 9, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/help_feedback/help_feedback_page.dart": {"language": "Dart", "code": 708, "comment": 39, "blank": 53}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/splash/splash_page.dart": {"language": "Dart", "code": 177, "comment": 12, "blank": 23}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/settings/settings_page.dart": {"language": "Dart", "code": 776, "comment": 56, "blank": 68}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/wage_wallet_page.dart": {"language": "Dart", "code": 780, "comment": 34, "blank": 52}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/store_page.dart": {"language": "Dart", "code": 1930, "comment": 97, "blank": 121}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/home/<USER>": {"language": "Dart", "code": 1237, "comment": 97, "blank": 81}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/app_initialization_service.dart": {"language": "Dart", "code": 58, "comment": 17, "blank": 19}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/global_timer_service.dart": {"language": "Dart", "code": 366, "comment": 89, "blank": 99}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/first_time_service.dart": {"language": "Dart", "code": 44, "comment": 16, "blank": 13}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/study_session_completion_service.dart": {"language": "Dart", "code": 185, "comment": 37, "blank": 45}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/providers/item_effect_provider.dart": {"language": "Dart", "code": 42, "comment": 9, "blank": 11}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/drawable/launch_background.xml": {"language": "XML", "code": 4, "comment": 7, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/widgets/active_effects_widget.dart": {"language": "Dart", "code": 303, "comment": 2, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/services/item_effect_service.dart": {"language": "Dart", "code": 232, "comment": 42, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/models/item_effect_models.dart": {"language": "Dart", "code": 220, "comment": 22, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/models/wage_transaction.dart": {"language": "Dart", "code": 125, "comment": 5, "blank": 13}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/services/wage_service.dart": {"language": "Dart", "code": 207, "comment": 18, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/services/item_timebox_integration_service.dart": {"language": "Dart", "code": 176, "comment": 47, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/providers/study_session_completion_provider.dart": {"language": "Dart", "code": 19, "comment": 2, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/config/image_compression_config.dart": {"language": "Dart", "code": 67, "comment": 20, "blank": 18}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/services/premium_article_service.dart": {"language": "Dart", "code": 259, "comment": 35, "blank": 51}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/l10n/app_localizations_en.dart": {"language": "Dart", "code": 175, "comment": 3, "blank": 77}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/l10n/app_localizations_zh.dart": {"language": "Dart", "code": 165, "comment": 3, "blank": 77}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/l10n/app_localizations.dart": {"language": "Dart", "code": 129, "comment": 360, "blank": 90}, "file:///Users/<USER>/development/flutter_apps/oneday/devtools_options.yaml": {"language": "YAML", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/ARCHITECTURE.md": {"language": "<PERSON><PERSON>", "code": 214, "comment": 0, "blank": 55}, "file:///Users/<USER>/development/flutter_apps/oneday/.cursorrules.md": {"language": "<PERSON><PERSON>", "code": 436, "comment": 0, "blank": 123}, "file:///Users/<USER>/development/flutter_apps/oneday/l10n.yaml": {"language": "YAML", "code": 4, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/test_apps/category_edit_demo.dart": {"language": "Dart", "code": 276, "comment": 7, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/example/study_session_completion_demo.dart": {"language": "Dart", "code": 429, "comment": 7, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/test_apps/task_sync_verification.dart": {"language": "Dart", "code": 320, "comment": 9, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/example/achievement_wage_example.dart": {"language": "Dart", "code": 106, "comment": 19, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/REMOVE_DUPLICATE_MENU_ITEMS.md": {"language": "<PERSON><PERSON>", "code": 111, "comment": 0, "blank": 36}, "file:///Users/<USER>/development/flutter_apps/oneday/example/timebox_rest_skip_demo.dart": {"language": "Dart", "code": 458, "comment": 24, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/CATEGORY_SELECTOR_PURE_TEXT_FIX.md": {"language": "<PERSON><PERSON>", "code": 118, "comment": 0, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/example/calendar_responsive_demo.dart": {"language": "Dart", "code": 342, "comment": 16, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/UI_IMPROVEMENTS.md": {"language": "<PERSON><PERSON>", "code": 116, "comment": 0, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/widget/services/widget_service.dart": {"language": "Dart", "code": 126, "comment": 88, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/widget/services/memory_palace_integration_service.dart": {"language": "Dart", "code": 350, "comment": 38, "blank": 47}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/IPAD_CALENDAR_RESPONSIVE_FIX.md": {"language": "<PERSON><PERSON>", "code": 178, "comment": 0, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/IMAGE_COMPRESSION_IMPROVEMENTS.md": {"language": "<PERSON><PERSON>", "code": 103, "comment": 0, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/.augment/rules/rules.md": {"language": "<PERSON><PERSON>", "code": 387, "comment": 0, "blank": 114}, "file:///Users/<USER>/development/flutter_apps/oneday/example/calendar_task_display_demo.dart": {"language": "Dart", "code": 396, "comment": 20, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/test/custom_action_editor_test.dart": {"language": "Dart", "code": 66, "comment": 14, "blank": 14}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_position_fix_verification.dart": {"language": "Dart", "code": 38, "comment": 4, "blank": 11}, "file:///Users/<USER>/development/flutter_apps/oneday/test/category_edit_state_test.dart": {"language": "Dart", "code": 151, "comment": 40, "blank": 55}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_complete_image_compression.dart": {"language": "Dart", "code": 186, "comment": 9, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/calendar/calendar_page.dart": {"language": "Dart", "code": 1094, "comment": 89, "blank": 77}, "file:///Users/<USER>/development/flutter_apps/oneday/test/developer_tools_test.dart": {"language": "Dart", "code": 132, "comment": 12, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/web/manifest.json": {"language": "JSON", "code": 35, "comment": 0, "blank": 0}, "file:///Users/<USER>/development/flutter_apps/oneday/test/custom_library_navigation_test.dart": {"language": "Dart", "code": 119, "comment": 22, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/test/add_child_category_test.dart": {"language": "Dart", "code": 146, "comment": 30, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Podfile": {"language": "<PERSON>", "code": 32, "comment": 1, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/test/achievement_unlock_navigation_test.dart": {"language": "Dart", "code": 79, "comment": 12, "blank": 13}, "file:///Users/<USER>/development/flutter_apps/oneday/example/color_scheme_comparison_demo.dart": {"language": "Dart", "code": 434, "comment": 16, "blank": 31}, "file:///Users/<USER>/development/flutter_apps/oneday/test/memory_palace_card_test.dart": {"language": "Dart", "code": 200, "comment": 13, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/test/category_sidebar_edit_test.dart": {"language": "Dart", "code": 177, "comment": 45, "blank": 60}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_coordinate_transformation_fix.dart": {"language": "Dart", "code": 111, "comment": 16, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/test/timebox_consistency_test.dart": {"language": "Dart", "code": 137, "comment": 26, "blank": 46}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_bubble_center_alignment.dart": {"language": "Dart", "code": 93, "comment": 8, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/IPAD_CALENDAR_OVERFLOW_FIX_COMPLETE.md": {"language": "<PERSON><PERSON>", "code": 129, "comment": 0, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/test/photo_album_dialog_test.dart": {"language": "Dart", "code": 96, "comment": 21, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/test/action_library_category_test.dart": {"language": "Dart", "code": 36, "comment": 2, "blank": 9}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/values-night/styles.xml": {"language": "XML", "code": 9, "comment": 9, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/test/store_activation_test.dart": {"language": "Dart", "code": 95, "comment": 14, "blank": 23}, "file:///Users/<USER>/development/flutter_apps/oneday/web/index.html": {"language": "HTML", "code": 19, "comment": 15, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/oneday/example/navigation_bottom_bar_demo.dart": {"language": "Dart", "code": 366, "comment": 9, "blank": 15}, "file:///Users/<USER>/development/flutter_apps/oneday/test_apps/premium_article_access_test.dart": {"language": "Dart", "code": 107, "comment": 22, "blank": 31}, "file:///Users/<USER>/development/flutter_apps/oneday/test/exercise_library_ui_test.dart": {"language": "Dart", "code": 156, "comment": 31, "blank": 55}, "file:///Users/<USER>/development/flutter_apps/oneday/test_apps/simple_dialog_test.dart": {"language": "Dart", "code": 215, "comment": 3, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/test/coordinate_system_logic_test.dart": {"language": "Dart", "code": 174, "comment": 32, "blank": 46}, "file:///Users/<USER>/development/flutter_apps/oneday/test_apps/category_management_demo.dart": {"language": "Dart", "code": 100, "comment": 1, "blank": 7}, "file:///Users/<USER>/development/flutter_apps/oneday/test_apps/community_share_test.dart": {"language": "Dart", "code": 317, "comment": 19, "blank": 41}, "file:///Users/<USER>/development/flutter_apps/oneday/example/responsive_layout_demo.dart": {"language": "Dart", "code": 560, "comment": 13, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/test_apps/snackbar_duration_test.dart": {"language": "Dart", "code": 172, "comment": 6, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/test_apps/category_cleanup_demo.dart": {"language": "Dart", "code": 253, "comment": 8, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/test_apps/task_sync_demo.dart": {"language": "Dart", "code": 256, "comment": 4, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/test_apps/wage_calculation_test.dart": {"language": "Dart", "code": 228, "comment": 9, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/test_apps/simple_cleanup_test.dart": {"language": "Dart", "code": 211, "comment": 4, "blank": 23}, "file:///Users/<USER>/development/flutter_apps/oneday/test_apps/pdf_color_demo.dart": {"language": "Dart", "code": 256, "comment": 14, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/test_apps/task_count_verification.dart": {"language": "Dart", "code": 330, "comment": 13, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/test_apps/system_category_share_demo.dart": {"language": "Dart", "code": 296, "comment": 12, "blank": 23}, "file:///Users/<USER>/development/flutter_apps/oneday/test_apps/category_removal_demo.dart": {"language": "Dart", "code": 296, "comment": 8, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_image_compression_implementation.dart": {"language": "Dart", "code": 147, "comment": 7, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_matrix_fix_verification.dart": {"language": "Dart", "code": 105, "comment": 14, "blank": 31}, "file:///Users/<USER>/development/flutter_apps/oneday/test/category_sidebar_test.dart": {"language": "Dart", "code": 118, "comment": 20, "blank": 34}, "file:///Users/<USER>/development/flutter_apps/oneday/test/timebox_rest_skip_test.dart": {"language": "Dart", "code": 192, "comment": 20, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/test/photo_album_dialog_layout_test.dart": {"language": "Dart", "code": 75, "comment": 24, "blank": 31}, "file:///Users/<USER>/development/flutter_apps/oneday/test/achievement_wage_integration_test.dart": {"language": "Dart", "code": 78, "comment": 15, "blank": 21}, "file:///Users/<USER>/development/flutter_apps/oneday/test/widget_test.dart": {"language": "Dart", "code": 11, "comment": 9, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/test/reflection_calendar_responsive_test.dart": {"language": "Dart", "code": 140, "comment": 30, "blank": 49}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_matrix_analysis.dart": {"language": "Dart", "code": 130, "comment": 8, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/test/three_dot_menu_test.dart": {"language": "Dart", "code": 108, "comment": 24, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/test/task_category_management_page_test.dart": {"language": "Dart", "code": 120, "comment": 25, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/test/category_removal_test.dart": {"language": "Dart", "code": 151, "comment": 27, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_coordinate_analysis.dart": {"language": "Dart", "code": 104, "comment": 9, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/test/vocabulary_scrollbar_test.dart": {"language": "Dart", "code": 98, "comment": 8, "blank": 22}, "file:///Users/<USER>/development/flutter_apps/oneday/test/category_popup_menu_test.dart": {"language": "Dart", "code": 157, "comment": 38, "blank": 52}, "file:///Users/<USER>/development/flutter_apps/oneday/test/context_aware_album_creation_test.dart": {"language": "Dart", "code": 97, "comment": 6, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/test/custom_library_section_header_test.dart": {"language": "Dart", "code": 92, "comment": 25, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/test/profile_avatar_click_test.dart": {"language": "Dart", "code": 137, "comment": 16, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/test/calendar_task_display_test.dart": {"language": "Dart", "code": 260, "comment": 19, "blank": 36}, "file:///Users/<USER>/development/flutter_apps/oneday/test/final_category_edit_test.dart": {"language": "Dart", "code": 106, "comment": 36, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/test/premium_article_service_test.dart": {"language": "Dart", "code": 114, "comment": 10, "blank": 30}, "file:///Users/<USER>/development/flutter_apps/oneday/test/providers_integration_test.dart": {"language": "Dart", "code": 83, "comment": 9, "blank": 18}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_coordinate_debug.dart": {"language": "Dart", "code": 84, "comment": 5, "blank": 18}, "file:///Users/<USER>/development/flutter_apps/oneday/test/vocabulary_test.dart": {"language": "Dart", "code": 211, "comment": 9, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/test/responsive_layout_test.dart": {"language": "Dart", "code": 230, "comment": 12, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/test/pdf_color_export_test.dart": {"language": "Dart", "code": 87, "comment": 12, "blank": 14}, "file:///Users/<USER>/development/flutter_apps/oneday/test/ui_overflow_test.dart": {"language": "Dart", "code": 101, "comment": 20, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/test/graduate_vocabulary_manager_test.dart": {"language": "Dart", "code": 58, "comment": 6, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/test/custom_library_navigation_unit_test.dart": {"language": "Dart", "code": 50, "comment": 15, "blank": 16}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_bubble_alignment_verification.dart": {"language": "Dart", "code": 104, "comment": 10, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/test/learning_efficiency_metrics_test.dart": {"language": "Dart", "code": 145, "comment": 10, "blank": 13}, "file:///Users/<USER>/development/flutter_apps/oneday/test/integration_test.dart": {"language": "Dart", "code": 89, "comment": 27, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/test/community_storage_test.dart": {"language": "Dart", "code": 105, "comment": 13, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/test/bubble_offset_verification.dart": {"language": "Dart", "code": 74, "comment": 11, "blank": 23}, "file:///Users/<USER>/development/flutter_apps/oneday/test/manage_custom_libraries_dialog_test.dart": {"language": "Dart", "code": 65, "comment": 9, "blank": 18}, "file:///Users/<USER>/development/flutter_apps/oneday/test/memory_palace_persistence_test.dart": {"language": "Dart", "code": 166, "comment": 14, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_bubble_alignment_adjustment.dart": {"language": "Dart", "code": 104, "comment": 9, "blank": 23}, "file:///Users/<USER>/development/flutter_apps/oneday/test/word_meaning_service_test.dart": {"language": "Dart", "code": 61, "comment": 2, "blank": 14}, "file:///Users/<USER>/development/flutter_apps/oneday/test/task_category_test.dart": {"language": "Dart", "code": 201, "comment": 17, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/test/task_sync_test.dart": {"language": "Dart", "code": 164, "comment": 27, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/community/USER_COMMUNITY_TEMPLATE.md": {"language": "<PERSON><PERSON>", "code": 261, "comment": 0, "blank": 122}, "file:///Users/<USER>/development/flutter_apps/oneday/test/store_new_items_test.dart": {"language": "Dart", "code": 95, "comment": 13, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/test/custom_action_display_test.dart": {"language": "Dart", "code": 78, "comment": 9, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/test/profanity_filter_integration_test.dart": {"language": "Dart", "code": 94, "comment": 11, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/test/category_edit_integration_test.dart": {"language": "Dart", "code": 108, "comment": 29, "blank": 41}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_compilation_fix.dart": {"language": "Dart", "code": 144, "comment": 7, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/test/exercise_library_sidebar_test.dart": {"language": "Dart", "code": 76, "comment": 15, "blank": 23}, "file:///Users/<USER>/development/flutter_apps/oneday/test/date_format_test.dart": {"language": "Dart", "code": 44, "comment": 5, "blank": 13}, "file:///Users/<USER>/development/flutter_apps/oneday/test/photo_album_creator_test.dart": {"language": "Dart", "code": 81, "comment": 2, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/test/simple_profanity_test.dart": {"language": "Dart", "code": 47, "comment": 1, "blank": 16}, "file:///Users/<USER>/development/flutter_apps/oneday/test/ipad_calendar_overflow_test.dart": {"language": "Dart", "code": 119, "comment": 21, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/test/profile_edit_navigation_test.dart": {"language": "Dart", "code": 59, "comment": 17, "blank": 23}, "file:///Users/<USER>/development/flutter_apps/oneday/test/create_album_success_message_test.dart": {"language": "Dart", "code": 172, "comment": 20, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/test/onboarding_web_navigation_test.dart": {"language": "Dart", "code": 30, "comment": 9, "blank": 11}, "file:///Users/<USER>/development/flutter_apps/oneday/test/default_category_edit_test.dart": {"language": "Dart", "code": 132, "comment": 31, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/test/reflection_log_integration_test.dart": {"language": "Dart", "code": 61, "comment": 11, "blank": 19}, "file:///Users/<USER>/development/flutter_apps/oneday/test/navigation_bottom_bar_test.dart": {"language": "Dart", "code": 164, "comment": 37, "blank": 50}, "file:///Users/<USER>/development/flutter_apps/oneday/test/integration/study_session_completion_integration_test.dart": {"language": "Dart", "code": 103, "comment": 52, "blank": 53}, "file:///Users/<USER>/development/flutter_apps/oneday/test/image_compression_quality_test.dart": {"language": "Dart", "code": 97, "comment": 14, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/test/services/study_session_completion_service_test.mocks.dart": {"language": "Dart", "code": 402, "comment": 26, "blank": 59}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/README.md": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/test/services/study_session_completion_service_test.dart": {"language": "Dart", "code": 240, "comment": 32, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_bubble_positioning_fix.dart": {"language": "Dart", "code": 109, "comment": 6, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/test/category_edit_fix_test.dart": {"language": "Dart", "code": 104, "comment": 15, "blank": 21}, "file:///Users/<USER>/development/flutter_apps/oneday/test/bubble_positioning_analysis.dart": {"language": "Dart", "code": 114, "comment": 9, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/guides/PROFILE_EDIT_VERIFICATION_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 121, "comment": 0, "blank": 49}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/guides/UI_DEMO_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 91, "comment": 0, "blank": 22}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/guides/CROSS_ORIENTATION_POSITIONING_SOLUTION_TEMPLATE.md": {"language": "<PERSON><PERSON>", "code": 471, "comment": 0, "blank": 109}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/guides/IMAGE_PICKER_DEBUG_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 181, "comment": 0, "blank": 44}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/guides/README.md": {"language": "<PERSON><PERSON>", "code": 91, "comment": 0, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/guides/TIMER_FLOATING_WINDOW_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 69, "comment": 0, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/guides/ICON_SETUP_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 231, "comment": 0, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/guides/DEVELOPER_ENTRANCE_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 59, "comment": 0, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/guides/POSITIONING_QUICK_REFERENCE.md": {"language": "<PERSON><PERSON>", "code": 133, "comment": 0, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/guides/SYSTEM_FLOATING_TIMER_USER_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 117, "comment": 0, "blank": 41}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/DEBUG_PHASE_1_COMPLETE.md": {"language": "<PERSON><PERSON>", "code": 93, "comment": 0, "blank": 31}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/guides/FLOATING_TIMER_DEBUG_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 199, "comment": 0, "blank": 60}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/FLOATING_WINDOW_TEST_CHECKLIST.md": {"language": "<PERSON><PERSON>", "code": 114, "comment": 0, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/DRAG_FEATURE_DEBUG_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 138, "comment": 0, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/README.md": {"language": "<PERSON><PERSON>", "code": 92, "comment": 0, "blank": 30}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/STUDY_SESSION_COMPLETION_VERIFICATION.md": {"language": "<PERSON><PERSON>", "code": 153, "comment": 0, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/BUBBLE_OFFSET_DEBUGGING.md": {"language": "<PERSON><PERSON>", "code": 121, "comment": 0, "blank": 30}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/COVER_MODE_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 115, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/QUICK_TEST_STEPS.md": {"language": "<PERSON><PERSON>", "code": 85, "comment": 0, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/TEMP_BUBBLE_DRAG_DEBUG_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 125, "comment": 0, "blank": 34}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/ACHIEVEMENT_SYSTEM_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 87, "comment": 0, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/USER_IMPORTED_PHOTOS_DEBUG_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 111, "comment": 0, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/MEMORY_ALBUM_DRAG_FEATURE_TEST.md": {"language": "<PERSON><PERSON>", "code": 97, "comment": 0, "blank": 36}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/COMPREHENSIVE_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 276, "comment": 0, "blank": 96}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/TESTING_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 103, "comment": 0, "blank": 34}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/DRAGGABLE_ANNOTATION_SYSTEM_TEST.md": {"language": "<PERSON><PERSON>", "code": 162, "comment": 0, "blank": 47}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/POMODORO_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 105, "comment": 0, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/FINAL_NAVIGATION_TEST_REPORT.md": {"language": "<PERSON><PERSON>", "code": 118, "comment": 0, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/TIMER_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 121, "comment": 0, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/business/RISK_ASSESSMENT_STRATEGY.md": {"language": "<PERSON><PERSON>", "code": 242, "comment": 0, "blank": 77}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/business/COMMUNITY_BUSINESS_PLAN.md": {"language": "<PERSON><PERSON>", "code": 403, "comment": 0, "blank": 130}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/VOCABULARY_SERVICE_FIX.md": {"language": "<PERSON><PERSON>", "code": 219, "comment": 0, "blank": 52}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/business/IMPLEMENTATION_CHECKLIST.md": {"language": "<PERSON><PERSON>", "code": 245, "comment": 0, "blank": 59}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/business/FINANCIAL_MODEL_ANALYSIS.md": {"language": "<PERSON><PERSON>", "code": 184, "comment": 0, "blank": 60}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/TIMEBOX_REST_SKIP_NAVIGATION_FIX.md": {"language": "<PERSON><PERSON>", "code": 157, "comment": 0, "blank": 52}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/EXERCISE_LIBRARY_UI_OPTIMIZATION.md": {"language": "<PERSON><PERSON>", "code": 111, "comment": 0, "blank": 36}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/DEBUGGING_STEP_1_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 115, "comment": 0, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/HOW_TO_TEST_NAVIGATION_FIX.md": {"language": "<PERSON><PERSON>", "code": 147, "comment": 0, "blank": 51}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/ANNOTATION_SCALE_FIX_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 154, "comment": 0, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/KNOWLEDGE_POINT_SYNC_FIX.md": {"language": "<PERSON><PERSON>", "code": 191, "comment": 0, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/COMPREHENSIVE_NAVIGATION_AUDIT.md": {"language": "<PERSON><PERSON>", "code": 192, "comment": 0, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/TIMEBOX_TIMER_FIXES.md": {"language": "<PERSON><PERSON>", "code": 115, "comment": 0, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/TIMER_FIXES_PROGRESS.md": {"language": "<PERSON><PERSON>", "code": 131, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/WORD_TRAINING_OPTIMIZATION.md": {"language": "<PERSON><PERSON>", "code": 108, "comment": 0, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/RADAR_CHART_TECHNICAL_ANALYSIS.md": {"language": "<PERSON><PERSON>", "code": 103, "comment": 0, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/FLOATING_WINDOW_FINAL_OPTIMIZATION.md": {"language": "<PERSON><PERSON>", "code": 142, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/PROBLEM_SOLUTIONS.md": {"language": "<PERSON><PERSON>", "code": 317, "comment": 0, "blank": 112}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/CALENDAR_TASK_DISPLAY_FIX.md": {"language": "<PERSON><PERSON>", "code": 219, "comment": 0, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/DIALOG_LAYOUT_BUGFIX.md": {"language": "<PERSON><PERSON>", "code": 206, "comment": 0, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/DIALOG_LAYOUT_OPTIMIZATION.md": {"language": "<PERSON><PERSON>", "code": 192, "comment": 0, "blank": 46}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/IPAD_STAGE_MANAGER_LAYOUT_FIX.md": {"language": "<PERSON><PERSON>", "code": 200, "comment": 0, "blank": 31}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/NAVIGATION_FIX_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 133, "comment": 0, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/COLOR_SYSTEM_RESTORATION.md": {"language": "<PERSON><PERSON>", "code": 134, "comment": 0, "blank": 34}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/README.md": {"language": "<PERSON><PERSON>", "code": 54, "comment": 0, "blank": 22}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/ANNOTATION_SCALE_FIX_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 144, "comment": 0, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/NAVIGATION_FIX_PROGRESS_REPORT.md": {"language": "<PERSON><PERSON>", "code": 121, "comment": 0, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/UI_OVERFLOW_FIXES_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 98, "comment": 0, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/BOTTOM_NAVIGATION_BAR_FIX.md": {"language": "<PERSON><PERSON>", "code": 244, "comment": 0, "blank": 50}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/PDF_COLOR_RESTORATION.md": {"language": "<PERSON><PERSON>", "code": 160, "comment": 0, "blank": 41}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/SIDEBAR_BUTTON_OPTIMIZATION.md": {"language": "<PERSON><PERSON>", "code": 147, "comment": 0, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/EXPORT_FUNCTIONALITY.md": {"language": "<PERSON><PERSON>", "code": 160, "comment": 0, "blank": 31}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/FINAL_DIALOG_BACKGROUND_REPORT.md": {"language": "<PERSON><PERSON>", "code": 145, "comment": 0, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/HELP_FEEDBACK.md": {"language": "<PERSON><PERSON>", "code": 105, "comment": 0, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/CUSTOM_ACTION_LIBRARY_USER_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 127, "comment": 0, "blank": 50}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/VOCABULARY_MANAGER_IMPLEMENTATION_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 156, "comment": 0, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/SILENT_REFRESH_OPTIMIZATION.md": {"language": "<PERSON><PERSON>", "code": 175, "comment": 0, "blank": 44}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/DIALOG_BACKGROUND_BEFORE_AFTER_COMPARISON.md": {"language": "<PERSON><PERSON>", "code": 215, "comment": 0, "blank": 59}, "file:///Users/<USER>/development/flutter_apps/oneday/test/standardized_coordinate_system_test.dart": {"language": "Dart", "code": 269, "comment": 28, "blank": 50}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/CREATE_ACTION_LIBRARY_BUTTON_IMPLEMENTATION_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 204, "comment": 0, "blank": 45}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/FLOATING_WINDOW_OPTIMIZATION_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 129, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/WORD_DETAIL_DIALOG_IMPLEMENTATION.md": {"language": "<PERSON><PERSON>", "code": 214, "comment": 0, "blank": 57}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/FLOATING_TIMER_FIXES_VERIFICATION.md": {"language": "<PERSON><PERSON>", "code": 190, "comment": 0, "blank": 50}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/CUSTOM_ACTION_LIBRARY_IMPLEMENTATION.md": {"language": "<PERSON><PERSON>", "code": 183, "comment": 0, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/GRADUATE_VOCABULARY_MANAGER.md": {"language": "<PERSON><PERSON>", "code": 137, "comment": 0, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/NEW_STORE_ITEMS.md": {"language": "<PERSON><PERSON>", "code": 125, "comment": 0, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/SILENT_HOME_REFRESH.md": {"language": "<PERSON><PERSON>", "code": 110, "comment": 0, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/PROFILE_DATA_SYNC.md": {"language": "<PERSON><PERSON>", "code": 129, "comment": 0, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/CREATE_ACTION_LIBRARY_BUTTON_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 142, "comment": 0, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/core/NOTION_PROJECT_TEMPLATE.md": {"language": "<PERSON><PERSON>", "code": 137, "comment": 0, "blank": 58}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/DIALOG_BACKGROUND_UNIFICATION.md": {"language": "<PERSON><PERSON>", "code": 213, "comment": 0, "blank": 63}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/core/README.md": {"language": "<PERSON><PERSON>", "code": 32, "comment": 0, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/INTERNATIONALIZATION_IMPLEMENTATION.md": {"language": "<PERSON><PERSON>", "code": 126, "comment": 0, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/DEVELOPER_TOOLS_UPDATE.md": {"language": "<PERSON><PERSON>", "code": 149, "comment": 0, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/SWIPE_GESTURE_DEMO.md": {"language": "<PERSON><PERSON>", "code": 216, "comment": 0, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/core/ONEDAY_PROJECT_ANALYSIS.md": {"language": "<PERSON><PERSON>", "code": 105, "comment": 0, "blank": 47}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/SHARE_UI_RESTORE_FEATURE.md": {"language": "<PERSON><PERSON>", "code": 120, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/README.md": {"language": "<PERSON><PERSON>", "code": 41, "comment": 0, "blank": 18}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/core/FEATURES.md": {"language": "<PERSON><PERSON>", "code": 601, "comment": 0, "blank": 120}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/core/ARCHITECTURE.md": {"language": "<PERSON><PERSON>", "code": 380, "comment": 0, "blank": 81}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/CUSTOM_LIBRARY_EDITOR_IMPROVEMENTS.md": {"language": "<PERSON><PERSON>", "code": 101, "comment": 0, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/SHARE_FUNCTION_SIMPLIFICATION.md": {"language": "<PERSON><PERSON>", "code": 99, "comment": 0, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/OPTIMIZING_CURSOR_AI.md": {"language": "<PERSON><PERSON>", "code": 182, "comment": 0, "blank": 58}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/REMOVE_CAMERA_FEATURE.md": {"language": "<PERSON><PERSON>", "code": 105, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/PROFANITY_FILTER_DEBUG_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 113, "comment": 0, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/ABILITY_RADAR_FEATURE.md": {"language": "<PERSON><PERSON>", "code": 137, "comment": 0, "blank": 47}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/VOCABULARY_SCROLLBAR_ENHANCEMENT.md": {"language": "<PERSON><PERSON>", "code": 126, "comment": 0, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/PROFANITY_FILTER_SYSTEM_DESIGN.md": {"language": "<PERSON><PERSON>", "code": 206, "comment": 0, "blank": 53}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/STUDY_STATISTICS_REALTIME_UPDATE_IMPLEMENTATION.md": {"language": "<PERSON><PERSON>", "code": 145, "comment": 0, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/POMODORO_TIMER_IMPLEMENTATION.md": {"language": "<PERSON><PERSON>", "code": 155, "comment": 0, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/core/PRD.md": {"language": "<PERSON><PERSON>", "code": 339, "comment": 0, "blank": 97}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/COMMUNITY_ARTICLE_IMPORT_FEATURE.md": {"language": "<PERSON><PERSON>", "code": 118, "comment": 0, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/EDIT_ALBUM_ENHANCEMENT.md": {"language": "<PERSON><PERSON>", "code": 167, "comment": 0, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/FLOATING_TIMER_SYSTEM_LEVEL_IMPLEMENTATION.md": {"language": "<PERSON><PERSON>", "code": 160, "comment": 2, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/CUSTOM_EXERCISE_CATEGORY_FEATURE.md": {"language": "<PERSON><PERSON>", "code": 193, "comment": 0, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/releases/README.md": {"language": "<PERSON><PERSON>", "code": 107, "comment": 0, "blank": 30}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/WORD_DISPLAY_LOWERCASE_UPDATE.md": {"language": "<PERSON><PERSON>", "code": 117, "comment": 0, "blank": 30}, "file:///Users/<USER>/development/flutter_apps/oneday/tempPrompts.md": {"language": "<PERSON><PERSON>", "code": 201, "comment": 0, "blank": 83}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Runner/AppDelegate.swift": {"language": "Swift", "code": 11, "comment": 0, "blank": 3}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/ACTION_LIBRARY_SELECTOR_OVERFLOW_FIX.md": {"language": "<PERSON><PERSON>", "code": 168, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/COMPRESSED_IMAGE_OFFSET_FIX.md": {"language": "<PERSON><PERSON>", "code": 110, "comment": 0, "blank": 34}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/CUSTOM_ACTION_EDIT_SAVE_FIX.md": {"language": "<PERSON><PERSON>", "code": 131, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/FINAL_PRECISION_ADJUSTMENT.md": {"language": "<PERSON><PERSON>", "code": 101, "comment": 0, "blank": 30}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/PROFILE_AVATAR_CLICK_UPDATE.md": {"language": "<PERSON><PERSON>", "code": 108, "comment": 0, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/DRAG_POSITION_JUMP_FIX.md": {"language": "<PERSON><PERSON>", "code": 106, "comment": 0, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/FINAL_OFFSET_ADJUSTMENT_WITH_DEBUG.md": {"language": "<PERSON><PERSON>", "code": 123, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/SNACKBAR_AND_COMMUNITY_FIXES.md": {"language": "<PERSON><PERSON>", "code": 163, "comment": 0, "blank": 52}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/ACHIEVEMENT_UNLOCK_NAVIGATION_FIX.md": {"language": "<PERSON><PERSON>", "code": 161, "comment": 0, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/BUBBLE_POSITIONING_FINAL_FIX.md": {"language": "<PERSON><PERSON>", "code": 117, "comment": 0, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/BUBBLE_BORDER_CONSISTENCY_FIX.md": {"language": "<PERSON><PERSON>", "code": 135, "comment": 0, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/CLICK_POSITION_FIX.md": {"language": "<PERSON><PERSON>", "code": 126, "comment": 0, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/USER_IMPORTED_PHOTOS_FIX.md": {"language": "<PERSON><PERSON>", "code": 146, "comment": 0, "blank": 44}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/NOTION_STYLE_UPDATES.md": {"language": "<PERSON><PERSON>", "code": 125, "comment": 0, "blank": 44}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/DYNAMIC_OFFSET_ADJUSTMENT_FIX.md": {"language": "<PERSON><PERSON>", "code": 129, "comment": 0, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/BUBBLE_CLICK_POSITION_FINAL_ALIGNMENT_FIX.md": {"language": "<PERSON><PERSON>", "code": 124, "comment": 0, "blank": 34}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/FINE_TUNING_OFFSET_ADJUSTMENT.md": {"language": "<PERSON><PERSON>", "code": 90, "comment": 0, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/blueprint.md": {"language": "<PERSON><PERSON>", "code": 69, "comment": 0, "blank": 19}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/FLOATING_TIMER_FEATURE.md": {"language": "<PERSON><PERSON>", "code": 74, "comment": 0, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Runner/MainFlutterWindow.swift": {"language": "Swift", "code": 12, "comment": 0, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/BUBBLE_POSITION_ADJUSTMENT.md": {"language": "<PERSON><PERSON>", "code": 100, "comment": 0, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/COORDINATE_SYSTEM_FINAL_FIX.md": {"language": "<PERSON><PERSON>", "code": 111, "comment": 0, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/STANDARDIZED_COORDINATE_SYSTEM_IMPLEMENTATION.md": {"language": "<PERSON><PERSON>", "code": 146, "comment": 0, "blank": 46}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/RunnerTests/RunnerTests.swift": {"language": "Swift", "code": 7, "comment": 2, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/BUBBLE_POSITIONING_PRECISE_FIX.md": {"language": "<PERSON><PERSON>", "code": 130, "comment": 0, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/UNIVERSAL_COMPRESSED_IMAGE_FIX_VERIFICATION.md": {"language": "<PERSON><PERSON>", "code": 102, "comment": 0, "blank": 31}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/study_time/study_time_statistics_test.dart": {"language": "Dart", "code": 264, "comment": 13, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/profile/image_cropper_test.dart": {"language": "Dart", "code": 202, "comment": 10, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/profile/profile_data_sync_test.dart": {"language": "Dart", "code": 165, "comment": 21, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/profile/image_cropper_test.mocks.dart": {"language": "Dart", "code": 40, "comment": 20, "blank": 7}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/exercise/category_integration_test.dart": {"language": "Dart", "code": 128, "comment": 22, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/EXERCISE_LIBRARY_PAGE_IMPORT_FIX.md": {"language": "<PERSON><PERSON>", "code": 91, "comment": 0, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/exercise/custom_action_library_ui_test.dart": {"language": "Dart", "code": 178, "comment": 27, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Flutter/GeneratedPluginRegistrant.swift": {"language": "Swift", "code": 16, "comment": 3, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/exercise/exercise_library_page_import_test.dart": {"language": "Dart", "code": 21, "comment": 5, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/profile/user_profile_test.dart": {"language": "Dart", "code": 191, "comment": 10, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/exercise/custom_action_library_test.dart": {"language": "Dart", "code": 155, "comment": 32, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/exercise/custom_category_test.dart": {"language": "Dart", "code": 113, "comment": 21, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Runner/Base.lproj/MainMenu.xib": {"language": "XML", "code": 343, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/vocabulary/word_detail_dialog_test.dart": {"language": "Dart", "code": 300, "comment": 22, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/data/vocabulary.json": {"language": "JSON", "code": 84516, "comment": 0, "blank": 0}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json": {"language": "JSON", "code": 68, "comment": 0, "blank": 0}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/time_box/timebox_ui_test.dart": {"language": "Dart", "code": 125, "comment": 11, "blank": 18}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/auth/register_page_test.dart": {"language": "Dart", "code": 41, "comment": 10, "blank": 15}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/exercise/exercise_library_share_test.dart": {"language": "Dart", "code": 250, "comment": 26, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/time_box/action_library_selector_overflow_test.dart": {"language": "Dart", "code": 301, "comment": 30, "blank": 49}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/ui/dropdown_background_test.dart": {"language": "Dart", "code": 352, "comment": 25, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/help_feedback/help_feedback_page_test.dart": {"language": "Dart", "code": 88, "comment": 17, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/ui/material3_surface_tinting_fix_test.dart": {"language": "Dart", "code": 260, "comment": 16, "blank": 23}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/ui/color_consistency_test.dart": {"language": "Dart", "code": 222, "comment": 19, "blank": 30}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/ui/dialog_background_simple_test.dart": {"language": "Dart", "code": 198, "comment": 17, "blank": 23}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/ui/dialog_background_test.dart": {"language": "Dart", "code": 228, "comment": 23, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/ui/dropdown_pure_white_test.dart": {"language": "Dart", "code": 261, "comment": 15, "blank": 22}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/ui/final_verification_test.dart": {"language": "Dart", "code": 233, "comment": 24, "blank": 29}}