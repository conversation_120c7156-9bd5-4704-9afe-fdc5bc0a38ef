# Summary

Date : 2025-08-01 02:33:34

Directory /Users/<USER>/development/flutter_apps/oneday

Total : 513 files,  212345 codes, 9846 comments, 19803 blanks, all 241994 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Dart | 300 | 100,131 | 9,478 | 12,570 | 122,179 |
| JSON | 9 | 85,362 | 0 | 6 | 85,368 |
| Markdown | 148 | 24,175 | 2 | 6,734 | 30,911 |
| HTML | 3 | 712 | 38 | 59 | 809 |
| C++ | 16 | 560 | 131 | 189 | 880 |
| XML | 10 | 482 | 56 | 16 | 554 |
| CMake | 8 | 467 | 0 | 92 | 559 |
| Python | 1 | 152 | 26 | 32 | 210 |
| Swift | 9 | 137 | 27 | 43 | 207 |
| YAML | 5 | 95 | 85 | 40 | 220 |
| Ruby | 2 | 64 | 3 | 20 | 87 |
| Java Properties | 2 | 8 | 0 | 2 | 10 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 513 | 212,345 | 9,846 | 19,803 | 241,994 |
| . (Files) | 11 | 2,999 | 85 | 798 | 3,882 |
| .augment | 1 | 387 | 0 | 114 | 501 |
| .augment/rules | 1 | 387 | 0 | 114 | 501 |
| android | 9 | 86 | 54 | 15 | 155 |
| android (Files) | 1 | 3 | 0 | 1 | 4 |
| android/app | 7 | 78 | 54 | 13 | 145 |
| android/app/src | 7 | 78 | 54 | 13 | 145 |
| android/app/src/debug | 1 | 3 | 4 | 1 | 8 |
| android/app/src/main | 5 | 72 | 46 | 11 | 129 |
| android/app/src/main (Files) | 1 | 46 | 14 | 5 | 65 |
| android/app/src/main/res | 4 | 26 | 32 | 6 | 64 |
| android/app/src/main/res/drawable | 1 | 4 | 7 | 2 | 13 |
| android/app/src/main/res/drawable-v21 | 1 | 4 | 7 | 2 | 13 |
| android/app/src/main/res/values | 1 | 9 | 9 | 1 | 19 |
| android/app/src/main/res/values-night | 1 | 9 | 9 | 1 | 19 |
| android/app/src/profile | 1 | 3 | 4 | 1 | 8 |
| android/gradle | 1 | 5 | 0 | 1 | 6 |
| android/gradle/wrapper | 1 | 5 | 0 | 1 | 6 |
| assets | 8 | 85,846 | 23 | 71 | 85,940 |
| assets/data | 7 | 85,813 | 23 | 58 | 85,894 |
| assets/icons | 1 | 33 | 0 | 13 | 46 |
| docs | 138 | 20,716 | 2 | 5,806 | 26,524 |
| docs (Files) | 1 | 71 | 0 | 20 | 91 |
| docs/business | 4 | 1,074 | 0 | 326 | 1,400 |
| docs/community | 1 | 261 | 0 | 122 | 383 |
| docs/core | 6 | 1,594 | 0 | 413 | 2,007 |
| docs/development | 22 | 2,895 | 2 | 804 | 3,701 |
| docs/features | 17 | 2,689 | 0 | 748 | 3,437 |
| docs/fixes | 22 | 2,718 | 0 | 812 | 3,530 |
| docs/guides | 11 | 1,763 | 0 | 464 | 2,227 |
| docs/releases | 1 | 107 | 0 | 30 | 137 |
| docs/testing | 18 | 2,213 | 0 | 637 | 2,850 |
| docs/troubleshooting | 29 | 4,576 | 0 | 1,215 | 5,791 |
| docs/ui_improvements | 6 | 755 | 0 | 215 | 970 |
| example | 8 | 3,091 | 124 | 213 | 3,428 |
| ios | 12 | 327 | 26 | 45 | 398 |
| ios (Files) | 1 | 32 | 2 | 10 | 44 |
| ios/OneDayWidget | 3 | 72 | 20 | 22 | 114 |
| ios/Runner | 7 | 216 | 2 | 9 | 227 |
| ios/Runner (Files) | 2 | 13 | 0 | 3 | 16 |
| ios/Runner/Assets.xcassets | 3 | 142 | 0 | 4 | 146 |
| ios/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 116 | 0 | 1 | 117 |
| ios/Runner/Assets.xcassets/LaunchImage.imageset | 2 | 26 | 0 | 3 | 29 |
| ios/Runner/Base.lproj | 2 | 61 | 2 | 2 | 65 |
| ios/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| lib | 169 | 79,022 | 7,341 | 8,875 | 95,238 |
| lib (Files) | 1 | 254 | 25 | 13 | 292 |
| lib/core | 2 | 1,622 | 33 | 29 | 1,684 |
| lib/core/constants | 1 | 35 | 5 | 3 | 43 |
| lib/core/data | 1 | 1,587 | 28 | 26 | 1,641 |
| lib/debug | 7 | 1,585 | 69 | 169 | 1,823 |
| lib/features | 136 | 71,800 | 6,262 | 7,767 | 85,829 |
| lib/features/ability_radar | 11 | 3,553 | 359 | 498 | 4,410 |
| lib/features/ability_radar/models | 2 | 421 | 76 | 83 | 580 |
| lib/features/ability_radar/pages | 1 | 635 | 52 | 58 | 745 |
| lib/features/ability_radar/providers | 2 | 443 | 26 | 63 | 532 |
| lib/features/ability_radar/services | 2 | 805 | 104 | 177 | 1,086 |
| lib/features/ability_radar/widgets | 4 | 1,249 | 101 | 117 | 1,467 |
| lib/features/achievement | 17 | 4,490 | 456 | 630 | 5,576 |
| lib/features/achievement (Files) | 1 | 132 | 0 | 41 | 173 |
| lib/features/achievement/data | 1 | 444 | 18 | 12 | 474 |
| lib/features/achievement/models | 6 | 1,065 | 205 | 195 | 1,465 |
| lib/features/achievement/pages | 2 | 821 | 34 | 59 | 914 |
| lib/features/achievement/providers | 1 | 224 | 38 | 44 | 306 |
| lib/features/achievement/services | 2 | 445 | 99 | 129 | 673 |
| lib/features/achievement/widgets | 4 | 1,359 | 62 | 150 | 1,571 |
| lib/features/auth | 9 | 2,791 | 314 | 294 | 3,399 |
| lib/features/auth (Files) | 5 | 2,167 | 125 | 152 | 2,444 |
| lib/features/auth/providers | 2 | 365 | 38 | 63 | 466 |
| lib/features/auth/services | 2 | 259 | 151 | 79 | 489 |
| lib/features/calendar | 1 | 1,094 | 89 | 77 | 1,260 |
| lib/features/community | 8 | 4,362 | 255 | 396 | 5,013 |
| lib/features/daily_plan | 4 | 620 | 106 | 133 | 859 |
| lib/features/daily_plan/models | 2 | 200 | 42 | 41 | 283 |
| lib/features/daily_plan/notifiers | 1 | 204 | 31 | 44 | 279 |
| lib/features/daily_plan/services | 1 | 216 | 33 | 48 | 297 |
| lib/features/exercise | 15 | 11,910 | 764 | 955 | 13,629 |
| lib/features/exercise (Files) | 14 | 11,588 | 732 | 910 | 13,230 |
| lib/features/exercise/providers | 1 | 322 | 32 | 45 | 399 |
| lib/features/help_feedback | 2 | 1,060 | 50 | 85 | 1,195 |
| lib/features/home | 1 | 1,237 | 97 | 81 | 1,415 |
| lib/features/learning_report | 7 | 3,335 | 397 | 405 | 4,137 |
| lib/features/learning_report (Files) | 1 | 795 | 40 | 58 | 893 |
| lib/features/learning_report/models | 2 | 504 | 112 | 102 | 718 |
| lib/features/learning_report/providers | 1 | 148 | 32 | 40 | 220 |
| lib/features/learning_report/services | 2 | 1,030 | 177 | 157 | 1,364 |
| lib/features/learning_report/widgets | 1 | 858 | 36 | 48 | 942 |
| lib/features/main | 1 | 105 | 6 | 10 | 121 |
| lib/features/memory_palace | 6 | 11,175 | 1,246 | 1,420 | 13,841 |
| lib/features/memory_palace (Files) | 3 | 10,575 | 1,168 | 1,294 | 13,037 |
| lib/features/memory_palace/providers | 1 | 191 | 18 | 33 | 242 |
| lib/features/memory_palace/utils | 2 | 409 | 60 | 93 | 562 |
| lib/features/onboarding | 2 | 1,016 | 77 | 101 | 1,194 |
| lib/features/photo_album | 1 | 792 | 48 | 78 | 918 |
| lib/features/profile | 6 | 2,163 | 157 | 244 | 2,564 |
| lib/features/profile (Files) | 1 | 825 | 44 | 70 | 939 |
| lib/features/profile/models | 2 | 115 | 25 | 27 | 167 |
| lib/features/profile/pages | 1 | 851 | 45 | 74 | 970 |
| lib/features/profile/providers | 1 | 189 | 20 | 35 | 244 |
| lib/features/profile/services | 1 | 183 | 23 | 38 | 244 |
| lib/features/reflection | 1 | 1,327 | 66 | 136 | 1,529 |
| lib/features/settings | 1 | 776 | 56 | 68 | 900 |
| lib/features/splash | 1 | 177 | 12 | 23 | 212 |
| lib/features/study_time | 5 | 1,350 | 194 | 234 | 1,778 |
| lib/features/study_time (Files) | 1 | 253 | 11 | 17 | 281 |
| lib/features/study_time/models | 2 | 439 | 92 | 80 | 611 |
| lib/features/study_time/providers | 1 | 310 | 26 | 51 | 387 |
| lib/features/study_time/services | 1 | 348 | 65 | 86 | 499 |
| lib/features/time_box | 7 | 5,387 | 428 | 467 | 6,282 |
| lib/features/time_box (Files) | 1 | 3,495 | 261 | 245 | 4,001 |
| lib/features/time_box/managers | 1 | 311 | 47 | 54 | 412 |
| lib/features/time_box/models | 2 | 426 | 73 | 80 | 579 |
| lib/features/time_box/pages | 1 | 489 | 14 | 35 | 538 |
| lib/features/time_box/providers | 1 | 261 | 27 | 32 | 320 |
| lib/features/time_box/widgets | 1 | 405 | 6 | 21 | 432 |
| lib/features/vocabulary | 16 | 7,818 | 618 | 899 | 9,335 |
| lib/features/vocabulary (Files) | 15 | 7,555 | 591 | 856 | 9,002 |
| lib/features/vocabulary/providers | 1 | 263 | 27 | 43 | 333 |
| lib/features/wage_system | 10 | 4,274 | 311 | 408 | 4,993 |
| lib/features/wage_system (Files) | 2 | 2,710 | 131 | 173 | 3,014 |
| lib/features/wage_system/models | 2 | 345 | 27 | 41 | 413 |
| lib/features/wage_system/providers | 1 | 42 | 9 | 11 | 62 |
| lib/features/wage_system/services | 4 | 874 | 142 | 166 | 1,182 |
| lib/features/wage_system/widgets | 1 | 303 | 2 | 17 | 322 |
| lib/features/widget | 4 | 988 | 156 | 125 | 1,269 |
| lib/features/widget/models | 1 | 152 | 15 | 22 | 189 |
| lib/features/widget/services | 2 | 476 | 126 | 79 | 681 |
| lib/features/widget/utils | 1 | 360 | 15 | 24 | 399 |
| lib/l10n | 3 | 469 | 366 | 244 | 1,079 |
| lib/providers | 1 | 51 | 6 | 11 | 68 |
| lib/router | 1 | 392 | 40 | 48 | 480 |
| lib/services | 8 | 1,499 | 287 | 300 | 2,086 |
| lib/services (Files) | 7 | 1,480 | 285 | 296 | 2,061 |
| lib/services/providers | 1 | 19 | 2 | 4 | 25 |
| lib/shared | 9 | 1,254 | 243 | 274 | 1,771 |
| lib/shared/config | 1 | 67 | 20 | 18 | 105 |
| lib/shared/services | 1 | 160 | 27 | 37 | 224 |
| lib/shared/utils | 5 | 761 | 171 | 162 | 1,094 |
| lib/shared/widgets | 2 | 266 | 25 | 57 | 348 |
| lib/shared/widgets (Files) | 1 | 204 | 17 | 43 | 264 |
| lib/shared/widgets/icons | 1 | 62 | 8 | 14 | 84 |
| lib/utils | 1 | 96 | 10 | 20 | 126 |
| linux | 9 | 335 | 37 | 92 | 464 |
| linux (Files) | 1 | 104 | 0 | 25 | 129 |
| linux/flutter | 4 | 115 | 9 | 27 | 151 |
| linux/runner | 4 | 116 | 28 | 40 | 184 |
| macos | 7 | 489 | 6 | 26 | 521 |
| macos (Files) | 1 | 32 | 1 | 10 | 43 |
| macos/Flutter | 1 | 16 | 3 | 4 | 23 |
| macos/Runner | 4 | 434 | 0 | 8 | 442 |
| macos/Runner (Files) | 2 | 23 | 0 | 7 | 30 |
| macos/Runner/Assets.xcassets | 1 | 68 | 0 | 0 | 68 |
| macos/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 68 | 0 | 0 | 68 |
| macos/Runner/Base.lproj | 1 | 343 | 0 | 1 | 344 |
| macos/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| scripts | 5 | 497 | 66 | 108 | 671 |
| test | 105 | 14,172 | 1,834 | 3,093 | 19,099 |
| test (Files) | 79 | 9,111 | 1,288 | 2,279 | 12,678 |
| test/features | 23 | 4,316 | 436 | 663 | 5,415 |
| test/features/auth | 1 | 41 | 10 | 15 | 66 |
| test/features/exercise | 6 | 845 | 133 | 177 | 1,155 |
| test/features/help_feedback | 1 | 88 | 17 | 33 | 138 |
| test/features/profile | 4 | 598 | 61 | 120 | 779 |
| test/features/study_time | 1 | 264 | 13 | 38 | 315 |
| test/features/time_box | 2 | 426 | 41 | 67 | 534 |
| test/features/ui | 7 | 1,754 | 139 | 184 | 2,077 |
| test/features/vocabulary | 1 | 300 | 22 | 29 | 351 |
| test/integration | 1 | 103 | 52 | 53 | 208 |
| test/services | 2 | 642 | 58 | 98 | 798 |
| test_apps | 15 | 3,633 | 139 | 354 | 4,126 |
| web | 2 | 54 | 15 | 5 | 74 |
| windows | 14 | 691 | 94 | 188 | 973 |
| windows (Files) | 1 | 89 | 0 | 20 | 109 |
| windows/flutter | 4 | 140 | 9 | 29 | 178 |
| windows/runner | 9 | 462 | 85 | 139 | 686 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)