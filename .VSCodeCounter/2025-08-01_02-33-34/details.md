# Details

Date : 2025-08-01 02:33:34

Directory /Users/<USER>/development/flutter_apps/oneday

Total : 513 files,  212345 codes, 9846 comments, 19803 blanks, all 241994 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.augment/rules/rules.md](/.augment/rules/rules.md) | Markdown | 387 | 0 | 114 | 501 |
| [.cursorrules.md](/.cursorrules.md) | Markdown | 436 | 0 | 123 | 559 |
| [ARCHITECTURE.md](/ARCHITECTURE.md) | Markdown | 214 | 0 | 55 | 269 |
| [FEATURES.md](/FEATURES.md) | Markdown | 395 | 0 | 105 | 500 |
| [PROBLEM\_SOLUTIONS.md](/PROBLEM_SOLUTIONS.md) | Markdown | 476 | 0 | 131 | 607 |
| [README.md](/README.md) | Markdown | 1,182 | 0 | 261 | 1,443 |
| [analysis\_options.yaml](/analysis_options.yaml) | YAML | 4 | 21 | 4 | 29 |
| [android/app/src/debug/AndroidManifest.xml](/android/app/src/debug/AndroidManifest.xml) | XML | 3 | 4 | 1 | 8 |
| [android/app/src/main/AndroidManifest.xml](/android/app/src/main/AndroidManifest.xml) | XML | 46 | 14 | 5 | 65 |
| [android/app/src/main/res/drawable-v21/launch\_background.xml](/android/app/src/main/res/drawable-v21/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [android/app/src/main/res/drawable/launch\_background.xml](/android/app/src/main/res/drawable/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [android/app/src/main/res/values-night/styles.xml](/android/app/src/main/res/values-night/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [android/app/src/main/res/values/styles.xml](/android/app/src/main/res/values/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [android/app/src/profile/AndroidManifest.xml](/android/app/src/profile/AndroidManifest.xml) | XML | 3 | 4 | 1 | 8 |
| [android/gradle.properties](/android/gradle.properties) | Java Properties | 3 | 0 | 1 | 4 |
| [android/gradle/wrapper/gradle-wrapper.properties](/android/gradle/wrapper/gradle-wrapper.properties) | Java Properties | 5 | 0 | 1 | 6 |
| [assets/data/Action.html](/assets/data/Action.html) | HTML | 0 | 0 | 1 | 1 |
| [assets/data/prefixes.json](/assets/data/prefixes.json) | JSON | 88 | 0 | 1 | 89 |
| [assets/data/profanity\_words.json](/assets/data/profanity_words.json) | JSON | 259 | 0 | 1 | 260 |
| [assets/data/suffixes.json](/assets/data/suffixes.json) | JSON | 99 | 0 | 1 | 100 |
| [assets/data/vocabulary.json](/assets/data/vocabulary.json) | JSON | 84,516 | 0 | 0 | 84,516 |
| [assets/data/word\_roots.json](/assets/data/word_roots.json) | JSON | 158 | 0 | 1 | 159 |
| [assets/data/动作.html](/assets/data/%E5%8A%A8%E4%BD%9C.html) | HTML | 693 | 23 | 53 | 769 |
| [assets/icons/README.md](/assets/icons/README.md) | Markdown | 33 | 0 | 13 | 46 |
| [build.yaml](/build.yaml) | YAML | 11 | 6 | 1 | 18 |
| [devtools\_options.yaml](/devtools_options.yaml) | YAML | 3 | 0 | 1 | 4 |
| [docs/README.md](/docs/README.md) | Markdown | 71 | 0 | 20 | 91 |
| [docs/business/COMMUNITY\_BUSINESS\_PLAN.md](/docs/business/COMMUNITY_BUSINESS_PLAN.md) | Markdown | 403 | 0 | 130 | 533 |
| [docs/business/FINANCIAL\_MODEL\_ANALYSIS.md](/docs/business/FINANCIAL_MODEL_ANALYSIS.md) | Markdown | 184 | 0 | 60 | 244 |
| [docs/business/IMPLEMENTATION\_CHECKLIST.md](/docs/business/IMPLEMENTATION_CHECKLIST.md) | Markdown | 245 | 0 | 59 | 304 |
| [docs/business/RISK\_ASSESSMENT\_STRATEGY.md](/docs/business/RISK_ASSESSMENT_STRATEGY.md) | Markdown | 242 | 0 | 77 | 319 |
| [docs/community/USER\_COMMUNITY\_TEMPLATE.md](/docs/community/USER_COMMUNITY_TEMPLATE.md) | Markdown | 261 | 0 | 122 | 383 |
| [docs/core/ARCHITECTURE.md](/docs/core/ARCHITECTURE.md) | Markdown | 380 | 0 | 81 | 461 |
| [docs/core/FEATURES.md](/docs/core/FEATURES.md) | Markdown | 601 | 0 | 120 | 721 |
| [docs/core/NOTION\_PROJECT\_TEMPLATE.md](/docs/core/NOTION_PROJECT_TEMPLATE.md) | Markdown | 137 | 0 | 58 | 195 |
| [docs/core/ONEDAY\_PROJECT\_ANALYSIS.md](/docs/core/ONEDAY_PROJECT_ANALYSIS.md) | Markdown | 105 | 0 | 47 | 152 |
| [docs/core/PRD.md](/docs/core/PRD.md) | Markdown | 339 | 0 | 97 | 436 |
| [docs/core/README.md](/docs/core/README.md) | Markdown | 32 | 0 | 10 | 42 |
| [docs/development/ABILITY\_RADAR\_FEATURE.md](/docs/development/ABILITY_RADAR_FEATURE.md) | Markdown | 137 | 0 | 47 | 184 |
| [docs/development/COMMUNITY\_ARTICLE\_IMPORT\_FEATURE.md](/docs/development/COMMUNITY_ARTICLE_IMPORT_FEATURE.md) | Markdown | 118 | 0 | 37 | 155 |
| [docs/development/CUSTOM\_EXERCISE\_CATEGORY\_FEATURE.md](/docs/development/CUSTOM_EXERCISE_CATEGORY_FEATURE.md) | Markdown | 193 | 0 | 43 | 236 |
| [docs/development/CUSTOM\_LIBRARY\_EDITOR\_IMPROVEMENTS.md](/docs/development/CUSTOM_LIBRARY_EDITOR_IMPROVEMENTS.md) | Markdown | 101 | 0 | 27 | 128 |
| [docs/development/EDIT\_ALBUM\_ENHANCEMENT.md](/docs/development/EDIT_ALBUM_ENHANCEMENT.md) | Markdown | 167 | 0 | 43 | 210 |
| [docs/development/FLOATING\_TIMER\_FEATURE.md](/docs/development/FLOATING_TIMER_FEATURE.md) | Markdown | 74 | 0 | 20 | 94 |
| [docs/development/FLOATING\_TIMER\_SYSTEM\_LEVEL\_IMPLEMENTATION.md](/docs/development/FLOATING_TIMER_SYSTEM_LEVEL_IMPLEMENTATION.md) | Markdown | 160 | 2 | 40 | 202 |
| [docs/development/INTERNATIONALIZATION\_IMPLEMENTATION.md](/docs/development/INTERNATIONALIZATION_IMPLEMENTATION.md) | Markdown | 126 | 0 | 37 | 163 |
| [docs/development/NOTION\_STYLE\_UPDATES.md](/docs/development/NOTION_STYLE_UPDATES.md) | Markdown | 125 | 0 | 44 | 169 |
| [docs/development/OPTIMIZING\_CURSOR\_AI.md](/docs/development/OPTIMIZING_CURSOR_AI.md) | Markdown | 182 | 0 | 58 | 240 |
| [docs/development/POMODORO\_TIMER\_IMPLEMENTATION.md](/docs/development/POMODORO_TIMER_IMPLEMENTATION.md) | Markdown | 155 | 0 | 27 | 182 |
| [docs/development/PROFANITY\_FILTER\_DEBUG\_GUIDE.md](/docs/development/PROFANITY_FILTER_DEBUG_GUIDE.md) | Markdown | 113 | 0 | 33 | 146 |
| [docs/development/PROFANITY\_FILTER\_SYSTEM\_DESIGN.md](/docs/development/PROFANITY_FILTER_SYSTEM_DESIGN.md) | Markdown | 206 | 0 | 53 | 259 |
| [docs/development/README.md](/docs/development/README.md) | Markdown | 41 | 0 | 18 | 59 |
| [docs/development/REMOVE\_CAMERA\_FEATURE.md](/docs/development/REMOVE_CAMERA_FEATURE.md) | Markdown | 105 | 0 | 35 | 140 |
| [docs/development/SHARE\_FUNCTION\_SIMPLIFICATION.md](/docs/development/SHARE_FUNCTION_SIMPLIFICATION.md) | Markdown | 99 | 0 | 29 | 128 |
| [docs/development/SHARE\_UI\_RESTORE\_FEATURE.md](/docs/development/SHARE_UI_RESTORE_FEATURE.md) | Markdown | 120 | 0 | 35 | 155 |
| [docs/development/STUDY\_STATISTICS\_REALTIME\_UPDATE\_IMPLEMENTATION.md](/docs/development/STUDY_STATISTICS_REALTIME_UPDATE_IMPLEMENTATION.md) | Markdown | 145 | 0 | 43 | 188 |
| [docs/development/SWIPE\_GESTURE\_DEMO.md](/docs/development/SWIPE_GESTURE_DEMO.md) | Markdown | 216 | 0 | 48 | 264 |
| [docs/development/VOCABULARY\_SCROLLBAR\_ENHANCEMENT.md](/docs/development/VOCABULARY_SCROLLBAR_ENHANCEMENT.md) | Markdown | 126 | 0 | 38 | 164 |
| [docs/development/WORD\_DISPLAY\_LOWERCASE\_UPDATE.md](/docs/development/WORD_DISPLAY_LOWERCASE_UPDATE.md) | Markdown | 117 | 0 | 30 | 147 |
| [docs/development/blueprint.md](/docs/development/blueprint.md) | Markdown | 69 | 0 | 19 | 88 |
| [docs/features/CREATE\_ACTION\_LIBRARY\_BUTTON\_GUIDE.md](/docs/features/CREATE_ACTION_LIBRARY_BUTTON_GUIDE.md) | Markdown | 142 | 0 | 37 | 179 |
| [docs/features/CREATE\_ACTION\_LIBRARY\_BUTTON\_IMPLEMENTATION\_SUMMARY.md](/docs/features/CREATE_ACTION_LIBRARY_BUTTON_IMPLEMENTATION_SUMMARY.md) | Markdown | 204 | 0 | 45 | 249 |
| [docs/features/CUSTOM\_ACTION\_LIBRARY\_IMPLEMENTATION.md](/docs/features/CUSTOM_ACTION_LIBRARY_IMPLEMENTATION.md) | Markdown | 183 | 0 | 42 | 225 |
| [docs/features/CUSTOM\_ACTION\_LIBRARY\_USER\_GUIDE.md](/docs/features/CUSTOM_ACTION_LIBRARY_USER_GUIDE.md) | Markdown | 127 | 0 | 50 | 177 |
| [docs/features/DEVELOPER\_TOOLS\_UPDATE.md](/docs/features/DEVELOPER_TOOLS_UPDATE.md) | Markdown | 149 | 0 | 48 | 197 |
| [docs/features/DIALOG\_BACKGROUND\_BEFORE\_AFTER\_COMPARISON.md](/docs/features/DIALOG_BACKGROUND_BEFORE_AFTER_COMPARISON.md) | Markdown | 215 | 0 | 59 | 274 |
| [docs/features/DIALOG\_BACKGROUND\_UNIFICATION.md](/docs/features/DIALOG_BACKGROUND_UNIFICATION.md) | Markdown | 213 | 0 | 63 | 276 |
| [docs/features/EXPORT\_FUNCTIONALITY.md](/docs/features/EXPORT_FUNCTIONALITY.md) | Markdown | 160 | 0 | 31 | 191 |
| [docs/features/FINAL\_DIALOG\_BACKGROUND\_REPORT.md](/docs/features/FINAL_DIALOG_BACKGROUND_REPORT.md) | Markdown | 145 | 0 | 40 | 185 |
| [docs/features/GRADUATE\_VOCABULARY\_MANAGER.md](/docs/features/GRADUATE_VOCABULARY_MANAGER.md) | Markdown | 137 | 0 | 42 | 179 |
| [docs/features/HELP\_FEEDBACK.md](/docs/features/HELP_FEEDBACK.md) | Markdown | 105 | 0 | 32 | 137 |
| [docs/features/NEW\_STORE\_ITEMS.md](/docs/features/NEW_STORE_ITEMS.md) | Markdown | 125 | 0 | 40 | 165 |
| [docs/features/PROFILE\_DATA\_SYNC.md](/docs/features/PROFILE_DATA_SYNC.md) | Markdown | 129 | 0 | 39 | 168 |
| [docs/features/SILENT\_HOME\_REFRESH.md](/docs/features/SILENT_HOME_REFRESH.md) | Markdown | 110 | 0 | 37 | 147 |
| [docs/features/SILENT\_REFRESH\_OPTIMIZATION.md](/docs/features/SILENT_REFRESH_OPTIMIZATION.md) | Markdown | 175 | 0 | 44 | 219 |
| [docs/features/VOCABULARY\_MANAGER\_IMPLEMENTATION\_SUMMARY.md](/docs/features/VOCABULARY_MANAGER_IMPLEMENTATION_SUMMARY.md) | Markdown | 156 | 0 | 42 | 198 |
| [docs/features/WORD\_DETAIL\_DIALOG\_IMPLEMENTATION.md](/docs/features/WORD_DETAIL_DIALOG_IMPLEMENTATION.md) | Markdown | 214 | 0 | 57 | 271 |
| [docs/fixes/ACHIEVEMENT\_UNLOCK\_NAVIGATION\_FIX.md](/docs/fixes/ACHIEVEMENT_UNLOCK_NAVIGATION_FIX.md) | Markdown | 161 | 0 | 40 | 201 |
| [docs/fixes/ACTION\_LIBRARY\_SELECTOR\_OVERFLOW\_FIX.md](/docs/fixes/ACTION_LIBRARY_SELECTOR_OVERFLOW_FIX.md) | Markdown | 168 | 0 | 35 | 203 |
| [docs/fixes/BUBBLE\_BORDER\_CONSISTENCY\_FIX.md](/docs/fixes/BUBBLE_BORDER_CONSISTENCY_FIX.md) | Markdown | 135 | 0 | 42 | 177 |
| [docs/fixes/BUBBLE\_CLICK\_POSITION\_FINAL\_ALIGNMENT\_FIX.md](/docs/fixes/BUBBLE_CLICK_POSITION_FINAL_ALIGNMENT_FIX.md) | Markdown | 124 | 0 | 34 | 158 |
| [docs/fixes/BUBBLE\_POSITIONING\_FINAL\_FIX.md](/docs/fixes/BUBBLE_POSITIONING_FINAL_FIX.md) | Markdown | 117 | 0 | 32 | 149 |
| [docs/fixes/BUBBLE\_POSITIONING\_PRECISE\_FIX.md](/docs/fixes/BUBBLE_POSITIONING_PRECISE_FIX.md) | Markdown | 130 | 0 | 43 | 173 |
| [docs/fixes/BUBBLE\_POSITION\_ADJUSTMENT.md](/docs/fixes/BUBBLE_POSITION_ADJUSTMENT.md) | Markdown | 100 | 0 | 29 | 129 |
| [docs/fixes/CLICK\_POSITION\_FIX.md](/docs/fixes/CLICK_POSITION_FIX.md) | Markdown | 126 | 0 | 37 | 163 |
| [docs/fixes/COMPRESSED\_IMAGE\_OFFSET\_FIX.md](/docs/fixes/COMPRESSED_IMAGE_OFFSET_FIX.md) | Markdown | 110 | 0 | 34 | 144 |
| [docs/fixes/COORDINATE\_SYSTEM\_FINAL\_FIX.md](/docs/fixes/COORDINATE_SYSTEM_FINAL_FIX.md) | Markdown | 111 | 0 | 38 | 149 |
| [docs/fixes/CUSTOM\_ACTION\_EDIT\_SAVE\_FIX.md](/docs/fixes/CUSTOM_ACTION_EDIT_SAVE_FIX.md) | Markdown | 131 | 0 | 35 | 166 |
| [docs/fixes/DRAG\_POSITION\_JUMP\_FIX.md](/docs/fixes/DRAG_POSITION_JUMP_FIX.md) | Markdown | 106 | 0 | 29 | 135 |
| [docs/fixes/DYNAMIC\_OFFSET\_ADJUSTMENT\_FIX.md](/docs/fixes/DYNAMIC_OFFSET_ADJUSTMENT_FIX.md) | Markdown | 129 | 0 | 37 | 166 |
| [docs/fixes/EXERCISE\_LIBRARY\_PAGE\_IMPORT\_FIX.md](/docs/fixes/EXERCISE_LIBRARY_PAGE_IMPORT_FIX.md) | Markdown | 91 | 0 | 43 | 134 |
| [docs/fixes/FINAL\_OFFSET\_ADJUSTMENT\_WITH\_DEBUG.md](/docs/fixes/FINAL_OFFSET_ADJUSTMENT_WITH_DEBUG.md) | Markdown | 123 | 0 | 35 | 158 |
| [docs/fixes/FINAL\_PRECISION\_ADJUSTMENT.md](/docs/fixes/FINAL_PRECISION_ADJUSTMENT.md) | Markdown | 101 | 0 | 30 | 131 |
| [docs/fixes/FINE\_TUNING\_OFFSET\_ADJUSTMENT.md](/docs/fixes/FINE_TUNING_OFFSET_ADJUSTMENT.md) | Markdown | 90 | 0 | 29 | 119 |
| [docs/fixes/PROFILE\_AVATAR\_CLICK\_UPDATE.md](/docs/fixes/PROFILE_AVATAR_CLICK_UPDATE.md) | Markdown | 108 | 0 | 37 | 145 |
| [docs/fixes/SNACKBAR\_AND\_COMMUNITY\_FIXES.md](/docs/fixes/SNACKBAR_AND_COMMUNITY_FIXES.md) | Markdown | 163 | 0 | 52 | 215 |
| [docs/fixes/STANDARDIZED\_COORDINATE\_SYSTEM\_IMPLEMENTATION.md](/docs/fixes/STANDARDIZED_COORDINATE_SYSTEM_IMPLEMENTATION.md) | Markdown | 146 | 0 | 46 | 192 |
| [docs/fixes/UNIVERSAL\_COMPRESSED\_IMAGE\_FIX\_VERIFICATION.md](/docs/fixes/UNIVERSAL_COMPRESSED_IMAGE_FIX_VERIFICATION.md) | Markdown | 102 | 0 | 31 | 133 |
| [docs/fixes/USER\_IMPORTED\_PHOTOS\_FIX.md](/docs/fixes/USER_IMPORTED_PHOTOS_FIX.md) | Markdown | 146 | 0 | 44 | 190 |
| [docs/guides/CROSS\_ORIENTATION\_POSITIONING\_SOLUTION\_TEMPLATE.md](/docs/guides/CROSS_ORIENTATION_POSITIONING_SOLUTION_TEMPLATE.md) | Markdown | 471 | 0 | 109 | 580 |
| [docs/guides/DEVELOPER\_ENTRANCE\_GUIDE.md](/docs/guides/DEVELOPER_ENTRANCE_GUIDE.md) | Markdown | 59 | 0 | 24 | 83 |
| [docs/guides/FLOATING\_TIMER\_DEBUG\_GUIDE.md](/docs/guides/FLOATING_TIMER_DEBUG_GUIDE.md) | Markdown | 199 | 0 | 60 | 259 |
| [docs/guides/ICON\_SETUP\_GUIDE.md](/docs/guides/ICON_SETUP_GUIDE.md) | Markdown | 231 | 0 | 28 | 259 |
| [docs/guides/IMAGE\_PICKER\_DEBUG\_GUIDE.md](/docs/guides/IMAGE_PICKER_DEBUG_GUIDE.md) | Markdown | 181 | 0 | 44 | 225 |
| [docs/guides/POSITIONING\_QUICK\_REFERENCE.md](/docs/guides/POSITIONING_QUICK_REFERENCE.md) | Markdown | 133 | 0 | 33 | 166 |
| [docs/guides/PROFILE\_EDIT\_VERIFICATION\_GUIDE.md](/docs/guides/PROFILE_EDIT_VERIFICATION_GUIDE.md) | Markdown | 121 | 0 | 49 | 170 |
| [docs/guides/README.md](/docs/guides/README.md) | Markdown | 91 | 0 | 27 | 118 |
| [docs/guides/SYSTEM\_FLOATING\_TIMER\_USER\_GUIDE.md](/docs/guides/SYSTEM_FLOATING_TIMER_USER_GUIDE.md) | Markdown | 117 | 0 | 41 | 158 |
| [docs/guides/TIMER\_FLOATING\_WINDOW\_GUIDE.md](/docs/guides/TIMER_FLOATING_WINDOW_GUIDE.md) | Markdown | 69 | 0 | 27 | 96 |
| [docs/guides/UI\_DEMO\_GUIDE.md](/docs/guides/UI_DEMO_GUIDE.md) | Markdown | 91 | 0 | 22 | 113 |
| [docs/releases/README.md](/docs/releases/README.md) | Markdown | 107 | 0 | 30 | 137 |
| [docs/testing/ACHIEVEMENT\_SYSTEM\_TEST\_GUIDE.md](/docs/testing/ACHIEVEMENT_SYSTEM_TEST_GUIDE.md) | Markdown | 87 | 0 | 28 | 115 |
| [docs/testing/BUBBLE\_OFFSET\_DEBUGGING.md](/docs/testing/BUBBLE_OFFSET_DEBUGGING.md) | Markdown | 121 | 0 | 30 | 151 |
| [docs/testing/COMPREHENSIVE\_TEST\_GUIDE.md](/docs/testing/COMPREHENSIVE_TEST_GUIDE.md) | Markdown | 276 | 0 | 96 | 372 |
| [docs/testing/COVER\_MODE\_TEST\_GUIDE.md](/docs/testing/COVER_MODE_TEST_GUIDE.md) | Markdown | 115 | 0 | 35 | 150 |
| [docs/testing/DEBUGGING\_STEP\_1\_TEST\_GUIDE.md](/docs/testing/DEBUGGING_STEP_1_TEST_GUIDE.md) | Markdown | 115 | 0 | 27 | 142 |
| [docs/testing/DEBUG\_PHASE\_1\_COMPLETE.md](/docs/testing/DEBUG_PHASE_1_COMPLETE.md) | Markdown | 93 | 0 | 31 | 124 |
| [docs/testing/DRAGGABLE\_ANNOTATION\_SYSTEM\_TEST.md](/docs/testing/DRAGGABLE_ANNOTATION_SYSTEM_TEST.md) | Markdown | 162 | 0 | 47 | 209 |
| [docs/testing/DRAG\_FEATURE\_DEBUG\_GUIDE.md](/docs/testing/DRAG_FEATURE_DEBUG_GUIDE.md) | Markdown | 138 | 0 | 33 | 171 |
| [docs/testing/FLOATING\_WINDOW\_TEST\_CHECKLIST.md](/docs/testing/FLOATING_WINDOW_TEST_CHECKLIST.md) | Markdown | 114 | 0 | 27 | 141 |
| [docs/testing/MEMORY\_ALBUM\_DRAG\_FEATURE\_TEST.md](/docs/testing/MEMORY_ALBUM_DRAG_FEATURE_TEST.md) | Markdown | 97 | 0 | 36 | 133 |
| [docs/testing/POMODORO\_TEST\_GUIDE.md](/docs/testing/POMODORO_TEST_GUIDE.md) | Markdown | 105 | 0 | 20 | 125 |
| [docs/testing/QUICK\_TEST\_STEPS.md](/docs/testing/QUICK_TEST_STEPS.md) | Markdown | 85 | 0 | 26 | 111 |
| [docs/testing/README.md](/docs/testing/README.md) | Markdown | 92 | 0 | 30 | 122 |
| [docs/testing/STUDY\_SESSION\_COMPLETION\_VERIFICATION.md](/docs/testing/STUDY_SESSION_COMPLETION_VERIFICATION.md) | Markdown | 153 | 0 | 38 | 191 |
| [docs/testing/TEMP\_BUBBLE\_DRAG\_DEBUG\_GUIDE.md](/docs/testing/TEMP_BUBBLE_DRAG_DEBUG_GUIDE.md) | Markdown | 125 | 0 | 34 | 159 |
| [docs/testing/TESTING\_GUIDE.md](/docs/testing/TESTING_GUIDE.md) | Markdown | 103 | 0 | 34 | 137 |
| [docs/testing/TIMER\_TEST\_GUIDE.md](/docs/testing/TIMER_TEST_GUIDE.md) | Markdown | 121 | 0 | 32 | 153 |
| [docs/testing/USER\_IMPORTED\_PHOTOS\_DEBUG\_GUIDE.md](/docs/testing/USER_IMPORTED_PHOTOS_DEBUG_GUIDE.md) | Markdown | 111 | 0 | 33 | 144 |
| [docs/troubleshooting/ANNOTATION\_SCALE\_FIX\_SUMMARY.md](/docs/troubleshooting/ANNOTATION_SCALE_FIX_SUMMARY.md) | Markdown | 154 | 0 | 43 | 197 |
| [docs/troubleshooting/ANNOTATION\_SCALE\_FIX\_TEST\_GUIDE.md](/docs/troubleshooting/ANNOTATION_SCALE_FIX_TEST_GUIDE.md) | Markdown | 144 | 0 | 43 | 187 |
| [docs/troubleshooting/BOTTOM\_NAVIGATION\_BAR\_FIX.md](/docs/troubleshooting/BOTTOM_NAVIGATION_BAR_FIX.md) | Markdown | 244 | 0 | 50 | 294 |
| [docs/troubleshooting/CALENDAR\_TASK\_DISPLAY\_FIX.md](/docs/troubleshooting/CALENDAR_TASK_DISPLAY_FIX.md) | Markdown | 219 | 0 | 39 | 258 |
| [docs/troubleshooting/COLOR\_SYSTEM\_RESTORATION.md](/docs/troubleshooting/COLOR_SYSTEM_RESTORATION.md) | Markdown | 134 | 0 | 34 | 168 |
| [docs/troubleshooting/COMPREHENSIVE\_NAVIGATION\_AUDIT.md](/docs/troubleshooting/COMPREHENSIVE_NAVIGATION_AUDIT.md) | Markdown | 192 | 0 | 48 | 240 |
| [docs/troubleshooting/DIALOG\_LAYOUT\_BUGFIX.md](/docs/troubleshooting/DIALOG_LAYOUT_BUGFIX.md) | Markdown | 206 | 0 | 42 | 248 |
| [docs/troubleshooting/DIALOG\_LAYOUT\_OPTIMIZATION.md](/docs/troubleshooting/DIALOG_LAYOUT_OPTIMIZATION.md) | Markdown | 192 | 0 | 46 | 238 |
| [docs/troubleshooting/EXERCISE\_LIBRARY\_UI\_OPTIMIZATION.md](/docs/troubleshooting/EXERCISE_LIBRARY_UI_OPTIMIZATION.md) | Markdown | 111 | 0 | 36 | 147 |
| [docs/troubleshooting/FINAL\_NAVIGATION\_TEST\_REPORT.md](/docs/troubleshooting/FINAL_NAVIGATION_TEST_REPORT.md) | Markdown | 118 | 0 | 38 | 156 |
| [docs/troubleshooting/FLOATING\_TIMER\_FIXES\_VERIFICATION.md](/docs/troubleshooting/FLOATING_TIMER_FIXES_VERIFICATION.md) | Markdown | 190 | 0 | 50 | 240 |
| [docs/troubleshooting/FLOATING\_WINDOW\_FINAL\_OPTIMIZATION.md](/docs/troubleshooting/FLOATING_WINDOW_FINAL_OPTIMIZATION.md) | Markdown | 142 | 0 | 35 | 177 |
| [docs/troubleshooting/FLOATING\_WINDOW\_OPTIMIZATION\_SUMMARY.md](/docs/troubleshooting/FLOATING_WINDOW_OPTIMIZATION_SUMMARY.md) | Markdown | 129 | 0 | 35 | 164 |
| [docs/troubleshooting/HOW\_TO\_TEST\_NAVIGATION\_FIX.md](/docs/troubleshooting/HOW_TO_TEST_NAVIGATION_FIX.md) | Markdown | 147 | 0 | 51 | 198 |
| [docs/troubleshooting/IPAD\_STAGE\_MANAGER\_LAYOUT\_FIX.md](/docs/troubleshooting/IPAD_STAGE_MANAGER_LAYOUT_FIX.md) | Markdown | 200 | 0 | 31 | 231 |
| [docs/troubleshooting/KNOWLEDGE\_POINT\_SYNC\_FIX.md](/docs/troubleshooting/KNOWLEDGE_POINT_SYNC_FIX.md) | Markdown | 191 | 0 | 42 | 233 |
| [docs/troubleshooting/NAVIGATION\_FIX\_PROGRESS\_REPORT.md](/docs/troubleshooting/NAVIGATION_FIX_PROGRESS_REPORT.md) | Markdown | 121 | 0 | 38 | 159 |
| [docs/troubleshooting/NAVIGATION\_FIX\_SUMMARY.md](/docs/troubleshooting/NAVIGATION_FIX_SUMMARY.md) | Markdown | 133 | 0 | 39 | 172 |
| [docs/troubleshooting/PDF\_COLOR\_RESTORATION.md](/docs/troubleshooting/PDF_COLOR_RESTORATION.md) | Markdown | 160 | 0 | 41 | 201 |
| [docs/troubleshooting/PROBLEM\_SOLUTIONS.md](/docs/troubleshooting/PROBLEM_SOLUTIONS.md) | Markdown | 317 | 0 | 112 | 429 |
| [docs/troubleshooting/RADAR\_CHART\_TECHNICAL\_ANALYSIS.md](/docs/troubleshooting/RADAR_CHART_TECHNICAL_ANALYSIS.md) | Markdown | 103 | 0 | 29 | 132 |
| [docs/troubleshooting/README.md](/docs/troubleshooting/README.md) | Markdown | 54 | 0 | 22 | 76 |
| [docs/troubleshooting/SIDEBAR\_BUTTON\_OPTIMIZATION.md](/docs/troubleshooting/SIDEBAR_BUTTON_OPTIMIZATION.md) | Markdown | 147 | 0 | 37 | 184 |
| [docs/troubleshooting/TIMEBOX\_REST\_SKIP\_NAVIGATION\_FIX.md](/docs/troubleshooting/TIMEBOX_REST_SKIP_NAVIGATION_FIX.md) | Markdown | 157 | 0 | 52 | 209 |
| [docs/troubleshooting/TIMEBOX\_TIMER\_FIXES.md](/docs/troubleshooting/TIMEBOX_TIMER_FIXES.md) | Markdown | 115 | 0 | 27 | 142 |
| [docs/troubleshooting/TIMER\_FIXES\_PROGRESS.md](/docs/troubleshooting/TIMER_FIXES_PROGRESS.md) | Markdown | 131 | 0 | 35 | 166 |
| [docs/troubleshooting/UI\_OVERFLOW\_FIXES\_SUMMARY.md](/docs/troubleshooting/UI_OVERFLOW_FIXES_SUMMARY.md) | Markdown | 98 | 0 | 29 | 127 |
| [docs/troubleshooting/VOCABULARY\_SERVICE\_FIX.md](/docs/troubleshooting/VOCABULARY_SERVICE_FIX.md) | Markdown | 219 | 0 | 52 | 271 |
| [docs/troubleshooting/WORD\_TRAINING\_OPTIMIZATION.md](/docs/troubleshooting/WORD_TRAINING_OPTIMIZATION.md) | Markdown | 108 | 0 | 39 | 147 |
| [docs/ui\_improvements/CATEGORY\_SELECTOR\_PURE\_TEXT\_FIX.md](/docs/ui_improvements/CATEGORY_SELECTOR_PURE_TEXT_FIX.md) | Markdown | 118 | 0 | 28 | 146 |
| [docs/ui\_improvements/IMAGE\_COMPRESSION\_IMPROVEMENTS.md](/docs/ui_improvements/IMAGE_COMPRESSION_IMPROVEMENTS.md) | Markdown | 103 | 0 | 38 | 141 |
| [docs/ui\_improvements/IPAD\_CALENDAR\_OVERFLOW\_FIX\_COMPLETE.md](/docs/ui_improvements/IPAD_CALENDAR_OVERFLOW_FIX_COMPLETE.md) | Markdown | 129 | 0 | 33 | 162 |
| [docs/ui\_improvements/IPAD\_CALENDAR\_RESPONSIVE\_FIX.md](/docs/ui_improvements/IPAD_CALENDAR_RESPONSIVE_FIX.md) | Markdown | 178 | 0 | 38 | 216 |
| [docs/ui\_improvements/REMOVE\_DUPLICATE\_MENU\_ITEMS.md](/docs/ui_improvements/REMOVE_DUPLICATE_MENU_ITEMS.md) | Markdown | 111 | 0 | 36 | 147 |
| [docs/ui\_improvements/UI\_IMPROVEMENTS.md](/docs/ui_improvements/UI_IMPROVEMENTS.md) | Markdown | 116 | 0 | 42 | 158 |
| [example/achievement\_wage\_example.dart](/example/achievement_wage_example.dart) | Dart | 106 | 19 | 28 | 153 |
| [example/calendar\_responsive\_demo.dart](/example/calendar_responsive_demo.dart) | Dart | 342 | 16 | 24 | 382 |
| [example/calendar\_task\_display\_demo.dart](/example/calendar_task_display_demo.dart) | Dart | 396 | 20 | 25 | 441 |
| [example/color\_scheme\_comparison\_demo.dart](/example/color_scheme_comparison_demo.dart) | Dart | 434 | 16 | 31 | 481 |
| [example/navigation\_bottom\_bar\_demo.dart](/example/navigation_bottom_bar_demo.dart) | Dart | 366 | 9 | 15 | 390 |
| [example/responsive\_layout\_demo.dart](/example/responsive_layout_demo.dart) | Dart | 560 | 13 | 26 | 599 |
| [example/study\_session\_completion\_demo.dart](/example/study_session_completion_demo.dart) | Dart | 429 | 7 | 24 | 460 |
| [example/timebox\_rest\_skip\_demo.dart](/example/timebox_rest_skip_demo.dart) | Dart | 458 | 24 | 40 | 522 |
| [ios/OneDayWidget/AppIntent.swift](/ios/OneDayWidget/AppIntent.swift) | Swift | 8 | 7 | 4 | 19 |
| [ios/OneDayWidget/OneDayWidgetBundle.swift](/ios/OneDayWidget/OneDayWidgetBundle.swift) | Swift | 8 | 6 | 3 | 17 |
| [ios/OneDayWidget/OneDayWidgetControl.swift](/ios/OneDayWidget/OneDayWidgetControl.swift) | Swift | 56 | 7 | 15 | 78 |
| [ios/Podfile](/ios/Podfile) | Ruby | 32 | 2 | 10 | 44 |
| [ios/Runner/AppDelegate.swift](/ios/Runner/AppDelegate.swift) | Swift | 12 | 0 | 2 | 14 |
| [ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 116 | 0 | 1 | 117 |
| [ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json](/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json) | JSON | 23 | 0 | 1 | 24 |
| [ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md](/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md) | Markdown | 3 | 0 | 2 | 5 |
| [ios/Runner/Base.lproj/LaunchScreen.storyboard](/ios/Runner/Base.lproj/LaunchScreen.storyboard) | XML | 36 | 1 | 1 | 38 |
| [ios/Runner/Base.lproj/Main.storyboard](/ios/Runner/Base.lproj/Main.storyboard) | XML | 25 | 1 | 1 | 27 |
| [ios/Runner/Runner-Bridging-Header.h](/ios/Runner/Runner-Bridging-Header.h) | C++ | 1 | 0 | 1 | 2 |
| [ios/RunnerTests/RunnerTests.swift](/ios/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [l10n.yaml](/l10n.yaml) | YAML | 4 | 0 | 1 | 5 |
| [lib/core/constants/app\_icons.dart](/lib/core/constants/app_icons.dart) | Dart | 35 | 5 | 3 | 43 |
| [lib/core/data/pao\_exercises\_data.dart](/lib/core/data/pao_exercises_data.dart) | Dart | 1,587 | 28 | 26 | 1,641 |
| [lib/debug/image\_cropper\_test\_page.dart](/lib/debug/image_cropper_test_page.dart) | Dart | 228 | 11 | 22 | 261 |
| [lib/debug/image\_picker\_debug\_page.dart](/lib/debug/image_picker_debug_page.dart) | Dart | 264 | 14 | 32 | 310 |
| [lib/debug/profanity\_filter\_manual\_test\_page.dart](/lib/debug/profanity_filter_manual_test_page.dart) | Dart | 291 | 10 | 31 | 332 |
| [lib/debug/profanity\_filter\_test\_page.dart](/lib/debug/profanity_filter_test_page.dart) | Dart | 189 | 2 | 28 | 219 |
| [lib/debug/profile\_save\_test\_page.dart](/lib/debug/profile_save_test_page.dart) | Dart | 276 | 20 | 34 | 330 |
| [lib/debug/radar\_test\_page.dart](/lib/debug/radar_test_page.dart) | Dart | 69 | 2 | 7 | 78 |
| [lib/debug/route\_debug\_page.dart](/lib/debug/route_debug_page.dart) | Dart | 268 | 10 | 15 | 293 |
| [lib/features/ability\_radar/models/ability\_radar\_models.dart](/lib/features/ability_radar/models/ability_radar_models.dart) | Dart | 222 | 44 | 65 | 331 |
| [lib/features/ability\_radar/models/ability\_radar\_models.g.dart](/lib/features/ability_radar/models/ability_radar_models.g.dart) | Dart | 199 | 32 | 18 | 249 |
| [lib/features/ability\_radar/pages/ability\_radar\_page.dart](/lib/features/ability_radar/pages/ability_radar_page.dart) | Dart | 635 | 52 | 58 | 745 |
| [lib/features/ability\_radar/providers/ability\_radar\_mock\_providers.dart](/lib/features/ability_radar/providers/ability_radar_mock_providers.dart) | Dart | 140 | 5 | 6 | 151 |
| [lib/features/ability\_radar/providers/ability\_radar\_providers.dart](/lib/features/ability_radar/providers/ability_radar_providers.dart) | Dart | 303 | 21 | 57 | 381 |
| [lib/features/ability\_radar/services/ability\_radar\_service.dart](/lib/features/ability_radar/services/ability_radar_service.dart) | Dart | 461 | 45 | 106 | 612 |
| [lib/features/ability\_radar/services/radar\_share\_service.dart](/lib/features/ability_radar/services/radar_share_service.dart) | Dart | 344 | 59 | 71 | 474 |
| [lib/features/ability\_radar/widgets/ability\_radar\_chart.dart](/lib/features/ability_radar/widgets/ability_radar_chart.dart) | Dart | 337 | 31 | 27 | 395 |
| [lib/features/ability\_radar/widgets/game\_style\_rank\_widget.dart](/lib/features/ability_radar/widgets/game_style_rank_widget.dart) | Dart | 242 | 20 | 31 | 293 |
| [lib/features/ability\_radar/widgets/radar\_community\_share\_dialog.dart](/lib/features/ability_radar/widgets/radar_community_share_dialog.dart) | Dart | 411 | 24 | 33 | 468 |
| [lib/features/ability\_radar/widgets/radar\_share\_panel.dart](/lib/features/ability_radar/widgets/radar_share_panel.dart) | Dart | 259 | 26 | 26 | 311 |
| [lib/features/achievement/README.md](/lib/features/achievement/README.md) | Markdown | 132 | 0 | 41 | 173 |
| [lib/features/achievement/data/achievements\_data.dart](/lib/features/achievement/data/achievements_data.dart) | Dart | 444 | 18 | 12 | 474 |
| [lib/features/achievement/models/achievement.dart](/lib/features/achievement/models/achievement.dart) | Dart | 169 | 29 | 39 | 237 |
| [lib/features/achievement/models/achievement.g.dart](/lib/features/achievement/models/achievement.g.dart) | Dart | 125 | 24 | 11 | 160 |
| [lib/features/achievement/models/badge.dart](/lib/features/achievement/models/badge.dart) | Dart | 230 | 50 | 69 | 349 |
| [lib/features/achievement/models/badge.g.dart](/lib/features/achievement/models/badge.g.dart) | Dart | 184 | 40 | 17 | 241 |
| [lib/features/achievement/models/user\_level.dart](/lib/features/achievement/models/user_level.dart) | Dart | 266 | 41 | 48 | 355 |
| [lib/features/achievement/models/user\_level.g.dart](/lib/features/achievement/models/user_level.g.dart) | Dart | 91 | 21 | 11 | 123 |
| [lib/features/achievement/pages/achievement\_page.dart](/lib/features/achievement/pages/achievement_page.dart) | Dart | 485 | 21 | 39 | 545 |
| [lib/features/achievement/pages/leaderboard\_page.dart](/lib/features/achievement/pages/leaderboard_page.dart) | Dart | 336 | 13 | 20 | 369 |
| [lib/features/achievement/providers/achievement\_provider.dart](/lib/features/achievement/providers/achievement_provider.dart) | Dart | 224 | 38 | 44 | 306 |
| [lib/features/achievement/services/achievement\_service.dart](/lib/features/achievement/services/achievement_service.dart) | Dart | 332 | 38 | 66 | 436 |
| [lib/features/achievement/services/achievement\_trigger\_service.dart](/lib/features/achievement/services/achievement_trigger_service.dart) | Dart | 113 | 61 | 63 | 237 |
| [lib/features/achievement/widgets/achievement\_grid.dart](/lib/features/achievement/widgets/achievement_grid.dart) | Dart | 380 | 19 | 32 | 431 |
| [lib/features/achievement/widgets/achievement\_unlock\_notification.dart](/lib/features/achievement/widgets/achievement_unlock_notification.dart) | Dart | 330 | 21 | 47 | 398 |
| [lib/features/achievement/widgets/skill\_levels\_card.dart](/lib/features/achievement/widgets/skill_levels_card.dart) | Dart | 323 | 15 | 42 | 380 |
| [lib/features/achievement/widgets/user\_level\_card.dart](/lib/features/achievement/widgets/user_level_card.dart) | Dart | 326 | 7 | 29 | 362 |
| [lib/features/auth/forgot\_password\_page.dart](/lib/features/auth/forgot_password_page.dart) | Dart | 266 | 17 | 20 | 303 |
| [lib/features/auth/login\_page.dart](/lib/features/auth/login_page.dart) | Dart | 710 | 42 | 48 | 800 |
| [lib/features/auth/phone\_login\_page.dart](/lib/features/auth/phone_login_page.dart) | Dart | 394 | 20 | 30 | 444 |
| [lib/features/auth/providers/phone\_login\_provider.dart](/lib/features/auth/providers/phone_login_provider.dart) | Dart | 228 | 25 | 38 | 291 |
| [lib/features/auth/providers/wechat\_login\_provider.dart](/lib/features/auth/providers/wechat_login_provider.dart) | Dart | 137 | 13 | 25 | 175 |
| [lib/features/auth/register\_page.dart](/lib/features/auth/register_page.dart) | Dart | 481 | 28 | 32 | 541 |
| [lib/features/auth/reset\_password\_page.dart](/lib/features/auth/reset_password_page.dart) | Dart | 316 | 18 | 22 | 356 |
| [lib/features/auth/services/sms\_service.dart](/lib/features/auth/services/sms_service.dart) | Dart | 174 | 31 | 46 | 251 |
| [lib/features/auth/services/wechat\_auth\_service.dart](/lib/features/auth/services/wechat_auth_service.dart) | Dart | 85 | 120 | 33 | 238 |
| [lib/features/calendar/calendar\_page.dart](/lib/features/calendar/calendar_page.dart) | Dart | 1,094 | 89 | 77 | 1,260 |
| [lib/features/community/article\_import\_service.dart](/lib/features/community/article_import_service.dart) | Dart | 244 | 29 | 43 | 316 |
| [lib/features/community/community\_feed\_page.dart](/lib/features/community/community_feed_page.dart) | Dart | 1,291 | 73 | 89 | 1,453 |
| [lib/features/community/community\_post\_editor\_page.dart](/lib/features/community/community_post_editor_page.dart) | Dart | 905 | 52 | 79 | 1,036 |
| [lib/features/community/community\_storage\_service.dart](/lib/features/community/community_storage_service.dart) | Dart | 121 | 11 | 20 | 152 |
| [lib/features/community/content\_report\_dialog.dart](/lib/features/community/content_report_dialog.dart) | Dart | 364 | 7 | 18 | 389 |
| [lib/features/community/content\_report\_service.dart](/lib/features/community/content_report_service.dart) | Dart | 347 | 33 | 57 | 437 |
| [lib/features/community/profanity\_filter\_service.dart](/lib/features/community/profanity_filter_service.dart) | Dart | 368 | 34 | 60 | 462 |
| [lib/features/community/profanity\_filter\_settings\_page.dart](/lib/features/community/profanity_filter_settings_page.dart) | Dart | 722 | 16 | 30 | 768 |
| [lib/features/daily\_plan/models/daily\_plan.dart](/lib/features/daily_plan/models/daily_plan.dart) | Dart | 151 | 29 | 34 | 214 |
| [lib/features/daily\_plan/models/daily\_plan.g.dart](/lib/features/daily_plan/models/daily_plan.g.dart) | Dart | 49 | 13 | 7 | 69 |
| [lib/features/daily\_plan/notifiers/daily\_plan\_notifier.dart](/lib/features/daily_plan/notifiers/daily_plan_notifier.dart) | Dart | 204 | 31 | 44 | 279 |
| [lib/features/daily\_plan/services/daily\_plan\_storage\_service.dart](/lib/features/daily_plan/services/daily_plan_storage_service.dart) | Dart | 216 | 33 | 48 | 297 |
| [lib/features/exercise/action\_library\_category\_manager.dart](/lib/features/exercise/action_library_category_manager.dart) | Dart | 312 | 24 | 33 | 369 |
| [lib/features/exercise/add\_to\_memory\_dialog.dart](/lib/features/exercise/add_to_memory_dialog.dart) | Dart | 182 | 10 | 17 | 209 |
| [lib/features/exercise/create\_action\_library\_dialog.dart](/lib/features/exercise/create_action_library_dialog.dart) | Dart | 345 | 7 | 20 | 372 |
| [lib/features/exercise/create\_custom\_library\_dialog.dart](/lib/features/exercise/create_custom_library_dialog.dart) | Dart | 297 | 7 | 17 | 321 |
| [lib/features/exercise/custom\_action\_editor\_dialog.dart](/lib/features/exercise/custom_action_editor_dialog.dart) | Dart | 535 | 19 | 39 | 593 |
| [lib/features/exercise/custom\_action\_library.dart](/lib/features/exercise/custom_action_library.dart) | Dart | 207 | 25 | 29 | 261 |
| [lib/features/exercise/custom\_action\_library\_service.dart](/lib/features/exercise/custom_action_library_service.dart) | Dart | 258 | 33 | 49 | 340 |
| [lib/features/exercise/custom\_exercise\_category.dart](/lib/features/exercise/custom_exercise_category.dart) | Dart | 299 | 37 | 50 | 386 |
| [lib/features/exercise/custom\_library\_editor\_page.dart](/lib/features/exercise/custom_library_editor_page.dart) | Dart | 714 | 42 | 54 | 810 |
| [lib/features/exercise/exercise\_library\_page.dart](/lib/features/exercise/exercise_library_page.dart) | Dart | 4,769 | 304 | 311 | 5,384 |
| [lib/features/exercise/exercise\_session\_page.dart](/lib/features/exercise/exercise_session_page.dart) | Dart | 998 | 71 | 83 | 1,152 |
| [lib/features/exercise/focused\_training\_page.dart](/lib/features/exercise/focused_training_page.dart) | Dart | 461 | 18 | 41 | 520 |
| [lib/features/exercise/manage\_custom\_libraries\_dialog.dart](/lib/features/exercise/manage_custom_libraries_dialog.dart) | Dart | 2,005 | 103 | 122 | 2,230 |
| [lib/features/exercise/pao\_integration\_service.dart](/lib/features/exercise/pao_integration_service.dart) | Dart | 206 | 32 | 45 | 283 |
| [lib/features/exercise/providers/exercise\_providers.dart](/lib/features/exercise/providers/exercise_providers.dart) | Dart | 322 | 32 | 45 | 399 |
| [lib/features/help\_feedback/help\_feedback\_page.dart](/lib/features/help_feedback/help_feedback_page.dart) | Dart | 708 | 39 | 53 | 800 |
| [lib/features/help\_feedback/user\_guide\_page.dart](/lib/features/help_feedback/user_guide_page.dart) | Dart | 352 | 11 | 32 | 395 |
| [lib/features/home/<USER>/lib/features/home/<USER>
| [lib/features/learning\_report/learning\_report\_page.dart](/lib/features/learning_report/learning_report_page.dart) | Dart | 795 | 40 | 58 | 893 |
| [lib/features/learning\_report/models/learning\_report\_models.dart](/lib/features/learning_report/models/learning_report_models.dart) | Dart | 203 | 61 | 77 | 341 |
| [lib/features/learning\_report/models/learning\_report\_models.g.dart](/lib/features/learning_report/models/learning_report_models.g.dart) | Dart | 301 | 51 | 25 | 377 |
| [lib/features/learning\_report/providers/learning\_report\_providers.dart](/lib/features/learning_report/providers/learning_report_providers.dart) | Dart | 148 | 32 | 40 | 220 |
| [lib/features/learning\_report/services/learning\_report\_export\_service.dart](/lib/features/learning_report/services/learning_report_export_service.dart) | Dart | 556 | 116 | 57 | 729 |
| [lib/features/learning\_report/services/learning\_report\_service.dart](/lib/features/learning_report/services/learning_report_service.dart) | Dart | 474 | 61 | 100 | 635 |
| [lib/features/learning\_report/widgets/chart\_widgets.dart](/lib/features/learning_report/widgets/chart_widgets.dart) | Dart | 858 | 36 | 48 | 942 |
| [lib/features/main/main\_container\_page.dart](/lib/features/main/main_container_page.dart) | Dart | 105 | 6 | 10 | 121 |
| [lib/features/memory\_palace/palace\_manager\_page.dart](/lib/features/memory_palace/palace_manager_page.dart) | Dart | 5,078 | 381 | 452 | 5,911 |
| [lib/features/memory\_palace/providers/memory\_palace\_provider.dart](/lib/features/memory_palace/providers/memory_palace_provider.dart) | Dart | 191 | 18 | 33 | 242 |
| [lib/features/memory\_palace/scene\_detail\_page.dart](/lib/features/memory_palace/scene_detail_page.dart) | Dart | 3,901 | 620 | 643 | 5,164 |
| [lib/features/memory\_palace/scene\_detail\_page\_copy.dart](/lib/features/memory_palace/scene_detail_page_copy.dart) | Dart | 1,596 | 167 | 199 | 1,962 |
| [lib/features/memory\_palace/utils/anchor\_data\_migration.dart](/lib/features/memory_palace/utils/anchor_data_migration.dart) | Dart | 214 | 30 | 51 | 295 |
| [lib/features/memory\_palace/utils/image\_coordinate\_system.dart](/lib/features/memory_palace/utils/image_coordinate_system.dart) | Dart | 195 | 30 | 42 | 267 |
| [lib/features/onboarding/notion\_style\_onboarding\_page.dart](/lib/features/onboarding/notion_style_onboarding_page.dart) | Dart | 246 | 18 | 25 | 289 |
| [lib/features/onboarding/onboarding\_page.dart](/lib/features/onboarding/onboarding_page.dart) | Dart | 770 | 59 | 76 | 905 |
| [lib/features/photo\_album/photo\_album\_creator\_page.dart](/lib/features/photo_album/photo_album_creator_page.dart) | Dart | 792 | 48 | 78 | 918 |
| [lib/features/profile/models/user\_profile.dart](/lib/features/profile/models/user_profile.dart) | Dart | 84 | 14 | 21 | 119 |
| [lib/features/profile/models/user\_profile.g.dart](/lib/features/profile/models/user_profile.g.dart) | Dart | 31 | 11 | 6 | 48 |
| [lib/features/profile/pages/profile\_edit\_page.dart](/lib/features/profile/pages/profile_edit_page.dart) | Dart | 851 | 45 | 74 | 970 |
| [lib/features/profile/profile\_page.dart](/lib/features/profile/profile_page.dart) | Dart | 825 | 44 | 70 | 939 |
| [lib/features/profile/providers/user\_profile\_provider.dart](/lib/features/profile/providers/user_profile_provider.dart) | Dart | 189 | 20 | 35 | 244 |
| [lib/features/profile/services/user\_profile\_service.dart](/lib/features/profile/services/user_profile_service.dart) | Dart | 183 | 23 | 38 | 244 |
| [lib/features/reflection/reflection\_log\_page.dart](/lib/features/reflection/reflection_log_page.dart) | Dart | 1,327 | 66 | 136 | 1,529 |
| [lib/features/settings/settings\_page.dart](/lib/features/settings/settings_page.dart) | Dart | 776 | 56 | 68 | 900 |
| [lib/features/splash/splash\_page.dart](/lib/features/splash/splash_page.dart) | Dart | 177 | 12 | 23 | 212 |
| [lib/features/study\_time/models/study\_time\_models.dart](/lib/features/study_time/models/study_time_models.dart) | Dart | 264 | 58 | 68 | 390 |
| [lib/features/study\_time/models/study\_time\_models.g.dart](/lib/features/study_time/models/study_time_models.g.dart) | Dart | 175 | 34 | 12 | 221 |
| [lib/features/study\_time/providers/study\_time\_providers.dart](/lib/features/study_time/providers/study_time_providers.dart) | Dart | 310 | 26 | 51 | 387 |
| [lib/features/study\_time/services/study\_time\_statistics\_service.dart](/lib/features/study_time/services/study_time_statistics_service.dart) | Dart | 348 | 65 | 86 | 499 |
| [lib/features/study\_time/test\_data\_sync\_page.dart](/lib/features/study_time/test_data_sync_page.dart) | Dart | 253 | 11 | 17 | 281 |
| [lib/features/time\_box/managers/task\_category\_manager.dart](/lib/features/time_box/managers/task_category_manager.dart) | Dart | 311 | 47 | 54 | 412 |
| [lib/features/time\_box/models/timebox\_models.dart](/lib/features/time_box/models/timebox_models.dart) | Dart | 355 | 58 | 72 | 485 |
| [lib/features/time\_box/models/timebox\_models.g.dart](/lib/features/time_box/models/timebox_models.g.dart) | Dart | 71 | 15 | 8 | 94 |
| [lib/features/time\_box/pages/task\_category\_management\_page.dart](/lib/features/time_box/pages/task_category_management_page.dart) | Dart | 489 | 14 | 35 | 538 |
| [lib/features/time\_box/providers/timebox\_provider.dart](/lib/features/time_box/providers/timebox_provider.dart) | Dart | 261 | 27 | 32 | 320 |
| [lib/features/time\_box/timebox\_list\_page.dart](/lib/features/time_box/timebox_list_page.dart) | Dart | 3,495 | 261 | 245 | 4,001 |
| [lib/features/time\_box/widgets/study\_session\_completion\_dialog.dart](/lib/features/time_box/widgets/study_session_completion_dialog.dart) | Dart | 405 | 6 | 21 | 432 |
| [lib/features/vocabulary/create\_vocabulary\_page.dart](/lib/features/vocabulary/create_vocabulary_page.dart) | Dart | 452 | 11 | 39 | 502 |
| [lib/features/vocabulary/custom\_vocabulary\_manager\_page.dart](/lib/features/vocabulary/custom_vocabulary_manager_page.dart) | Dart | 518 | 30 | 38 | 586 |
| [lib/features/vocabulary/fsrs\_algorithm.dart](/lib/features/vocabulary/fsrs_algorithm.dart) | Dart | 223 | 44 | 40 | 307 |
| [lib/features/vocabulary/fsrs\_service.dart](/lib/features/vocabulary/fsrs_service.dart) | Dart | 316 | 115 | 70 | 501 |
| [lib/features/vocabulary/graduate\_vocabulary\_manager\_page.dart](/lib/features/vocabulary/graduate_vocabulary_manager_page.dart) | Dart | 598 | 37 | 49 | 684 |
| [lib/features/vocabulary/providers/vocabulary\_providers.dart](/lib/features/vocabulary/providers/vocabulary_providers.dart) | Dart | 263 | 27 | 43 | 333 |
| [lib/features/vocabulary/vocabulary\_cache\_manager.dart](/lib/features/vocabulary/vocabulary_cache_manager.dart) | Dart | 248 | 33 | 64 | 345 |
| [lib/features/vocabulary/vocabulary\_category\_page.dart](/lib/features/vocabulary/vocabulary_category_page.dart) | Dart | 1,358 | 71 | 97 | 1,526 |
| [lib/features/vocabulary/vocabulary\_learning\_service.dart](/lib/features/vocabulary/vocabulary_learning_service.dart) | Dart | 232 | 28 | 47 | 307 |
| [lib/features/vocabulary/vocabulary\_manager\_page.dart](/lib/features/vocabulary/vocabulary_manager_page.dart) | Dart | 1,002 | 42 | 75 | 1,119 |
| [lib/features/vocabulary/vocabulary\_page.dart](/lib/features/vocabulary/vocabulary_page.dart) | Dart | 188 | 2 | 13 | 203 |
| [lib/features/vocabulary/vocabulary\_service.dart](/lib/features/vocabulary/vocabulary_service.dart) | Dart | 636 | 83 | 129 | 848 |
| [lib/features/vocabulary/vocabulary\_statistics\_page.dart](/lib/features/vocabulary/vocabulary_statistics_page.dart) | Dart | 845 | 29 | 83 | 957 |
| [lib/features/vocabulary/word\_meaning\_service.dart](/lib/features/vocabulary/word_meaning_service.dart) | Dart | 161 | 17 | 32 | 210 |
| [lib/features/vocabulary/word\_model.dart](/lib/features/vocabulary/word_model.dart) | Dart | 590 | 21 | 43 | 654 |
| [lib/features/vocabulary/word\_root\_service.dart](/lib/features/vocabulary/word_root_service.dart) | Dart | 188 | 28 | 37 | 253 |
| [lib/features/wage\_system/models/item\_effect\_models.dart](/lib/features/wage_system/models/item_effect_models.dart) | Dart | 220 | 22 | 28 | 270 |
| [lib/features/wage\_system/models/wage\_transaction.dart](/lib/features/wage_system/models/wage_transaction.dart) | Dart | 125 | 5 | 13 | 143 |
| [lib/features/wage\_system/providers/item\_effect\_provider.dart](/lib/features/wage_system/providers/item_effect_provider.dart) | Dart | 42 | 9 | 11 | 62 |
| [lib/features/wage\_system/services/item\_effect\_service.dart](/lib/features/wage_system/services/item_effect_service.dart) | Dart | 232 | 42 | 42 | 316 |
| [lib/features/wage\_system/services/item\_timebox\_integration\_service.dart](/lib/features/wage_system/services/item_timebox_integration_service.dart) | Dart | 176 | 47 | 35 | 258 |
| [lib/features/wage\_system/services/premium\_article\_service.dart](/lib/features/wage_system/services/premium_article_service.dart) | Dart | 259 | 35 | 51 | 345 |
| [lib/features/wage\_system/services/wage\_service.dart](/lib/features/wage_system/services/wage_service.dart) | Dart | 207 | 18 | 38 | 263 |
| [lib/features/wage\_system/store\_page.dart](/lib/features/wage_system/store_page.dart) | Dart | 1,930 | 97 | 121 | 2,148 |
| [lib/features/wage\_system/wage\_wallet\_page.dart](/lib/features/wage_system/wage_wallet_page.dart) | Dart | 780 | 34 | 52 | 866 |
| [lib/features/wage\_system/widgets/active\_effects\_widget.dart](/lib/features/wage_system/widgets/active_effects_widget.dart) | Dart | 303 | 2 | 17 | 322 |
| [lib/features/widget/models/widget\_models.dart](/lib/features/widget/models/widget_models.dart) | Dart | 152 | 15 | 22 | 189 |
| [lib/features/widget/services/memory\_palace\_integration\_service.dart](/lib/features/widget/services/memory_palace_integration_service.dart) | Dart | 350 | 38 | 47 | 435 |
| [lib/features/widget/services/widget\_service.dart](/lib/features/widget/services/widget_service.dart) | Dart | 126 | 88 | 32 | 246 |
| [lib/features/widget/utils/error\_handler.dart](/lib/features/widget/utils/error_handler.dart) | Dart | 360 | 15 | 24 | 399 |
| [lib/l10n/app\_localizations.dart](/lib/l10n/app_localizations.dart) | Dart | 129 | 360 | 90 | 579 |
| [lib/l10n/app\_localizations\_en.dart](/lib/l10n/app_localizations_en.dart) | Dart | 175 | 3 | 77 | 255 |
| [lib/l10n/app\_localizations\_zh.dart](/lib/l10n/app_localizations_zh.dart) | Dart | 165 | 3 | 77 | 245 |
| [lib/main.dart](/lib/main.dart) | Dart | 254 | 25 | 13 | 292 |
| [lib/providers/navigation\_provider.dart](/lib/providers/navigation_provider.dart) | Dart | 51 | 6 | 11 | 68 |
| [lib/router/app\_router.dart](/lib/router/app_router.dart) | Dart | 392 | 40 | 48 | 480 |
| [lib/services/app\_initialization\_service.dart](/lib/services/app_initialization_service.dart) | Dart | 58 | 17 | 19 | 94 |
| [lib/services/first\_time\_service.dart](/lib/services/first_time_service.dart) | Dart | 44 | 16 | 13 | 73 |
| [lib/services/floating\_timer\_service.dart](/lib/services/floating_timer_service.dart) | Dart | 487 | 77 | 79 | 643 |
| [lib/services/global\_timer\_service.dart](/lib/services/global_timer_service.dart) | Dart | 366 | 89 | 99 | 554 |
| [lib/services/locale\_service.dart](/lib/services/locale_service.dart) | Dart | 84 | 23 | 17 | 124 |
| [lib/services/providers/study\_session\_completion\_provider.dart](/lib/services/providers/study_session_completion_provider.dart) | Dart | 19 | 2 | 4 | 25 |
| [lib/services/study\_session\_completion\_service.dart](/lib/services/study_session_completion_service.dart) | Dart | 185 | 37 | 45 | 267 |
| [lib/services/system\_overlay\_permission.dart](/lib/services/system_overlay_permission.dart) | Dart | 256 | 26 | 24 | 306 |
| [lib/shared/config/image\_compression\_config.dart](/lib/shared/config/image_compression_config.dart) | Dart | 67 | 20 | 18 | 105 |
| [lib/shared/services/enhanced\_share\_service.dart](/lib/shared/services/enhanced_share_service.dart) | Dart | 160 | 27 | 37 | 224 |
| [lib/shared/utils/image\_compression\_utils.dart](/lib/shared/utils/image_compression_utils.dart) | Dart | 213 | 41 | 47 | 301 |
| [lib/shared/utils/image\_watermark\_utils.dart](/lib/shared/utils/image_watermark_utils.dart) | Dart | 285 | 62 | 59 | 406 |
| [lib/shared/utils/silent\_refresh\_utils.dart](/lib/shared/utils/silent_refresh_utils.dart) | Dart | 104 | 35 | 21 | 160 |
| [lib/shared/utils/simple\_image\_test.dart](/lib/shared/utils/simple_image_test.dart) | Dart | 82 | 14 | 26 | 122 |
| [lib/shared/utils/ui\_utils.dart](/lib/shared/utils/ui_utils.dart) | Dart | 77 | 19 | 9 | 105 |
| [lib/shared/widgets/icons/wechat\_icon.dart](/lib/shared/widgets/icons/wechat_icon.dart) | Dart | 62 | 8 | 14 | 84 |
| [lib/shared/widgets/oneday\_logo.dart](/lib/shared/widgets/oneday_logo.dart) | Dart | 204 | 17 | 43 | 264 |
| [lib/utils/category\_cleanup\_helper.dart](/lib/utils/category_cleanup_helper.dart) | Dart | 96 | 10 | 20 | 126 |
| [linux/CMakeLists.txt](/linux/CMakeLists.txt) | CMake | 104 | 0 | 25 | 129 |
| [linux/flutter/CMakeLists.txt](/linux/flutter/CMakeLists.txt) | CMake | 79 | 0 | 10 | 89 |
| [linux/flutter/generated\_plugin\_registrant.cc](/linux/flutter/generated_plugin_registrant.cc) | C++ | 11 | 4 | 5 | 20 |
| [linux/flutter/generated\_plugin\_registrant.h](/linux/flutter/generated_plugin_registrant.h) | C++ | 5 | 5 | 6 | 16 |
| [linux/flutter/generated\_plugins.cmake](/linux/flutter/generated_plugins.cmake) | CMake | 20 | 0 | 6 | 26 |
| [linux/runner/CMakeLists.txt](/linux/runner/CMakeLists.txt) | CMake | 21 | 0 | 6 | 27 |
| [linux/runner/main.cc](/linux/runner/main.cc) | C++ | 5 | 0 | 2 | 7 |
| [linux/runner/my\_application.cc](/linux/runner/my_application.cc) | C++ | 83 | 21 | 27 | 131 |
| [linux/runner/my\_application.h](/linux/runner/my_application.h) | C++ | 7 | 7 | 5 | 19 |
| [macos/Flutter/GeneratedPluginRegistrant.swift](/macos/Flutter/GeneratedPluginRegistrant.swift) | Swift | 16 | 3 | 4 | 23 |
| [macos/Podfile](/macos/Podfile) | Ruby | 32 | 1 | 10 | 43 |
| [macos/Runner/AppDelegate.swift](/macos/Runner/AppDelegate.swift) | Swift | 11 | 0 | 3 | 14 |
| [macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 68 | 0 | 0 | 68 |
| [macos/Runner/Base.lproj/MainMenu.xib](/macos/Runner/Base.lproj/MainMenu.xib) | XML | 343 | 0 | 1 | 344 |
| [macos/Runner/MainFlutterWindow.swift](/macos/Runner/MainFlutterWindow.swift) | Swift | 12 | 0 | 4 | 16 |
| [macos/RunnerTests/RunnerTests.swift](/macos/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [pubspec.yaml](/pubspec.yaml) | YAML | 73 | 58 | 33 | 164 |
| [scripts/clean\_category\_data.dart](/scripts/clean_category_data.dart) | Dart | 35 | 8 | 6 | 49 |
| [scripts/clear\_wage\_statistics.dart](/scripts/clear_wage_statistics.dart) | Dart | 25 | 4 | 2 | 31 |
| [scripts/transform\_vocabulary.py](/scripts/transform_vocabulary.py) | Python | 152 | 26 | 32 | 210 |
| [scripts/verify\_navigation\_fix.dart](/scripts/verify_navigation_fix.dart) | Dart | 174 | 20 | 44 | 238 |
| [scripts/verify\_radar\_fix.dart](/scripts/verify_radar_fix.dart) | Dart | 111 | 8 | 24 | 143 |
| [tempPrompts.md](/tempPrompts.md) | Markdown | 201 | 0 | 83 | 284 |
| [test/achievement\_unlock\_navigation\_test.dart](/test/achievement_unlock_navigation_test.dart) | Dart | 79 | 12 | 13 | 104 |
| [test/achievement\_wage\_integration\_test.dart](/test/achievement_wage_integration_test.dart) | Dart | 78 | 15 | 21 | 114 |
| [test/action\_library\_category\_test.dart](/test/action_library_category_test.dart) | Dart | 36 | 2 | 9 | 47 |
| [test/add\_child\_category\_test.dart](/test/add_child_category_test.dart) | Dart | 146 | 30 | 48 | 224 |
| [test/bubble\_offset\_verification.dart](/test/bubble_offset_verification.dart) | Dart | 74 | 11 | 23 | 108 |
| [test/bubble\_positioning\_analysis.dart](/test/bubble_positioning_analysis.dart) | Dart | 114 | 9 | 26 | 149 |
| [test/calendar\_task\_display\_test.dart](/test/calendar_task_display_test.dart) | Dart | 260 | 19 | 36 | 315 |
| [test/category\_edit\_fix\_test.dart](/test/category_edit_fix_test.dart) | Dart | 104 | 15 | 21 | 140 |
| [test/category\_edit\_integration\_test.dart](/test/category_edit_integration_test.dart) | Dart | 108 | 29 | 41 | 178 |
| [test/category\_edit\_state\_test.dart](/test/category_edit_state_test.dart) | Dart | 151 | 40 | 55 | 246 |
| [test/category\_popup\_menu\_test.dart](/test/category_popup_menu_test.dart) | Dart | 157 | 38 | 52 | 247 |
| [test/category\_removal\_test.dart](/test/category_removal_test.dart) | Dart | 151 | 27 | 32 | 210 |
| [test/category\_sidebar\_edit\_test.dart](/test/category_sidebar_edit_test.dart) | Dart | 177 | 45 | 60 | 282 |
| [test/category\_sidebar\_test.dart](/test/category_sidebar_test.dart) | Dart | 118 | 20 | 34 | 172 |
| [test/community\_storage\_test.dart](/test/community_storage_test.dart) | Dart | 105 | 13 | 17 | 135 |
| [test/context\_aware\_album\_creation\_test.dart](/test/context_aware_album_creation_test.dart) | Dart | 97 | 6 | 20 | 123 |
| [test/coordinate\_system\_logic\_test.dart](/test/coordinate_system_logic_test.dart) | Dart | 174 | 32 | 46 | 252 |
| [test/create\_album\_success\_message\_test.dart](/test/create_album_success_message_test.dart) | Dart | 172 | 20 | 27 | 219 |
| [test/custom\_action\_display\_test.dart](/test/custom_action_display_test.dart) | Dart | 78 | 9 | 10 | 97 |
| [test/custom\_action\_editor\_test.dart](/test/custom_action_editor_test.dart) | Dart | 66 | 14 | 14 | 94 |
| [test/custom\_library\_navigation\_test.dart](/test/custom_library_navigation_test.dart) | Dart | 119 | 22 | 27 | 168 |
| [test/custom\_library\_navigation\_unit\_test.dart](/test/custom_library_navigation_unit_test.dart) | Dart | 50 | 15 | 16 | 81 |
| [test/custom\_library\_section\_header\_test.dart](/test/custom_library_section_header_test.dart) | Dart | 92 | 25 | 38 | 155 |
| [test/date\_format\_test.dart](/test/date_format_test.dart) | Dart | 44 | 5 | 13 | 62 |
| [test/default\_category\_edit\_test.dart](/test/default_category_edit_test.dart) | Dart | 132 | 31 | 43 | 206 |
| [test/developer\_tools\_test.dart](/test/developer_tools_test.dart) | Dart | 132 | 12 | 29 | 173 |
| [test/exercise\_library\_sidebar\_test.dart](/test/exercise_library_sidebar_test.dart) | Dart | 76 | 15 | 23 | 114 |
| [test/exercise\_library\_ui\_test.dart](/test/exercise_library_ui_test.dart) | Dart | 156 | 31 | 55 | 242 |
| [test/features/auth/register\_page\_test.dart](/test/features/auth/register_page_test.dart) | Dart | 41 | 10 | 15 | 66 |
| [test/features/exercise/category\_integration\_test.dart](/test/features/exercise/category_integration_test.dart) | Dart | 128 | 22 | 27 | 177 |
| [test/features/exercise/custom\_action\_library\_test.dart](/test/features/exercise/custom_action_library_test.dart) | Dart | 155 | 32 | 43 | 230 |
| [test/features/exercise/custom\_action\_library\_ui\_test.dart](/test/features/exercise/custom_action_library_ui_test.dart) | Dart | 178 | 27 | 39 | 244 |
| [test/features/exercise/custom\_category\_test.dart](/test/features/exercise/custom_category_test.dart) | Dart | 113 | 21 | 24 | 158 |
| [test/features/exercise/exercise\_library\_page\_import\_test.dart](/test/features/exercise/exercise_library_page_import_test.dart) | Dart | 21 | 5 | 6 | 32 |
| [test/features/exercise/exercise\_library\_share\_test.dart](/test/features/exercise/exercise_library_share_test.dart) | Dart | 250 | 26 | 38 | 314 |
| [test/features/help\_feedback/help\_feedback\_page\_test.dart](/test/features/help_feedback/help_feedback_page_test.dart) | Dart | 88 | 17 | 33 | 138 |
| [test/features/profile/image\_cropper\_test.dart](/test/features/profile/image_cropper_test.dart) | Dart | 202 | 10 | 32 | 244 |
| [test/features/profile/image\_cropper\_test.mocks.dart](/test/features/profile/image_cropper_test.mocks.dart) | Dart | 40 | 20 | 7 | 67 |
| [test/features/profile/profile\_data\_sync\_test.dart](/test/features/profile/profile_data_sync_test.dart) | Dart | 165 | 21 | 38 | 224 |
| [test/features/profile/user\_profile\_test.dart](/test/features/profile/user_profile_test.dart) | Dart | 191 | 10 | 43 | 244 |
| [test/features/study\_time/study\_time\_statistics\_test.dart](/test/features/study_time/study_time_statistics_test.dart) | Dart | 264 | 13 | 38 | 315 |
| [test/features/time\_box/action\_library\_selector\_overflow\_test.dart](/test/features/time_box/action_library_selector_overflow_test.dart) | Dart | 301 | 30 | 49 | 380 |
| [test/features/time\_box/timebox\_ui\_test.dart](/test/features/time_box/timebox_ui_test.dart) | Dart | 125 | 11 | 18 | 154 |
| [test/features/ui/color\_consistency\_test.dart](/test/features/ui/color_consistency_test.dart) | Dart | 222 | 19 | 30 | 271 |
| [test/features/ui/dialog\_background\_simple\_test.dart](/test/features/ui/dialog_background_simple_test.dart) | Dart | 198 | 17 | 23 | 238 |
| [test/features/ui/dialog\_background\_test.dart](/test/features/ui/dialog_background_test.dart) | Dart | 228 | 23 | 32 | 283 |
| [test/features/ui/dropdown\_background\_test.dart](/test/features/ui/dropdown_background_test.dart) | Dart | 352 | 25 | 25 | 402 |
| [test/features/ui/dropdown\_pure\_white\_test.dart](/test/features/ui/dropdown_pure_white_test.dart) | Dart | 261 | 15 | 22 | 298 |
| [test/features/ui/final\_verification\_test.dart](/test/features/ui/final_verification_test.dart) | Dart | 233 | 24 | 29 | 286 |
| [test/features/ui/material3\_surface\_tinting\_fix\_test.dart](/test/features/ui/material3_surface_tinting_fix_test.dart) | Dart | 260 | 16 | 23 | 299 |
| [test/features/vocabulary/word\_detail\_dialog\_test.dart](/test/features/vocabulary/word_detail_dialog_test.dart) | Dart | 300 | 22 | 29 | 351 |
| [test/final\_category\_edit\_test.dart](/test/final_category_edit_test.dart) | Dart | 106 | 36 | 43 | 185 |
| [test/graduate\_vocabulary\_manager\_test.dart](/test/graduate_vocabulary_manager_test.dart) | Dart | 58 | 6 | 20 | 84 |
| [test/image\_compression\_quality\_test.dart](/test/image_compression_quality_test.dart) | Dart | 97 | 14 | 25 | 136 |
| [test/integration/study\_session\_completion\_integration\_test.dart](/test/integration/study_session_completion_integration_test.dart) | Dart | 103 | 52 | 53 | 208 |
| [test/integration\_test.dart](/test/integration_test.dart) | Dart | 89 | 27 | 39 | 155 |
| [test/ipad\_calendar\_overflow\_test.dart](/test/ipad_calendar_overflow_test.dart) | Dart | 119 | 21 | 38 | 178 |
| [test/learning\_efficiency\_metrics\_test.dart](/test/learning_efficiency_metrics_test.dart) | Dart | 145 | 10 | 13 | 168 |
| [test/manage\_custom\_libraries\_dialog\_test.dart](/test/manage_custom_libraries_dialog_test.dart) | Dart | 65 | 9 | 18 | 92 |
| [test/memory\_palace\_card\_test.dart](/test/memory_palace_card_test.dart) | Dart | 200 | 13 | 29 | 242 |
| [test/memory\_palace\_persistence\_test.dart](/test/memory_palace_persistence_test.dart) | Dart | 166 | 14 | 25 | 205 |
| [test/navigation\_bottom\_bar\_test.dart](/test/navigation_bottom_bar_test.dart) | Dart | 164 | 37 | 50 | 251 |
| [test/onboarding\_web\_navigation\_test.dart](/test/onboarding_web_navigation_test.dart) | Dart | 30 | 9 | 11 | 50 |
| [test/pdf\_color\_export\_test.dart](/test/pdf_color_export_test.dart) | Dart | 87 | 12 | 14 | 113 |
| [test/photo\_album\_creator\_test.dart](/test/photo_album_creator_test.dart) | Dart | 81 | 2 | 17 | 100 |
| [test/photo\_album\_dialog\_layout\_test.dart](/test/photo_album_dialog_layout_test.dart) | Dart | 75 | 24 | 31 | 130 |
| [test/photo\_album\_dialog\_test.dart](/test/photo_album_dialog_test.dart) | Dart | 96 | 21 | 28 | 145 |
| [test/premium\_article\_service\_test.dart](/test/premium_article_service_test.dart) | Dart | 114 | 10 | 30 | 154 |
| [test/profanity\_filter\_integration\_test.dart](/test/profanity_filter_integration_test.dart) | Dart | 94 | 11 | 25 | 130 |
| [test/profile\_avatar\_click\_test.dart](/test/profile_avatar_click_test.dart) | Dart | 137 | 16 | 33 | 186 |
| [test/profile\_edit\_navigation\_test.dart](/test/profile_edit_navigation_test.dart) | Dart | 59 | 17 | 23 | 99 |
| [test/providers\_integration\_test.dart](/test/providers_integration_test.dart) | Dart | 83 | 9 | 18 | 110 |
| [test/reflection\_calendar\_responsive\_test.dart](/test/reflection_calendar_responsive_test.dart) | Dart | 140 | 30 | 49 | 219 |
| [test/reflection\_log\_integration\_test.dart](/test/reflection_log_integration_test.dart) | Dart | 61 | 11 | 19 | 91 |
| [test/responsive\_layout\_test.dart](/test/responsive_layout_test.dart) | Dart | 230 | 12 | 24 | 266 |
| [test/services/study\_session\_completion\_service\_test.dart](/test/services/study_session_completion_service_test.dart) | Dart | 240 | 32 | 39 | 311 |
| [test/services/study\_session\_completion\_service\_test.mocks.dart](/test/services/study_session_completion_service_test.mocks.dart) | Dart | 402 | 26 | 59 | 487 |
| [test/simple\_profanity\_test.dart](/test/simple_profanity_test.dart) | Dart | 47 | 1 | 16 | 64 |
| [test/standardized\_coordinate\_system\_test.dart](/test/standardized_coordinate_system_test.dart) | Dart | 269 | 28 | 50 | 347 |
| [test/store\_activation\_test.dart](/test/store_activation_test.dart) | Dart | 95 | 14 | 23 | 132 |
| [test/store\_new\_items\_test.dart](/test/store_new_items_test.dart) | Dart | 95 | 13 | 27 | 135 |
| [test/task\_category\_management\_page\_test.dart](/test/task_category_management_page_test.dart) | Dart | 120 | 25 | 39 | 184 |
| [test/task\_category\_test.dart](/test/task_category_test.dart) | Dart | 201 | 17 | 48 | 266 |
| [test/task\_sync\_test.dart](/test/task_sync_test.dart) | Dart | 164 | 27 | 42 | 233 |
| [test/test\_bubble\_alignment\_adjustment.dart](/test/test_bubble_alignment_adjustment.dart) | Dart | 104 | 9 | 23 | 136 |
| [test/test\_bubble\_alignment\_verification.dart](/test/test_bubble_alignment_verification.dart) | Dart | 104 | 10 | 26 | 140 |
| [test/test\_bubble\_center\_alignment.dart](/test/test_bubble_center_alignment.dart) | Dart | 93 | 8 | 20 | 121 |
| [test/test\_bubble\_positioning\_fix.dart](/test/test_bubble_positioning_fix.dart) | Dart | 109 | 6 | 27 | 142 |
| [test/test\_compilation\_fix.dart](/test/test_compilation_fix.dart) | Dart | 144 | 7 | 29 | 180 |
| [test/test\_complete\_image\_compression.dart](/test/test_complete_image_compression.dart) | Dart | 186 | 9 | 38 | 233 |
| [test/test\_coordinate\_analysis.dart](/test/test_coordinate_analysis.dart) | Dart | 104 | 9 | 27 | 140 |
| [test/test\_coordinate\_debug.dart](/test/test_coordinate_debug.dart) | Dart | 84 | 5 | 18 | 107 |
| [test/test\_coordinate\_transformation\_fix.dart](/test/test_coordinate_transformation_fix.dart) | Dart | 111 | 16 | 24 | 151 |
| [test/test\_image\_compression\_implementation.dart](/test/test_image_compression_implementation.dart) | Dart | 147 | 7 | 33 | 187 |
| [test/test\_matrix\_analysis.dart](/test/test_matrix_analysis.dart) | Dart | 130 | 8 | 37 | 175 |
| [test/test\_matrix\_fix\_verification.dart](/test/test_matrix_fix_verification.dart) | Dart | 105 | 14 | 31 | 150 |
| [test/test\_position\_fix\_verification.dart](/test/test_position_fix_verification.dart) | Dart | 38 | 4 | 11 | 53 |
| [test/three\_dot\_menu\_test.dart](/test/three_dot_menu_test.dart) | Dart | 108 | 24 | 32 | 164 |
| [test/timebox\_consistency\_test.dart](/test/timebox_consistency_test.dart) | Dart | 137 | 26 | 46 | 209 |
| [test/timebox\_rest\_skip\_test.dart](/test/timebox_rest_skip_test.dart) | Dart | 192 | 20 | 39 | 251 |
| [test/ui\_overflow\_test.dart](/test/ui_overflow_test.dart) | Dart | 101 | 20 | 32 | 153 |
| [test/vocabulary\_scrollbar\_test.dart](/test/vocabulary_scrollbar_test.dart) | Dart | 98 | 8 | 22 | 128 |
| [test/vocabulary\_test.dart](/test/vocabulary_test.dart) | Dart | 211 | 9 | 28 | 248 |
| [test/widget\_test.dart](/test/widget_test.dart) | Dart | 11 | 9 | 6 | 26 |
| [test/word\_meaning\_service\_test.dart](/test/word_meaning_service_test.dart) | Dart | 61 | 2 | 14 | 77 |
| [test\_apps/category\_cleanup\_demo.dart](/test_apps/category_cleanup_demo.dart) | Dart | 253 | 8 | 26 | 287 |
| [test\_apps/category\_edit\_demo.dart](/test_apps/category_edit_demo.dart) | Dart | 276 | 7 | 24 | 307 |
| [test\_apps/category\_management\_demo.dart](/test_apps/category_management_demo.dart) | Dart | 100 | 1 | 7 | 108 |
| [test\_apps/category\_removal\_demo.dart](/test_apps/category_removal_demo.dart) | Dart | 296 | 8 | 26 | 330 |
| [test\_apps/community\_share\_test.dart](/test_apps/community_share_test.dart) | Dart | 317 | 19 | 41 | 377 |
| [test\_apps/pdf\_color\_demo.dart](/test_apps/pdf_color_demo.dart) | Dart | 256 | 14 | 17 | 287 |
| [test\_apps/premium\_article\_access\_test.dart](/test_apps/premium_article_access_test.dart) | Dart | 107 | 22 | 31 | 160 |
| [test\_apps/simple\_cleanup\_test.dart](/test_apps/simple_cleanup_test.dart) | Dart | 211 | 4 | 23 | 238 |
| [test\_apps/simple\_dialog\_test.dart](/test_apps/simple_dialog_test.dart) | Dart | 215 | 3 | 17 | 235 |
| [test\_apps/snackbar\_duration\_test.dart](/test_apps/snackbar_duration_test.dart) | Dart | 172 | 6 | 10 | 188 |
| [test\_apps/system\_category\_share\_demo.dart](/test_apps/system_category_share_demo.dart) | Dart | 296 | 12 | 23 | 331 |
| [test\_apps/task\_count\_verification.dart](/test_apps/task_count_verification.dart) | Dart | 330 | 13 | 28 | 371 |
| [test\_apps/task\_sync\_demo.dart](/test_apps/task_sync_demo.dart) | Dart | 256 | 4 | 27 | 287 |
| [test\_apps/task\_sync\_verification.dart](/test_apps/task_sync_verification.dart) | Dart | 320 | 9 | 28 | 357 |
| [test\_apps/wage\_calculation\_test.dart](/test_apps/wage_calculation_test.dart) | Dart | 228 | 9 | 26 | 263 |
| [web/index.html](/web/index.html) | HTML | 19 | 15 | 5 | 39 |
| [web/manifest.json](/web/manifest.json) | JSON | 35 | 0 | 0 | 35 |
| [windows/CMakeLists.txt](/windows/CMakeLists.txt) | CMake | 89 | 0 | 20 | 109 |
| [windows/flutter/CMakeLists.txt](/windows/flutter/CMakeLists.txt) | CMake | 98 | 0 | 12 | 110 |
| [windows/flutter/generated\_plugin\_registrant.cc](/windows/flutter/generated_plugin_registrant.cc) | C++ | 15 | 4 | 5 | 24 |
| [windows/flutter/generated\_plugin\_registrant.h](/windows/flutter/generated_plugin_registrant.h) | C++ | 5 | 5 | 6 | 16 |
| [windows/flutter/generated\_plugins.cmake](/windows/flutter/generated_plugins.cmake) | CMake | 22 | 0 | 6 | 28 |
| [windows/runner/CMakeLists.txt](/windows/runner/CMakeLists.txt) | CMake | 34 | 0 | 7 | 41 |
| [windows/runner/flutter\_window.cpp](/windows/runner/flutter_window.cpp) | C++ | 49 | 7 | 16 | 72 |
| [windows/runner/flutter\_window.h](/windows/runner/flutter_window.h) | C++ | 20 | 5 | 9 | 34 |
| [windows/runner/main.cpp](/windows/runner/main.cpp) | C++ | 30 | 4 | 10 | 44 |
| [windows/runner/resource.h](/windows/runner/resource.h) | C++ | 9 | 6 | 2 | 17 |
| [windows/runner/utils.cpp](/windows/runner/utils.cpp) | C++ | 54 | 2 | 10 | 66 |
| [windows/runner/utils.h](/windows/runner/utils.h) | C++ | 8 | 6 | 6 | 20 |
| [windows/runner/win32\_window.cpp](/windows/runner/win32_window.cpp) | C++ | 210 | 24 | 55 | 289 |
| [windows/runner/win32\_window.h](/windows/runner/win32_window.h) | C++ | 48 | 31 | 24 | 103 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)