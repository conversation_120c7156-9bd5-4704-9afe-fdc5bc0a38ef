import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/features/time_box/providers/timebox_provider.dart';
import 'package:oneday/features/time_box/models/timebox_models.dart';
import 'package:oneday/features/study_time/providers/study_time_providers.dart';

void main() {
  runApp(const ProviderScope(child: TaskSyncVerificationApp()));
}

class TaskSyncVerificationApp extends StatelessWidget {
  const TaskSyncVerificationApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Task Sync Verification',
      theme: ThemeData(primarySwatch: Colors.green, useMaterial3: true),
      home: const TaskSyncVerificationPage(),
    );
  }
}

class TaskSyncVerificationPage extends ConsumerStatefulWidget {
  const TaskSyncVerificationPage({super.key});

  @override
  ConsumerState<TaskSyncVerificationPage> createState() =>
      _TaskSyncVerificationPageState();
}

class _TaskSyncVerificationPageState
    extends ConsumerState<TaskSyncVerificationPage> {
  bool _showAllTasks = false;

  // 模拟TimeBox界面的过滤逻辑（修复后）
  List<TimeBoxTask> get _filteredTasks {
    final timeBoxState = ref.watch(timeBoxProvider);
    final today = DateTime.now();

    var filtered = timeBoxState.tasks.where((task) {
      // 如果不显示所有任务，则只显示今日相关的任务
      if (!_showAllTasks) {
        final isToday =
            (task.createdAt.year == today.year &&
                task.createdAt.month == today.month &&
                task.createdAt.day == today.day) ||
            (task.startTime != null &&
                task.startTime!.year == today.year &&
                task.startTime!.month == today.month &&
                task.startTime!.day == today.day);

        if (!isToday) return false;
      }

      return true;
    }).toList();

    return filtered;
  }

  // TimeBox界面统计（修复后）
  Map<String, dynamic> get _timeBoxStats {
    final currentTasks = _filteredTasks;

    final completedTasks = currentTasks
        .where((task) => task.status == TaskStatus.completed)
        .length;

    return {
      'totalTasks': currentTasks.length,
      'completedTasks': completedTasks,
    };
  }

  // 首页统计（基于startTime的今日任务）
  Map<String, dynamic> get _homePageStats {
    final today = DateTime.now();
    final timeBoxState = ref.watch(timeBoxProvider);
    final todayTasks = timeBoxState.tasks.where((task) {
      if (task.startTime == null) return false;
      return task.startTime!.year == today.year &&
          task.startTime!.month == today.month &&
          task.startTime!.day == today.day;
    }).toList();

    final completedTasks = todayTasks
        .where((task) => task.status == TaskStatus.completed)
        .length;

    return {'totalTasks': todayTasks.length, 'completedTasks': completedTasks};
  }

  @override
  Widget build(BuildContext context) {
    final timeBoxState = ref.watch(timeBoxProvider);
    final todayStudySummary = ref.watch(todayStudySummaryProvider);
    final timeBoxStats = _timeBoxStats;
    final homePageStats = _homePageStats;

    // 检查数据一致性
    final isConsistent =
        !_showAllTasks &&
        timeBoxStats['totalTasks'] == homePageStats['totalTasks'] &&
        timeBoxStats['completedTasks'] == homePageStats['completedTasks'];

    return Scaffold(
      appBar: AppBar(
        title: const Text('任务同步验证'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 修复状态
            Card(
              color: isConsistent
                  ? Colors.green.shade50
                  : Colors.orange.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          isConsistent ? Icons.check_circle : Icons.warning,
                          color: isConsistent ? Colors.green : Colors.orange,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          isConsistent ? '✅ 数据同步正常' : '⚠️ 数据不一致',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                color: isConsistent
                                    ? Colors.green
                                    : Colors.orange,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      isConsistent
                          ? 'TimeBox界面与首页统计数据一致'
                          : 'TimeBox界面与首页统计数据不一致',
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 显示模式控制
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '显示模式',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    SwitchListTile(
                      title: const Text('显示所有任务'),
                      subtitle: Text(_showAllTasks ? '显示所有任务' : '仅显示今日任务'),
                      value: _showAllTasks,
                      onChanged: (value) {
                        setState(() {
                          _showAllTasks = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 数据对比
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '数据对比',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),

                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            'TimeBox界面',
                            '${timeBoxStats['completedTasks']}/${timeBoxStats['totalTasks']}',
                            _showAllTasks ? Colors.blue : Colors.green,
                            _showAllTasks ? '显示所有任务' : '显示今日任务',
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatCard(
                            '首页统计',
                            '${homePageStats['completedTasks']}/${homePageStats['totalTasks']}',
                            Colors.purple,
                            '基于startTime',
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),

                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            '首页Provider',
                            todayStudySummary['completedTasks'] ?? '0/0',
                            Colors.orange,
                            'todayStudySummary',
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatCard(
                            '总任务数',
                            '${timeBoxState.tasks.length}',
                            Colors.grey,
                            '所有任务',
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const Spacer(),

            // 任务列表预览
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '当前显示的任务 (${_filteredTasks.length}个)',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    ...(_filteredTasks
                        .take(4)
                        .map(
                          (task) => Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Row(
                              children: [
                                Icon(
                                  task.status == TaskStatus.completed
                                      ? Icons.check_circle
                                      : task.status == TaskStatus.inProgress
                                      ? Icons.play_circle
                                      : Icons.radio_button_unchecked,
                                  color: task.status.color,
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    task.title,
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                ),
                                Text(
                                  task.status.displayName,
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: task.status.color,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )),
                    if (_filteredTasks.length > 4)
                      Text(
                        '... 还有 ${_filteredTasks.length - 4} 个任务',
                        style: const TextStyle(
                          fontSize: 10,
                          color: Colors.grey,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    Color color,
    String subtitle,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            subtitle,
            style: TextStyle(fontSize: 10, color: color.withValues(alpha: 0.7)),
          ),
        ],
      ),
    );
  }
}
