import 'package:flutter/material.dart';
import 'package:oneday/features/community/community_storage_service.dart';
import 'package:oneday/features/community/community_feed_page.dart';
import 'package:oneday/features/exercise/action_library_category_manager.dart';

/// 社区分享功能测试页面
///
/// 用于测试动作库分类分享到社区的功能是否正常工作
class CommunityShareTestPage extends StatefulWidget {
  const CommunityShareTestPage({super.key});

  @override
  State<CommunityShareTestPage> createState() => _CommunityShareTestPageState();
}

class _CommunityShareTestPageState extends State<CommunityShareTestPage> {
  final CommunityStorageService _storageService =
      CommunityStorageService.instance;
  int _postCount = 0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadPostCount();
  }

  Future<void> _loadPostCount() async {
    final posts = await _storageService.getAllPosts();
    setState(() {
      _postCount = posts.length;
    });
  }

  /// 模拟分类分享功能
  Future<void> _simulateShareCategory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 创建测试分类节点
      final testCategory = ActionLibraryCategoryNode(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        title: '测试分类_${DateTime.now().hour}:${DateTime.now().minute}',
        children: [
          ActionLibraryCategoryNode(id: 'sub1', title: '子分类1', children: []),
          ActionLibraryCategoryNode(id: 'sub2', title: '子分类2', children: []),
        ],
      );

      // 模拟分享过程
      await _createTestCategorySharePost(testCategory);

      // 刷新帖子数量
      await _loadPostCount();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('测试分类"${testCategory.title}"已成功共享到社区！'),
            backgroundColor: const Color(0xFF0F7B6C),
            duration: const Duration(milliseconds: 300),
            action: SnackBarAction(
              label: '查看社区',
              textColor: Colors.white,
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CommunityFeedPage(),
                  ),
                );
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('分享失败: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 创建测试分类共享帖子
  Future<void> _createTestCategorySharePost(
    ActionLibraryCategoryNode node,
  ) async {
    // 获取下一个帖子ID
    final postId = await _storageService.getNextPostId();

    // 创建用户信息
    final currentUser = UserInfo(
      id: 1,
      username: '测试用户',
      avatar: '',
      isVerified: false,
    );

    // 构建分类结构描述
    final categoryStructure = _buildCategoryStructureText(node);
    final childrenCount = _getCategoryChildrenCount(node);

    // 构建帖子内容
    final content =
        '''🗂️ 测试分享动作库分类结构：${node.title}

📊 分类统计：
• 总分类数：${childrenCount + 1} 个
• 子分类层级：${_getMaxDepth(node)} 层

📋 分类结构：
$categoryStructure

💡 这是一个测试分类结构，用于验证分享功能是否正常工作！

#动作库分类 #测试 #功能验证''';

    // 创建新帖子
    final newPost = CommunityPost(
      id: postId,
      author: currentUser,
      content: content,
      type: PostType.experience,
      tags: ['动作库分类', '测试', '功能验证'],
      images: [],
      likeCount: 0,
      commentCount: 0,
      shareCount: 0,
      createdAt: DateTime.now(),
      isLiked: false,
    );

    // 保存到社区存储
    final success = await _storageService.addPost(newPost);
    if (!success) {
      throw Exception('保存到社区失败');
    }

    // 确保数据已经持久化
    await _storageService.initialize();
    final allPosts = await _storageService.getAllPosts();
    final savedPostExists = allPosts.any((post) => post.id == postId);

    if (!savedPostExists) {
      throw Exception('帖子保存验证失败');
    }

    print('✅ 测试分类"${node.title}"已成功共享到社区，帖子ID: $postId');
    print('📊 当前社区帖子总数: ${allPosts.length}');
  }

  /// 构建分类结构文本
  String _buildCategoryStructureText(
    ActionLibraryCategoryNode node, {
    int level = 0,
  }) {
    final indent = '  ' * level;
    final prefix = level == 0 ? '📁' : '📂';

    String result = '$indent$prefix ${node.title}\n';

    for (var child in node.children) {
      result += _buildCategoryStructureText(child, level: level + 1);
    }

    return result;
  }

  /// 获取分类子节点数量（递归计算）
  int _getCategoryChildrenCount(ActionLibraryCategoryNode node) {
    int count = node.children.length;
    for (var child in node.children) {
      count += _getCategoryChildrenCount(child);
    }
    return count;
  }

  /// 获取分类树的最大深度
  int _getMaxDepth(ActionLibraryCategoryNode node, {int currentDepth = 0}) {
    if (node.children.isEmpty) {
      return currentDepth;
    }

    int maxChildDepth = currentDepth;
    for (var child in node.children) {
      final childDepth = _getMaxDepth(child, currentDepth: currentDepth + 1);
      if (childDepth > maxChildDepth) {
        maxChildDepth = childDepth;
      }
    }

    return maxChildDepth;
  }

  /// 清空所有帖子
  Future<void> _clearAllPosts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _storageService.clearAllPosts();
      await _loadPostCount();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('所有帖子已清空'),
            backgroundColor: Color(0xFF2E7EED),
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('清空失败: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('社区分享功能测试'),
        backgroundColor: const Color(0xFF2F76DA),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '📊 当前状态',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('社区帖子总数：$_postCount'),
                    const SizedBox(height: 8),
                    Text('测试时间：${DateTime.now().toString().substring(0, 19)}'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            ElevatedButton(
              onPressed: _isLoading ? null : _simulateShareCategory,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0F7B6C),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isLoading
                  ? const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        ),
                        SizedBox(width: 8),
                        Text('分享中...'),
                      ],
                    )
                  : const Text('🗂️ 模拟分类分享'),
            ),
            const SizedBox(height: 16),

            ElevatedButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CommunityFeedPage(),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2E7EED),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('📱 查看社区页面'),
            ),
            const SizedBox(height: 16),

            ElevatedButton(
              onPressed: _isLoading ? null : _clearAllPosts,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFE03E3E),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('🗑️ 清空所有帖子'),
            ),
            const SizedBox(height: 32),

            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFF7F6F3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFFE3E2E0)),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '🧪 测试说明',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '1. 点击"模拟分类分享"创建测试帖子\n'
                    '2. 点击"查看社区页面"验证帖子是否显示\n'
                    '3. 检查帖子内容和时间是否正确\n'
                    '4. 测试完成后可清空所有帖子',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF787774),
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

void main() {
  runApp(
    MaterialApp(
      title: 'Community Share Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'SF Pro Display',
      ),
      home: const CommunityShareTestPage(),
    ),
  );
}
