import 'package:flutter/material.dart';
import 'package:oneday/features/community/community_storage_service.dart';
import 'package:oneday/features/community/community_feed_page.dart';
import 'package:oneday/core/data/pao_exercises_data.dart';

/// 系统分类共享功能演示页面
class SystemCategoryShareDemo extends StatefulWidget {
  const SystemCategoryShareDemo({super.key});

  @override
  State<SystemCategoryShareDemo> createState() =>
      _SystemCategoryShareDemoState();
}

class _SystemCategoryShareDemoState extends State<SystemCategoryShareDemo> {
  final CommunityStorageService _storageService =
      CommunityStorageService.instance;
  int _postCount = 0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadPostCount();
  }

  Future<void> _loadPostCount() async {
    final posts = await _storageService.getAllPosts();
    setState(() {
      _postCount = posts.length;
    });
  }

  /// 模拟系统分类共享功能
  Future<void> _shareSystemCategory(String categoryName) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 获取系统分类的动作数据
      final categoryExercises = PAOExercisesData.getExercisesByCategory(
        categoryName,
      );
      if (categoryExercises == null || categoryExercises.isEmpty) {
        throw Exception('分类"$categoryName"没有找到动作数据');
      }

      // 获取下一个帖子ID
      final postId = await _storageService.getNextPostId();

      // 创建当前用户信息
      final currentUser = UserInfo(
        id: 1,
        username: '系统分类测试用户',
        avatar: '',
        isVerified: false,
      );

      // 构建分类信息内容
      final categoryInfo = StringBuffer();
      categoryInfo.writeln('🏃‍♂️ 系统动作库分类分享');
      categoryInfo.writeln('');
      categoryInfo.writeln('📂 分类名称：$categoryName');
      categoryInfo.writeln('📝 分类类型：系统内置分类');
      categoryInfo.writeln('🎯 动作数量：${categoryExercises.length} 个');
      categoryInfo.writeln('');

      if (categoryExercises.isNotEmpty) {
        categoryInfo.writeln('💪 包含动作：');
        int count = 0;
        for (final exercise in categoryExercises.values) {
          if (count >= 5) {
            categoryInfo.writeln('... 还有 ${categoryExercises.length - 5} 个动作');
            break;
          }
          categoryInfo.writeln(
            '• ${exercise.letter} - ${exercise.name} (${exercise.nameEn})',
          );
          count++;
        }
        categoryInfo.writeln('');
      }

      categoryInfo.writeln('🔗 来自OneDay系统动作库');
      categoryInfo.writeln('');
      categoryInfo.writeln('#系统动作库 #$categoryName #动觉记忆 #学习方法');

      // 创建社区帖子
      final newPost = CommunityPost(
        id: postId,
        author: currentUser,
        content: categoryInfo.toString(),
        type: PostType.experience,
        tags: ['系统动作库', categoryName, '动觉记忆', '学习方法'],
        images: [],
        likeCount: 0,
        commentCount: 0,
        shareCount: 0,
        createdAt: DateTime.now(),
        isLiked: false,
      );

      // 保存到社区存储
      final success = await _storageService.addPost(newPost);
      if (!success) {
        throw Exception('保存到社区失败');
      }

      // 更新帖子计数
      await _loadPostCount();

      if (mounted) {
        final messenger = ScaffoldMessenger.of(context);
        messenger.showSnackBar(
          SnackBar(
            content: Text('系统分类"$categoryName"已成功共享到社区！'),
            backgroundColor: const Color(0xFF0F7B6C),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        final messenger = ScaffoldMessenger.of(context);
        messenger.showSnackBar(
          SnackBar(
            content: Text('共享失败：$e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('系统分类共享演示'),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF37352F),
        elevation: 0,
      ),
      backgroundColor: const Color(0xFFF7F6F3),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 状态信息
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFFE3E2E0)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '📊 社区状态',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '当前社区帖子数量：$_postCount',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF787774),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // 系统分类列表
            const Text(
              '🏃‍♂️ 系统动作库分类',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 12),

            Expanded(
              child: ListView(
                children: [
                  _buildCategoryCard('健身', '💪', '力量训练、有氧运动等'),
                  _buildCategoryCard('瑜伽', '🧘‍♀️', '体式练习、冥想放松等'),
                  _buildCategoryCard('养生', '☯️', '传统保健、穴位按摩等'),
                  _buildCategoryCard('篮球', '🏀', '运球、投篮、战术等'),
                  _buildCategoryCard('足球', '⚽', '传球、射门、技巧等'),
                  _buildCategoryCard('拉伸', '💼', '办公室拉伸、缓解疲劳等'),
                  _buildCategoryCard('护眼', '👁️', '眼部保健、视力保护等'),
                ],
              ),
            ),

            // 底部操作
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFFE3E2E0)),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        final messenger = ScaffoldMessenger.of(context);
                        await _storageService.clearAllPosts();
                        await _loadPostCount();
                        if (mounted) {
                          messenger.showSnackBar(
                            const SnackBar(
                              content: Text('已清空所有社区帖子'),
                              backgroundColor: Color(0xFF787774),
                            ),
                          );
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF787774),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      icon: const Icon(Icons.clear_all),
                      label: const Text('清空帖子'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const CommunityFeedPage(),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2E7EED),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      icon: const Icon(Icons.forum),
                      label: const Text('查看社区'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryCard(String name, String icon, String description) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 0,
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: const BorderSide(color: Color(0xFFE3E2E0)),
        ),
        child: ListTile(
          leading: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(icon, style: const TextStyle(fontSize: 24)),
            ),
          ),
          title: Text(
            name,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          subtitle: Text(
            description,
            style: const TextStyle(fontSize: 14, color: Color(0xFF787774)),
          ),
          trailing: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : IconButton(
                  onPressed: () => _shareSystemCategory(name),
                  icon: const Icon(Icons.share),
                  color: const Color(0xFF2E7EED),
                  tooltip: '共享到社区',
                ),
        ),
      ),
    );
  }
}
