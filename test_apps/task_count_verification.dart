import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/features/time_box/providers/timebox_provider.dart';
import 'package:oneday/features/time_box/models/timebox_models.dart';

void main() {
  runApp(const ProviderScope(child: TaskCountVerificationApp()));
}

class TaskCountVerificationApp extends StatelessWidget {
  const TaskCountVerificationApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Task Count Verification',
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      home: const TaskCountVerificationPage(),
    );
  }
}

class TaskCountVerificationPage extends ConsumerStatefulWidget {
  const TaskCountVerificationPage({super.key});

  @override
  ConsumerState<TaskCountVerificationPage> createState() =>
      _TaskCountVerificationPageState();
}

class _TaskCountVerificationPageState
    extends ConsumerState<TaskCountVerificationPage> {
  TaskStatus? _selectedStatus;
  TaskPriority? _selectedPriority;
  String? _selectedCategory;

  // 过滤后的任务列表（模拟TimeBoxListPage的逻辑）
  List<TimeBoxTask> get _filteredTasks {
    final timeBoxState = ref.watch(timeBoxProvider);
    var filtered = timeBoxState.tasks.where((task) {
      // 状态过滤
      if (_selectedStatus != null && task.status != _selectedStatus) {
        return false;
      }

      // 优先级过滤
      if (_selectedPriority != null && task.priority != _selectedPriority) {
        return false;
      }

      // 分类过滤
      if (_selectedCategory != null && task.category != _selectedCategory) {
        return false;
      }

      return true;
    }).toList();

    return filtered;
  }

  // 当前显示任务的统计数据（修复后的逻辑）
  Map<String, dynamic> get _currentStats {
    final currentTasks = _filteredTasks;

    final completedTasks = currentTasks
        .where((task) => task.status == TaskStatus.completed)
        .length;
    final totalPlannedMinutes = currentTasks.fold<int>(
      0,
      (sum, task) => sum + task.plannedMinutes,
    );
    final totalEarnedWage = currentTasks.fold<double>(
      0,
      (sum, task) => sum + task.calculateWage(),
    );

    return {
      'totalTasks': currentTasks.length,
      'completedTasks': completedTasks,
      'totalMinutes': totalPlannedMinutes,
      'totalWage': totalEarnedWage,
    };
  }

  // 旧的统计逻辑（基于创建日期）
  Map<String, dynamic> get _oldTodayStats {
    final today = DateTime.now();
    final timeBoxState = ref.watch(timeBoxProvider);
    final todayTasks = timeBoxState.tasks.where((task) {
      return task.createdAt.year == today.year &&
          task.createdAt.month == today.month &&
          task.createdAt.day == today.day;
    }).toList();

    final completedTasks = todayTasks
        .where((task) => task.status == TaskStatus.completed)
        .length;

    return {'totalTasks': todayTasks.length, 'completedTasks': completedTasks};
  }

  @override
  Widget build(BuildContext context) {
    final timeBoxState = ref.watch(timeBoxProvider);
    final currentStats = _currentStats;
    final oldStats = _oldTodayStats;

    return Scaffold(
      appBar: AppBar(
        title: const Text('任务计数验证'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 问题说明
            Card(
              color: Colors.red.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.bug_report, color: Colors.red),
                        const SizedBox(width: 8),
                        Text(
                          '问题描述',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                                color: Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text('界面显示4个任务，但统计显示1/3'),
                    const Text('原因：统计只计算今日创建的任务，界面显示所有任务'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 数据对比
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '数据对比',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),

                    // 总任务数
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            '总任务数',
                            '${timeBoxState.tasks.length}',
                            Colors.grey,
                            '所有任务',
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildStatCard(
                            '显示任务数',
                            '${_filteredTasks.length}',
                            Colors.blue,
                            '界面显示',
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),

                    // 统计对比
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            '修复后统计',
                            '${currentStats['completedTasks']}/${currentStats['totalTasks']}',
                            Colors.green,
                            '与界面一致',
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildStatCard(
                            '修复前统计',
                            '${oldStats['completedTasks']}/${oldStats['totalTasks']}',
                            Colors.red,
                            '只计算今日创建',
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 筛选控制
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '筛选测试',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    const Text('测试筛选功能是否影响统计一致性'),
                    const SizedBox(height: 12),

                    // 状态筛选
                    Wrap(
                      spacing: 8,
                      children: [
                        FilterChip(
                          label: const Text('全部状态'),
                          selected: _selectedStatus == null,
                          onSelected: (selected) {
                            setState(() {
                              _selectedStatus = null;
                            });
                          },
                        ),
                        ...TaskStatus.values.map((status) {
                          return FilterChip(
                            label: Text(status.displayName),
                            selected: _selectedStatus == status,
                            onSelected: (selected) {
                              setState(() {
                                _selectedStatus = selected ? status : null;
                              });
                            },
                          );
                        }),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const Spacer(),

            // 任务列表预览
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '当前显示的任务 (${_filteredTasks.length}个)',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    ...(_filteredTasks
                        .take(4)
                        .map(
                          (task) => Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Row(
                              children: [
                                Icon(
                                  task.status == TaskStatus.completed
                                      ? Icons.check_circle
                                      : task.status == TaskStatus.inProgress
                                      ? Icons.play_circle
                                      : Icons.radio_button_unchecked,
                                  color: task.status.color,
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    task.title,
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                ),
                                Text(
                                  task.status.displayName,
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: task.status.color,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )),
                    if (_filteredTasks.length > 4)
                      Text(
                        '... 还有 ${_filteredTasks.length - 4} 个任务',
                        style: const TextStyle(
                          fontSize: 10,
                          color: Colors.grey,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    Color color,
    String subtitle,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            subtitle,
            style: TextStyle(fontSize: 10, color: color.withValues(alpha: 0.7)),
          ),
        ],
      ),
    );
  }
}
