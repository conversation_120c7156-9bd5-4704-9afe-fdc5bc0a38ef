import 'package:flutter/material.dart';
import 'package:oneday/shared/utils/ui_utils.dart';

/// SnackBar显示时长测试页面
/// 
/// 用于验证SnackBar显示时间是否已修改为0.2秒
class SnackBarDurationTestPage extends StatefulWidget {
  const SnackBarDurationTestPage({super.key});

  @override
  State<SnackBarDurationTestPage> createState() => _SnackBarDurationTestPageState();
}

class _SnackBarDurationTestPageState extends State<SnackBarDurationTestPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SnackBar时长测试'),
        backgroundColor: const Color(0xFF2F76DA),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'SnackBar显示时长测试',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF37352F),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            
            const Text(
              '测试说明：',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '• 点击下方按钮测试不同类型的SnackBar\n'
              '• 成功提示现在应该只显示0.2秒\n'
              '• 观察弹窗是否快速消失',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF787774),
                height: 1.5,
              ),
            ),
            const SizedBox(height: 32),
            
            // 成功提示测试
            ElevatedButton(
              onPressed: () {
                UIUtils.showSuccessSnackBar(
                  context,
                  '分类"篮球"已成功共享到社区！',
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0F7B6C),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                '测试成功提示 (0.2秒)',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
            ),
            const SizedBox(height: 16),
            
            // 直接SnackBar测试
            ElevatedButton(
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('分类"健身"已共享到社区'),
                    backgroundColor: Color(0xFF0F7B6C),
                    duration: Duration(milliseconds: 200),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0F7B6C),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                '测试直接SnackBar (0.2秒)',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
            ),
            const SizedBox(height: 16),
            
            // 对比测试 - 1秒显示
            ElevatedButton(
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('对比：这是1秒显示的SnackBar'),
                    backgroundColor: Color(0xFF2E7EED),
                    duration: Duration(seconds: 1),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2E7EED),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                '对比测试 (1秒)',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
            ),
            const SizedBox(height: 32),
            
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFF7F6F3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFFE3E2E0)),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '✅ 修改内容：',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• UIUtils.showSuccessSnackBar: 1秒 → 0.2秒\n'
                    '• 分类共享成功提示: 默认 → 0.2秒\n'
                    '• 任务创建成功提示: 2秒 → 0.2秒\n'
                    '• 分类管理操作提示: 默认 → 0.2秒\n'
                    '• 社区发布成功提示: 3秒 → 0.2秒',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF787774),
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

void main() {
  runApp(MaterialApp(
    title: 'SnackBar Duration Test',
    theme: ThemeData(
      primarySwatch: Colors.blue,
      fontFamily: 'SF Pro Display',
    ),
    home: const SnackBarDurationTestPage(),
  ));
}
