# 应用图标资源文件夹

## 📁 文件夹说明

这个文件夹用于存放OneDay应用的各种图标资源。

## 🎨 需要添加的图标文件

请将您设计的OneDay图标（全球网络风格，中心有字母"D"）保存为以下文件：

### 主图标
- `app_icon.png` (512x512px) - 主应用图标，用于自动生成其他尺寸

### 手动创建的图标（可选）
- `icon_16.png` (16x16px)
- `icon_32.png` (32x32px) 
- `icon_64.png` (64x64px)
- `icon_128.png` (128x128px)
- `icon_256.png` (256x256px)
- `icon_512.png` (512x512px)
- `icon_1024.png` (1024x1024px)

## 🚀 自动生成图标

1. 将您的512x512px图标保存为 `app_icon.png`
2. 运行以下命令自动生成所有平台的图标：

```bash
flutter pub get
flutter pub run flutter_launcher_icons:main
```

这将自动创建：
- Android各种密度的图标
- iOS各种尺寸的图标  
- Web、Windows、macOS图标

## 💡 设计建议

基于您提供的图标设计：
- 保持线条清晰，确保在小尺寸下仍可识别
- 使用品牌色 #2E7EED 或深色 #37352F
- 背景建议使用透明或白色
- 格式：PNG（支持透明度）

您的全球网络设计非常符合OneDay跨平台学习的理念！🌐📚 