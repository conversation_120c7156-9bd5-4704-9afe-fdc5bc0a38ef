/// 清除学习时间统计和工资数据的脚本
///
/// 用于修复工资计算错误导致的缓存数据问题
/// 这个脚本会指导用户手动清除数据
void main() async {
  print('🧹 清除学习时间统计和工资数据指南');
  print('');

  print('由于这是一个Flutter应用，需要通过应用内的调试功能来清除数据。');
  print('');
  print('📱 请按照以下步骤操作：');
  print('');
  print('1. 启动OneDay应用');
  print('2. 进入"时间盒子"页面');
  print('3. 在Debug模式下，点击AppBar右上角的清理图标（🧹）');
  print('4. 确认清除工资统计数据');
  print('');
  print('或者，您可以手动删除应用数据：');
  print('');
  print('🔧 手动清除方法：');
  print('- iOS: 删除并重新安装应用');
  print('- Android: 设置 > 应用 > OneDay > 存储 > 清除数据');
  print('');
  print('📊 清除后的效果：');
  print('- 学习时间统计数据将重置');
  print('- 工资计算将使用修复后的逻辑');
  print('- 406分钟的任务应显示 ¥1,354 收入');
  print('');
  print('💡 提示: 如果您是开发者，可以在应用中使用调试按钮来清除数据。');
}
