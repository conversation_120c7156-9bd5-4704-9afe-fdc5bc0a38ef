import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';

/// 清理分类数据的脚本
///
/// 这个脚本会：
/// 1. 清理所有旧的分类数据
/// 2. 清理版本信息
/// 3. 强制应用重新初始化为4个系统分类
void main() async {
  print('🧹 开始清理分类数据...');

  try {
    // 初始化SharedPreferences
    final prefs = await SharedPreferences.getInstance();

    // 清理分类相关的所有数据
    final keysToRemove = [
      'task_categories_v1',
      'task_categories_version',
      'timebox_tasks_v1', // 也清理任务数据以避免迁移
    ];

    for (String key in keysToRemove) {
      if (prefs.containsKey(key)) {
        await prefs.remove(key);
        print('🗑️ 已清理: $key');
      } else {
        print('ℹ️ 未找到: $key');
      }
    }

    print('✅ 数据清理完成！');
    print('');
    print('📋 清理结果：');
    print('- 旧的分类数据已删除');
    print('- 版本信息已重置');
    print('- 任务数据已清理（避免迁移）');
    print('');
    print('🔄 下次启动应用时将：');
    print('- 初始化4个系统分类：计算机科学、数学、英语、政治');
    print('- 不再显示"休息"和"其他"分类');
    print('- 设置版本为v2');
  } catch (e) {
    print('❌ 清理失败: $e');
    exit(1);
  }
}
