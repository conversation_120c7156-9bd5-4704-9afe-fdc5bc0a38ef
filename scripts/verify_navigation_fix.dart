#!/usr/bin/env dart

/// 验证底部导航栏隐藏修复的脚本
///
/// 检查路由配置和导航逻辑是否正确实现
library;

import 'dart:io';

void main() {
  print('🔍 验证 OneDay 应用底部导航栏隐藏修复...\n');

  // 检查路由配置文件
  _checkRouterConfiguration();

  // 检查商城页面修复
  _checkStorePageFix();

  // 检查社区页面修复
  _checkCommunityPageFix();

  // 检查新修复的页面
  _checkNewFixedPages();

  // 检查文档完整性
  _checkDocumentation();

  print('\n✅ 验证完成！检查修复进度。');
}

void _checkRouterConfiguration() {
  print('📋 检查路由配置...');

  final routerFile = File('lib/router/app_router.dart');
  if (!routerFile.existsSync()) {
    print('❌ 路由配置文件不存在');
    return;
  }

  final content = routerFile.readAsStringSync();

  // 检查新增的路由常量
  final requiredConstants = [
    'wageWallet = \'/wage-wallet\'',
    'inventory = \'/inventory\'',
    'communityPostEditor = \'/community-post-editor\'',
  ];

  for (final constant in requiredConstants) {
    if (content.contains(constant)) {
      print('  ✅ 路由常量已添加: $constant');
    } else {
      print('  ❌ 缺少路由常量: $constant');
    }
  }

  // 检查独立路由配置
  final requiredRoutes = [
    'path: wageWallet',
    'path: inventory',
    'path: communityPostEditor',
  ];

  for (final route in requiredRoutes) {
    if (content.contains(route)) {
      print('  ✅ 独立路由已配置: $route');
    } else {
      print('  ❌ 缺少独立路由: $route');
    }
  }

  // 检查导入
  if (content.contains(
    'import \'../features/community/community_post_editor_page.dart\'',
  )) {
    print('  ✅ CommunityPostEditorPage 导入已添加');
  } else {
    print('  ❌ 缺少 CommunityPostEditorPage 导入');
  }

  if (content.contains('show InventoryPage')) {
    print('  ✅ InventoryPage 导入已添加');
  } else {
    print('  ❌ 缺少 InventoryPage 导入');
  }
}

void _checkStorePageFix() {
  print('\n🛍️ 检查商城页面修复...');

  final storeFile = File('lib/features/wage_system/store_page.dart');
  if (!storeFile.existsSync()) {
    print('❌ 商城页面文件不存在');
    return;
  }

  final content = storeFile.readAsStringSync();

  // 检查 go_router 导入
  if (content.contains('import \'package:go_router/go_router.dart\'')) {
    print('  ✅ go_router 导入已添加');
  } else {
    print('  ❌ 缺少 go_router 导入');
  }

  // 检查修复后的导航逻辑
  if (content.contains('context.push(') && content.contains('\'/inventory\'')) {
    print('  ✅ 背包导航已修复为使用 context.push()');
  } else {
    print('  ❌ 背包导航仍使用旧的 MaterialPageRoute');
  }

  // 检查参数传递
  if (content.contains('extra: {') &&
      content.contains('\'inventory\': _userInventory')) {
    print('  ✅ 参数传递已正确实现');
  } else {
    print('  ❌ 参数传递实现不正确');
  }
}

void _checkCommunityPageFix() {
  print('\n✍️ 检查社区页面修复...');

  final communityFile = File('lib/features/community/community_feed_page.dart');
  if (!communityFile.existsSync()) {
    print('❌ 社区页面文件不存在');
    return;
  }

  final content = communityFile.readAsStringSync();

  // 检查 go_router 导入
  if (content.contains('import \'package:go_router/go_router.dart\'')) {
    print('  ✅ go_router 导入已添加');
  } else {
    print('  ❌ 缺少 go_router 导入');
  }

  // 检查修复后的导航逻辑
  if (content.contains('context.push(\'/community-post-editor\')')) {
    print('  ✅ 编辑文章导航已修复为使用 context.push()');
  } else {
    print('  ❌ 编辑文章导航仍使用旧的 MaterialPageRoute');
  }

  // 检查是否移除了旧的导入
  if (!content.contains('import \'community_post_editor_page.dart\'')) {
    print('  ✅ 旧的直接导入已移除');
  } else {
    print('  ⚠️ 仍存在旧的直接导入，可能需要清理');
  }
}

void _checkDocumentation() {
  print('\n📚 检查文档完整性...');

  final docFile = File('docs/troubleshooting/BOTTOM_NAVIGATION_BAR_FIX.md');
  if (docFile.existsSync()) {
    print('  ✅ 修复文档已创建');

    final content = docFile.readAsStringSync();
    if (content.contains('## 🐛 问题描述') &&
        content.contains('## 💡 解决方案') &&
        content.contains('## ✅ 修复效果')) {
      print('  ✅ 文档结构完整');
    } else {
      print('  ⚠️ 文档结构可能不完整');
    }
  } else {
    print('  ❌ 修复文档不存在');
  }

  final demoFile = File('example/navigation_bottom_bar_demo.dart');
  if (demoFile.existsSync()) {
    print('  ✅ 演示文件已创建');
  } else {
    print('  ❌ 演示文件不存在');
  }
}

void _checkNewFixedPages() {
  print('\n🔧 检查新修复的页面...');

  // 检查时间盒子页面修复
  final timeboxFile = File('lib/features/time_box/timebox_list_page.dart');
  if (timeboxFile.existsSync()) {
    final content = timeboxFile.readAsStringSync();

    if (content.contains('context.push(') &&
        content.contains('\'/kinesthetic-learning\'')) {
      print('  ✅ 时间盒子动觉学习导航已修复');
    } else {
      print('  ❌ 时间盒子动觉学习导航仍使用旧方式');
    }
  }

  // 检查具身记忆页面修复
  final exerciseFile = File('lib/features/exercise/exercise_library_page.dart');
  if (exerciseFile.existsSync()) {
    final content = exerciseFile.readAsStringSync();

    if (content.contains('context.push(') &&
        content.contains('\'/exercise-session\'')) {
      print('  ✅ 具身记忆运动会话导航已修复');
    } else {
      print('  ❌ 具身记忆运动会话导航仍使用旧方式');
    }

    if (content.contains('context.push(') &&
        content.contains('\'/custom-library-editor\'')) {
      print('  ✅ 自定义动作库编辑器导航已修复');
    } else {
      print('  ❌ 自定义动作库编辑器导航仍使用旧方式');
    }
  }

  // 检查路由配置中的新路由
  final routerFile = File('lib/router/app_router.dart');
  if (routerFile.existsSync()) {
    final content = routerFile.readAsStringSync();

    final newRoutes = [
      'exerciseSession = \'/exercise-session\'',
      'kinestheticLearning = \'/kinesthetic-learning\'',
      'customLibraryEditor = \'/custom-library-editor\'',
    ];

    for (final route in newRoutes) {
      if (content.contains(route)) {
        print('  ✅ 新路由常量已添加: $route');
      } else {
        print('  ❌ 缺少新路由常量: $route');
      }
    }
  }
}
